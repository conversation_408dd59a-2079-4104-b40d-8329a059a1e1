{"version": 3, "sources": ["../../codemirror/addon/edit/closetag.js", "dep:codemirror_addon_edit_closetag_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n/**\n * Tag-closer extension for CodeMirror.\n *\n * This extension adds an \"autoCloseTags\" option that can be set to\n * either true to get the default behavior, or an object to further\n * configure its behavior.\n *\n * These are supported options:\n *\n * `whenClosing` (default true)\n *   Whether to autoclose when the '/' of a closing tag is typed.\n * `whenOpening` (default true)\n *   Whether to autoclose the tag when the final '>' of an opening\n *   tag is typed.\n * `dontCloseTags` (default is empty tags for HTML, none for XML)\n *   An array of tag names that should not be autoclosed.\n * `indentTags` (default is block tags for HTML, none for XML)\n *   An array of tag names that should, when opened, cause a\n *   blank line to be added inside the tag, and the blank line and\n *   closing line to be indented.\n * `emptyTags` (default is none)\n *   An array of XML tag names that should be autoclosed with '/>'.\n *\n * See demos/closetag.html for a usage example.\n */\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../fold/xml-fold\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../fold/xml-fold\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  CodeMirror.defineOption(\"autoCloseTags\", false, function(cm, val, old) {\n    if (old != CodeMirror.Init && old)\n      cm.removeKeyMap(\"autoCloseTags\");\n    if (!val) return;\n    var map = {name: \"autoCloseTags\"};\n    if (typeof val != \"object\" || val.whenClosing !== false)\n      map[\"'/'\"] = function(cm) { return autoCloseSlash(cm); };\n    if (typeof val != \"object\" || val.whenOpening !== false)\n      map[\"'>'\"] = function(cm) { return autoCloseGT(cm); };\n    cm.addKeyMap(map);\n  });\n\n  var htmlDontClose = [\"area\", \"base\", \"br\", \"col\", \"command\", \"embed\", \"hr\", \"img\", \"input\", \"keygen\", \"link\", \"meta\", \"param\",\n                       \"source\", \"track\", \"wbr\"];\n  var htmlIndent = [\"applet\", \"blockquote\", \"body\", \"button\", \"div\", \"dl\", \"fieldset\", \"form\", \"frameset\", \"h1\", \"h2\", \"h3\", \"h4\",\n                    \"h5\", \"h6\", \"head\", \"html\", \"iframe\", \"layer\", \"legend\", \"object\", \"ol\", \"p\", \"select\", \"table\", \"ul\"];\n\n  function autoCloseGT(cm) {\n    if (cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n    var ranges = cm.listSelections(), replacements = [];\n    var opt = cm.getOption(\"autoCloseTags\");\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var pos = ranges[i].head, tok = cm.getTokenAt(pos);\n      var inner = CodeMirror.innerMode(cm.getMode(), tok.state), state = inner.state;\n      var tagInfo = inner.mode.xmlCurrentTag && inner.mode.xmlCurrentTag(state)\n      var tagName = tagInfo && tagInfo.name\n      if (!tagName) return CodeMirror.Pass\n\n      var html = inner.mode.configuration == \"html\";\n      var dontCloseTags = (typeof opt == \"object\" && opt.dontCloseTags) || (html && htmlDontClose);\n      var indentTags = (typeof opt == \"object\" && opt.indentTags) || (html && htmlIndent);\n\n      if (tok.end > pos.ch) tagName = tagName.slice(0, tagName.length - tok.end + pos.ch);\n      var lowerTagName = tagName.toLowerCase();\n      // Don't process the '>' at the end of an end-tag or self-closing tag\n      if (!tagName ||\n          tok.type == \"string\" && (tok.end != pos.ch || !/[\\\"\\']/.test(tok.string.charAt(tok.string.length - 1)) || tok.string.length == 1) ||\n          tok.type == \"tag\" && tagInfo.close ||\n          tok.string.indexOf(\"/\") == (pos.ch - tok.start - 1) || // match something like <someTagName />\n          dontCloseTags && indexOf(dontCloseTags, lowerTagName) > -1 ||\n          closingTagExists(cm, inner.mode.xmlCurrentContext && inner.mode.xmlCurrentContext(state) || [], tagName, pos, true))\n        return CodeMirror.Pass;\n\n      var emptyTags = typeof opt == \"object\" && opt.emptyTags;\n      if (emptyTags && indexOf(emptyTags, tagName) > -1) {\n        replacements[i] = { text: \"/>\", newPos: CodeMirror.Pos(pos.line, pos.ch + 2) };\n        continue;\n      }\n\n      var indent = indentTags && indexOf(indentTags, lowerTagName) > -1;\n      replacements[i] = {indent: indent,\n                         text: \">\" + (indent ? \"\\n\\n\" : \"\") + \"</\" + tagName + \">\",\n                         newPos: indent ? CodeMirror.Pos(pos.line + 1, 0) : CodeMirror.Pos(pos.line, pos.ch + 1)};\n    }\n\n    var dontIndentOnAutoClose = (typeof opt == \"object\" && opt.dontIndentOnAutoClose);\n    for (var i = ranges.length - 1; i >= 0; i--) {\n      var info = replacements[i];\n      cm.replaceRange(info.text, ranges[i].head, ranges[i].anchor, \"+insert\");\n      var sel = cm.listSelections().slice(0);\n      sel[i] = {head: info.newPos, anchor: info.newPos};\n      cm.setSelections(sel);\n      if (!dontIndentOnAutoClose && info.indent) {\n        cm.indentLine(info.newPos.line, null, true);\n        cm.indentLine(info.newPos.line + 1, null, true);\n      }\n    }\n  }\n\n  function autoCloseCurrent(cm, typingSlash) {\n    var ranges = cm.listSelections(), replacements = [];\n    var head = typingSlash ? \"/\" : \"</\";\n    var opt = cm.getOption(\"autoCloseTags\");\n    var dontIndentOnAutoClose = (typeof opt == \"object\" && opt.dontIndentOnSlash);\n    for (var i = 0; i < ranges.length; i++) {\n      if (!ranges[i].empty()) return CodeMirror.Pass;\n      var pos = ranges[i].head, tok = cm.getTokenAt(pos);\n      var inner = CodeMirror.innerMode(cm.getMode(), tok.state), state = inner.state;\n      if (typingSlash && (tok.type == \"string\" || tok.string.charAt(0) != \"<\" ||\n                          tok.start != pos.ch - 1))\n        return CodeMirror.Pass;\n      // Kludge to get around the fact that we are not in XML mode\n      // when completing in JS/CSS snippet in htmlmixed mode. Does not\n      // work for other XML embedded languages (there is no general\n      // way to go from a mixed mode to its current XML state).\n      var replacement, mixed = inner.mode.name != \"xml\" && cm.getMode().name == \"htmlmixed\"\n      if (mixed && inner.mode.name == \"javascript\") {\n        replacement = head + \"script\";\n      } else if (mixed && inner.mode.name == \"css\") {\n        replacement = head + \"style\";\n      } else {\n        var context = inner.mode.xmlCurrentContext && inner.mode.xmlCurrentContext(state)\n        var top = context.length ? context[context.length - 1] : \"\"\n        if (!context || (context.length && closingTagExists(cm, context, top, pos)))\n          return CodeMirror.Pass;\n        replacement = head + top\n      }\n      if (cm.getLine(pos.line).charAt(tok.end) != \">\") replacement += \">\";\n      replacements[i] = replacement;\n    }\n    cm.replaceSelections(replacements);\n    ranges = cm.listSelections();\n    if (!dontIndentOnAutoClose) {\n        for (var i = 0; i < ranges.length; i++)\n            if (i == ranges.length - 1 || ranges[i].head.line < ranges[i + 1].head.line)\n                cm.indentLine(ranges[i].head.line);\n    }\n  }\n\n  function autoCloseSlash(cm) {\n    if (cm.getOption(\"disableInput\")) return CodeMirror.Pass;\n    return autoCloseCurrent(cm, true);\n  }\n\n  CodeMirror.commands.closeTag = function(cm) { return autoCloseCurrent(cm); };\n\n  function indexOf(collection, elt) {\n    if (collection.indexOf) return collection.indexOf(elt);\n    for (var i = 0, e = collection.length; i < e; ++i)\n      if (collection[i] == elt) return i;\n    return -1;\n  }\n\n  // If xml-fold is loaded, we use its functionality to try and verify\n  // whether a given tag is actually unclosed.\n  function closingTagExists(cm, context, tagName, pos, newTag) {\n    if (!CodeMirror.scanForClosingTag) return false;\n    var end = Math.min(cm.lastLine() + 1, pos.line + 500);\n    var nextClose = CodeMirror.scanForClosingTag(cm, pos, null, end);\n    if (!nextClose || nextClose.tag != tagName) return false;\n    // If the immediate wrapping context contains onCx instances of\n    // the same tag, a closing tag only exists if there are at least\n    // that many closing tags of that type following.\n    var onCx = newTag ? 1 : 0\n    for (var i = context.length - 1; i >= 0; i--) {\n      if (context[i] == tagName) ++onCx\n      else break\n    }\n    pos = nextClose.to;\n    for (var i = 1; i < onCx; i++) {\n      var next = CodeMirror.scanForClosingTag(cm, pos, null, end);\n      if (!next || next.tag != tagName) return false;\n      pos = next.to;\n    }\n    return true;\n  }\n});\n", "export default require(\"./node_modules/codemirror/addon/edit/closetag.js\");"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AA6BA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,kBAA2B;AAAA,eACzD,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,kBAAkB,GAAG,GAAG;AAAA;AAExD,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB,MAAAA,YAAW,aAAa,iBAAiB,OAAO,SAAS,IAAI,KAAK,KAAK;AACrE,YAAI,OAAOA,YAAW,QAAQ;AAC5B,aAAG,aAAa,eAAe;AACjC,YAAI,CAAC;AAAK;AACV,YAAI,MAAM,EAAC,MAAM,gBAAe;AAChC,YAAI,OAAO,OAAO,YAAY,IAAI,gBAAgB;AAChD,cAAI,SAAS,SAASC,KAAI;AAAE,mBAAO,eAAeA,GAAE;AAAA,UAAG;AACzD,YAAI,OAAO,OAAO,YAAY,IAAI,gBAAgB;AAChD,cAAI,SAAS,SAASA,KAAI;AAAE,mBAAO,YAAYA,GAAE;AAAA,UAAG;AACtD,WAAG,UAAU,GAAG;AAAA,MAClB,CAAC;AAED,UAAI,gBAAgB;AAAA,QAAC;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAM;AAAA,QAAO;AAAA,QAAW;AAAA,QAAS;AAAA,QAAM;AAAA,QAAO;AAAA,QAAS;AAAA,QAAU;AAAA,QAAQ;AAAA,QAAQ;AAAA,QACjG;AAAA,QAAU;AAAA,QAAS;AAAA,MAAK;AAC7C,UAAI,aAAa;AAAA,QAAC;AAAA,QAAU;AAAA,QAAc;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAO;AAAA,QAAM;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAY;AAAA,QAAM;AAAA,QAAM;AAAA,QAAM;AAAA,QACzG;AAAA,QAAM;AAAA,QAAM;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA,QAAU;AAAA,QAAU;AAAA,QAAM;AAAA,QAAK;AAAA,QAAU;AAAA,QAAS;AAAA,MAAI;AAEvH,eAAS,YAAY,IAAI;AACvB,YAAI,GAAG,UAAU,cAAc;AAAG,iBAAOD,YAAW;AACpD,YAAI,SAAS,GAAG,eAAe,GAAG,eAAe,CAAC;AAClD,YAAI,MAAM,GAAG,UAAU,eAAe;AACtC,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,CAAC,OAAO,GAAG,MAAM;AAAG,mBAAOA,YAAW;AAC1C,cAAI,MAAM,OAAO,GAAG,MAAM,MAAM,GAAG,WAAW,GAAG;AACjD,cAAI,QAAQA,YAAW,UAAU,GAAG,QAAQ,GAAG,IAAI,KAAK,GAAG,QAAQ,MAAM;AACzE,cAAI,UAAU,MAAM,KAAK,iBAAiB,MAAM,KAAK,cAAc,KAAK;AACxE,cAAI,UAAU,WAAW,QAAQ;AACjC,cAAI,CAAC;AAAS,mBAAOA,YAAW;AAEhC,cAAI,OAAO,MAAM,KAAK,iBAAiB;AACvC,cAAI,gBAAiB,OAAO,OAAO,YAAY,IAAI,iBAAmB,QAAQ;AAC9E,cAAI,aAAc,OAAO,OAAO,YAAY,IAAI,cAAgB,QAAQ;AAExE,cAAI,IAAI,MAAM,IAAI;AAAI,sBAAU,QAAQ,MAAM,GAAG,QAAQ,SAAS,IAAI,MAAM,IAAI,EAAE;AAClF,cAAI,eAAe,QAAQ,YAAY;AAEvC,cAAI,CAAC,WACD,IAAI,QAAQ,aAAa,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,OAAO,OAAO,IAAI,OAAO,SAAS,CAAC,CAAC,KAAK,IAAI,OAAO,UAAU,MAC/H,IAAI,QAAQ,SAAS,QAAQ,SAC7B,IAAI,OAAO,QAAQ,GAAG,KAAM,IAAI,KAAK,IAAI,QAAQ,KACjD,iBAAiB,QAAQ,eAAe,YAAY,IAAI,MACxD,iBAAiB,IAAI,MAAM,KAAK,qBAAqB,MAAM,KAAK,kBAAkB,KAAK,KAAK,CAAC,GAAG,SAAS,KAAK,IAAI;AACpH,mBAAOA,YAAW;AAEpB,cAAI,YAAY,OAAO,OAAO,YAAY,IAAI;AAC9C,cAAI,aAAa,QAAQ,WAAW,OAAO,IAAI,IAAI;AACjD,yBAAa,KAAK,EAAE,MAAM,MAAM,QAAQA,YAAW,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,EAAE;AAC7E;AAAA,UACF;AAEA,cAAI,SAAS,cAAc,QAAQ,YAAY,YAAY,IAAI;AAC/D,uBAAa,KAAK;AAAA,YAAC;AAAA,YACA,MAAM,OAAO,SAAS,SAAS,MAAM,OAAO,UAAU;AAAA,YACtD,QAAQ,SAASA,YAAW,IAAI,IAAI,OAAO,GAAG,CAAC,IAAIA,YAAW,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC;AAAA,UAAC;AAAA,QAC5G;AAEA,YAAI,wBAAyB,OAAO,OAAO,YAAY,IAAI;AAC3D,iBAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,cAAI,OAAO,aAAa;AACxB,aAAG,aAAa,KAAK,MAAM,OAAO,GAAG,MAAM,OAAO,GAAG,QAAQ,SAAS;AACtE,cAAI,MAAM,GAAG,eAAe,EAAE,MAAM,CAAC;AACrC,cAAI,KAAK,EAAC,MAAM,KAAK,QAAQ,QAAQ,KAAK,OAAM;AAChD,aAAG,cAAc,GAAG;AACpB,cAAI,CAAC,yBAAyB,KAAK,QAAQ;AACzC,eAAG,WAAW,KAAK,OAAO,MAAM,MAAM,IAAI;AAC1C,eAAG,WAAW,KAAK,OAAO,OAAO,GAAG,MAAM,IAAI;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAEA,eAAS,iBAAiB,IAAI,aAAa;AACzC,YAAI,SAAS,GAAG,eAAe,GAAG,eAAe,CAAC;AAClD,YAAI,OAAO,cAAc,MAAM;AAC/B,YAAI,MAAM,GAAG,UAAU,eAAe;AACtC,YAAI,wBAAyB,OAAO,OAAO,YAAY,IAAI;AAC3D,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAI,CAAC,OAAO,GAAG,MAAM;AAAG,mBAAOA,YAAW;AAC1C,cAAI,MAAM,OAAO,GAAG,MAAM,MAAM,GAAG,WAAW,GAAG;AACjD,cAAI,QAAQA,YAAW,UAAU,GAAG,QAAQ,GAAG,IAAI,KAAK,GAAG,QAAQ,MAAM;AACzE,cAAI,gBAAgB,IAAI,QAAQ,YAAY,IAAI,OAAO,OAAO,CAAC,KAAK,OAChD,IAAI,SAAS,IAAI,KAAK;AACxC,mBAAOA,YAAW;AAKpB,cAAI,aAAa,QAAQ,MAAM,KAAK,QAAQ,SAAS,GAAG,QAAQ,EAAE,QAAQ;AAC1E,cAAI,SAAS,MAAM,KAAK,QAAQ,cAAc;AAC5C,0BAAc,OAAO;AAAA,UACvB,WAAW,SAAS,MAAM,KAAK,QAAQ,OAAO;AAC5C,0BAAc,OAAO;AAAA,UACvB,OAAO;AACL,gBAAI,UAAU,MAAM,KAAK,qBAAqB,MAAM,KAAK,kBAAkB,KAAK;AAChF,gBAAI,MAAM,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK;AACzD,gBAAI,CAAC,WAAY,QAAQ,UAAU,iBAAiB,IAAI,SAAS,KAAK,GAAG;AACvE,qBAAOA,YAAW;AACpB,0BAAc,OAAO;AAAA,UACvB;AACA,cAAI,GAAG,QAAQ,IAAI,IAAI,EAAE,OAAO,IAAI,GAAG,KAAK;AAAK,2BAAe;AAChE,uBAAa,KAAK;AAAA,QACpB;AACA,WAAG,kBAAkB,YAAY;AACjC,iBAAS,GAAG,eAAe;AAC3B,YAAI,CAAC,uBAAuB;AACxB,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,gBAAI,KAAK,OAAO,SAAS,KAAK,OAAO,GAAG,KAAK,OAAO,OAAO,IAAI,GAAG,KAAK;AACnE,iBAAG,WAAW,OAAO,GAAG,KAAK,IAAI;AAAA,QAC7C;AAAA,MACF;AAEA,eAAS,eAAe,IAAI;AAC1B,YAAI,GAAG,UAAU,cAAc;AAAG,iBAAOA,YAAW;AACpD,eAAO,iBAAiB,IAAI,IAAI;AAAA,MAClC;AAEA,MAAAA,YAAW,SAAS,WAAW,SAAS,IAAI;AAAE,eAAO,iBAAiB,EAAE;AAAA,MAAG;AAE3E,eAAS,QAAQ,YAAY,KAAK;AAChC,YAAI,WAAW;AAAS,iBAAO,WAAW,QAAQ,GAAG;AACrD,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,EAAE;AAC9C,cAAI,WAAW,MAAM;AAAK,mBAAO;AACnC,eAAO;AAAA,MACT;AAIA,eAAS,iBAAiB,IAAI,SAAS,SAAS,KAAK,QAAQ;AAC3D,YAAI,CAACA,YAAW;AAAmB,iBAAO;AAC1C,YAAI,MAAM,KAAK,IAAI,GAAG,SAAS,IAAI,GAAG,IAAI,OAAO,GAAG;AACpD,YAAI,YAAYA,YAAW,kBAAkB,IAAI,KAAK,MAAM,GAAG;AAC/D,YAAI,CAAC,aAAa,UAAU,OAAO;AAAS,iBAAO;AAInD,YAAI,OAAO,SAAS,IAAI;AACxB,iBAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,cAAI,QAAQ,MAAM;AAAS,cAAE;AAAA;AACxB;AAAA,QACP;AACA,cAAM,UAAU;AAChB,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAI,OAAOA,YAAW,kBAAkB,IAAI,KAAK,MAAM,GAAG;AAC1D,cAAI,CAAC,QAAQ,KAAK,OAAO;AAAS,mBAAO;AACzC,gBAAM,KAAK;AAAA,QACb;AACA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA;AAAA;;;ACxLD,IAAO,4CAAQ;", "names": ["CodeMirror", "cm"]}