import { get, post, del, put, file } from '/@/utils/request';

export default {
    project:{
      getList: (params: object) => get('/project/list', params),
      getOptionList:(params: object) => get('/project/optionList', params),
      add: (data: object) => post('/project/add', data),
      edit: (data: object) => put('/project/edit', data),
      del: (id: string) => del('/project/del', { id }),
      upload: (data: FormData) => post('/project/upload', data),
      export: (params: object) => file('/project/export', params),
      transfer: (params: object) => put('/project/transfer', params),
      getInstances: (params: object) => get('/project/instanceList', params),
      getCompanyList: () => get('/project/companyList')
    },
    instance: {
      getShowList: (params: object) => get('/product/instance/showList', params),
      transfer: (params: object) => put('/product/instance/transferAll', params)
    }
    
  }