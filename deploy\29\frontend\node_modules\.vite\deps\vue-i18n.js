import {
  require_vue
} from "./chunk-J6ZXQYW2.js";
import "./chunk-3FGAYEUV.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-J43GMYXM.js";

// node_modules/@intlify/shared/dist/shared.esm-bundler.js
var shared_esm_bundler_exports = {};
__export(shared_esm_bundler_exports, {
  assign: () => assign,
  createEmitter: () => createEmitter,
  escapeHtml: () => escapeHtml,
  format: () => format,
  friendlyJSONstringify: () => friendlyJSONstringify,
  generateCodeFrame: () => generateCodeFrame,
  generateFormatCacheKey: () => generateFormatCacheKey,
  getGlobalThis: () => getGlobalThis,
  hasOwn: () => hasOwn,
  inBrowser: () => inBrowser,
  isArray: () => isArray,
  isBoolean: () => isBoolean,
  isDate: () => isDate,
  isEmptyObject: () => isEmptyObject,
  isFunction: () => isFunction,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isPlainObject: () => isPlainObject,
  isPromise: () => isPromise,
  isRegExp: () => isRegExp,
  isString: () => isString,
  isSymbol: () => isSymbol,
  makeSymbol: () => makeSymbol,
  mark: () => mark,
  measure: () => measure,
  objectToString: () => objectToString,
  toDisplayString: () => toDisplayString,
  toTypeString: () => toTypeString,
  warn: () => warn
});
function format(message, ...args) {
  if (args.length === 1 && isObject(args[0])) {
    args = args[0];
  }
  if (!args || !args.hasOwnProperty) {
    args = {};
  }
  return message.replace(RE_ARGS, (match, identifier) => {
    return args.hasOwnProperty(identifier) ? args[identifier] : "";
  });
}
function warn(msg, err) {
  if (typeof console !== "undefined") {
    console.warn(`[intlify] ` + msg);
    if (err) {
      console.warn(err.stack);
    }
  }
}
function escapeHtml(rawText) {
  return rawText.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
function hasOwn(obj, key) {
  return hasOwnProperty.call(obj, key);
}
function generateCodeFrame(source, start = 0, end = source.length) {
  const lines = source.split(/\r?\n/);
  let count = 0;
  const res = [];
  for (let i = 0; i < lines.length; i++) {
    count += lines[i].length + 1;
    if (count >= start) {
      for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {
        if (j < 0 || j >= lines.length)
          continue;
        const line = j + 1;
        res.push(`${line}${" ".repeat(3 - String(line).length)}|  ${lines[j]}`);
        const lineLength = lines[j].length;
        if (j === i) {
          const pad = start - (count - lineLength) + 1;
          const length = Math.max(1, end > count ? lineLength - pad : end - start);
          res.push(`   |  ` + " ".repeat(pad) + "^".repeat(length));
        } else if (j > i) {
          if (end > count) {
            const length = Math.max(Math.min(end - count, lineLength), 1);
            res.push(`   |  ` + "^".repeat(length));
          }
          count += lineLength + 1;
        }
      }
      break;
    }
  }
  return res.join("\n");
}
function createEmitter() {
  const events = /* @__PURE__ */ new Map();
  const emitter = {
    events,
    on(event, handler) {
      const handlers = events.get(event);
      const added = handlers && handlers.push(handler);
      if (!added) {
        events.set(event, [handler]);
      }
    },
    off(event, handler) {
      const handlers = events.get(event);
      if (handlers) {
        handlers.splice(handlers.indexOf(handler) >>> 0, 1);
      }
    },
    emit(event, payload) {
      (events.get(event) || []).slice().map((handler) => handler(payload));
      (events.get("*") || []).slice().map((handler) => handler(event, payload));
    }
  };
  return emitter;
}
var inBrowser, mark, measure, RE_ARGS, hasSymbol, makeSymbol, generateFormatCacheKey, friendlyJSONstringify, isNumber, isDate, isRegExp, isEmptyObject, assign, _globalThis, getGlobalThis, hasOwnProperty, isArray, isFunction, isString, isBoolean, isSymbol, isObject, isPromise, objectToString, toTypeString, isPlainObject, toDisplayString, RANGE;
var init_shared_esm_bundler = __esm({
  "node_modules/@intlify/shared/dist/shared.esm-bundler.js"() {
    inBrowser = typeof window !== "undefined";
    if (true) {
      const perf = inBrowser && window.performance;
      if (perf && perf.mark && perf.measure && perf.clearMarks && perf.clearMeasures) {
        mark = (tag) => perf.mark(tag);
        measure = (name, startTag, endTag) => {
          perf.measure(name, startTag, endTag);
          perf.clearMarks(startTag);
          perf.clearMarks(endTag);
        };
      }
    }
    RE_ARGS = /\{([0-9a-zA-Z]+)\}/g;
    hasSymbol = typeof Symbol === "function" && typeof Symbol.toStringTag === "symbol";
    makeSymbol = (name) => hasSymbol ? Symbol(name) : name;
    generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });
    friendlyJSONstringify = (json) => JSON.stringify(json).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029").replace(/\u0027/g, "\\u0027");
    isNumber = (val) => typeof val === "number" && isFinite(val);
    isDate = (val) => toTypeString(val) === "[object Date]";
    isRegExp = (val) => toTypeString(val) === "[object RegExp]";
    isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;
    assign = Object.assign;
    getGlobalThis = () => {
      return _globalThis || (_globalThis = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
    };
    hasOwnProperty = Object.prototype.hasOwnProperty;
    isArray = Array.isArray;
    isFunction = (val) => typeof val === "function";
    isString = (val) => typeof val === "string";
    isBoolean = (val) => typeof val === "boolean";
    isSymbol = (val) => typeof val === "symbol";
    isObject = (val) => val !== null && typeof val === "object";
    isPromise = (val) => {
      return isObject(val) && isFunction(val.then) && isFunction(val.catch);
    };
    objectToString = Object.prototype.toString;
    toTypeString = (value) => objectToString.call(value);
    isPlainObject = (val) => toTypeString(val) === "[object Object]";
    toDisplayString = (val) => {
      return val == null ? "" : isArray(val) || isPlainObject(val) && val.toString === objectToString ? JSON.stringify(val, null, 2) : String(val);
    };
    RANGE = 2;
  }
});

// node_modules/@intlify/message-resolver/dist/message-resolver.esm-bundler.js
function hasOwn2(obj, key) {
  return hasOwnProperty2.call(obj, key);
}
function isLiteral(exp) {
  return literalValueRE.test(exp);
}
function stripQuotes(str) {
  const a = str.charCodeAt(0);
  const b = str.charCodeAt(str.length - 1);
  return a === b && (a === 34 || a === 39) ? str.slice(1, -1) : str;
}
function getPathCharType(ch) {
  if (ch === void 0 || ch === null) {
    return "o";
  }
  const code = ch.charCodeAt(0);
  switch (code) {
    case 91:
    case 93:
    case 46:
    case 34:
    case 39:
      return ch;
    case 95:
    case 36:
    case 45:
      return "i";
    case 9:
    case 10:
    case 13:
    case 160:
    case 65279:
    case 8232:
    case 8233:
      return "w";
  }
  return "i";
}
function formatSubPath(path) {
  const trimmed = path.trim();
  if (path.charAt(0) === "0" && isNaN(parseInt(path))) {
    return false;
  }
  return isLiteral(trimmed) ? stripQuotes(trimmed) : "*" + trimmed;
}
function parse(path) {
  const keys = [];
  let index = -1;
  let mode = 0;
  let subPathDepth = 0;
  let c;
  let key;
  let newChar;
  let type;
  let transition;
  let action;
  let typeMap;
  const actions = [];
  actions[0] = () => {
    if (key === void 0) {
      key = newChar;
    } else {
      key += newChar;
    }
  };
  actions[1] = () => {
    if (key !== void 0) {
      keys.push(key);
      key = void 0;
    }
  };
  actions[2] = () => {
    actions[0]();
    subPathDepth++;
  };
  actions[3] = () => {
    if (subPathDepth > 0) {
      subPathDepth--;
      mode = 4;
      actions[0]();
    } else {
      subPathDepth = 0;
      if (key === void 0) {
        return false;
      }
      key = formatSubPath(key);
      if (key === false) {
        return false;
      } else {
        actions[1]();
      }
    }
  };
  function maybeUnescapeQuote() {
    const nextChar = path[index + 1];
    if (mode === 5 && nextChar === "'" || mode === 6 && nextChar === '"') {
      index++;
      newChar = "\\" + nextChar;
      actions[0]();
      return true;
    }
  }
  while (mode !== null) {
    index++;
    c = path[index];
    if (c === "\\" && maybeUnescapeQuote()) {
      continue;
    }
    type = getPathCharType(c);
    typeMap = pathStateMachine[mode];
    transition = typeMap[type] || typeMap["l"] || 8;
    if (transition === 8) {
      return;
    }
    mode = transition[0];
    if (transition[1] !== void 0) {
      action = actions[transition[1]];
      if (action) {
        newChar = c;
        if (action() === false) {
          return;
        }
      }
    }
    if (mode === 7) {
      return keys;
    }
  }
}
function resolveValue(obj, path) {
  if (!isObject2(obj)) {
    return null;
  }
  let hit = cache.get(path);
  if (!hit) {
    hit = parse(path);
    if (hit) {
      cache.set(path, hit);
    }
  }
  if (!hit) {
    return null;
  }
  const len = hit.length;
  let last = obj;
  let i = 0;
  while (i < len) {
    const val = last[hit[i]];
    if (val === void 0) {
      return null;
    }
    last = val;
    i++;
  }
  return last;
}
function handleFlatJson(obj) {
  if (!isObject2(obj)) {
    return obj;
  }
  for (const key in obj) {
    if (!hasOwn2(obj, key)) {
      continue;
    }
    if (!key.includes(".")) {
      if (isObject2(obj[key])) {
        handleFlatJson(obj[key]);
      }
    } else {
      const subKeys = key.split(".");
      const lastIndex = subKeys.length - 1;
      let currentObj = obj;
      for (let i = 0; i < lastIndex; i++) {
        if (!(subKeys[i] in currentObj)) {
          currentObj[subKeys[i]] = {};
        }
        currentObj = currentObj[subKeys[i]];
      }
      currentObj[subKeys[lastIndex]] = obj[key];
      delete obj[key];
      if (isObject2(currentObj[subKeys[lastIndex]])) {
        handleFlatJson(currentObj[subKeys[lastIndex]]);
      }
    }
  }
  return obj;
}
var hasOwnProperty2, isObject2, pathStateMachine, literalValueRE, cache;
var init_message_resolver_esm_bundler = __esm({
  "node_modules/@intlify/message-resolver/dist/message-resolver.esm-bundler.js"() {
    if (true)
      ;
    hasOwnProperty2 = Object.prototype.hasOwnProperty;
    isObject2 = (val) => val !== null && typeof val === "object";
    pathStateMachine = [];
    pathStateMachine[0] = {
      ["w"]: [0],
      ["i"]: [3, 0],
      ["["]: [4],
      ["o"]: [7]
    };
    pathStateMachine[1] = {
      ["w"]: [1],
      ["."]: [2],
      ["["]: [4],
      ["o"]: [7]
    };
    pathStateMachine[2] = {
      ["w"]: [2],
      ["i"]: [3, 0],
      ["0"]: [3, 0]
    };
    pathStateMachine[3] = {
      ["i"]: [3, 0],
      ["0"]: [3, 0],
      ["w"]: [1, 1],
      ["."]: [2, 1],
      ["["]: [4, 1],
      ["o"]: [7, 1]
    };
    pathStateMachine[4] = {
      ["'"]: [5, 0],
      ['"']: [6, 0],
      ["["]: [
        4,
        2
      ],
      ["]"]: [1, 3],
      ["o"]: 8,
      ["l"]: [4, 0]
    };
    pathStateMachine[5] = {
      ["'"]: [4, 0],
      ["o"]: 8,
      ["l"]: [5, 0]
    };
    pathStateMachine[6] = {
      ['"']: [4, 0],
      ["o"]: 8,
      ["l"]: [6, 0]
    };
    literalValueRE = /^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;
    cache = /* @__PURE__ */ new Map();
  }
});

// node_modules/@intlify/runtime/dist/runtime.esm-bundler.js
function pluralDefault(choice, choicesLength) {
  choice = Math.abs(choice);
  if (choicesLength === 2) {
    return choice ? choice > 1 ? 1 : 0 : 1;
  }
  return choice ? Math.min(choice, 2) : 0;
}
function getPluralIndex(options) {
  const index = isNumber(options.pluralIndex) ? options.pluralIndex : -1;
  return options.named && (isNumber(options.named.count) || isNumber(options.named.n)) ? isNumber(options.named.count) ? options.named.count : isNumber(options.named.n) ? options.named.n : index : index;
}
function normalizeNamed(pluralIndex, props) {
  if (!props.count) {
    props.count = pluralIndex;
  }
  if (!props.n) {
    props.n = pluralIndex;
  }
}
function createMessageContext(options = {}) {
  const locale = options.locale;
  const pluralIndex = getPluralIndex(options);
  const pluralRule = isObject(options.pluralRules) && isString(locale) && isFunction(options.pluralRules[locale]) ? options.pluralRules[locale] : pluralDefault;
  const orgPluralRule = isObject(options.pluralRules) && isString(locale) && isFunction(options.pluralRules[locale]) ? pluralDefault : void 0;
  const plural = (messages) => messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];
  const _list = options.list || [];
  const list = (index) => _list[index];
  const _named = options.named || {};
  isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);
  const named = (key) => _named[key];
  function message(key) {
    const msg = isFunction(options.messages) ? options.messages(key) : isObject(options.messages) ? options.messages[key] : false;
    return !msg ? options.parent ? options.parent.message(key) : DEFAULT_MESSAGE : msg;
  }
  const _modifier = (name) => options.modifiers ? options.modifiers[name] : DEFAULT_MODIFIER;
  const normalize = isPlainObject(options.processor) && isFunction(options.processor.normalize) ? options.processor.normalize : DEFAULT_NORMALIZE;
  const interpolate = isPlainObject(options.processor) && isFunction(options.processor.interpolate) ? options.processor.interpolate : DEFAULT_INTERPOLATE;
  const type = isPlainObject(options.processor) && isString(options.processor.type) ? options.processor.type : DEFAULT_MESSAGE_DATA_TYPE;
  const ctx = {
    ["list"]: list,
    ["named"]: named,
    ["plural"]: plural,
    ["linked"]: (key, modifier) => {
      const msg = message(key)(ctx);
      return isString(modifier) ? _modifier(modifier)(msg) : msg;
    },
    ["message"]: message,
    ["type"]: type,
    ["interpolate"]: interpolate,
    ["normalize"]: normalize
  };
  return ctx;
}
var DEFAULT_MODIFIER, DEFAULT_MESSAGE, DEFAULT_MESSAGE_DATA_TYPE, DEFAULT_NORMALIZE, DEFAULT_INTERPOLATE;
var init_runtime_esm_bundler = __esm({
  "node_modules/@intlify/runtime/dist/runtime.esm-bundler.js"() {
    init_shared_esm_bundler();
    DEFAULT_MODIFIER = (str) => str;
    DEFAULT_MESSAGE = (ctx) => "";
    DEFAULT_MESSAGE_DATA_TYPE = "text";
    DEFAULT_NORMALIZE = (values) => values.length === 0 ? "" : values.join("");
    DEFAULT_INTERPOLATE = toDisplayString;
  }
});

// node_modules/@intlify/message-compiler/dist/message-compiler.esm-bundler.js
function createCompileError(code, loc, options = {}) {
  const { domain, messages, args } = options;
  const msg = true ? format((messages || errorMessages)[code] || "", ...args || []) : code;
  const error = new SyntaxError(String(msg));
  error.code = code;
  if (loc) {
    error.location = loc;
  }
  error.domain = domain;
  return error;
}
function defaultOnError(error) {
  throw error;
}
function createPosition(line, column, offset) {
  return { line, column, offset };
}
function createLocation(start, end, source) {
  const loc = { start, end };
  if (source != null) {
    loc.source = source;
  }
  return loc;
}
function createScanner(str) {
  const _buf = str;
  let _index = 0;
  let _line = 1;
  let _column = 1;
  let _peekOffset = 0;
  const isCRLF = (index2) => _buf[index2] === CHAR_CR && _buf[index2 + 1] === CHAR_LF;
  const isLF = (index2) => _buf[index2] === CHAR_LF;
  const isPS = (index2) => _buf[index2] === CHAR_PS;
  const isLS = (index2) => _buf[index2] === CHAR_LS;
  const isLineEnd = (index2) => isCRLF(index2) || isLF(index2) || isPS(index2) || isLS(index2);
  const index = () => _index;
  const line = () => _line;
  const column = () => _column;
  const peekOffset = () => _peekOffset;
  const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];
  const currentChar = () => charAt(_index);
  const currentPeek = () => charAt(_index + _peekOffset);
  function next() {
    _peekOffset = 0;
    if (isLineEnd(_index)) {
      _line++;
      _column = 0;
    }
    if (isCRLF(_index)) {
      _index++;
    }
    _index++;
    _column++;
    return _buf[_index];
  }
  function peek() {
    if (isCRLF(_index + _peekOffset)) {
      _peekOffset++;
    }
    _peekOffset++;
    return _buf[_index + _peekOffset];
  }
  function reset() {
    _index = 0;
    _line = 1;
    _column = 1;
    _peekOffset = 0;
  }
  function resetPeek(offset = 0) {
    _peekOffset = offset;
  }
  function skipToPeek() {
    const target = _index + _peekOffset;
    while (target !== _index) {
      next();
    }
    _peekOffset = 0;
  }
  return {
    index,
    line,
    column,
    peekOffset,
    charAt,
    currentChar,
    currentPeek,
    next,
    peek,
    reset,
    resetPeek,
    skipToPeek
  };
}
function createTokenizer(source, options = {}) {
  const location = options.location !== false;
  const _scnr = createScanner(source);
  const currentOffset = () => _scnr.index();
  const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());
  const _initLoc = currentPosition();
  const _initOffset = currentOffset();
  const _context = {
    currentType: 14,
    offset: _initOffset,
    startLoc: _initLoc,
    endLoc: _initLoc,
    lastType: 14,
    lastOffset: _initOffset,
    lastStartLoc: _initLoc,
    lastEndLoc: _initLoc,
    braceNest: 0,
    inLinked: false,
    text: ""
  };
  const context = () => _context;
  const { onError } = options;
  function emitError(code, pos, offset, ...args) {
    const ctx = context();
    pos.column += offset;
    pos.offset += offset;
    if (onError) {
      const loc = createLocation(ctx.startLoc, pos);
      const err = createCompileError(code, loc, {
        domain: ERROR_DOMAIN$1,
        args
      });
      onError(err);
    }
  }
  function getToken(context2, type, value) {
    context2.endLoc = currentPosition();
    context2.currentType = type;
    const token = { type };
    if (location) {
      token.loc = createLocation(context2.startLoc, context2.endLoc);
    }
    if (value != null) {
      token.value = value;
    }
    return token;
  }
  const getEndToken = (context2) => getToken(context2, 14);
  function eat(scnr, ch) {
    if (scnr.currentChar() === ch) {
      scnr.next();
      return ch;
    } else {
      emitError(0, currentPosition(), 0, ch);
      return "";
    }
  }
  function peekSpaces(scnr) {
    let buf = "";
    while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {
      buf += scnr.currentPeek();
      scnr.peek();
    }
    return buf;
  }
  function skipSpaces(scnr) {
    const buf = peekSpaces(scnr);
    scnr.skipToPeek();
    return buf;
  }
  function isIdentifierStart(ch) {
    if (ch === EOF) {
      return false;
    }
    const cc = ch.charCodeAt(0);
    return cc >= 97 && cc <= 122 || cc >= 65 && cc <= 90 || cc === 95;
  }
  function isNumberStart(ch) {
    if (ch === EOF) {
      return false;
    }
    const cc = ch.charCodeAt(0);
    return cc >= 48 && cc <= 57;
  }
  function isNamedIdentifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ret = isIdentifierStart(scnr.currentPeek());
    scnr.resetPeek();
    return ret;
  }
  function isListIdentifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ch = scnr.currentPeek() === "-" ? scnr.peek() : scnr.currentPeek();
    const ret = isNumberStart(ch);
    scnr.resetPeek();
    return ret;
  }
  function isLiteralStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === LITERAL_DELIMITER;
    scnr.resetPeek();
    return ret;
  }
  function isLinkedDotStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 8) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === ".";
    scnr.resetPeek();
    return ret;
  }
  function isLinkedModifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 9) {
      return false;
    }
    peekSpaces(scnr);
    const ret = isIdentifierStart(scnr.currentPeek());
    scnr.resetPeek();
    return ret;
  }
  function isLinkedDelimiterStart(scnr, context2) {
    const { currentType } = context2;
    if (!(currentType === 8 || currentType === 12)) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === ":";
    scnr.resetPeek();
    return ret;
  }
  function isLinkedReferStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 10) {
      return false;
    }
    const fn = () => {
      const ch = scnr.currentPeek();
      if (ch === "{") {
        return isIdentifierStart(scnr.peek());
      } else if (ch === "@" || ch === "%" || ch === "|" || ch === ":" || ch === "." || ch === CHAR_SP || !ch) {
        return false;
      } else if (ch === CHAR_LF) {
        scnr.peek();
        return fn();
      } else {
        return isIdentifierStart(ch);
      }
    };
    const ret = fn();
    scnr.resetPeek();
    return ret;
  }
  function isPluralStart(scnr) {
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === "|";
    scnr.resetPeek();
    return ret;
  }
  function isTextStart(scnr, reset = true) {
    const fn = (hasSpace = false, prev = "", detectModulo = false) => {
      const ch = scnr.currentPeek();
      if (ch === "{") {
        return prev === "%" ? false : hasSpace;
      } else if (ch === "@" || !ch) {
        return prev === "%" ? true : hasSpace;
      } else if (ch === "%") {
        scnr.peek();
        return fn(hasSpace, "%", true);
      } else if (ch === "|") {
        return prev === "%" || detectModulo ? true : !(prev === CHAR_SP || prev === CHAR_LF);
      } else if (ch === CHAR_SP) {
        scnr.peek();
        return fn(true, CHAR_SP, detectModulo);
      } else if (ch === CHAR_LF) {
        scnr.peek();
        return fn(true, CHAR_LF, detectModulo);
      } else {
        return true;
      }
    };
    const ret = fn();
    reset && scnr.resetPeek();
    return ret;
  }
  function takeChar(scnr, fn) {
    const ch = scnr.currentChar();
    if (ch === EOF) {
      return EOF;
    }
    if (fn(ch)) {
      scnr.next();
      return ch;
    }
    return null;
  }
  function takeIdentifierChar(scnr) {
    const closure = (ch) => {
      const cc = ch.charCodeAt(0);
      return cc >= 97 && cc <= 122 || cc >= 65 && cc <= 90 || cc >= 48 && cc <= 57 || cc === 95 || cc === 36;
    };
    return takeChar(scnr, closure);
  }
  function takeDigit(scnr) {
    const closure = (ch) => {
      const cc = ch.charCodeAt(0);
      return cc >= 48 && cc <= 57;
    };
    return takeChar(scnr, closure);
  }
  function takeHexDigit(scnr) {
    const closure = (ch) => {
      const cc = ch.charCodeAt(0);
      return cc >= 48 && cc <= 57 || cc >= 65 && cc <= 70 || cc >= 97 && cc <= 102;
    };
    return takeChar(scnr, closure);
  }
  function getDigits(scnr) {
    let ch = "";
    let num = "";
    while (ch = takeDigit(scnr)) {
      num += ch;
    }
    return num;
  }
  function readText(scnr) {
    let buf = "";
    while (true) {
      const ch = scnr.currentChar();
      if (ch === "{" || ch === "}" || ch === "@" || ch === "|" || !ch) {
        break;
      } else if (ch === "%") {
        if (isTextStart(scnr)) {
          buf += ch;
          scnr.next();
        } else {
          break;
        }
      } else if (ch === CHAR_SP || ch === CHAR_LF) {
        if (isTextStart(scnr)) {
          buf += ch;
          scnr.next();
        } else if (isPluralStart(scnr)) {
          break;
        } else {
          buf += ch;
          scnr.next();
        }
      } else {
        buf += ch;
        scnr.next();
      }
    }
    return buf;
  }
  function readNamedIdentifier(scnr) {
    skipSpaces(scnr);
    let ch = "";
    let name = "";
    while (ch = takeIdentifierChar(scnr)) {
      name += ch;
    }
    if (scnr.currentChar() === EOF) {
      emitError(6, currentPosition(), 0);
    }
    return name;
  }
  function readListIdentifier(scnr) {
    skipSpaces(scnr);
    let value = "";
    if (scnr.currentChar() === "-") {
      scnr.next();
      value += `-${getDigits(scnr)}`;
    } else {
      value += getDigits(scnr);
    }
    if (scnr.currentChar() === EOF) {
      emitError(6, currentPosition(), 0);
    }
    return value;
  }
  function readLiteral(scnr) {
    skipSpaces(scnr);
    eat(scnr, `'`);
    let ch = "";
    let literal = "";
    const fn = (x) => x !== LITERAL_DELIMITER && x !== CHAR_LF;
    while (ch = takeChar(scnr, fn)) {
      if (ch === "\\") {
        literal += readEscapeSequence(scnr);
      } else {
        literal += ch;
      }
    }
    const current = scnr.currentChar();
    if (current === CHAR_LF || current === EOF) {
      emitError(2, currentPosition(), 0);
      if (current === CHAR_LF) {
        scnr.next();
        eat(scnr, `'`);
      }
      return literal;
    }
    eat(scnr, `'`);
    return literal;
  }
  function readEscapeSequence(scnr) {
    const ch = scnr.currentChar();
    switch (ch) {
      case "\\":
      case `'`:
        scnr.next();
        return `\\${ch}`;
      case "u":
        return readUnicodeEscapeSequence(scnr, ch, 4);
      case "U":
        return readUnicodeEscapeSequence(scnr, ch, 6);
      default:
        emitError(3, currentPosition(), 0, ch);
        return "";
    }
  }
  function readUnicodeEscapeSequence(scnr, unicode, digits) {
    eat(scnr, unicode);
    let sequence = "";
    for (let i = 0; i < digits; i++) {
      const ch = takeHexDigit(scnr);
      if (!ch) {
        emitError(4, currentPosition(), 0, `\\${unicode}${sequence}${scnr.currentChar()}`);
        break;
      }
      sequence += ch;
    }
    return `\\${unicode}${sequence}`;
  }
  function readInvalidIdentifier(scnr) {
    skipSpaces(scnr);
    let ch = "";
    let identifiers = "";
    const closure = (ch2) => ch2 !== "{" && ch2 !== "}" && ch2 !== CHAR_SP && ch2 !== CHAR_LF;
    while (ch = takeChar(scnr, closure)) {
      identifiers += ch;
    }
    return identifiers;
  }
  function readLinkedModifier(scnr) {
    let ch = "";
    let name = "";
    while (ch = takeIdentifierChar(scnr)) {
      name += ch;
    }
    return name;
  }
  function readLinkedRefer(scnr) {
    const fn = (detect = false, buf) => {
      const ch = scnr.currentChar();
      if (ch === "{" || ch === "%" || ch === "@" || ch === "|" || !ch) {
        return buf;
      } else if (ch === CHAR_SP) {
        return buf;
      } else if (ch === CHAR_LF) {
        buf += ch;
        scnr.next();
        return fn(detect, buf);
      } else {
        buf += ch;
        scnr.next();
        return fn(true, buf);
      }
    };
    return fn(false, "");
  }
  function readPlural(scnr) {
    skipSpaces(scnr);
    const plural = eat(scnr, "|");
    skipSpaces(scnr);
    return plural;
  }
  function readTokenInPlaceholder(scnr, context2) {
    let token = null;
    const ch = scnr.currentChar();
    switch (ch) {
      case "{":
        if (context2.braceNest >= 1) {
          emitError(8, currentPosition(), 0);
        }
        scnr.next();
        token = getToken(context2, 2, "{");
        skipSpaces(scnr);
        context2.braceNest++;
        return token;
      case "}":
        if (context2.braceNest > 0 && context2.currentType === 2) {
          emitError(7, currentPosition(), 0);
        }
        scnr.next();
        token = getToken(context2, 3, "}");
        context2.braceNest--;
        context2.braceNest > 0 && skipSpaces(scnr);
        if (context2.inLinked && context2.braceNest === 0) {
          context2.inLinked = false;
        }
        return token;
      case "@":
        if (context2.braceNest > 0) {
          emitError(6, currentPosition(), 0);
        }
        token = readTokenInLinked(scnr, context2) || getEndToken(context2);
        context2.braceNest = 0;
        return token;
      default:
        let validNamedIdentifier = true;
        let validListIdentifier = true;
        let validLiteral = true;
        if (isPluralStart(scnr)) {
          if (context2.braceNest > 0) {
            emitError(6, currentPosition(), 0);
          }
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (context2.braceNest > 0 && (context2.currentType === 5 || context2.currentType === 6 || context2.currentType === 7)) {
          emitError(6, currentPosition(), 0);
          context2.braceNest = 0;
          return readToken(scnr, context2);
        }
        if (validNamedIdentifier = isNamedIdentifierStart(scnr, context2)) {
          token = getToken(context2, 5, readNamedIdentifier(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (validListIdentifier = isListIdentifierStart(scnr, context2)) {
          token = getToken(context2, 6, readListIdentifier(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (validLiteral = isLiteralStart(scnr, context2)) {
          token = getToken(context2, 7, readLiteral(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {
          token = getToken(context2, 13, readInvalidIdentifier(scnr));
          emitError(1, currentPosition(), 0, token.value);
          skipSpaces(scnr);
          return token;
        }
        break;
    }
    return token;
  }
  function readTokenInLinked(scnr, context2) {
    const { currentType } = context2;
    let token = null;
    const ch = scnr.currentChar();
    if ((currentType === 8 || currentType === 9 || currentType === 12 || currentType === 10) && (ch === CHAR_LF || ch === CHAR_SP)) {
      emitError(9, currentPosition(), 0);
    }
    switch (ch) {
      case "@":
        scnr.next();
        token = getToken(context2, 8, "@");
        context2.inLinked = true;
        return token;
      case ".":
        skipSpaces(scnr);
        scnr.next();
        return getToken(context2, 9, ".");
      case ":":
        skipSpaces(scnr);
        scnr.next();
        return getToken(context2, 10, ":");
      default:
        if (isPluralStart(scnr)) {
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (isLinkedDotStart(scnr, context2) || isLinkedDelimiterStart(scnr, context2)) {
          skipSpaces(scnr);
          return readTokenInLinked(scnr, context2);
        }
        if (isLinkedModifierStart(scnr, context2)) {
          skipSpaces(scnr);
          return getToken(context2, 12, readLinkedModifier(scnr));
        }
        if (isLinkedReferStart(scnr, context2)) {
          skipSpaces(scnr);
          if (ch === "{") {
            return readTokenInPlaceholder(scnr, context2) || token;
          } else {
            return getToken(context2, 11, readLinkedRefer(scnr));
          }
        }
        if (currentType === 8) {
          emitError(9, currentPosition(), 0);
        }
        context2.braceNest = 0;
        context2.inLinked = false;
        return readToken(scnr, context2);
    }
  }
  function readToken(scnr, context2) {
    let token = { type: 14 };
    if (context2.braceNest > 0) {
      return readTokenInPlaceholder(scnr, context2) || getEndToken(context2);
    }
    if (context2.inLinked) {
      return readTokenInLinked(scnr, context2) || getEndToken(context2);
    }
    const ch = scnr.currentChar();
    switch (ch) {
      case "{":
        return readTokenInPlaceholder(scnr, context2) || getEndToken(context2);
      case "}":
        emitError(5, currentPosition(), 0);
        scnr.next();
        return getToken(context2, 3, "}");
      case "@":
        return readTokenInLinked(scnr, context2) || getEndToken(context2);
      default:
        if (isPluralStart(scnr)) {
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (isTextStart(scnr)) {
          return getToken(context2, 0, readText(scnr));
        }
        if (ch === "%") {
          scnr.next();
          return getToken(context2, 4, "%");
        }
        break;
    }
    return token;
  }
  function nextToken() {
    const { currentType, offset, startLoc, endLoc } = _context;
    _context.lastType = currentType;
    _context.lastOffset = offset;
    _context.lastStartLoc = startLoc;
    _context.lastEndLoc = endLoc;
    _context.offset = currentOffset();
    _context.startLoc = currentPosition();
    if (_scnr.currentChar() === EOF) {
      return getToken(_context, 14);
    }
    return readToken(_scnr, _context);
  }
  return {
    nextToken,
    currentOffset,
    currentPosition,
    context
  };
}
function fromEscapeSequence(match, codePoint4, codePoint6) {
  switch (match) {
    case `\\\\`:
      return `\\`;
    case `\\'`:
      return `'`;
    default: {
      const codePoint = parseInt(codePoint4 || codePoint6, 16);
      if (codePoint <= 55295 || codePoint >= 57344) {
        return String.fromCodePoint(codePoint);
      }
      return "\uFFFD";
    }
  }
}
function createParser(options = {}) {
  const location = options.location !== false;
  const { onError } = options;
  function emitError(tokenzer, code, start, offset, ...args) {
    const end = tokenzer.currentPosition();
    end.offset += offset;
    end.column += offset;
    if (onError) {
      const loc = createLocation(start, end);
      const err = createCompileError(code, loc, {
        domain: ERROR_DOMAIN,
        args
      });
      onError(err);
    }
  }
  function startNode(type, offset, loc) {
    const node = {
      type,
      start: offset,
      end: offset
    };
    if (location) {
      node.loc = { start: loc, end: loc };
    }
    return node;
  }
  function endNode(node, offset, pos, type) {
    node.end = offset;
    if (type) {
      node.type = type;
    }
    if (location && node.loc) {
      node.loc.end = pos;
    }
  }
  function parseText(tokenizer, value) {
    const context = tokenizer.context();
    const node = startNode(3, context.offset, context.startLoc);
    node.value = value;
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseList(tokenizer, index) {
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(5, offset, loc);
    node.index = parseInt(index, 10);
    tokenizer.nextToken();
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseNamed(tokenizer, key) {
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(4, offset, loc);
    node.key = key;
    tokenizer.nextToken();
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseLiteral(tokenizer, value) {
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(9, offset, loc);
    node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);
    tokenizer.nextToken();
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseLinkedModifier(tokenizer) {
    const token = tokenizer.nextToken();
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(8, offset, loc);
    if (token.type !== 12) {
      emitError(tokenizer, 11, context.lastStartLoc, 0);
      node.value = "";
      endNode(node, offset, loc);
      return {
        nextConsumeToken: token,
        node
      };
    }
    if (token.value == null) {
      emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
    }
    node.value = token.value || "";
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return {
      node
    };
  }
  function parseLinkedKey(tokenizer, value) {
    const context = tokenizer.context();
    const node = startNode(7, context.offset, context.startLoc);
    node.value = value;
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseLinked(tokenizer) {
    const context = tokenizer.context();
    const linkedNode = startNode(6, context.offset, context.startLoc);
    let token = tokenizer.nextToken();
    if (token.type === 9) {
      const parsed = parseLinkedModifier(tokenizer);
      linkedNode.modifier = parsed.node;
      token = parsed.nextConsumeToken || tokenizer.nextToken();
    }
    if (token.type !== 10) {
      emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
    }
    token = tokenizer.nextToken();
    if (token.type === 2) {
      token = tokenizer.nextToken();
    }
    switch (token.type) {
      case 11:
        if (token.value == null) {
          emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseLinkedKey(tokenizer, token.value || "");
        break;
      case 5:
        if (token.value == null) {
          emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseNamed(tokenizer, token.value || "");
        break;
      case 6:
        if (token.value == null) {
          emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseList(tokenizer, token.value || "");
        break;
      case 7:
        if (token.value == null) {
          emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseLiteral(tokenizer, token.value || "");
        break;
      default:
        emitError(tokenizer, 12, context.lastStartLoc, 0);
        const nextContext = tokenizer.context();
        const emptyLinkedKeyNode = startNode(7, nextContext.offset, nextContext.startLoc);
        emptyLinkedKeyNode.value = "";
        endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);
        linkedNode.key = emptyLinkedKeyNode;
        endNode(linkedNode, nextContext.offset, nextContext.startLoc);
        return {
          nextConsumeToken: token,
          node: linkedNode
        };
    }
    endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());
    return {
      node: linkedNode
    };
  }
  function parseMessage(tokenizer) {
    const context = tokenizer.context();
    const startOffset = context.currentType === 1 ? tokenizer.currentOffset() : context.offset;
    const startLoc = context.currentType === 1 ? context.endLoc : context.startLoc;
    const node = startNode(2, startOffset, startLoc);
    node.items = [];
    let nextToken = null;
    do {
      const token = nextToken || tokenizer.nextToken();
      nextToken = null;
      switch (token.type) {
        case 0:
          if (token.value == null) {
            emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseText(tokenizer, token.value || ""));
          break;
        case 6:
          if (token.value == null) {
            emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseList(tokenizer, token.value || ""));
          break;
        case 5:
          if (token.value == null) {
            emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseNamed(tokenizer, token.value || ""));
          break;
        case 7:
          if (token.value == null) {
            emitError(tokenizer, 13, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseLiteral(tokenizer, token.value || ""));
          break;
        case 8:
          const parsed = parseLinked(tokenizer);
          node.items.push(parsed.node);
          nextToken = parsed.nextConsumeToken || null;
          break;
      }
    } while (context.currentType !== 14 && context.currentType !== 1);
    const endOffset = context.currentType === 1 ? context.lastOffset : tokenizer.currentOffset();
    const endLoc = context.currentType === 1 ? context.lastEndLoc : tokenizer.currentPosition();
    endNode(node, endOffset, endLoc);
    return node;
  }
  function parsePlural(tokenizer, offset, loc, msgNode) {
    const context = tokenizer.context();
    let hasEmptyMessage = msgNode.items.length === 0;
    const node = startNode(1, offset, loc);
    node.cases = [];
    node.cases.push(msgNode);
    do {
      const msg = parseMessage(tokenizer);
      if (!hasEmptyMessage) {
        hasEmptyMessage = msg.items.length === 0;
      }
      node.cases.push(msg);
    } while (context.currentType !== 14);
    if (hasEmptyMessage) {
      emitError(tokenizer, 10, loc, 0);
    }
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseResource(tokenizer) {
    const context = tokenizer.context();
    const { offset, startLoc } = context;
    const msgNode = parseMessage(tokenizer);
    if (context.currentType === 14) {
      return msgNode;
    } else {
      return parsePlural(tokenizer, offset, startLoc, msgNode);
    }
  }
  function parse2(source) {
    const tokenizer = createTokenizer(source, assign({}, options));
    const context = tokenizer.context();
    const node = startNode(0, context.offset, context.startLoc);
    if (location && node.loc) {
      node.loc.source = source;
    }
    node.body = parseResource(tokenizer);
    if (context.currentType !== 14) {
      emitError(tokenizer, 13, context.lastStartLoc, 0, source[context.offset] || "");
    }
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  return { parse: parse2 };
}
function getTokenCaption(token) {
  if (token.type === 14) {
    return "EOF";
  }
  const name = (token.value || "").replace(/\r?\n/gu, "\\n");
  return name.length > 10 ? name.slice(0, 9) + "\u2026" : name;
}
function createTransformer(ast, options = {}) {
  const _context = {
    ast,
    helpers: /* @__PURE__ */ new Set()
  };
  const context = () => _context;
  const helper = (name) => {
    _context.helpers.add(name);
    return name;
  };
  return { context, helper };
}
function traverseNodes(nodes, transformer) {
  for (let i = 0; i < nodes.length; i++) {
    traverseNode(nodes[i], transformer);
  }
}
function traverseNode(node, transformer) {
  switch (node.type) {
    case 1:
      traverseNodes(node.cases, transformer);
      transformer.helper("plural");
      break;
    case 2:
      traverseNodes(node.items, transformer);
      break;
    case 6:
      const linked = node;
      traverseNode(linked.key, transformer);
      transformer.helper("linked");
      break;
    case 5:
      transformer.helper("interpolate");
      transformer.helper("list");
      break;
    case 4:
      transformer.helper("interpolate");
      transformer.helper("named");
      break;
  }
}
function transform(ast, options = {}) {
  const transformer = createTransformer(ast);
  transformer.helper("normalize");
  ast.body && traverseNode(ast.body, transformer);
  const context = transformer.context();
  ast.helpers = Array.from(context.helpers);
}
function createCodeGenerator(ast, options) {
  const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;
  const _context = {
    source: ast.loc.source,
    filename,
    code: "",
    column: 1,
    line: 1,
    offset: 0,
    map: void 0,
    breakLineCode,
    needIndent: _needIndent,
    indentLevel: 0
  };
  const context = () => _context;
  function push(code, node) {
    _context.code += code;
  }
  function _newline(n, withBreakLine = true) {
    const _breakLineCode = withBreakLine ? breakLineCode : "";
    push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);
  }
  function indent(withNewLine = true) {
    const level = ++_context.indentLevel;
    withNewLine && _newline(level);
  }
  function deindent(withNewLine = true) {
    const level = --_context.indentLevel;
    withNewLine && _newline(level);
  }
  function newline() {
    _newline(_context.indentLevel);
  }
  const helper = (key) => `_${key}`;
  const needIndent = () => _context.needIndent;
  return {
    context,
    push,
    indent,
    deindent,
    newline,
    helper,
    needIndent
  };
}
function generateLinkedNode(generator, node) {
  const { helper } = generator;
  generator.push(`${helper("linked")}(`);
  generateNode(generator, node.key);
  if (node.modifier) {
    generator.push(`, `);
    generateNode(generator, node.modifier);
  }
  generator.push(`)`);
}
function generateMessageNode(generator, node) {
  const { helper, needIndent } = generator;
  generator.push(`${helper("normalize")}([`);
  generator.indent(needIndent());
  const length = node.items.length;
  for (let i = 0; i < length; i++) {
    generateNode(generator, node.items[i]);
    if (i === length - 1) {
      break;
    }
    generator.push(", ");
  }
  generator.deindent(needIndent());
  generator.push("])");
}
function generatePluralNode(generator, node) {
  const { helper, needIndent } = generator;
  if (node.cases.length > 1) {
    generator.push(`${helper("plural")}([`);
    generator.indent(needIndent());
    const length = node.cases.length;
    for (let i = 0; i < length; i++) {
      generateNode(generator, node.cases[i]);
      if (i === length - 1) {
        break;
      }
      generator.push(", ");
    }
    generator.deindent(needIndent());
    generator.push(`])`);
  }
}
function generateResource(generator, node) {
  if (node.body) {
    generateNode(generator, node.body);
  } else {
    generator.push("null");
  }
}
function generateNode(generator, node) {
  const { helper } = generator;
  switch (node.type) {
    case 0:
      generateResource(generator, node);
      break;
    case 1:
      generatePluralNode(generator, node);
      break;
    case 2:
      generateMessageNode(generator, node);
      break;
    case 6:
      generateLinkedNode(generator, node);
      break;
    case 8:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 7:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 5:
      generator.push(`${helper("interpolate")}(${helper("list")}(${node.index}))`, node);
      break;
    case 4:
      generator.push(`${helper("interpolate")}(${helper("named")}(${JSON.stringify(node.key)}))`, node);
      break;
    case 9:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 3:
      generator.push(JSON.stringify(node.value), node);
      break;
    default:
      if (true) {
        throw new Error(`unhandled codegen node type: ${node.type}`);
      }
  }
}
function baseCompile(source, options = {}) {
  const assignedOptions = assign({}, options);
  const parser = createParser(assignedOptions);
  const ast = parser.parse(source);
  transform(ast, assignedOptions);
  return generate(ast, assignedOptions);
}
var errorMessages, CHAR_SP, CHAR_CR, CHAR_LF, CHAR_LS, CHAR_PS, EOF, LITERAL_DELIMITER, ERROR_DOMAIN$1, ERROR_DOMAIN, KNOWN_ESCAPES, generate;
var init_message_compiler_esm_bundler = __esm({
  "node_modules/@intlify/message-compiler/dist/message-compiler.esm-bundler.js"() {
    init_shared_esm_bundler();
    errorMessages = {
      [0]: `Expected token: '{0}'`,
      [1]: `Invalid token in placeholder: '{0}'`,
      [2]: `Unterminated single quote in placeholder`,
      [3]: `Unknown escape sequence: \\{0}`,
      [4]: `Invalid unicode escape sequence: {0}`,
      [5]: `Unbalanced closing brace`,
      [6]: `Unterminated closing brace`,
      [7]: `Empty placeholder`,
      [8]: `Not allowed nest placeholder`,
      [9]: `Invalid linked format`,
      [10]: `Plural must have messages`,
      [11]: `Unexpected empty linked modifier`,
      [12]: `Unexpected empty linked key`,
      [13]: `Unexpected lexical analysis in token: '{0}'`
    };
    CHAR_SP = " ";
    CHAR_CR = "\r";
    CHAR_LF = "\n";
    CHAR_LS = String.fromCharCode(8232);
    CHAR_PS = String.fromCharCode(8233);
    EOF = void 0;
    LITERAL_DELIMITER = "'";
    ERROR_DOMAIN$1 = "tokenizer";
    ERROR_DOMAIN = "parser";
    KNOWN_ESCAPES = /(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;
    generate = (ast, options = {}) => {
      const mode = isString(options.mode) ? options.mode : "normal";
      const filename = isString(options.filename) ? options.filename : "message.intl";
      const sourceMap = !!options.sourceMap;
      const breakLineCode = options.breakLineCode != null ? options.breakLineCode : mode === "arrow" ? ";" : "\n";
      const needIndent = options.needIndent ? options.needIndent : mode !== "arrow";
      const helpers = ast.helpers || [];
      const generator = createCodeGenerator(ast, {
        mode,
        filename,
        sourceMap,
        breakLineCode,
        needIndent
      });
      generator.push(mode === "normal" ? `function __msg__ (ctx) {` : `(ctx) => {`);
      generator.indent(needIndent);
      if (helpers.length > 0) {
        generator.push(`const { ${helpers.map((s) => `${s}: _${s}`).join(", ")} } = ctx`);
        generator.newline();
      }
      generator.push(`return `);
      generateNode(generator, ast);
      generator.deindent(needIndent);
      generator.push(`}`);
      const { code, map } = generator.context();
      return {
        ast,
        code,
        map: map ? map.toJSON() : void 0
      };
    };
  }
});

// node_modules/@intlify/devtools-if/dist/devtools-if.esm-bundler.js
var IntlifyDevToolsHooks;
var init_devtools_if_esm_bundler = __esm({
  "node_modules/@intlify/devtools-if/dist/devtools-if.esm-bundler.js"() {
    IntlifyDevToolsHooks = {
      I18nInit: "i18n:init",
      FunctionTranslate: "function:translate"
    };
  }
});

// node_modules/@intlify/core-base/dist/core-base.esm-bundler.js
var core_base_esm_bundler_exports = {};
__export(core_base_esm_bundler_exports, {
  DEFAULT_MESSAGE_DATA_TYPE: () => DEFAULT_MESSAGE_DATA_TYPE,
  MISSING_RESOLVE_VALUE: () => MISSING_RESOLVE_VALUE,
  NOT_REOSLVED: () => NOT_REOSLVED,
  VERSION: () => VERSION,
  clearCompileCache: () => clearCompileCache,
  clearDateTimeFormat: () => clearDateTimeFormat,
  clearNumberFormat: () => clearNumberFormat,
  compileToFunction: () => compileToFunction,
  createCompileError: () => createCompileError,
  createCoreContext: () => createCoreContext,
  createCoreError: () => createCoreError,
  createMessageContext: () => createMessageContext,
  datetime: () => datetime,
  getAdditionalMeta: () => getAdditionalMeta,
  getDevToolsHook: () => getDevToolsHook,
  getLocaleChain: () => getLocaleChain,
  getWarnMessage: () => getWarnMessage,
  handleFlatJson: () => handleFlatJson,
  handleMissing: () => handleMissing,
  initI18nDevTools: () => initI18nDevTools,
  isMessageFunction: () => isMessageFunction,
  isTranslateFallbackWarn: () => isTranslateFallbackWarn,
  isTranslateMissingWarn: () => isTranslateMissingWarn,
  number: () => number,
  parse: () => parse,
  parseDateTimeArgs: () => parseDateTimeArgs,
  parseNumberArgs: () => parseNumberArgs,
  parseTranslateArgs: () => parseTranslateArgs,
  registerMessageCompiler: () => registerMessageCompiler,
  resolveValue: () => resolveValue,
  setAdditionalMeta: () => setAdditionalMeta,
  setDevToolsHook: () => setDevToolsHook,
  translate: () => translate,
  translateDevTools: () => translateDevTools,
  updateFallbackLocale: () => updateFallbackLocale
});
function setDevToolsHook(hook) {
  devtools = hook;
}
function getDevToolsHook() {
  return devtools;
}
function initI18nDevTools(i18n, version, meta) {
  devtools && devtools.emit(IntlifyDevToolsHooks.I18nInit, {
    timestamp: Date.now(),
    i18n,
    version,
    meta
  });
}
function createDevToolsHook(hook) {
  return (payloads) => devtools && devtools.emit(hook, payloads);
}
function getWarnMessage(code, ...args) {
  return format(warnMessages[code], ...args);
}
function getDefaultLinkedModifiers() {
  return {
    upper: (val) => isString(val) ? val.toUpperCase() : val,
    lower: (val) => isString(val) ? val.toLowerCase() : val,
    capitalize: (val) => isString(val) ? `${val.charAt(0).toLocaleUpperCase()}${val.substr(1)}` : val
  };
}
function registerMessageCompiler(compiler) {
  _compiler = compiler;
}
function createCoreContext(options = {}) {
  const version = isString(options.version) ? options.version : VERSION;
  const locale = isString(options.locale) ? options.locale : "en-US";
  const fallbackLocale = isArray(options.fallbackLocale) || isPlainObject(options.fallbackLocale) || isString(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : locale;
  const messages = isPlainObject(options.messages) ? options.messages : { [locale]: {} };
  const datetimeFormats = isPlainObject(options.datetimeFormats) ? options.datetimeFormats : { [locale]: {} };
  const numberFormats = isPlainObject(options.numberFormats) ? options.numberFormats : { [locale]: {} };
  const modifiers = assign({}, options.modifiers || {}, getDefaultLinkedModifiers());
  const pluralRules = options.pluralRules || {};
  const missing = isFunction(options.missing) ? options.missing : null;
  const missingWarn = isBoolean(options.missingWarn) || isRegExp(options.missingWarn) ? options.missingWarn : true;
  const fallbackWarn = isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;
  const fallbackFormat = !!options.fallbackFormat;
  const unresolving = !!options.unresolving;
  const postTranslation = isFunction(options.postTranslation) ? options.postTranslation : null;
  const processor = isPlainObject(options.processor) ? options.processor : null;
  const warnHtmlMessage = isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
  const escapeParameter = !!options.escapeParameter;
  const messageCompiler = isFunction(options.messageCompiler) ? options.messageCompiler : _compiler;
  const onWarn = isFunction(options.onWarn) ? options.onWarn : warn;
  const internalOptions = options;
  const __datetimeFormatters = isObject(internalOptions.__datetimeFormatters) ? internalOptions.__datetimeFormatters : /* @__PURE__ */ new Map();
  const __numberFormatters = isObject(internalOptions.__numberFormatters) ? internalOptions.__numberFormatters : /* @__PURE__ */ new Map();
  const __meta = isObject(internalOptions.__meta) ? internalOptions.__meta : {};
  _cid++;
  const context = {
    version,
    cid: _cid,
    locale,
    fallbackLocale,
    messages,
    datetimeFormats,
    numberFormats,
    modifiers,
    pluralRules,
    missing,
    missingWarn,
    fallbackWarn,
    fallbackFormat,
    unresolving,
    postTranslation,
    processor,
    warnHtmlMessage,
    escapeParameter,
    messageCompiler,
    onWarn,
    __datetimeFormatters,
    __numberFormatters,
    __meta
  };
  if (true) {
    context.__v_emitter = internalOptions.__v_emitter != null ? internalOptions.__v_emitter : void 0;
  }
  if (true) {
    initI18nDevTools(context, version, __meta);
  }
  return context;
}
function isTranslateFallbackWarn(fallback, key) {
  return fallback instanceof RegExp ? fallback.test(key) : fallback;
}
function isTranslateMissingWarn(missing, key) {
  return missing instanceof RegExp ? missing.test(key) : missing;
}
function handleMissing(context, key, locale, missingWarn, type) {
  const { missing, onWarn } = context;
  if (true) {
    const emitter = context.__v_emitter;
    if (emitter) {
      emitter.emit("missing", {
        locale,
        key,
        type,
        groupId: `${type}:${key}`
      });
    }
  }
  if (missing !== null) {
    const ret = missing(context, locale, key, type);
    return isString(ret) ? ret : key;
  } else {
    if (isTranslateMissingWarn(missingWarn, key)) {
      onWarn(getWarnMessage(0, { key, locale }));
    }
    return key;
  }
}
function getLocaleChain(ctx, fallback, start) {
  const context = ctx;
  if (!context.__localeChainCache) {
    context.__localeChainCache = /* @__PURE__ */ new Map();
  }
  let chain = context.__localeChainCache.get(start);
  if (!chain) {
    chain = [];
    let block = [start];
    while (isArray(block)) {
      block = appendBlockToChain(chain, block, fallback);
    }
    const defaults = isArray(fallback) ? fallback : isPlainObject(fallback) ? fallback["default"] ? fallback["default"] : null : fallback;
    block = isString(defaults) ? [defaults] : defaults;
    if (isArray(block)) {
      appendBlockToChain(chain, block, false);
    }
    context.__localeChainCache.set(start, chain);
  }
  return chain;
}
function appendBlockToChain(chain, block, blocks) {
  let follow = true;
  for (let i = 0; i < block.length && isBoolean(follow); i++) {
    const locale = block[i];
    if (isString(locale)) {
      follow = appendLocaleToChain(chain, block[i], blocks);
    }
  }
  return follow;
}
function appendLocaleToChain(chain, locale, blocks) {
  let follow;
  const tokens = locale.split("-");
  do {
    const target = tokens.join("-");
    follow = appendItemToChain(chain, target, blocks);
    tokens.splice(-1, 1);
  } while (tokens.length && follow === true);
  return follow;
}
function appendItemToChain(chain, target, blocks) {
  let follow = false;
  if (!chain.includes(target)) {
    follow = true;
    if (target) {
      follow = target[target.length - 1] !== "!";
      const locale = target.replace(/!/g, "");
      chain.push(locale);
      if ((isArray(blocks) || isPlainObject(blocks)) && blocks[locale]) {
        follow = blocks[locale];
      }
    }
  }
  return follow;
}
function updateFallbackLocale(ctx, locale, fallback) {
  const context = ctx;
  context.__localeChainCache = /* @__PURE__ */ new Map();
  getLocaleChain(ctx, fallback, locale);
}
function checkHtmlMessage(source, options) {
  const warnHtmlMessage = isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
  if (warnHtmlMessage && RE_HTML_TAG.test(source)) {
    warn(format(WARN_MESSAGE, { source }));
  }
}
function clearCompileCache() {
  compileCache = /* @__PURE__ */ Object.create(null);
}
function compileToFunction(source, options = {}) {
  {
    checkHtmlMessage(source, options);
    const onCacheKey = options.onCacheKey || defaultOnCacheKey;
    const key = onCacheKey(source);
    const cached = compileCache[key];
    if (cached) {
      return cached;
    }
    let occurred = false;
    const onError = options.onError || defaultOnError;
    options.onError = (err) => {
      occurred = true;
      onError(err);
    };
    const { code } = baseCompile(source, options);
    const msg = new Function(`return ${code}`)();
    return !occurred ? compileCache[key] = msg : msg;
  }
}
function createCoreError(code) {
  return createCompileError(code, null, true ? { messages: errorMessages2 } : void 0);
}
function translate(context, ...args) {
  const { fallbackFormat, postTranslation, unresolving, fallbackLocale, messages } = context;
  const [key, options] = parseTranslateArgs(...args);
  const missingWarn = isBoolean(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const escapeParameter = isBoolean(options.escapeParameter) ? options.escapeParameter : context.escapeParameter;
  const resolvedMessage = !!options.resolvedMessage;
  const defaultMsgOrKey = isString(options.default) || isBoolean(options.default) ? !isBoolean(options.default) ? options.default : key : fallbackFormat ? key : "";
  const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== "";
  const locale = isString(options.locale) ? options.locale : context.locale;
  escapeParameter && escapeParams(options);
  let [format2, targetLocale, message] = !resolvedMessage ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) : [
    key,
    locale,
    messages[locale] || {}
  ];
  let cacheBaseKey = key;
  if (!resolvedMessage && !(isString(format2) || isMessageFunction(format2))) {
    if (enableDefaultMsg) {
      format2 = defaultMsgOrKey;
      cacheBaseKey = format2;
    }
  }
  if (!resolvedMessage && (!(isString(format2) || isMessageFunction(format2)) || !isString(targetLocale))) {
    return unresolving ? NOT_REOSLVED : key;
  }
  if (isString(format2) && context.messageCompiler == null) {
    warn(`The message format compilation is not supported in this build. Because message compiler isn't included. You need to pre-compilation all message format. So translate function return '${key}'.`);
    return key;
  }
  let occurred = false;
  const errorDetector = () => {
    occurred = true;
  };
  const msg = !isMessageFunction(format2) ? compileMessageFormat(context, key, targetLocale, format2, cacheBaseKey, errorDetector) : format2;
  if (occurred) {
    return format2;
  }
  const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);
  const msgContext = createMessageContext(ctxOptions);
  const messaged = evaluateMessage(context, msg, msgContext);
  const ret = postTranslation ? postTranslation(messaged) : messaged;
  if (true) {
    const payloads = {
      timestamp: Date.now(),
      key: isString(key) ? key : isMessageFunction(format2) ? format2.key : "",
      locale: targetLocale || (isMessageFunction(format2) ? format2.locale : ""),
      format: isString(format2) ? format2 : isMessageFunction(format2) ? format2.source : "",
      message: ret
    };
    payloads.meta = assign({}, context.__meta, getAdditionalMeta() || {});
    translateDevTools(payloads);
  }
  return ret;
}
function escapeParams(options) {
  if (isArray(options.list)) {
    options.list = options.list.map((item) => isString(item) ? escapeHtml(item) : item);
  } else if (isObject(options.named)) {
    Object.keys(options.named).forEach((key) => {
      if (isString(options.named[key])) {
        options.named[key] = escapeHtml(options.named[key]);
      }
    });
  }
}
function resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {
  const { messages, onWarn } = context;
  const locales = getLocaleChain(context, fallbackLocale, locale);
  let message = {};
  let targetLocale;
  let format2 = null;
  let from = locale;
  let to = null;
  const type = "translate";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(1, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    message = messages[targetLocale] || {};
    let start = null;
    let startTag;
    let endTag;
    if (inBrowser) {
      start = window.performance.now();
      startTag = "intlify-message-resolve-start";
      endTag = "intlify-message-resolve-end";
      mark && mark(startTag);
    }
    if ((format2 = resolveValue(message, key)) === null) {
      format2 = message[key];
    }
    if (inBrowser) {
      const end = window.performance.now();
      const emitter = context.__v_emitter;
      if (emitter && start && format2) {
        emitter.emit("message-resolve", {
          type: "message-resolve",
          key,
          message: format2,
          time: end - start,
          groupId: `${type}:${key}`
        });
      }
      if (startTag && endTag && mark && measure) {
        mark(endTag);
        measure("intlify message resolve", startTag, endTag);
      }
    }
    if (isString(format2) || isFunction(format2))
      break;
    const missingRet = handleMissing(context, key, targetLocale, missingWarn, type);
    if (missingRet !== key) {
      format2 = missingRet;
    }
    from = to;
  }
  return [format2, targetLocale, message];
}
function compileMessageFormat(context, key, targetLocale, format2, cacheBaseKey, errorDetector) {
  const { messageCompiler, warnHtmlMessage } = context;
  if (isMessageFunction(format2)) {
    const msg2 = format2;
    msg2.locale = msg2.locale || targetLocale;
    msg2.key = msg2.key || key;
    return msg2;
  }
  let start = null;
  let startTag;
  let endTag;
  if (inBrowser) {
    start = window.performance.now();
    startTag = "intlify-message-compilation-start";
    endTag = "intlify-message-compilation-end";
    mark && mark(startTag);
  }
  const msg = messageCompiler(format2, getCompileOptions(context, targetLocale, cacheBaseKey, format2, warnHtmlMessage, errorDetector));
  if (inBrowser) {
    const end = window.performance.now();
    const emitter = context.__v_emitter;
    if (emitter && start) {
      emitter.emit("message-compilation", {
        type: "message-compilation",
        message: format2,
        time: end - start,
        groupId: `${"translate"}:${key}`
      });
    }
    if (startTag && endTag && mark && measure) {
      mark(endTag);
      measure("intlify message compilation", startTag, endTag);
    }
  }
  msg.locale = targetLocale;
  msg.key = key;
  msg.source = format2;
  return msg;
}
function evaluateMessage(context, msg, msgCtx) {
  let start = null;
  let startTag;
  let endTag;
  if (inBrowser) {
    start = window.performance.now();
    startTag = "intlify-message-evaluation-start";
    endTag = "intlify-message-evaluation-end";
    mark && mark(startTag);
  }
  const messaged = msg(msgCtx);
  if (inBrowser) {
    const end = window.performance.now();
    const emitter = context.__v_emitter;
    if (emitter && start) {
      emitter.emit("message-evaluation", {
        type: "message-evaluation",
        value: messaged,
        time: end - start,
        groupId: `${"translate"}:${msg.key}`
      });
    }
    if (startTag && endTag && mark && measure) {
      mark(endTag);
      measure("intlify message evaluation", startTag, endTag);
    }
  }
  return messaged;
}
function parseTranslateArgs(...args) {
  const [arg1, arg2, arg3] = args;
  const options = {};
  if (!isString(arg1) && !isNumber(arg1) && !isMessageFunction(arg1)) {
    throw createCoreError(14);
  }
  const key = isNumber(arg1) ? String(arg1) : isMessageFunction(arg1) ? arg1 : arg1;
  if (isNumber(arg2)) {
    options.plural = arg2;
  } else if (isString(arg2)) {
    options.default = arg2;
  } else if (isPlainObject(arg2) && !isEmptyObject(arg2)) {
    options.named = arg2;
  } else if (isArray(arg2)) {
    options.list = arg2;
  }
  if (isNumber(arg3)) {
    options.plural = arg3;
  } else if (isString(arg3)) {
    options.default = arg3;
  } else if (isPlainObject(arg3)) {
    assign(options, arg3);
  }
  return [key, options];
}
function getCompileOptions(context, locale, key, source, warnHtmlMessage, errorDetector) {
  return {
    warnHtmlMessage,
    onError: (err) => {
      errorDetector && errorDetector(err);
      if (true) {
        const message = `Message compilation error: ${err.message}`;
        const codeFrame = err.location && generateCodeFrame(source, err.location.start.offset, err.location.end.offset);
        const emitter = context.__v_emitter;
        if (emitter) {
          emitter.emit("compile-error", {
            message: source,
            error: err.message,
            start: err.location && err.location.start.offset,
            end: err.location && err.location.end.offset,
            groupId: `${"translate"}:${key}`
          });
        }
        console.error(codeFrame ? `${message}
${codeFrame}` : message);
      } else {
        throw err;
      }
    },
    onCacheKey: (source2) => generateFormatCacheKey(locale, key, source2)
  };
}
function getMessageContextOptions(context, locale, message, options) {
  const { modifiers, pluralRules } = context;
  const resolveMessage = (key) => {
    const val = resolveValue(message, key);
    if (isString(val)) {
      let occurred = false;
      const errorDetector = () => {
        occurred = true;
      };
      const msg = compileMessageFormat(context, key, locale, val, key, errorDetector);
      return !occurred ? msg : NOOP_MESSAGE_FUNCTION;
    } else if (isMessageFunction(val)) {
      return val;
    } else {
      return NOOP_MESSAGE_FUNCTION;
    }
  };
  const ctxOptions = {
    locale,
    modifiers,
    pluralRules,
    messages: resolveMessage
  };
  if (context.processor) {
    ctxOptions.processor = context.processor;
  }
  if (options.list) {
    ctxOptions.list = options.list;
  }
  if (options.named) {
    ctxOptions.named = options.named;
  }
  if (isNumber(options.plural)) {
    ctxOptions.pluralIndex = options.plural;
  }
  return ctxOptions;
}
function datetime(context, ...args) {
  const { datetimeFormats, unresolving, fallbackLocale, onWarn } = context;
  const { __datetimeFormatters } = context;
  if (!Availabilities.dateTimeFormat) {
    onWarn(getWarnMessage(4));
    return MISSING_RESOLVE_VALUE;
  }
  const [key, value, options, overrides] = parseDateTimeArgs(...args);
  const missingWarn = isBoolean(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const part = !!options.part;
  const locale = isString(options.locale) ? options.locale : context.locale;
  const locales = getLocaleChain(context, fallbackLocale, locale);
  if (!isString(key) || key === "") {
    return new Intl.DateTimeFormat(locale).format(value);
  }
  let datetimeFormat = {};
  let targetLocale;
  let format2 = null;
  let from = locale;
  let to = null;
  const type = "datetime format";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(5, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    datetimeFormat = datetimeFormats[targetLocale] || {};
    format2 = datetimeFormat[key];
    if (isPlainObject(format2))
      break;
    handleMissing(context, key, targetLocale, missingWarn, type);
    from = to;
  }
  if (!isPlainObject(format2) || !isString(targetLocale)) {
    return unresolving ? NOT_REOSLVED : key;
  }
  let id = `${targetLocale}__${key}`;
  if (!isEmptyObject(overrides)) {
    id = `${id}__${JSON.stringify(overrides)}`;
  }
  let formatter = __datetimeFormatters.get(id);
  if (!formatter) {
    formatter = new Intl.DateTimeFormat(targetLocale, assign({}, format2, overrides));
    __datetimeFormatters.set(id, formatter);
  }
  return !part ? formatter.format(value) : formatter.formatToParts(value);
}
function parseDateTimeArgs(...args) {
  const [arg1, arg2, arg3, arg4] = args;
  let options = {};
  let overrides = {};
  let value;
  if (isString(arg1)) {
    if (!/\d{4}-\d{2}-\d{2}(T.*)?/.test(arg1)) {
      throw createCoreError(16);
    }
    value = new Date(arg1);
    try {
      value.toISOString();
    } catch (e) {
      throw createCoreError(16);
    }
  } else if (isDate(arg1)) {
    if (isNaN(arg1.getTime())) {
      throw createCoreError(15);
    }
    value = arg1;
  } else if (isNumber(arg1)) {
    value = arg1;
  } else {
    throw createCoreError(14);
  }
  if (isString(arg2)) {
    options.key = arg2;
  } else if (isPlainObject(arg2)) {
    options = arg2;
  }
  if (isString(arg3)) {
    options.locale = arg3;
  } else if (isPlainObject(arg3)) {
    overrides = arg3;
  }
  if (isPlainObject(arg4)) {
    overrides = arg4;
  }
  return [options.key || "", value, options, overrides];
}
function clearDateTimeFormat(ctx, locale, format2) {
  const context = ctx;
  for (const key in format2) {
    const id = `${locale}__${key}`;
    if (!context.__datetimeFormatters.has(id)) {
      continue;
    }
    context.__datetimeFormatters.delete(id);
  }
}
function number(context, ...args) {
  const { numberFormats, unresolving, fallbackLocale, onWarn } = context;
  const { __numberFormatters } = context;
  if (!Availabilities.numberFormat) {
    onWarn(getWarnMessage(2));
    return MISSING_RESOLVE_VALUE;
  }
  const [key, value, options, overrides] = parseNumberArgs(...args);
  const missingWarn = isBoolean(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const part = !!options.part;
  const locale = isString(options.locale) ? options.locale : context.locale;
  const locales = getLocaleChain(context, fallbackLocale, locale);
  if (!isString(key) || key === "") {
    return new Intl.NumberFormat(locale).format(value);
  }
  let numberFormat = {};
  let targetLocale;
  let format2 = null;
  let from = locale;
  let to = null;
  const type = "number format";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(3, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    numberFormat = numberFormats[targetLocale] || {};
    format2 = numberFormat[key];
    if (isPlainObject(format2))
      break;
    handleMissing(context, key, targetLocale, missingWarn, type);
    from = to;
  }
  if (!isPlainObject(format2) || !isString(targetLocale)) {
    return unresolving ? NOT_REOSLVED : key;
  }
  let id = `${targetLocale}__${key}`;
  if (!isEmptyObject(overrides)) {
    id = `${id}__${JSON.stringify(overrides)}`;
  }
  let formatter = __numberFormatters.get(id);
  if (!formatter) {
    formatter = new Intl.NumberFormat(targetLocale, assign({}, format2, overrides));
    __numberFormatters.set(id, formatter);
  }
  return !part ? formatter.format(value) : formatter.formatToParts(value);
}
function parseNumberArgs(...args) {
  const [arg1, arg2, arg3, arg4] = args;
  let options = {};
  let overrides = {};
  if (!isNumber(arg1)) {
    throw createCoreError(14);
  }
  const value = arg1;
  if (isString(arg2)) {
    options.key = arg2;
  } else if (isPlainObject(arg2)) {
    options = arg2;
  }
  if (isString(arg3)) {
    options.locale = arg3;
  } else if (isPlainObject(arg3)) {
    overrides = arg3;
  }
  if (isPlainObject(arg4)) {
    overrides = arg4;
  }
  return [options.key || "", value, options, overrides];
}
function clearNumberFormat(ctx, locale, format2) {
  const context = ctx;
  for (const key in format2) {
    const id = `${locale}__${key}`;
    if (!context.__numberFormatters.has(id)) {
      continue;
    }
    context.__numberFormatters.delete(id);
  }
}
var devtools, translateDevTools, warnMessages, VERSION, NOT_REOSLVED, MISSING_RESOLVE_VALUE, _compiler, _additionalMeta, setAdditionalMeta, getAdditionalMeta, _cid, RE_HTML_TAG, WARN_MESSAGE, defaultOnCacheKey, compileCache, errorMessages2, NOOP_MESSAGE_FUNCTION, isMessageFunction, intlDefined, Availabilities;
var init_core_base_esm_bundler = __esm({
  "node_modules/@intlify/core-base/dist/core-base.esm-bundler.js"() {
    init_shared_esm_bundler();
    init_message_resolver_esm_bundler();
    init_message_resolver_esm_bundler();
    init_runtime_esm_bundler();
    init_runtime_esm_bundler();
    init_message_compiler_esm_bundler();
    init_message_compiler_esm_bundler();
    init_devtools_if_esm_bundler();
    devtools = null;
    translateDevTools = createDevToolsHook(IntlifyDevToolsHooks.FunctionTranslate);
    warnMessages = {
      [0]: `Not found '{key}' key in '{locale}' locale messages.`,
      [1]: `Fall back to translate '{key}' key with '{target}' locale.`,
      [2]: `Cannot format a number value due to not supported Intl.NumberFormat.`,
      [3]: `Fall back to number format '{key}' key with '{target}' locale.`,
      [4]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,
      [5]: `Fall back to datetime format '{key}' key with '{target}' locale.`
    };
    VERSION = "9.1.10";
    NOT_REOSLVED = -1;
    MISSING_RESOLVE_VALUE = "";
    _additionalMeta = null;
    setAdditionalMeta = (meta) => {
      _additionalMeta = meta;
    };
    getAdditionalMeta = () => _additionalMeta;
    _cid = 0;
    RE_HTML_TAG = /<\/?[\w\s="/.':;#-\/]+>/;
    WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;
    defaultOnCacheKey = (source) => source;
    compileCache = /* @__PURE__ */ Object.create(null);
    errorMessages2 = {
      [14]: "Invalid arguments",
      [15]: "The date provided is an invalid Date object.Make sure your Date represents a valid date.",
      [16]: "The argument provided is not a valid ISO date string"
    };
    NOOP_MESSAGE_FUNCTION = () => "";
    isMessageFunction = (val) => isFunction(val);
    intlDefined = typeof Intl !== "undefined";
    Availabilities = {
      dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== "undefined",
      numberFormat: intlDefined && typeof Intl.NumberFormat !== "undefined"
    };
    {
      if (false) {
        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;
      }
    }
  }
});

// node_modules/vue-i18n/dist/vue-i18n.cjs.js
var require_vue_i18n_cjs = __commonJS({
  "node_modules/vue-i18n/dist/vue-i18n.cjs.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var shared = (init_shared_esm_bundler(), __toCommonJS(shared_esm_bundler_exports));
    var coreBase = (init_core_base_esm_bundler(), __toCommonJS(core_base_esm_bundler_exports));
    var vue = require_vue();
    var VERSION2 = "9.1.10";
    var warnMessages2 = {
      [6]: `Fall back to {type} '{key}' with root locale.`,
      [7]: `Not supported 'preserve'.`,
      [8]: `Not supported 'formatter'.`,
      [9]: `Not supported 'preserveDirectiveContent'.`,
      [10]: `Not supported 'getChoiceIndex'.`,
      [11]: `Component name legacy compatible: '{name}' -> 'i18n'`,
      [12]: `Not found parent scope. use the global scope.`
    };
    function getWarnMessage2(code, ...args) {
      return shared.format(warnMessages2[code], ...args);
    }
    function createI18nError(code, ...args) {
      return coreBase.createCompileError(code, null, { messages: errorMessages3, args });
    }
    var errorMessages3 = {
      [14]: "Unexpected return type in composer",
      [15]: "Invalid argument",
      [16]: "Must be called at the top of a `setup` function",
      [17]: "Need to install with `app.use` function",
      [22]: "Unexpected error",
      [18]: "Not available in legacy mode",
      [19]: `Required in value: {0}`,
      [20]: `Invalid value`,
      [21]: `Cannot setup vue-devtools plugin`
    };
    var DEVTOOLS_META = "__INTLIFY_META__";
    var TransrateVNodeSymbol = shared.makeSymbol("__transrateVNode");
    var DatetimePartsSymbol = shared.makeSymbol("__datetimeParts");
    var NumberPartsSymbol = shared.makeSymbol("__numberParts");
    var EnableEmitter = shared.makeSymbol("__enableEmitter");
    var DisableEmitter = shared.makeSymbol("__disableEmitter");
    var SetPluralRulesSymbol = shared.makeSymbol("__setPluralRules");
    shared.makeSymbol("__intlifyMeta");
    var InejctWithOption = shared.makeSymbol("__injectWithOption");
    var composerID = 0;
    function defineCoreMissingHandler(missing) {
      return (ctx, locale, key, type) => {
        return missing(locale, key, vue.getCurrentInstance() || void 0, type);
      };
    }
    function getLocaleMessages(locale, options) {
      const { messages, __i18n } = options;
      const ret = shared.isPlainObject(messages) ? messages : shared.isArray(__i18n) ? {} : { [locale]: {} };
      if (shared.isArray(__i18n)) {
        __i18n.forEach(({ locale: locale2, resource }) => {
          if (locale2) {
            ret[locale2] = ret[locale2] || {};
            deepCopy(resource, ret[locale2]);
          } else {
            deepCopy(resource, ret);
          }
        });
      }
      if (options.flatJson) {
        for (const key in ret) {
          if (shared.hasOwn(ret, key)) {
            coreBase.handleFlatJson(ret[key]);
          }
        }
      }
      return ret;
    }
    var isNotObjectOrIsArray = (val) => !shared.isObject(val) || shared.isArray(val);
    function deepCopy(src, des) {
      if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {
        throw createI18nError(20);
      }
      for (const key in src) {
        if (shared.hasOwn(src, key)) {
          if (isNotObjectOrIsArray(src[key]) || isNotObjectOrIsArray(des[key])) {
            des[key] = src[key];
          } else {
            deepCopy(src[key], des[key]);
          }
        }
      }
    }
    var getMetaInfo = () => {
      const instance = vue.getCurrentInstance();
      return instance && instance.type[DEVTOOLS_META] ? { [DEVTOOLS_META]: instance.type[DEVTOOLS_META] } : null;
    };
    function createComposer(options = {}) {
      const { __root } = options;
      const _isGlobal = __root === void 0;
      let _inheritLocale = shared.isBoolean(options.inheritLocale) ? options.inheritLocale : true;
      const _locale = vue.ref(
        __root && _inheritLocale ? __root.locale.value : shared.isString(options.locale) ? options.locale : "en-US"
      );
      const _fallbackLocale = vue.ref(
        __root && _inheritLocale ? __root.fallbackLocale.value : shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value
      );
      const _messages = vue.ref(getLocaleMessages(_locale.value, options));
      const _datetimeFormats = vue.ref(shared.isPlainObject(options.datetimeFormats) ? options.datetimeFormats : { [_locale.value]: {} });
      const _numberFormats = vue.ref(shared.isPlainObject(options.numberFormats) ? options.numberFormats : { [_locale.value]: {} });
      let _missingWarn = __root ? __root.missingWarn : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn) ? options.missingWarn : true;
      let _fallbackWarn = __root ? __root.fallbackWarn : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;
      let _fallbackRoot = __root ? __root.fallbackRoot : shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      let _fallbackFormat = !!options.fallbackFormat;
      let _missing = shared.isFunction(options.missing) ? options.missing : null;
      let _runtimeMissing = shared.isFunction(options.missing) ? defineCoreMissingHandler(options.missing) : null;
      let _postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : null;
      let _warnHtmlMessage = shared.isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
      let _escapeParameter = !!options.escapeParameter;
      const _modifiers = __root ? __root.modifiers : shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      let _pluralRules = options.pluralRules || __root && __root.pluralRules;
      let _context;
      function getCoreContext() {
        return coreBase.createCoreContext({
          version: VERSION2,
          locale: _locale.value,
          fallbackLocale: _fallbackLocale.value,
          messages: _messages.value,
          datetimeFormats: _datetimeFormats.value,
          numberFormats: _numberFormats.value,
          modifiers: _modifiers,
          pluralRules: _pluralRules,
          missing: _runtimeMissing === null ? void 0 : _runtimeMissing,
          missingWarn: _missingWarn,
          fallbackWarn: _fallbackWarn,
          fallbackFormat: _fallbackFormat,
          unresolving: true,
          postTranslation: _postTranslation === null ? void 0 : _postTranslation,
          warnHtmlMessage: _warnHtmlMessage,
          escapeParameter: _escapeParameter,
          __datetimeFormatters: shared.isPlainObject(_context) ? _context.__datetimeFormatters : void 0,
          __numberFormatters: shared.isPlainObject(_context) ? _context.__numberFormatters : void 0,
          __v_emitter: shared.isPlainObject(_context) ? _context.__v_emitter : void 0,
          __meta: { framework: "vue" }
        });
      }
      _context = getCoreContext();
      coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
      function trackReactivityValues() {
        return [
          _locale.value,
          _fallbackLocale.value,
          _messages.value,
          _datetimeFormats.value,
          _numberFormats.value
        ];
      }
      const locale = vue.computed({
        get: () => _locale.value,
        set: (val) => {
          _locale.value = val;
          _context.locale = _locale.value;
        }
      });
      const fallbackLocale = vue.computed({
        get: () => _fallbackLocale.value,
        set: (val) => {
          _fallbackLocale.value = val;
          _context.fallbackLocale = _fallbackLocale.value;
          coreBase.updateFallbackLocale(_context, _locale.value, val);
        }
      });
      const messages = vue.computed(() => _messages.value);
      const datetimeFormats = vue.computed(() => _datetimeFormats.value);
      const numberFormats = vue.computed(() => _numberFormats.value);
      function getPostTranslationHandler() {
        return shared.isFunction(_postTranslation) ? _postTranslation : null;
      }
      function setPostTranslationHandler(handler) {
        _postTranslation = handler;
        _context.postTranslation = handler;
      }
      function getMissingHandler() {
        return _missing;
      }
      function setMissingHandler(handler) {
        if (handler !== null) {
          _runtimeMissing = defineCoreMissingHandler(handler);
        }
        _missing = handler;
        _context.missing = _runtimeMissing;
      }
      function isResolvedTranslateMessage(type, arg) {
        return type !== "translate" || !!arg.resolvedMessage === false;
      }
      function wrapWithDeps(fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) {
        trackReactivityValues();
        let ret;
        {
          try {
            coreBase.setAdditionalMeta(getMetaInfo());
            ret = fn(_context);
          } finally {
            coreBase.setAdditionalMeta(null);
          }
        }
        if (shared.isNumber(ret) && ret === coreBase.NOT_REOSLVED) {
          const [key, arg2] = argumentParser();
          if (__root && shared.isString(key) && isResolvedTranslateMessage(warnType, arg2)) {
            if (_fallbackRoot && (coreBase.isTranslateFallbackWarn(_fallbackWarn, key) || coreBase.isTranslateMissingWarn(_missingWarn, key))) {
              shared.warn(getWarnMessage2(6, {
                key,
                type: warnType
              }));
            }
            {
              const { __v_emitter: emitter } = _context;
              if (emitter && _fallbackRoot) {
                emitter.emit("fallback", {
                  type: warnType,
                  key,
                  to: "global",
                  groupId: `${warnType}:${key}`
                });
              }
            }
          }
          return __root && _fallbackRoot ? fallbackSuccess(__root) : fallbackFail(key);
        } else if (successCondition(ret)) {
          return ret;
        } else {
          throw createI18nError(14);
        }
      }
      function t(...args) {
        return wrapWithDeps((context) => coreBase.translate(context, ...args), () => coreBase.parseTranslateArgs(...args), "translate", (root) => root.t(...args), (key) => key, (val) => shared.isString(val));
      }
      function rt(...args) {
        const [arg1, arg2, arg3] = args;
        if (arg3 && !shared.isObject(arg3)) {
          throw createI18nError(15);
        }
        return t(...[arg1, arg2, shared.assign({ resolvedMessage: true }, arg3 || {})]);
      }
      function d(...args) {
        return wrapWithDeps((context) => coreBase.datetime(context, ...args), () => coreBase.parseDateTimeArgs(...args), "datetime format", (root) => root.d(...args), () => coreBase.MISSING_RESOLVE_VALUE, (val) => shared.isString(val));
      }
      function n(...args) {
        return wrapWithDeps((context) => coreBase.number(context, ...args), () => coreBase.parseNumberArgs(...args), "number format", (root) => root.n(...args), () => coreBase.MISSING_RESOLVE_VALUE, (val) => shared.isString(val));
      }
      function normalize(values) {
        return values.map((val) => shared.isString(val) ? vue.createVNode(vue.Text, null, val, 0) : val);
      }
      const interpolate = (val) => val;
      const processor = {
        normalize,
        interpolate,
        type: "vnode"
      };
      function transrateVNode(...args) {
        return wrapWithDeps(
          (context) => {
            let ret;
            const _context2 = context;
            try {
              _context2.processor = processor;
              ret = coreBase.translate(_context2, ...args);
            } finally {
              _context2.processor = null;
            }
            return ret;
          },
          () => coreBase.parseTranslateArgs(...args),
          "translate",
          (root) => root[TransrateVNodeSymbol](...args),
          (key) => [vue.createVNode(vue.Text, null, key, 0)],
          (val) => shared.isArray(val)
        );
      }
      function numberParts(...args) {
        return wrapWithDeps(
          (context) => coreBase.number(context, ...args),
          () => coreBase.parseNumberArgs(...args),
          "number format",
          (root) => root[NumberPartsSymbol](...args),
          () => [],
          (val) => shared.isString(val) || shared.isArray(val)
        );
      }
      function datetimeParts(...args) {
        return wrapWithDeps(
          (context) => coreBase.datetime(context, ...args),
          () => coreBase.parseDateTimeArgs(...args),
          "datetime format",
          (root) => root[DatetimePartsSymbol](...args),
          () => [],
          (val) => shared.isString(val) || shared.isArray(val)
        );
      }
      function setPluralRules(rules) {
        _pluralRules = rules;
        _context.pluralRules = _pluralRules;
      }
      function te(key, locale2) {
        const targetLocale = shared.isString(locale2) ? locale2 : _locale.value;
        const message = getLocaleMessage(targetLocale);
        return coreBase.resolveValue(message, key) !== null;
      }
      function resolveMessages(key) {
        let messages2 = null;
        const locales = coreBase.getLocaleChain(_context, _fallbackLocale.value, _locale.value);
        for (let i = 0; i < locales.length; i++) {
          const targetLocaleMessages = _messages.value[locales[i]] || {};
          const messageValue = coreBase.resolveValue(targetLocaleMessages, key);
          if (messageValue != null) {
            messages2 = messageValue;
            break;
          }
        }
        return messages2;
      }
      function tm(key) {
        const messages2 = resolveMessages(key);
        return messages2 != null ? messages2 : __root ? __root.tm(key) || {} : {};
      }
      function getLocaleMessage(locale2) {
        return _messages.value[locale2] || {};
      }
      function setLocaleMessage(locale2, message) {
        _messages.value[locale2] = message;
        _context.messages = _messages.value;
      }
      function mergeLocaleMessage(locale2, message) {
        _messages.value[locale2] = _messages.value[locale2] || {};
        deepCopy(message, _messages.value[locale2]);
        _context.messages = _messages.value;
      }
      function getDateTimeFormat(locale2) {
        return _datetimeFormats.value[locale2] || {};
      }
      function setDateTimeFormat(locale2, format2) {
        _datetimeFormats.value[locale2] = format2;
        _context.datetimeFormats = _datetimeFormats.value;
        coreBase.clearDateTimeFormat(_context, locale2, format2);
      }
      function mergeDateTimeFormat(locale2, format2) {
        _datetimeFormats.value[locale2] = shared.assign(_datetimeFormats.value[locale2] || {}, format2);
        _context.datetimeFormats = _datetimeFormats.value;
        coreBase.clearDateTimeFormat(_context, locale2, format2);
      }
      function getNumberFormat(locale2) {
        return _numberFormats.value[locale2] || {};
      }
      function setNumberFormat(locale2, format2) {
        _numberFormats.value[locale2] = format2;
        _context.numberFormats = _numberFormats.value;
        coreBase.clearNumberFormat(_context, locale2, format2);
      }
      function mergeNumberFormat(locale2, format2) {
        _numberFormats.value[locale2] = shared.assign(_numberFormats.value[locale2] || {}, format2);
        _context.numberFormats = _numberFormats.value;
        coreBase.clearNumberFormat(_context, locale2, format2);
      }
      composerID++;
      if (__root) {
        vue.watch(__root.locale, (val) => {
          if (_inheritLocale) {
            _locale.value = val;
            _context.locale = val;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        });
        vue.watch(__root.fallbackLocale, (val) => {
          if (_inheritLocale) {
            _fallbackLocale.value = val;
            _context.fallbackLocale = val;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        });
      }
      const composer = {
        id: composerID,
        locale,
        fallbackLocale,
        get inheritLocale() {
          return _inheritLocale;
        },
        set inheritLocale(val) {
          _inheritLocale = val;
          if (val && __root) {
            _locale.value = __root.locale.value;
            _fallbackLocale.value = __root.fallbackLocale.value;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        },
        get availableLocales() {
          return Object.keys(_messages.value).sort();
        },
        messages,
        datetimeFormats,
        numberFormats,
        get modifiers() {
          return _modifiers;
        },
        get pluralRules() {
          return _pluralRules || {};
        },
        get isGlobal() {
          return _isGlobal;
        },
        get missingWarn() {
          return _missingWarn;
        },
        set missingWarn(val) {
          _missingWarn = val;
          _context.missingWarn = _missingWarn;
        },
        get fallbackWarn() {
          return _fallbackWarn;
        },
        set fallbackWarn(val) {
          _fallbackWarn = val;
          _context.fallbackWarn = _fallbackWarn;
        },
        get fallbackRoot() {
          return _fallbackRoot;
        },
        set fallbackRoot(val) {
          _fallbackRoot = val;
        },
        get fallbackFormat() {
          return _fallbackFormat;
        },
        set fallbackFormat(val) {
          _fallbackFormat = val;
          _context.fallbackFormat = _fallbackFormat;
        },
        get warnHtmlMessage() {
          return _warnHtmlMessage;
        },
        set warnHtmlMessage(val) {
          _warnHtmlMessage = val;
          _context.warnHtmlMessage = val;
        },
        get escapeParameter() {
          return _escapeParameter;
        },
        set escapeParameter(val) {
          _escapeParameter = val;
          _context.escapeParameter = val;
        },
        t,
        rt,
        d,
        n,
        te,
        tm,
        getLocaleMessage,
        setLocaleMessage,
        mergeLocaleMessage,
        getDateTimeFormat,
        setDateTimeFormat,
        mergeDateTimeFormat,
        getNumberFormat,
        setNumberFormat,
        mergeNumberFormat,
        getPostTranslationHandler,
        setPostTranslationHandler,
        getMissingHandler,
        setMissingHandler,
        [TransrateVNodeSymbol]: transrateVNode,
        [NumberPartsSymbol]: numberParts,
        [DatetimePartsSymbol]: datetimeParts,
        [SetPluralRulesSymbol]: setPluralRules,
        [InejctWithOption]: options.__injectWithOption
      };
      {
        composer[EnableEmitter] = (emitter) => {
          _context.__v_emitter = emitter;
        };
        composer[DisableEmitter] = () => {
          _context.__v_emitter = void 0;
        };
      }
      return composer;
    }
    function convertComposerOptions(options) {
      const locale = shared.isString(options.locale) ? options.locale : "en-US";
      const fallbackLocale = shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : locale;
      const missing = shared.isFunction(options.missing) ? options.missing : void 0;
      const missingWarn = shared.isBoolean(options.silentTranslationWarn) || shared.isRegExp(options.silentTranslationWarn) ? !options.silentTranslationWarn : true;
      const fallbackWarn = shared.isBoolean(options.silentFallbackWarn) || shared.isRegExp(options.silentFallbackWarn) ? !options.silentFallbackWarn : true;
      const fallbackRoot = shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      const fallbackFormat = !!options.formatFallbackMessages;
      const modifiers = shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      const pluralizationRules = options.pluralizationRules;
      const postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : void 0;
      const warnHtmlMessage = shared.isString(options.warnHtmlInMessage) ? options.warnHtmlInMessage !== "off" : true;
      const escapeParameter = !!options.escapeParameterHtml;
      const inheritLocale = shared.isBoolean(options.sync) ? options.sync : true;
      if (options.formatter) {
        shared.warn(getWarnMessage2(8));
      }
      if (options.preserveDirectiveContent) {
        shared.warn(getWarnMessage2(9));
      }
      let messages = options.messages;
      if (shared.isPlainObject(options.sharedMessages)) {
        const sharedMessages = options.sharedMessages;
        const locales = Object.keys(sharedMessages);
        messages = locales.reduce((messages2, locale2) => {
          const message = messages2[locale2] || (messages2[locale2] = {});
          shared.assign(message, sharedMessages[locale2]);
          return messages2;
        }, messages || {});
      }
      const { __i18n, __root, __injectWithOption } = options;
      const datetimeFormats = options.datetimeFormats;
      const numberFormats = options.numberFormats;
      const flatJson = options.flatJson;
      return {
        locale,
        fallbackLocale,
        messages,
        flatJson,
        datetimeFormats,
        numberFormats,
        missing,
        missingWarn,
        fallbackWarn,
        fallbackRoot,
        fallbackFormat,
        modifiers,
        pluralRules: pluralizationRules,
        postTranslation,
        warnHtmlMessage,
        escapeParameter,
        inheritLocale,
        __i18n,
        __root,
        __injectWithOption
      };
    }
    function createVueI18n(options = {}) {
      const composer = createComposer(convertComposerOptions(options));
      const vueI18n = {
        id: composer.id,
        get locale() {
          return composer.locale.value;
        },
        set locale(val) {
          composer.locale.value = val;
        },
        get fallbackLocale() {
          return composer.fallbackLocale.value;
        },
        set fallbackLocale(val) {
          composer.fallbackLocale.value = val;
        },
        get messages() {
          return composer.messages.value;
        },
        get datetimeFormats() {
          return composer.datetimeFormats.value;
        },
        get numberFormats() {
          return composer.numberFormats.value;
        },
        get availableLocales() {
          return composer.availableLocales;
        },
        get formatter() {
          shared.warn(getWarnMessage2(8));
          return {
            interpolate() {
              return [];
            }
          };
        },
        set formatter(val) {
          shared.warn(getWarnMessage2(8));
        },
        get missing() {
          return composer.getMissingHandler();
        },
        set missing(handler) {
          composer.setMissingHandler(handler);
        },
        get silentTranslationWarn() {
          return shared.isBoolean(composer.missingWarn) ? !composer.missingWarn : composer.missingWarn;
        },
        set silentTranslationWarn(val) {
          composer.missingWarn = shared.isBoolean(val) ? !val : val;
        },
        get silentFallbackWarn() {
          return shared.isBoolean(composer.fallbackWarn) ? !composer.fallbackWarn : composer.fallbackWarn;
        },
        set silentFallbackWarn(val) {
          composer.fallbackWarn = shared.isBoolean(val) ? !val : val;
        },
        get modifiers() {
          return composer.modifiers;
        },
        get formatFallbackMessages() {
          return composer.fallbackFormat;
        },
        set formatFallbackMessages(val) {
          composer.fallbackFormat = val;
        },
        get postTranslation() {
          return composer.getPostTranslationHandler();
        },
        set postTranslation(handler) {
          composer.setPostTranslationHandler(handler);
        },
        get sync() {
          return composer.inheritLocale;
        },
        set sync(val) {
          composer.inheritLocale = val;
        },
        get warnHtmlInMessage() {
          return composer.warnHtmlMessage ? "warn" : "off";
        },
        set warnHtmlInMessage(val) {
          composer.warnHtmlMessage = val !== "off";
        },
        get escapeParameterHtml() {
          return composer.escapeParameter;
        },
        set escapeParameterHtml(val) {
          composer.escapeParameter = val;
        },
        get preserveDirectiveContent() {
          shared.warn(getWarnMessage2(9));
          return true;
        },
        set preserveDirectiveContent(val) {
          shared.warn(getWarnMessage2(9));
        },
        get pluralizationRules() {
          return composer.pluralRules || {};
        },
        __composer: composer,
        t(...args) {
          const [arg1, arg2, arg3] = args;
          const options2 = {};
          let list = null;
          let named = null;
          if (!shared.isString(arg1)) {
            throw createI18nError(15);
          }
          const key = arg1;
          if (shared.isString(arg2)) {
            options2.locale = arg2;
          } else if (shared.isArray(arg2)) {
            list = arg2;
          } else if (shared.isPlainObject(arg2)) {
            named = arg2;
          }
          if (shared.isArray(arg3)) {
            list = arg3;
          } else if (shared.isPlainObject(arg3)) {
            named = arg3;
          }
          return composer.t(key, list || named || {}, options2);
        },
        rt(...args) {
          return composer.rt(...args);
        },
        tc(...args) {
          const [arg1, arg2, arg3] = args;
          const options2 = { plural: 1 };
          let list = null;
          let named = null;
          if (!shared.isString(arg1)) {
            throw createI18nError(15);
          }
          const key = arg1;
          if (shared.isString(arg2)) {
            options2.locale = arg2;
          } else if (shared.isNumber(arg2)) {
            options2.plural = arg2;
          } else if (shared.isArray(arg2)) {
            list = arg2;
          } else if (shared.isPlainObject(arg2)) {
            named = arg2;
          }
          if (shared.isString(arg3)) {
            options2.locale = arg3;
          } else if (shared.isArray(arg3)) {
            list = arg3;
          } else if (shared.isPlainObject(arg3)) {
            named = arg3;
          }
          return composer.t(key, list || named || {}, options2);
        },
        te(key, locale) {
          return composer.te(key, locale);
        },
        tm(key) {
          return composer.tm(key);
        },
        getLocaleMessage(locale) {
          return composer.getLocaleMessage(locale);
        },
        setLocaleMessage(locale, message) {
          composer.setLocaleMessage(locale, message);
        },
        mergeLocaleMessage(locale, message) {
          composer.mergeLocaleMessage(locale, message);
        },
        d(...args) {
          return composer.d(...args);
        },
        getDateTimeFormat(locale) {
          return composer.getDateTimeFormat(locale);
        },
        setDateTimeFormat(locale, format2) {
          composer.setDateTimeFormat(locale, format2);
        },
        mergeDateTimeFormat(locale, format2) {
          composer.mergeDateTimeFormat(locale, format2);
        },
        n(...args) {
          return composer.n(...args);
        },
        getNumberFormat(locale) {
          return composer.getNumberFormat(locale);
        },
        setNumberFormat(locale, format2) {
          composer.setNumberFormat(locale, format2);
        },
        mergeNumberFormat(locale, format2) {
          composer.mergeNumberFormat(locale, format2);
        },
        getChoiceIndex(choice, choicesLength) {
          shared.warn(getWarnMessage2(10));
          return -1;
        },
        __onComponentInstanceCreated(target) {
          const { componentInstanceCreatedListener } = options;
          if (componentInstanceCreatedListener) {
            componentInstanceCreatedListener(target, vueI18n);
          }
        }
      };
      {
        vueI18n.__enableEmitter = (emitter) => {
          const __composer = composer;
          __composer[EnableEmitter] && __composer[EnableEmitter](emitter);
        };
        vueI18n.__disableEmitter = () => {
          const __composer = composer;
          __composer[DisableEmitter] && __composer[DisableEmitter]();
        };
      }
      return vueI18n;
    }
    var baseFormatProps = {
      tag: {
        type: [String, Object]
      },
      locale: {
        type: String
      },
      scope: {
        type: String,
        validator: (val) => val === "parent" || val === "global",
        default: "parent"
      },
      i18n: {
        type: Object
      }
    };
    var Translation = {
      name: "i18n-t",
      props: shared.assign({
        keypath: {
          type: String,
          required: true
        },
        plural: {
          type: [Number, String],
          validator: (val) => shared.isNumber(val) || !isNaN(val)
        }
      }, baseFormatProps),
      setup(props, context) {
        const { slots, attrs } = context;
        const i18n = props.i18n || useI18n({
          useScope: props.scope,
          __useComponent: true
        });
        const keys = Object.keys(slots).filter((key) => key !== "_");
        return () => {
          const options = {};
          if (props.locale) {
            options.locale = props.locale;
          }
          if (props.plural !== void 0) {
            options.plural = shared.isString(props.plural) ? +props.plural : props.plural;
          }
          const arg = getInterpolateArg(context, keys);
          const children = i18n[TransrateVNodeSymbol](props.keypath, arg, options);
          const assignedAttrs = shared.assign({}, attrs);
          return shared.isString(props.tag) ? vue.h(props.tag, assignedAttrs, children) : shared.isObject(props.tag) ? vue.h(props.tag, assignedAttrs, children) : vue.h(vue.Fragment, assignedAttrs, children);
        };
      }
    };
    function getInterpolateArg({ slots }, keys) {
      if (keys.length === 1 && keys[0] === "default") {
        return slots.default ? slots.default() : [];
      } else {
        return keys.reduce((arg, key) => {
          const slot = slots[key];
          if (slot) {
            arg[key] = slot();
          }
          return arg;
        }, {});
      }
    }
    function renderFormatter(props, context, slotKeys, partFormatter) {
      const { slots, attrs } = context;
      return () => {
        const options = { part: true };
        let overrides = {};
        if (props.locale) {
          options.locale = props.locale;
        }
        if (shared.isString(props.format)) {
          options.key = props.format;
        } else if (shared.isObject(props.format)) {
          if (shared.isString(props.format.key)) {
            options.key = props.format.key;
          }
          overrides = Object.keys(props.format).reduce((options2, prop) => {
            return slotKeys.includes(prop) ? shared.assign({}, options2, { [prop]: props.format[prop] }) : options2;
          }, {});
        }
        const parts = partFormatter(...[props.value, options, overrides]);
        let children = [options.key];
        if (shared.isArray(parts)) {
          children = parts.map((part, index) => {
            const slot = slots[part.type];
            return slot ? slot({ [part.type]: part.value, index, parts }) : [part.value];
          });
        } else if (shared.isString(parts)) {
          children = [parts];
        }
        const assignedAttrs = shared.assign({}, attrs);
        return shared.isString(props.tag) ? vue.h(props.tag, assignedAttrs, children) : shared.isObject(props.tag) ? vue.h(props.tag, assignedAttrs, children) : vue.h(vue.Fragment, assignedAttrs, children);
      };
    }
    var NUMBER_FORMAT_KEYS = [
      "localeMatcher",
      "style",
      "unit",
      "unitDisplay",
      "currency",
      "currencyDisplay",
      "useGrouping",
      "numberingSystem",
      "minimumIntegerDigits",
      "minimumFractionDigits",
      "maximumFractionDigits",
      "minimumSignificantDigits",
      "maximumSignificantDigits",
      "notation",
      "formatMatcher"
    ];
    var NumberFormat = {
      name: "i18n-n",
      props: shared.assign({
        value: {
          type: Number,
          required: true
        },
        format: {
          type: [String, Object]
        }
      }, baseFormatProps),
      setup(props, context) {
        const i18n = props.i18n || useI18n({ useScope: "parent", __useComponent: true });
        return renderFormatter(props, context, NUMBER_FORMAT_KEYS, (...args) => i18n[NumberPartsSymbol](...args));
      }
    };
    var DATETIME_FORMAT_KEYS = [
      "dateStyle",
      "timeStyle",
      "fractionalSecondDigits",
      "calendar",
      "dayPeriod",
      "numberingSystem",
      "localeMatcher",
      "timeZone",
      "hour12",
      "hourCycle",
      "formatMatcher",
      "weekday",
      "era",
      "year",
      "month",
      "day",
      "hour",
      "minute",
      "second",
      "timeZoneName"
    ];
    var DatetimeFormat = {
      name: "i18n-d",
      props: shared.assign({
        value: {
          type: [Number, Date],
          required: true
        },
        format: {
          type: [String, Object]
        }
      }, baseFormatProps),
      setup(props, context) {
        const i18n = props.i18n || useI18n({ useScope: "parent", __useComponent: true });
        return renderFormatter(props, context, DATETIME_FORMAT_KEYS, (...args) => i18n[DatetimePartsSymbol](...args));
      }
    };
    function getComposer$1(i18n, instance) {
      const i18nInternal = i18n;
      if (i18n.mode === "composition") {
        return i18nInternal.__getInstance(instance) || i18n.global;
      } else {
        const vueI18n = i18nInternal.__getInstance(instance);
        return vueI18n != null ? vueI18n.__composer : i18n.global.__composer;
      }
    }
    function vTDirective(i18n) {
      const bind = (el, { instance, value, modifiers }) => {
        if (!instance || !instance.$) {
          throw createI18nError(22);
        }
        const composer = getComposer$1(i18n, instance.$);
        if (modifiers.preserve) {
          shared.warn(getWarnMessage2(7));
        }
        const parsedValue = parseValue(value);
        el.textContent = composer.t(...makeParams(parsedValue));
      };
      return {
        beforeMount: bind,
        beforeUpdate: bind
      };
    }
    function parseValue(value) {
      if (shared.isString(value)) {
        return { path: value };
      } else if (shared.isPlainObject(value)) {
        if (!("path" in value)) {
          throw createI18nError(19, "path");
        }
        return value;
      } else {
        throw createI18nError(20);
      }
    }
    function makeParams(value) {
      const { path, locale, args, choice, plural } = value;
      const options = {};
      const named = args || {};
      if (shared.isString(locale)) {
        options.locale = locale;
      }
      if (shared.isNumber(choice)) {
        options.plural = choice;
      }
      if (shared.isNumber(plural)) {
        options.plural = plural;
      }
      return [path, named, options];
    }
    function apply(app, i18n, ...options) {
      const pluginOptions = shared.isPlainObject(options[0]) ? options[0] : {};
      const useI18nComponentName = !!pluginOptions.useI18nComponentName;
      const globalInstall = shared.isBoolean(pluginOptions.globalInstall) ? pluginOptions.globalInstall : true;
      if (globalInstall && useI18nComponentName) {
        shared.warn(getWarnMessage2(11, {
          name: Translation.name
        }));
      }
      if (globalInstall) {
        app.component(!useI18nComponentName ? Translation.name : "i18n", Translation);
        app.component(NumberFormat.name, NumberFormat);
        app.component(DatetimeFormat.name, DatetimeFormat);
      }
      app.directive("t", vTDirective(i18n));
    }
    function defineMixin(vuei18n, composer, i18n) {
      return {
        beforeCreate() {
          const instance = vue.getCurrentInstance();
          if (!instance) {
            throw createI18nError(22);
          }
          const options = this.$options;
          if (options.i18n) {
            const optionsI18n = options.i18n;
            if (options.__i18n) {
              optionsI18n.__i18n = options.__i18n;
            }
            optionsI18n.__root = composer;
            if (this === this.$root) {
              this.$i18n = mergeToRoot(vuei18n, optionsI18n);
            } else {
              optionsI18n.__injectWithOption = true;
              this.$i18n = createVueI18n(optionsI18n);
            }
          } else if (options.__i18n) {
            if (this === this.$root) {
              this.$i18n = mergeToRoot(vuei18n, options);
            } else {
              this.$i18n = createVueI18n({
                __i18n: options.__i18n,
                __injectWithOption: true,
                __root: composer
              });
            }
          } else {
            this.$i18n = vuei18n;
          }
          vuei18n.__onComponentInstanceCreated(this.$i18n);
          i18n.__setInstance(instance, this.$i18n);
          this.$t = (...args) => this.$i18n.t(...args);
          this.$rt = (...args) => this.$i18n.rt(...args);
          this.$tc = (...args) => this.$i18n.tc(...args);
          this.$te = (key, locale) => this.$i18n.te(key, locale);
          this.$d = (...args) => this.$i18n.d(...args);
          this.$n = (...args) => this.$i18n.n(...args);
          this.$tm = (key) => this.$i18n.tm(key);
        },
        mounted() {
        },
        beforeUnmount() {
          const instance = vue.getCurrentInstance();
          if (!instance) {
            throw createI18nError(22);
          }
          delete this.$t;
          delete this.$rt;
          delete this.$tc;
          delete this.$te;
          delete this.$d;
          delete this.$n;
          delete this.$tm;
          i18n.__deleteInstance(instance);
          delete this.$i18n;
        }
      };
    }
    function mergeToRoot(root, options) {
      root.locale = options.locale || root.locale;
      root.fallbackLocale = options.fallbackLocale || root.fallbackLocale;
      root.missing = options.missing || root.missing;
      root.silentTranslationWarn = options.silentTranslationWarn || root.silentFallbackWarn;
      root.silentFallbackWarn = options.silentFallbackWarn || root.silentFallbackWarn;
      root.formatFallbackMessages = options.formatFallbackMessages || root.formatFallbackMessages;
      root.postTranslation = options.postTranslation || root.postTranslation;
      root.warnHtmlInMessage = options.warnHtmlInMessage || root.warnHtmlInMessage;
      root.escapeParameterHtml = options.escapeParameterHtml || root.escapeParameterHtml;
      root.sync = options.sync || root.sync;
      root.__composer[SetPluralRulesSymbol](options.pluralizationRules || root.pluralizationRules);
      const messages = getLocaleMessages(root.locale, {
        messages: options.messages,
        __i18n: options.__i18n
      });
      Object.keys(messages).forEach((locale) => root.mergeLocaleMessage(locale, messages[locale]));
      if (options.datetimeFormats) {
        Object.keys(options.datetimeFormats).forEach((locale) => root.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));
      }
      if (options.numberFormats) {
        Object.keys(options.numberFormats).forEach((locale) => root.mergeNumberFormat(locale, options.numberFormats[locale]));
      }
      return root;
    }
    function createI18n(options = {}) {
      const __legacyMode = shared.isBoolean(options.legacy) ? options.legacy : true;
      const __globalInjection = !!options.globalInjection;
      const __instances = /* @__PURE__ */ new Map();
      const __global = __legacyMode ? createVueI18n(options) : createComposer(options);
      const symbol = shared.makeSymbol("vue-i18n");
      const i18n = {
        get mode() {
          return __legacyMode ? "legacy" : "composition";
        },
        async install(app, ...options2) {
          app.__VUE_I18N_SYMBOL__ = symbol;
          app.provide(app.__VUE_I18N_SYMBOL__, i18n);
          if (!__legacyMode && __globalInjection) {
            injectGlobalFields(app, i18n.global);
          }
          {
            apply(app, i18n, ...options2);
          }
          if (__legacyMode) {
            app.mixin(defineMixin(__global, __global.__composer, i18n));
          }
        },
        get global() {
          return __global;
        },
        __instances,
        __getInstance(component) {
          return __instances.get(component) || null;
        },
        __setInstance(component, instance) {
          __instances.set(component, instance);
        },
        __deleteInstance(component) {
          __instances.delete(component);
        }
      };
      return i18n;
    }
    function useI18n(options = {}) {
      const instance = vue.getCurrentInstance();
      if (instance == null) {
        throw createI18nError(16);
      }
      if (!instance.appContext.app.__VUE_I18N_SYMBOL__) {
        throw createI18nError(17);
      }
      const i18n = vue.inject(instance.appContext.app.__VUE_I18N_SYMBOL__);
      if (!i18n) {
        throw createI18nError(22);
      }
      const global2 = i18n.mode === "composition" ? i18n.global : i18n.global.__composer;
      const scope = shared.isEmptyObject(options) ? "__i18n" in instance.type ? "local" : "global" : !options.useScope ? "local" : options.useScope;
      if (scope === "global") {
        let messages = shared.isObject(options.messages) ? options.messages : {};
        if ("__i18nGlobal" in instance.type) {
          messages = getLocaleMessages(global2.locale.value, {
            messages,
            __i18n: instance.type.__i18nGlobal
          });
        }
        const locales = Object.keys(messages);
        if (locales.length) {
          locales.forEach((locale) => {
            global2.mergeLocaleMessage(locale, messages[locale]);
          });
        }
        if (shared.isObject(options.datetimeFormats)) {
          const locales2 = Object.keys(options.datetimeFormats);
          if (locales2.length) {
            locales2.forEach((locale) => {
              global2.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);
            });
          }
        }
        if (shared.isObject(options.numberFormats)) {
          const locales2 = Object.keys(options.numberFormats);
          if (locales2.length) {
            locales2.forEach((locale) => {
              global2.mergeNumberFormat(locale, options.numberFormats[locale]);
            });
          }
        }
        return global2;
      }
      if (scope === "parent") {
        let composer2 = getComposer(i18n, instance, options.__useComponent);
        if (composer2 == null) {
          {
            shared.warn(getWarnMessage2(12));
          }
          composer2 = global2;
        }
        return composer2;
      }
      if (i18n.mode === "legacy") {
        throw createI18nError(18);
      }
      const i18nInternal = i18n;
      let composer = i18nInternal.__getInstance(instance);
      if (composer == null) {
        const type = instance.type;
        const composerOptions = shared.assign({}, options);
        if (type.__i18n) {
          composerOptions.__i18n = type.__i18n;
        }
        if (global2) {
          composerOptions.__root = global2;
        }
        composer = createComposer(composerOptions);
        setupLifeCycle(i18nInternal, instance);
        i18nInternal.__setInstance(instance, composer);
      }
      return composer;
    }
    function getComposer(i18n, target, useComponent = false) {
      let composer = null;
      const root = target.root;
      let current = target.parent;
      while (current != null) {
        const i18nInternal = i18n;
        if (i18n.mode === "composition") {
          composer = i18nInternal.__getInstance(current);
        } else {
          const vueI18n = i18nInternal.__getInstance(current);
          if (vueI18n != null) {
            composer = vueI18n.__composer;
          }
          if (useComponent && composer && !composer[InejctWithOption]) {
            composer = null;
          }
        }
        if (composer != null) {
          break;
        }
        if (root === current) {
          break;
        }
        current = current.parent;
      }
      return composer;
    }
    function setupLifeCycle(i18n, target, composer) {
      vue.onMounted(() => {
      }, target);
      vue.onUnmounted(() => {
        i18n.__deleteInstance(target);
      }, target);
    }
    var globalExportProps = [
      "locale",
      "fallbackLocale",
      "availableLocales"
    ];
    var globalExportMethods = ["t", "rt", "d", "n", "tm"];
    function injectGlobalFields(app, composer) {
      const i18n = /* @__PURE__ */ Object.create(null);
      globalExportProps.forEach((prop) => {
        const desc = Object.getOwnPropertyDescriptor(composer, prop);
        if (!desc) {
          throw createI18nError(22);
        }
        const wrap = vue.isRef(desc.value) ? {
          get() {
            return desc.value.value;
          },
          set(val) {
            desc.value.value = val;
          }
        } : {
          get() {
            return desc.get && desc.get();
          }
        };
        Object.defineProperty(i18n, prop, wrap);
      });
      app.config.globalProperties.$i18n = i18n;
      globalExportMethods.forEach((method) => {
        const desc = Object.getOwnPropertyDescriptor(composer, method);
        if (!desc || !desc.value) {
          throw createI18nError(22);
        }
        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);
      });
    }
    coreBase.registerMessageCompiler(coreBase.compileToFunction);
    {
      const target = shared.getGlobalThis();
      target.__INTLIFY__ = true;
      coreBase.setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);
    }
    exports.DatetimeFormat = DatetimeFormat;
    exports.NumberFormat = NumberFormat;
    exports.Translation = Translation;
    exports.VERSION = VERSION2;
    exports.createI18n = createI18n;
    exports.useI18n = useI18n;
    exports.vTDirective = vTDirective;
  }
});

// dep:vue-i18n
var vue_i18n_default = require_vue_i18n_cjs();
export {
  vue_i18n_default as default
};
/*!
  * @intlify/core-base v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
/*!
  * @intlify/devtools-if v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
/*!
  * @intlify/message-compiler v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
/*!
  * @intlify/message-resolver v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
/*!
  * @intlify/runtime v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
/*!
  * @intlify/shared v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
/*!
  * vue-i18n v9.1.10
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
//# sourceMappingURL=vue-i18n.js.map
