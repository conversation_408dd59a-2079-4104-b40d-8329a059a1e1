{"version": 3, "sources": ["../../codemirror/addon/search/searchcursor.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"))\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod)\n  else // Plain browser env\n    mod(CodeMirror)\n})(function(CodeMirror) {\n  \"use strict\"\n  var Pos = CodeMirror.Pos\n\n  function regexpFlags(regexp) {\n    var flags = regexp.flags\n    return flags != null ? flags : (regexp.ignoreCase ? \"i\" : \"\")\n      + (regexp.global ? \"g\" : \"\")\n      + (regexp.multiline ? \"m\" : \"\")\n  }\n\n  function ensureFlags(regexp, flags) {\n    var current = regexpFlags(regexp), target = current\n    for (var i = 0; i < flags.length; i++) if (target.indexOf(flags.charAt(i)) == -1)\n      target += flags.charAt(i)\n    return current == target ? regexp : new RegExp(regexp.source, target)\n  }\n\n  function maybeMultiline(regexp) {\n    return /\\\\s|\\\\n|\\n|\\\\W|\\\\D|\\[\\^/.test(regexp.source)\n  }\n\n  function searchRegexpForward(doc, regexp, start) {\n    regexp = ensureFlags(regexp, \"g\")\n    for (var line = start.line, ch = start.ch, last = doc.lastLine(); line <= last; line++, ch = 0) {\n      regexp.lastIndex = ch\n      var string = doc.getLine(line), match = regexp.exec(string)\n      if (match)\n        return {from: Pos(line, match.index),\n                to: Pos(line, match.index + match[0].length),\n                match: match}\n    }\n  }\n\n  function searchRegexpForwardMultiline(doc, regexp, start) {\n    if (!maybeMultiline(regexp)) return searchRegexpForward(doc, regexp, start)\n\n    regexp = ensureFlags(regexp, \"gm\")\n    var string, chunk = 1\n    for (var line = start.line, last = doc.lastLine(); line <= last;) {\n      // This grows the search buffer in exponentially-sized chunks\n      // between matches, so that nearby matches are fast and don't\n      // require concatenating the whole document (in case we're\n      // searching for something that has tons of matches), but at the\n      // same time, the amount of retries is limited.\n      for (var i = 0; i < chunk; i++) {\n        if (line > last) break\n        var curLine = doc.getLine(line++)\n        string = string == null ? curLine : string + \"\\n\" + curLine\n      }\n      chunk = chunk * 2\n      regexp.lastIndex = start.ch\n      var match = regexp.exec(string)\n      if (match) {\n        var before = string.slice(0, match.index).split(\"\\n\"), inside = match[0].split(\"\\n\")\n        var startLine = start.line + before.length - 1, startCh = before[before.length - 1].length\n        return {from: Pos(startLine, startCh),\n                to: Pos(startLine + inside.length - 1,\n                        inside.length == 1 ? startCh + inside[0].length : inside[inside.length - 1].length),\n                match: match}\n      }\n    }\n  }\n\n  function lastMatchIn(string, regexp, endMargin) {\n    var match, from = 0\n    while (from <= string.length) {\n      regexp.lastIndex = from\n      var newMatch = regexp.exec(string)\n      if (!newMatch) break\n      var end = newMatch.index + newMatch[0].length\n      if (end > string.length - endMargin) break\n      if (!match || end > match.index + match[0].length)\n        match = newMatch\n      from = newMatch.index + 1\n    }\n    return match\n  }\n\n  function searchRegexpBackward(doc, regexp, start) {\n    regexp = ensureFlags(regexp, \"g\")\n    for (var line = start.line, ch = start.ch, first = doc.firstLine(); line >= first; line--, ch = -1) {\n      var string = doc.getLine(line)\n      var match = lastMatchIn(string, regexp, ch < 0 ? 0 : string.length - ch)\n      if (match)\n        return {from: Pos(line, match.index),\n                to: Pos(line, match.index + match[0].length),\n                match: match}\n    }\n  }\n\n  function searchRegexpBackwardMultiline(doc, regexp, start) {\n    if (!maybeMultiline(regexp)) return searchRegexpBackward(doc, regexp, start)\n    regexp = ensureFlags(regexp, \"gm\")\n    var string, chunkSize = 1, endMargin = doc.getLine(start.line).length - start.ch\n    for (var line = start.line, first = doc.firstLine(); line >= first;) {\n      for (var i = 0; i < chunkSize && line >= first; i++) {\n        var curLine = doc.getLine(line--)\n        string = string == null ? curLine : curLine + \"\\n\" + string\n      }\n      chunkSize *= 2\n\n      var match = lastMatchIn(string, regexp, endMargin)\n      if (match) {\n        var before = string.slice(0, match.index).split(\"\\n\"), inside = match[0].split(\"\\n\")\n        var startLine = line + before.length, startCh = before[before.length - 1].length\n        return {from: Pos(startLine, startCh),\n                to: Pos(startLine + inside.length - 1,\n                        inside.length == 1 ? startCh + inside[0].length : inside[inside.length - 1].length),\n                match: match}\n      }\n    }\n  }\n\n  var doFold, noFold\n  if (String.prototype.normalize) {\n    doFold = function(str) { return str.normalize(\"NFD\").toLowerCase() }\n    noFold = function(str) { return str.normalize(\"NFD\") }\n  } else {\n    doFold = function(str) { return str.toLowerCase() }\n    noFold = function(str) { return str }\n  }\n\n  // Maps a position in a case-folded line back to a position in the original line\n  // (compensating for codepoints increasing in number during folding)\n  function adjustPos(orig, folded, pos, foldFunc) {\n    if (orig.length == folded.length) return pos\n    for (var min = 0, max = pos + Math.max(0, orig.length - folded.length);;) {\n      if (min == max) return min\n      var mid = (min + max) >> 1\n      var len = foldFunc(orig.slice(0, mid)).length\n      if (len == pos) return mid\n      else if (len > pos) max = mid\n      else min = mid + 1\n    }\n  }\n\n  function searchStringForward(doc, query, start, caseFold) {\n    // Empty string would match anything and never progress, so we\n    // define it to match nothing instead.\n    if (!query.length) return null\n    var fold = caseFold ? doFold : noFold\n    var lines = fold(query).split(/\\r|\\n\\r?/)\n\n    search: for (var line = start.line, ch = start.ch, last = doc.lastLine() + 1 - lines.length; line <= last; line++, ch = 0) {\n      var orig = doc.getLine(line).slice(ch), string = fold(orig)\n      if (lines.length == 1) {\n        var found = string.indexOf(lines[0])\n        if (found == -1) continue search\n        var start = adjustPos(orig, string, found, fold) + ch\n        return {from: Pos(line, adjustPos(orig, string, found, fold) + ch),\n                to: Pos(line, adjustPos(orig, string, found + lines[0].length, fold) + ch)}\n      } else {\n        var cutFrom = string.length - lines[0].length\n        if (string.slice(cutFrom) != lines[0]) continue search\n        for (var i = 1; i < lines.length - 1; i++)\n          if (fold(doc.getLine(line + i)) != lines[i]) continue search\n        var end = doc.getLine(line + lines.length - 1), endString = fold(end), lastLine = lines[lines.length - 1]\n        if (endString.slice(0, lastLine.length) != lastLine) continue search\n        return {from: Pos(line, adjustPos(orig, string, cutFrom, fold) + ch),\n                to: Pos(line + lines.length - 1, adjustPos(end, endString, lastLine.length, fold))}\n      }\n    }\n  }\n\n  function searchStringBackward(doc, query, start, caseFold) {\n    if (!query.length) return null\n    var fold = caseFold ? doFold : noFold\n    var lines = fold(query).split(/\\r|\\n\\r?/)\n\n    search: for (var line = start.line, ch = start.ch, first = doc.firstLine() - 1 + lines.length; line >= first; line--, ch = -1) {\n      var orig = doc.getLine(line)\n      if (ch > -1) orig = orig.slice(0, ch)\n      var string = fold(orig)\n      if (lines.length == 1) {\n        var found = string.lastIndexOf(lines[0])\n        if (found == -1) continue search\n        return {from: Pos(line, adjustPos(orig, string, found, fold)),\n                to: Pos(line, adjustPos(orig, string, found + lines[0].length, fold))}\n      } else {\n        var lastLine = lines[lines.length - 1]\n        if (string.slice(0, lastLine.length) != lastLine) continue search\n        for (var i = 1, start = line - lines.length + 1; i < lines.length - 1; i++)\n          if (fold(doc.getLine(start + i)) != lines[i]) continue search\n        var top = doc.getLine(line + 1 - lines.length), topString = fold(top)\n        if (topString.slice(topString.length - lines[0].length) != lines[0]) continue search\n        return {from: Pos(line + 1 - lines.length, adjustPos(top, topString, top.length - lines[0].length, fold)),\n                to: Pos(line, adjustPos(orig, string, lastLine.length, fold))}\n      }\n    }\n  }\n\n  function SearchCursor(doc, query, pos, options) {\n    this.atOccurrence = false\n    this.afterEmptyMatch = false\n    this.doc = doc\n    pos = pos ? doc.clipPos(pos) : Pos(0, 0)\n    this.pos = {from: pos, to: pos}\n\n    var caseFold\n    if (typeof options == \"object\") {\n      caseFold = options.caseFold\n    } else { // Backwards compat for when caseFold was the 4th argument\n      caseFold = options\n      options = null\n    }\n\n    if (typeof query == \"string\") {\n      if (caseFold == null) caseFold = false\n      this.matches = function(reverse, pos) {\n        return (reverse ? searchStringBackward : searchStringForward)(doc, query, pos, caseFold)\n      }\n    } else {\n      query = ensureFlags(query, \"gm\")\n      if (!options || options.multiline !== false)\n        this.matches = function(reverse, pos) {\n          return (reverse ? searchRegexpBackwardMultiline : searchRegexpForwardMultiline)(doc, query, pos)\n        }\n      else\n        this.matches = function(reverse, pos) {\n          return (reverse ? searchRegexpBackward : searchRegexpForward)(doc, query, pos)\n        }\n    }\n  }\n\n  SearchCursor.prototype = {\n    findNext: function() {return this.find(false)},\n    findPrevious: function() {return this.find(true)},\n\n    find: function(reverse) {\n      var head = this.doc.clipPos(reverse ? this.pos.from : this.pos.to);\n      if (this.afterEmptyMatch && this.atOccurrence) {\n        // do not return the same 0 width match twice\n        head = Pos(head.line, head.ch)\n        if (reverse) {\n          head.ch--;\n          if (head.ch < 0) {\n            head.line--;\n            head.ch = (this.doc.getLine(head.line) || \"\").length;\n          }\n        } else {\n          head.ch++;\n          if (head.ch > (this.doc.getLine(head.line) || \"\").length) {\n            head.ch = 0;\n            head.line++;\n          }\n        }\n        if (CodeMirror.cmpPos(head, this.doc.clipPos(head)) != 0) {\n           return this.atOccurrence = false\n        }\n      }\n      var result = this.matches(reverse, head)\n      this.afterEmptyMatch = result && CodeMirror.cmpPos(result.from, result.to) == 0\n\n      if (result) {\n        this.pos = result\n        this.atOccurrence = true\n        return this.pos.match || true\n      } else {\n        var end = Pos(reverse ? this.doc.firstLine() : this.doc.lastLine() + 1, 0)\n        this.pos = {from: end, to: end}\n        return this.atOccurrence = false\n      }\n    },\n\n    from: function() {if (this.atOccurrence) return this.pos.from},\n    to: function() {if (this.atOccurrence) return this.pos.to},\n\n    replace: function(newText, origin) {\n      if (!this.atOccurrence) return\n      var lines = CodeMirror.splitLines(newText)\n      this.doc.replaceRange(lines, this.pos.from, this.pos.to, origin)\n      this.pos.to = Pos(this.pos.from.line + lines.length - 1,\n                        lines[lines.length - 1].length + (lines.length == 1 ? this.pos.from.ch : 0))\n    }\n  }\n\n  CodeMirror.defineExtension(\"getSearchCursor\", function(query, pos, caseFold) {\n    return new SearchCursor(this.doc, query, pos, caseFold)\n  })\n  CodeMirror.defineDocExtension(\"getSearchCursor\", function(query, pos, caseFold) {\n    return new SearchCursor(this, query, pos, caseFold)\n  })\n\n  CodeMirror.defineExtension(\"selectMatches\", function(query, caseFold) {\n    var ranges = []\n    var cur = this.getSearchCursor(query, this.getCursor(\"from\"), caseFold)\n    while (cur.findNext()) {\n      if (CodeMirror.cmpPos(cur.to(), this.getCursor(\"to\")) > 0) break\n      ranges.push({anchor: cur.from(), head: cur.to()})\n    }\n    if (ranges.length)\n      this.setSelections(ranges, 0)\n  })\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AACA,UAAI,MAAMA,YAAW;AAErB,eAAS,YAAY,QAAQ;AAC3B,YAAI,QAAQ,OAAO;AACnB,eAAO,SAAS,OAAO,SAAS,OAAO,aAAa,MAAM,OACrD,OAAO,SAAS,MAAM,OACtB,OAAO,YAAY,MAAM;AAAA,MAChC;AAEA,eAAS,YAAY,QAAQ,OAAO;AAClC,YAAI,UAAU,YAAY,MAAM,GAAG,SAAS;AAC5C,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAAK,cAAI,OAAO,QAAQ,MAAM,OAAO,CAAC,CAAC,KAAK;AAC5E,sBAAU,MAAM,OAAO,CAAC;AAC1B,eAAO,WAAW,SAAS,SAAS,IAAI,OAAO,OAAO,QAAQ,MAAM;AAAA,MACtE;AAEA,eAAS,eAAe,QAAQ;AAC9B,eAAO,0BAA0B,KAAK,OAAO,MAAM;AAAA,MACrD;AAEA,eAAS,oBAAoB,KAAK,QAAQ,OAAO;AAC/C,iBAAS,YAAY,QAAQ,GAAG;AAChC,iBAAS,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,OAAO,IAAI,SAAS,GAAG,QAAQ,MAAM,QAAQ,KAAK,GAAG;AAC9F,iBAAO,YAAY;AACnB,cAAI,SAAS,IAAI,QAAQ,IAAI,GAAG,QAAQ,OAAO,KAAK,MAAM;AAC1D,cAAI;AACF,mBAAO;AAAA,cAAC,MAAM,IAAI,MAAM,MAAM,KAAK;AAAA,cAC3B,IAAI,IAAI,MAAM,MAAM,QAAQ,MAAM,GAAG,MAAM;AAAA,cAC3C;AAAA,YAAY;AAAA,QACxB;AAAA,MACF;AAEA,eAAS,6BAA6B,KAAK,QAAQ,OAAO;AACxD,YAAI,CAAC,eAAe,MAAM;AAAG,iBAAO,oBAAoB,KAAK,QAAQ,KAAK;AAE1E,iBAAS,YAAY,QAAQ,IAAI;AACjC,YAAI,QAAQ,QAAQ;AACpB,iBAAS,OAAO,MAAM,MAAM,OAAO,IAAI,SAAS,GAAG,QAAQ,QAAO;AAMhE,mBAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,gBAAI,OAAO;AAAM;AACjB,gBAAI,UAAU,IAAI,QAAQ,MAAM;AAChC,qBAAS,UAAU,OAAO,UAAU,SAAS,OAAO;AAAA,UACtD;AACA,kBAAQ,QAAQ;AAChB,iBAAO,YAAY,MAAM;AACzB,cAAI,QAAQ,OAAO,KAAK,MAAM;AAC9B,cAAI,OAAO;AACT,gBAAI,SAAS,OAAO,MAAM,GAAG,MAAM,KAAK,EAAE,MAAM,IAAI,GAAG,SAAS,MAAM,GAAG,MAAM,IAAI;AACnF,gBAAI,YAAY,MAAM,OAAO,OAAO,SAAS,GAAG,UAAU,OAAO,OAAO,SAAS,GAAG;AACpF,mBAAO;AAAA,cAAC,MAAM,IAAI,WAAW,OAAO;AAAA,cAC5B,IAAI;AAAA,gBAAI,YAAY,OAAO,SAAS;AAAA,gBAC5B,OAAO,UAAU,IAAI,UAAU,OAAO,GAAG,SAAS,OAAO,OAAO,SAAS,GAAG;AAAA,cAAM;AAAA,cAC1F;AAAA,YAAY;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAEA,eAAS,YAAY,QAAQ,QAAQ,WAAW;AAC9C,YAAI,OAAO,OAAO;AAClB,eAAO,QAAQ,OAAO,QAAQ;AAC5B,iBAAO,YAAY;AACnB,cAAI,WAAW,OAAO,KAAK,MAAM;AACjC,cAAI,CAAC;AAAU;AACf,cAAI,MAAM,SAAS,QAAQ,SAAS,GAAG;AACvC,cAAI,MAAM,OAAO,SAAS;AAAW;AACrC,cAAI,CAAC,SAAS,MAAM,MAAM,QAAQ,MAAM,GAAG;AACzC,oBAAQ;AACV,iBAAO,SAAS,QAAQ;AAAA,QAC1B;AACA,eAAO;AAAA,MACT;AAEA,eAAS,qBAAqB,KAAK,QAAQ,OAAO;AAChD,iBAAS,YAAY,QAAQ,GAAG;AAChC,iBAAS,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,QAAQ,IAAI,UAAU,GAAG,QAAQ,OAAO,QAAQ,KAAK,IAAI;AAClG,cAAI,SAAS,IAAI,QAAQ,IAAI;AAC7B,cAAI,QAAQ,YAAY,QAAQ,QAAQ,KAAK,IAAI,IAAI,OAAO,SAAS,EAAE;AACvE,cAAI;AACF,mBAAO;AAAA,cAAC,MAAM,IAAI,MAAM,MAAM,KAAK;AAAA,cAC3B,IAAI,IAAI,MAAM,MAAM,QAAQ,MAAM,GAAG,MAAM;AAAA,cAC3C;AAAA,YAAY;AAAA,QACxB;AAAA,MACF;AAEA,eAAS,8BAA8B,KAAK,QAAQ,OAAO;AACzD,YAAI,CAAC,eAAe,MAAM;AAAG,iBAAO,qBAAqB,KAAK,QAAQ,KAAK;AAC3E,iBAAS,YAAY,QAAQ,IAAI;AACjC,YAAI,QAAQ,YAAY,GAAG,YAAY,IAAI,QAAQ,MAAM,IAAI,EAAE,SAAS,MAAM;AAC9E,iBAAS,OAAO,MAAM,MAAM,QAAQ,IAAI,UAAU,GAAG,QAAQ,SAAQ;AACnE,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,OAAO,KAAK;AACnD,gBAAI,UAAU,IAAI,QAAQ,MAAM;AAChC,qBAAS,UAAU,OAAO,UAAU,UAAU,OAAO;AAAA,UACvD;AACA,uBAAa;AAEb,cAAI,QAAQ,YAAY,QAAQ,QAAQ,SAAS;AACjD,cAAI,OAAO;AACT,gBAAI,SAAS,OAAO,MAAM,GAAG,MAAM,KAAK,EAAE,MAAM,IAAI,GAAG,SAAS,MAAM,GAAG,MAAM,IAAI;AACnF,gBAAI,YAAY,OAAO,OAAO,QAAQ,UAAU,OAAO,OAAO,SAAS,GAAG;AAC1E,mBAAO;AAAA,cAAC,MAAM,IAAI,WAAW,OAAO;AAAA,cAC5B,IAAI;AAAA,gBAAI,YAAY,OAAO,SAAS;AAAA,gBAC5B,OAAO,UAAU,IAAI,UAAU,OAAO,GAAG,SAAS,OAAO,OAAO,SAAS,GAAG;AAAA,cAAM;AAAA,cAC1F;AAAA,YAAY;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAEA,UAAI,QAAQ;AACZ,UAAI,OAAO,UAAU,WAAW;AAC9B,iBAAS,SAAS,KAAK;AAAE,iBAAO,IAAI,UAAU,KAAK,EAAE,YAAY;AAAA,QAAE;AACnE,iBAAS,SAAS,KAAK;AAAE,iBAAO,IAAI,UAAU,KAAK;AAAA,QAAE;AAAA,MACvD,OAAO;AACL,iBAAS,SAAS,KAAK;AAAE,iBAAO,IAAI,YAAY;AAAA,QAAE;AAClD,iBAAS,SAAS,KAAK;AAAE,iBAAO;AAAA,QAAI;AAAA,MACtC;AAIA,eAAS,UAAU,MAAM,QAAQ,KAAK,UAAU;AAC9C,YAAI,KAAK,UAAU,OAAO;AAAQ,iBAAO;AACzC,iBAAS,MAAM,GAAG,MAAM,MAAM,KAAK,IAAI,GAAG,KAAK,SAAS,OAAO,MAAM,OAAK;AACxE,cAAI,OAAO;AAAK,mBAAO;AACvB,cAAI,MAAO,MAAM,OAAQ;AACzB,cAAI,MAAM,SAAS,KAAK,MAAM,GAAG,GAAG,CAAC,EAAE;AACvC,cAAI,OAAO;AAAK,mBAAO;AAAA,mBACd,MAAM;AAAK,kBAAM;AAAA;AACrB,kBAAM,MAAM;AAAA,QACnB;AAAA,MACF;AAEA,eAAS,oBAAoB,KAAK,OAAO,OAAO,UAAU;AAGxD,YAAI,CAAC,MAAM;AAAQ,iBAAO;AAC1B,YAAI,OAAO,WAAW,SAAS;AAC/B,YAAI,QAAQ,KAAK,KAAK,EAAE,MAAM,UAAU;AAExC;AAAQ,mBAAS,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,OAAO,IAAI,SAAS,IAAI,IAAI,MAAM,QAAQ,QAAQ,MAAM,QAAQ,KAAK,GAAG;AACzH,gBAAI,OAAO,IAAI,QAAQ,IAAI,EAAE,MAAM,EAAE,GAAG,SAAS,KAAK,IAAI;AAC1D,gBAAI,MAAM,UAAU,GAAG;AACrB,kBAAI,QAAQ,OAAO,QAAQ,MAAM,EAAE;AACnC,kBAAI,SAAS;AAAI,yBAAS;AAC1B,kBAAI,QAAQ,UAAU,MAAM,QAAQ,OAAO,IAAI,IAAI;AACnD,qBAAO;AAAA,gBAAC,MAAM,IAAI,MAAM,UAAU,MAAM,QAAQ,OAAO,IAAI,IAAI,EAAE;AAAA,gBACzD,IAAI,IAAI,MAAM,UAAU,MAAM,QAAQ,QAAQ,MAAM,GAAG,QAAQ,IAAI,IAAI,EAAE;AAAA,cAAC;AAAA,YACpF,OAAO;AACL,kBAAI,UAAU,OAAO,SAAS,MAAM,GAAG;AACvC,kBAAI,OAAO,MAAM,OAAO,KAAK,MAAM;AAAI,yBAAS;AAChD,uBAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG;AACpC,oBAAI,KAAK,IAAI,QAAQ,OAAO,CAAC,CAAC,KAAK,MAAM;AAAI,2BAAS;AACxD,kBAAI,MAAM,IAAI,QAAQ,OAAO,MAAM,SAAS,CAAC,GAAG,YAAY,KAAK,GAAG,GAAG,WAAW,MAAM,MAAM,SAAS;AACvG,kBAAI,UAAU,MAAM,GAAG,SAAS,MAAM,KAAK;AAAU,yBAAS;AAC9D,qBAAO;AAAA,gBAAC,MAAM,IAAI,MAAM,UAAU,MAAM,QAAQ,SAAS,IAAI,IAAI,EAAE;AAAA,gBAC3D,IAAI,IAAI,OAAO,MAAM,SAAS,GAAG,UAAU,KAAK,WAAW,SAAS,QAAQ,IAAI,CAAC;AAAA,cAAC;AAAA,YAC5F;AAAA,UACF;AAAA,MACF;AAEA,eAAS,qBAAqB,KAAK,OAAO,OAAO,UAAU;AACzD,YAAI,CAAC,MAAM;AAAQ,iBAAO;AAC1B,YAAI,OAAO,WAAW,SAAS;AAC/B,YAAI,QAAQ,KAAK,KAAK,EAAE,MAAM,UAAU;AAExC;AAAQ,mBAAS,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,MAAM,QAAQ,QAAQ,OAAO,QAAQ,KAAK,IAAI;AAC7H,gBAAI,OAAO,IAAI,QAAQ,IAAI;AAC3B,gBAAI,KAAK;AAAI,qBAAO,KAAK,MAAM,GAAG,EAAE;AACpC,gBAAI,SAAS,KAAK,IAAI;AACtB,gBAAI,MAAM,UAAU,GAAG;AACrB,kBAAI,QAAQ,OAAO,YAAY,MAAM,EAAE;AACvC,kBAAI,SAAS;AAAI,yBAAS;AAC1B,qBAAO;AAAA,gBAAC,MAAM,IAAI,MAAM,UAAU,MAAM,QAAQ,OAAO,IAAI,CAAC;AAAA,gBACpD,IAAI,IAAI,MAAM,UAAU,MAAM,QAAQ,QAAQ,MAAM,GAAG,QAAQ,IAAI,CAAC;AAAA,cAAC;AAAA,YAC/E,OAAO;AACL,kBAAI,WAAW,MAAM,MAAM,SAAS;AACpC,kBAAI,OAAO,MAAM,GAAG,SAAS,MAAM,KAAK;AAAU,yBAAS;AAC3D,uBAAS,IAAI,GAAG,QAAQ,OAAO,MAAM,SAAS,GAAG,IAAI,MAAM,SAAS,GAAG;AACrE,oBAAI,KAAK,IAAI,QAAQ,QAAQ,CAAC,CAAC,KAAK,MAAM;AAAI,2BAAS;AACzD,kBAAI,MAAM,IAAI,QAAQ,OAAO,IAAI,MAAM,MAAM,GAAG,YAAY,KAAK,GAAG;AACpE,kBAAI,UAAU,MAAM,UAAU,SAAS,MAAM,GAAG,MAAM,KAAK,MAAM;AAAI,yBAAS;AAC9E,qBAAO;AAAA,gBAAC,MAAM,IAAI,OAAO,IAAI,MAAM,QAAQ,UAAU,KAAK,WAAW,IAAI,SAAS,MAAM,GAAG,QAAQ,IAAI,CAAC;AAAA,gBAChG,IAAI,IAAI,MAAM,UAAU,MAAM,QAAQ,SAAS,QAAQ,IAAI,CAAC;AAAA,cAAC;AAAA,YACvE;AAAA,UACF;AAAA,MACF;AAEA,eAAS,aAAa,KAAK,OAAO,KAAK,SAAS;AAC9C,aAAK,eAAe;AACpB,aAAK,kBAAkB;AACvB,aAAK,MAAM;AACX,cAAM,MAAM,IAAI,QAAQ,GAAG,IAAI,IAAI,GAAG,CAAC;AACvC,aAAK,MAAM,EAAC,MAAM,KAAK,IAAI,IAAG;AAE9B,YAAI;AACJ,YAAI,OAAO,WAAW,UAAU;AAC9B,qBAAW,QAAQ;AAAA,QACrB,OAAO;AACL,qBAAW;AACX,oBAAU;AAAA,QACZ;AAEA,YAAI,OAAO,SAAS,UAAU;AAC5B,cAAI,YAAY;AAAM,uBAAW;AACjC,eAAK,UAAU,SAAS,SAASC,MAAK;AACpC,oBAAQ,UAAU,uBAAuB,qBAAqB,KAAK,OAAOA,MAAK,QAAQ;AAAA,UACzF;AAAA,QACF,OAAO;AACL,kBAAQ,YAAY,OAAO,IAAI;AAC/B,cAAI,CAAC,WAAW,QAAQ,cAAc;AACpC,iBAAK,UAAU,SAAS,SAASA,MAAK;AACpC,sBAAQ,UAAU,gCAAgC,8BAA8B,KAAK,OAAOA,IAAG;AAAA,YACjG;AAAA;AAEA,iBAAK,UAAU,SAAS,SAASA,MAAK;AACpC,sBAAQ,UAAU,uBAAuB,qBAAqB,KAAK,OAAOA,IAAG;AAAA,YAC/E;AAAA,QACJ;AAAA,MACF;AAEA,mBAAa,YAAY;AAAA,QACvB,UAAU,WAAW;AAAC,iBAAO,KAAK,KAAK,KAAK;AAAA,QAAC;AAAA,QAC7C,cAAc,WAAW;AAAC,iBAAO,KAAK,KAAK,IAAI;AAAA,QAAC;AAAA,QAEhD,MAAM,SAAS,SAAS;AACtB,cAAI,OAAO,KAAK,IAAI,QAAQ,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE;AACjE,cAAI,KAAK,mBAAmB,KAAK,cAAc;AAE7C,mBAAO,IAAI,KAAK,MAAM,KAAK,EAAE;AAC7B,gBAAI,SAAS;AACX,mBAAK;AACL,kBAAI,KAAK,KAAK,GAAG;AACf,qBAAK;AACL,qBAAK,MAAM,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI;AAAA,cAChD;AAAA,YACF,OAAO;AACL,mBAAK;AACL,kBAAI,KAAK,MAAM,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ;AACxD,qBAAK,KAAK;AACV,qBAAK;AAAA,cACP;AAAA,YACF;AACA,gBAAID,YAAW,OAAO,MAAM,KAAK,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAG;AACvD,qBAAO,KAAK,eAAe;AAAA,YAC9B;AAAA,UACF;AACA,cAAI,SAAS,KAAK,QAAQ,SAAS,IAAI;AACvC,eAAK,kBAAkB,UAAUA,YAAW,OAAO,OAAO,MAAM,OAAO,EAAE,KAAK;AAE9E,cAAI,QAAQ;AACV,iBAAK,MAAM;AACX,iBAAK,eAAe;AACpB,mBAAO,KAAK,IAAI,SAAS;AAAA,UAC3B,OAAO;AACL,gBAAI,MAAM,IAAI,UAAU,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,SAAS,IAAI,GAAG,CAAC;AACzE,iBAAK,MAAM,EAAC,MAAM,KAAK,IAAI,IAAG;AAC9B,mBAAO,KAAK,eAAe;AAAA,UAC7B;AAAA,QACF;AAAA,QAEA,MAAM,WAAW;AAAC,cAAI,KAAK;AAAc,mBAAO,KAAK,IAAI;AAAA,QAAI;AAAA,QAC7D,IAAI,WAAW;AAAC,cAAI,KAAK;AAAc,mBAAO,KAAK,IAAI;AAAA,QAAE;AAAA,QAEzD,SAAS,SAAS,SAAS,QAAQ;AACjC,cAAI,CAAC,KAAK;AAAc;AACxB,cAAI,QAAQA,YAAW,WAAW,OAAO;AACzC,eAAK,IAAI,aAAa,OAAO,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM;AAC/D,eAAK,IAAI,KAAK;AAAA,YAAI,KAAK,IAAI,KAAK,OAAO,MAAM,SAAS;AAAA,YACpC,MAAM,MAAM,SAAS,GAAG,UAAU,MAAM,UAAU,IAAI,KAAK,IAAI,KAAK,KAAK;AAAA,UAAE;AAAA,QAC/F;AAAA,MACF;AAEA,MAAAA,YAAW,gBAAgB,mBAAmB,SAAS,OAAO,KAAK,UAAU;AAC3E,eAAO,IAAI,aAAa,KAAK,KAAK,OAAO,KAAK,QAAQ;AAAA,MACxD,CAAC;AACD,MAAAA,YAAW,mBAAmB,mBAAmB,SAAS,OAAO,KAAK,UAAU;AAC9E,eAAO,IAAI,aAAa,MAAM,OAAO,KAAK,QAAQ;AAAA,MACpD,CAAC;AAED,MAAAA,YAAW,gBAAgB,iBAAiB,SAAS,OAAO,UAAU;AACpE,YAAI,SAAS,CAAC;AACd,YAAI,MAAM,KAAK,gBAAgB,OAAO,KAAK,UAAU,MAAM,GAAG,QAAQ;AACtE,eAAO,IAAI,SAAS,GAAG;AACrB,cAAIA,YAAW,OAAO,IAAI,GAAG,GAAG,KAAK,UAAU,IAAI,CAAC,IAAI;AAAG;AAC3D,iBAAO,KAAK,EAAC,QAAQ,IAAI,KAAK,GAAG,MAAM,IAAI,GAAG,EAAC,CAAC;AAAA,QAClD;AACA,YAAI,OAAO;AACT,eAAK,cAAc,QAAQ,CAAC;AAAA,MAChC,CAAC;AAAA,IACH,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "pos"]}