{"version": 3, "sources": ["../../codemirror/addon/scroll/annotatescrollbar.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineExtension(\"annotateScrollbar\", function(options) {\n    if (typeof options == \"string\") options = {className: options};\n    return new Annotation(this, options);\n  });\n\n  CodeMirror.defineOption(\"scrollButtonHeight\", 0);\n\n  function Annotation(cm, options) {\n    this.cm = cm;\n    this.options = options;\n    this.buttonHeight = options.scrollButtonHeight || cm.getOption(\"scrollButtonHeight\");\n    this.annotations = [];\n    this.doRedraw = this.doUpdate = null;\n    this.div = cm.getWrapperElement().appendChild(document.createElement(\"div\"));\n    this.div.style.cssText = \"position: absolute; right: 0; top: 0; z-index: 7; pointer-events: none\";\n    this.computeScale();\n\n    function scheduleRedraw(delay) {\n      clearTimeout(self.doRedraw);\n      self.doRedraw = setTimeout(function() { self.redraw(); }, delay);\n    }\n\n    var self = this;\n    cm.on(\"refresh\", this.resizeHandler = function() {\n      clearTimeout(self.doUpdate);\n      self.doUpdate = setTimeout(function() {\n        if (self.computeScale()) scheduleRedraw(20);\n      }, 100);\n    });\n    cm.on(\"markerAdded\", this.resizeHandler);\n    cm.on(\"markerCleared\", this.resizeHandler);\n    if (options.listenForChanges !== false)\n      cm.on(\"changes\", this.changeHandler = function() {\n        scheduleRedraw(250);\n      });\n  }\n\n  Annotation.prototype.computeScale = function() {\n    var cm = this.cm;\n    var hScale = (cm.getWrapperElement().clientHeight - cm.display.barHeight - this.buttonHeight * 2) /\n      cm.getScrollerElement().scrollHeight\n    if (hScale != this.hScale) {\n      this.hScale = hScale;\n      return true;\n    }\n  };\n\n  Annotation.prototype.update = function(annotations) {\n    this.annotations = annotations;\n    this.redraw();\n  };\n\n  Annotation.prototype.redraw = function(compute) {\n    if (compute !== false) this.computeScale();\n    var cm = this.cm, hScale = this.hScale;\n\n    var frag = document.createDocumentFragment(), anns = this.annotations;\n\n    var wrapping = cm.getOption(\"lineWrapping\");\n    var singleLineH = wrapping && cm.defaultTextHeight() * 1.5;\n    var curLine = null, curLineObj = null;\n\n    function getY(pos, top) {\n      if (curLine != pos.line) {\n        curLine = pos.line\n        curLineObj = cm.getLineHandle(pos.line)\n        var visual = cm.getLineHandleVisualStart(curLineObj)\n        if (visual != curLineObj) {\n          curLine = cm.getLineNumber(visual)\n          curLineObj = visual\n        }\n      }\n      if ((curLineObj.widgets && curLineObj.widgets.length) ||\n          (wrapping && curLineObj.height > singleLineH))\n        return cm.charCoords(pos, \"local\")[top ? \"top\" : \"bottom\"];\n      var topY = cm.heightAtLine(curLineObj, \"local\");\n      return topY + (top ? 0 : curLineObj.height);\n    }\n\n    var lastLine = cm.lastLine()\n    if (cm.display.barWidth) for (var i = 0, nextTop; i < anns.length; i++) {\n      var ann = anns[i];\n      if (ann.to.line > lastLine) continue;\n      var top = nextTop || getY(ann.from, true) * hScale;\n      var bottom = getY(ann.to, false) * hScale;\n      while (i < anns.length - 1) {\n        if (anns[i + 1].to.line > lastLine) break;\n        nextTop = getY(anns[i + 1].from, true) * hScale;\n        if (nextTop > bottom + .9) break;\n        ann = anns[++i];\n        bottom = getY(ann.to, false) * hScale;\n      }\n      if (bottom == top) continue;\n      var height = Math.max(bottom - top, 3);\n\n      var elt = frag.appendChild(document.createElement(\"div\"));\n      elt.style.cssText = \"position: absolute; right: 0px; width: \" + Math.max(cm.display.barWidth - 1, 2) + \"px; top: \"\n        + (top + this.buttonHeight) + \"px; height: \" + height + \"px\";\n      elt.className = this.options.className;\n      if (ann.id) {\n        elt.setAttribute(\"annotation-id\", ann.id);\n      }\n    }\n    this.div.textContent = \"\";\n    this.div.appendChild(frag);\n  };\n\n  Annotation.prototype.clear = function() {\n    this.cm.off(\"refresh\", this.resizeHandler);\n    this.cm.off(\"markerAdded\", this.resizeHandler);\n    this.cm.off(\"markerCleared\", this.resizeHandler);\n    if (this.changeHandler) this.cm.off(\"changes\", this.changeHandler);\n    this.div.parentNode.removeChild(this.div);\n  };\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,gBAAgB,qBAAqB,SAAS,SAAS;AAChE,YAAI,OAAO,WAAW;AAAU,oBAAU,EAAC,WAAW,QAAO;AAC7D,eAAO,IAAI,WAAW,MAAM,OAAO;AAAA,MACrC,CAAC;AAED,MAAAA,YAAW,aAAa,sBAAsB,CAAC;AAE/C,eAAS,WAAW,IAAI,SAAS;AAC/B,aAAK,KAAK;AACV,aAAK,UAAU;AACf,aAAK,eAAe,QAAQ,sBAAsB,GAAG,UAAU,oBAAoB;AACnF,aAAK,cAAc,CAAC;AACpB,aAAK,WAAW,KAAK,WAAW;AAChC,aAAK,MAAM,GAAG,kBAAkB,EAAE,YAAY,SAAS,cAAc,KAAK,CAAC;AAC3E,aAAK,IAAI,MAAM,UAAU;AACzB,aAAK,aAAa;AAElB,iBAAS,eAAe,OAAO;AAC7B,uBAAa,KAAK,QAAQ;AAC1B,eAAK,WAAW,WAAW,WAAW;AAAE,iBAAK,OAAO;AAAA,UAAG,GAAG,KAAK;AAAA,QACjE;AAEA,YAAI,OAAO;AACX,WAAG,GAAG,WAAW,KAAK,gBAAgB,WAAW;AAC/C,uBAAa,KAAK,QAAQ;AAC1B,eAAK,WAAW,WAAW,WAAW;AACpC,gBAAI,KAAK,aAAa;AAAG,6BAAe,EAAE;AAAA,UAC5C,GAAG,GAAG;AAAA,QACR,CAAC;AACD,WAAG,GAAG,eAAe,KAAK,aAAa;AACvC,WAAG,GAAG,iBAAiB,KAAK,aAAa;AACzC,YAAI,QAAQ,qBAAqB;AAC/B,aAAG,GAAG,WAAW,KAAK,gBAAgB,WAAW;AAC/C,2BAAe,GAAG;AAAA,UACpB,CAAC;AAAA,MACL;AAEA,iBAAW,UAAU,eAAe,WAAW;AAC7C,YAAI,KAAK,KAAK;AACd,YAAI,UAAU,GAAG,kBAAkB,EAAE,eAAe,GAAG,QAAQ,YAAY,KAAK,eAAe,KAC7F,GAAG,mBAAmB,EAAE;AAC1B,YAAI,UAAU,KAAK,QAAQ;AACzB,eAAK,SAAS;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,iBAAW,UAAU,SAAS,SAAS,aAAa;AAClD,aAAK,cAAc;AACnB,aAAK,OAAO;AAAA,MACd;AAEA,iBAAW,UAAU,SAAS,SAAS,SAAS;AAC9C,YAAI,YAAY;AAAO,eAAK,aAAa;AACzC,YAAI,KAAK,KAAK,IAAI,SAAS,KAAK;AAEhC,YAAI,OAAO,SAAS,uBAAuB,GAAG,OAAO,KAAK;AAE1D,YAAI,WAAW,GAAG,UAAU,cAAc;AAC1C,YAAI,cAAc,YAAY,GAAG,kBAAkB,IAAI;AACvD,YAAI,UAAU,MAAM,aAAa;AAEjC,iBAAS,KAAK,KAAKC,MAAK;AACtB,cAAI,WAAW,IAAI,MAAM;AACvB,sBAAU,IAAI;AACd,yBAAa,GAAG,cAAc,IAAI,IAAI;AACtC,gBAAI,SAAS,GAAG,yBAAyB,UAAU;AACnD,gBAAI,UAAU,YAAY;AACxB,wBAAU,GAAG,cAAc,MAAM;AACjC,2BAAa;AAAA,YACf;AAAA,UACF;AACA,cAAK,WAAW,WAAW,WAAW,QAAQ,UACzC,YAAY,WAAW,SAAS;AACnC,mBAAO,GAAG,WAAW,KAAK,OAAO,EAAEA,OAAM,QAAQ;AACnD,cAAI,OAAO,GAAG,aAAa,YAAY,OAAO;AAC9C,iBAAO,QAAQA,OAAM,IAAI,WAAW;AAAA,QACtC;AAEA,YAAI,WAAW,GAAG,SAAS;AAC3B,YAAI,GAAG,QAAQ;AAAU,mBAAS,IAAI,GAAG,SAAS,IAAI,KAAK,QAAQ,KAAK;AACtE,gBAAI,MAAM,KAAK;AACf,gBAAI,IAAI,GAAG,OAAO;AAAU;AAC5B,gBAAI,MAAM,WAAW,KAAK,IAAI,MAAM,IAAI,IAAI;AAC5C,gBAAI,SAAS,KAAK,IAAI,IAAI,KAAK,IAAI;AACnC,mBAAO,IAAI,KAAK,SAAS,GAAG;AAC1B,kBAAI,KAAK,IAAI,GAAG,GAAG,OAAO;AAAU;AACpC,wBAAU,KAAK,KAAK,IAAI,GAAG,MAAM,IAAI,IAAI;AACzC,kBAAI,UAAU,SAAS;AAAI;AAC3B,oBAAM,KAAK,EAAE;AACb,uBAAS,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,YACjC;AACA,gBAAI,UAAU;AAAK;AACnB,gBAAI,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC;AAErC,gBAAI,MAAM,KAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AACxD,gBAAI,MAAM,UAAU,4CAA4C,KAAK,IAAI,GAAG,QAAQ,WAAW,GAAG,CAAC,IAAI,eAClG,MAAM,KAAK,gBAAgB,iBAAiB,SAAS;AAC1D,gBAAI,YAAY,KAAK,QAAQ;AAC7B,gBAAI,IAAI,IAAI;AACV,kBAAI,aAAa,iBAAiB,IAAI,EAAE;AAAA,YAC1C;AAAA,UACF;AACA,aAAK,IAAI,cAAc;AACvB,aAAK,IAAI,YAAY,IAAI;AAAA,MAC3B;AAEA,iBAAW,UAAU,QAAQ,WAAW;AACtC,aAAK,GAAG,IAAI,WAAW,KAAK,aAAa;AACzC,aAAK,GAAG,IAAI,eAAe,KAAK,aAAa;AAC7C,aAAK,GAAG,IAAI,iBAAiB,KAAK,aAAa;AAC/C,YAAI,KAAK;AAAe,eAAK,GAAG,IAAI,WAAW,KAAK,aAAa;AACjE,aAAK,IAAI,WAAW,YAAY,KAAK,GAAG;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror", "top"]}