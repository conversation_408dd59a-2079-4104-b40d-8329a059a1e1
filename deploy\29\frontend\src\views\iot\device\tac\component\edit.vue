<template>
    <div class="system-edit-dept-container">
        <el-dialog :title="(ruleForm.id ? '修改' : '添加') + 'tac码'" v-model="isShowDialog" width="769px">
            <el-form ref="formRef" :model="ruleForm" :rules="rules" size="default" label-width="90px">
                <el-row :gutter="35">

                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="厂家" prop="manufactor">
                            <el-input v-model="ruleForm.manufactor" placeholder="请输入固件名" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="型号" prop="model">
                            <el-input v-model="ruleForm.model" placeholder="请输入型号" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="tac码" prop="tac">
                            <el-input v-model="ruleForm.tac" placeholder="请输入tac码" min clearable></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" size="default">{{ ruleForm.id ? '修 改' : '添 加' }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';
import api from '/@/api/device';
import { tacValidate } from '/@/utils/validator';
import { ElMessage } from 'element-plus';

interface RuleFormState {
    id: string;
    manufactor: string;
    model: string;
    tac: string;
    status: number;
    create_by: string;
    created_at: string;
    update_by: string;
    updated_at: string;
}
interface FirmwareState {
    isShowDialog: boolean;
    ruleForm: RuleFormState;
    rules: object;
}

const baseForm: RuleFormState = {
    id: '',
    manufactor: '',
    model: '',
    tac: '',
    status: 0,
    create_by: '',
    created_at: '',
    update_by: '',
    updated_at: ''
};

export default defineComponent({
    name: 'EditFirmware',
    setup(prop, { emit }) {
        const formRef = ref<HTMLElement | null>(null);
        const state = reactive<FirmwareState>({
            isShowDialog: false,
            ruleForm: {
                ...baseForm,
            },
            //projectData: [], // 项目数据
            rules: {
                manufactor: [{ required: true, message: '厂家不能为空', trigger: 'blur' }],
                model: [{ required: true, message: '型号不能为空', trigger: 'blur' }],
                tac: [{ required: true, validator: tacValidate, trigger: 'blur' }]
            },
        });

        // 打开弹窗
        const openDialog = (row?: RuleFormState | number) => {
            resetForm();
            if (row && typeof row === 'object') {
                state.ruleForm = row;
            } else if (row && typeof row === 'string') {
                state.ruleForm.id = row;
            }
            state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };
        // 新增
        const onSubmit = () => {
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.validate((valid: boolean) => {
                if (valid) {
                    console.log(state.ruleForm);
                    if (!state.ruleForm.id) {
                        //添加
                        api.tac.add(state.ruleForm).then(() => {
                            ElMessage.success('tac码添加成功');
                            closeDialog(); // 关闭弹窗
                            emit('getTacList');
                        });
                    } else {
                        //修改
                        api.tac.edit(state.ruleForm).then(() => {
                            ElMessage.success('tac码修改成功');
                            closeDialog(); // 关闭弹窗
                            emit('getTacList');
                        });
                    }
                }
            });
        };
        const resetForm = () => {
            state.ruleForm = {
                ...baseForm,
            };
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.resetFields();
        };
        return {
            openDialog,
            closeDialog,
            onCancel,
            onSubmit,
            formRef,
            ...toRefs(state),
        };
    },
});
</script>
