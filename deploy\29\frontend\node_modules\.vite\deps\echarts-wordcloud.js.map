{"version": 3, "sources": ["../../echarts/lib/echarts.js", "../../echarts-wordcloud/src/WordCloudSeries.js", "../../echarts-wordcloud/src/WordCloudView.js", "../../echarts-wordcloud/src/layout.js", "../../echarts-wordcloud/src/wordCloud.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\nexport * from './export/core.js';\nimport { use } from './extension.js';\nimport { init } from './core/echarts.js';\nimport { install as CanvasRenderer } from './renderer/installCanvasRenderer.js';\nimport { install as DatasetComponent } from './component/dataset/install.js'; // Default to have canvas renderer and dataset for compitatble reason.\n\nuse([CanvasRenderer, DatasetComponent]); // TODO: Compatitable with the following code\n// import echarts from 'echarts/lib/echarts.js'\n\nexport default {\n  init: function () {\n    if (process.env.NODE_ENV !== 'production') {\n      /* eslint-disable-next-line */\n      console.error(\"\\\"import echarts from 'echarts/lib/echarts.js'\\\" is not supported anymore. Use \\\"import * as echarts from 'echarts/lib/echarts.js'\\\" instead;\");\n    } // @ts-ignore\n\n\n    return init.apply(null, arguments);\n  }\n}; // Import label layout by default.\n// TODO remove\n\nimport { installLabelLayout } from './label/installLabelLayout.js';\nuse(installLabelLayout);", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendSeriesModel({\n\n    type: 'series.wordCloud',\n\n    visualStyleAccessPath: 'textStyle',\n    visualStyleMapper: function (model) {\n        return {\n            fill: model.get('color')\n        };\n    },\n    visualDrawType: 'fill',\n\n    optionUpdated: function () {\n        var option = this.option;\n        option.gridSize = Math.max(Math.floor(option.gridSize), 4);\n    },\n\n    getInitialData: function (option, ecModel) {\n        var dimensions = echarts.helper.createDimensions(option.data, {\n            coordDimensions: ['value']\n        });\n        var list = new echarts.List(dimensions, this);\n        list.initData(option.data);\n        return list;\n    },\n\n    // Most of options are from https://github.com/timdream/wordcloud2.js/blob/gh-pages/API.md\n    defaultOption: {\n\n        maskImage: null,\n\n        // Shape can be 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'pentagon', 'star'\n        shape: 'circle',\n\n        left: 'center',\n\n        top: 'center',\n\n        width: '70%',\n\n        height: '80%',\n\n        sizeRange: [12, 60],\n\n        rotationRange: [-90, 90],\n\n        rotationStep: 45,\n\n        gridSize: 8,\n\n        drawOutOfBound: false,\n\n        textStyle: {\n            fontWeight: 'normal'\n        }\n    }\n});\n", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendChartView({\n\n    type: 'wordCloud',\n\n    render: function (seriesModel, ecModel, api) {\n        var group = this.group;\n        group.removeAll();\n\n        var data = seriesModel.getData();\n\n        var gridSize = seriesModel.get('gridSize');\n\n        seriesModel.layoutInstance.ondraw = function (text, size, dataIdx, drawn) {\n            var itemModel = data.getItemModel(dataIdx);\n            var textStyleModel = itemModel.getModel('textStyle');\n\n            var textEl = new echarts.graphic.Text({\n                style: echarts.helper.createTextStyle(textStyleModel),\n                scaleX: 1 / drawn.info.mu,\n                scaleY: 1 / drawn.info.mu,\n                x: (drawn.gx + drawn.info.gw / 2) * gridSize,\n                y: (drawn.gy + drawn.info.gh / 2) * gridSize,\n                rotation: drawn.rot\n            });\n            textEl.setStyle({\n                x: drawn.info.fillTextOffsetX,\n                y: drawn.info.fillTextOffsetY + size * 0.5,\n                text: text,\n                verticalAlign: 'middle',\n                fill: data.getItemVisual(dataIdx, 'style').fill,\n                fontSize: size\n            });\n\n            group.add(textEl);\n\n            data.setItemGraphicEl(dataIdx, textEl);\n\n            textEl.ensureState('emphasis').style = echarts.helper.createTextStyle(itemModel.getModel(['emphasis', 'textStyle']), {\n                state: 'emphasis'\n            });\n            textEl.ensureState('blur').style = echarts.helper.createTextStyle(itemModel.getModel(['blur', 'textStyle']), {\n                state: 'blur'\n            });\n\n            echarts.helper.enableHoverEmphasis(\n                textEl,\n                itemModel.get(['emphasis', 'focus']),\n                itemModel.get(['emphasis', 'blurScope'])\n            );\n\n            textEl.stateTransition = {\n                duration: seriesModel.get('animation') ? seriesModel.get(['stateAnimation', 'duration']) : 0,\n                easing: seriesModel.get(['stateAnimation', 'easing'])\n            };\n            // TODO\n            textEl.__highDownDispatcher = true;\n        };\n\n        this._model = seriesModel;\n    },\n\n    remove: function () {\n        this.group.removeAll();\n\n        this._model.layoutInstance.dispose();\n    },\n\n    dispose: function () {\n        this._model.layoutInstance.dispose();\n    }\n});\n", "/*!\n * wordcloud2.js\n * http://timdream.org/wordcloud2.js/\n *\n * Copyright 2011 - 2013 <PERSON>\n * Released under the MIT license\n */\n\n'use strict';\n\n// setImmediate\nif (!window.setImmediate) {\n  window.setImmediate = (function setupSetImmediate() {\n    return window.msSetImmediate ||\n    window.webkitSetImmediate ||\n    window.mozSetImmediate ||\n    window.oSetImmediate ||\n    (function setupSetZeroTimeout() {\n      if (!window.postMessage || !window.addEventListener) {\n        return null;\n      }\n\n      var callbacks = [undefined];\n      var message = 'zero-timeout-message';\n\n      // Like setTimeout, but only takes a function argument.  There's\n      // no time argument (always zero) and no arguments (you have to\n      // use a closure).\n      var setZeroTimeout = function setZeroTimeout(callback) {\n        var id = callbacks.length;\n        callbacks.push(callback);\n        window.postMessage(message + id.toString(36), '*');\n\n        return id;\n      };\n\n      window.addEventListener('message', function setZeroTimeoutMessage(evt) {\n        // Skipping checking event source, retarded I<PERSON> confused this window\n        // object with another in the presence of iframe\n        if (typeof evt.data !== 'string' ||\n            evt.data.substr(0, message.length) !== message/* ||\n            evt.source !== window */) {\n          return;\n        }\n\n        evt.stopImmediatePropagation();\n\n        var id = parseInt(evt.data.substr(message.length), 36);\n        if (!callbacks[id]) {\n          return;\n        }\n\n        callbacks[id]();\n        callbacks[id] = undefined;\n      }, true);\n\n      /* specify clearImmediate() here since we need the scope */\n      window.clearImmediate = function clearZeroTimeout(id) {\n        if (!callbacks[id]) {\n          return;\n        }\n\n        callbacks[id] = undefined;\n      };\n\n      return setZeroTimeout;\n    })() ||\n    // fallback\n    function setImmediateFallback(fn) {\n      window.setTimeout(fn, 0);\n    };\n  })();\n}\n\nif (!window.clearImmediate) {\n  window.clearImmediate = (function setupClearImmediate() {\n    return window.msClearImmediate ||\n    window.webkitClearImmediate ||\n    window.mozClearImmediate ||\n    window.oClearImmediate ||\n    // \"clearZeroTimeout\" is implement on the previous block ||\n    // fallback\n    function clearImmediateFallback(timer) {\n      window.clearTimeout(timer);\n    };\n  })();\n}\n\n  // Check if WordCloud can run on this browser\n  var isSupported = (function isSupported() {\n    var canvas = document.createElement('canvas');\n    if (!canvas || !canvas.getContext) {\n      return false;\n    }\n\n    var ctx = canvas.getContext('2d');\n    if (!ctx.getImageData) {\n      return false;\n    }\n    if (!ctx.fillText) {\n      return false;\n    }\n\n    if (!Array.prototype.some) {\n      return false;\n    }\n    if (!Array.prototype.push) {\n      return false;\n    }\n\n    return true;\n  }());\n\n  // Find out if the browser impose minium font size by\n  // drawing small texts on a canvas and measure it's width.\n  var minFontSize = (function getMinFontSize() {\n    if (!isSupported) {\n      return;\n    }\n\n    var ctx = document.createElement('canvas').getContext('2d');\n\n    // start from 20\n    var size = 20;\n\n    // two sizes to measure\n    var hanWidth, mWidth;\n\n    while (size) {\n      ctx.font = size.toString(10) + 'px sans-serif';\n      if ((ctx.measureText('\\uFF37').width === hanWidth) &&\n          (ctx.measureText('m').width) === mWidth) {\n        return (size + 1);\n      }\n\n      hanWidth = ctx.measureText('\\uFF37').width;\n      mWidth = ctx.measureText('m').width;\n\n      size--;\n    }\n\n    return 0;\n  })();\n\n  // Based on http://jsfromhell.com/array/shuffle\n  var shuffleArray = function shuffleArray(arr) {\n    for (var j, x, i = arr.length; i;\n      j = Math.floor(Math.random() * i),\n      x = arr[--i], arr[i] = arr[j],\n      arr[j] = x) {}\n    return arr;\n  };\n\n  var WordCloud = function WordCloud(elements, options) {\n    if (!isSupported) {\n      return;\n    }\n\n    if (!Array.isArray(elements)) {\n      elements = [elements];\n    }\n\n    elements.forEach(function(el, i) {\n      if (typeof el === 'string') {\n        elements[i] = document.getElementById(el);\n        if (!elements[i]) {\n          throw 'The element id specified is not found.';\n        }\n      } else if (!el.tagName && !el.appendChild) {\n        throw 'You must pass valid HTML elements, or ID of the element.';\n      }\n    });\n\n    /* Default values to be overwritten by options object */\n    var settings = {\n      list: [],\n      fontFamily: '\"Trebuchet MS\", \"Heiti TC\", \"微軟正黑體\", ' +\n                  '\"Arial Unicode MS\", \"Droid Fallback Sans\", sans-serif',\n      fontWeight: 'normal',\n      color: 'random-dark',\n      minSize: 0, // 0 to disable\n      weightFactor: 1,\n      clearCanvas: true,\n      backgroundColor: '#fff',  // opaque white = rgba(255, 255, 255, 1)\n\n      gridSize: 8,\n      drawOutOfBound: false,\n      origin: null,\n\n      drawMask: false,\n      maskColor: 'rgba(255,0,0,0.3)',\n      maskGapWidth: 0.3,\n\n      layoutAnimation: true,\n\n      wait: 0,\n      abortThreshold: 0, // disabled\n      abort: function noop() {},\n\n      minRotation: - Math.PI / 2,\n      maxRotation: Math.PI / 2,\n      rotationStep: 0.1,\n\n      shuffle: true,\n      rotateRatio: 0.1,\n\n      shape: 'circle',\n      ellipticity: 0.65,\n\n      classes: null,\n\n      hover: null,\n      click: null\n    };\n\n    if (options) {\n      for (var key in options) {\n        if (key in settings) {\n          settings[key] = options[key];\n        }\n      }\n    }\n\n    /* Convert weightFactor into a function */\n    if (typeof settings.weightFactor !== 'function') {\n      var factor = settings.weightFactor;\n      settings.weightFactor = function weightFactor(pt) {\n        return pt * factor; //in px\n      };\n    }\n\n    /* Convert shape into a function */\n    if (typeof settings.shape !== 'function') {\n      switch (settings.shape) {\n        case 'circle':\n        /* falls through */\n        default:\n          // 'circle' is the default and a shortcut in the code loop.\n          settings.shape = 'circle';\n          break;\n\n        case 'cardioid':\n          settings.shape = function shapeCardioid(theta) {\n            return 1 - Math.sin(theta);\n          };\n          break;\n\n        /*\n        To work out an X-gon, one has to calculate \"m\",\n        where 1/(cos(2*PI/X)+m*sin(2*PI/X)) = 1/(cos(0)+m*sin(0))\n        http://www.wolframalpha.com/input/?i=1%2F%28cos%282*PI%2FX%29%2Bm*sin%28\n        2*PI%2FX%29%29+%3D+1%2F%28cos%280%29%2Bm*sin%280%29%29\n        Copy the solution into polar equation r = 1/(cos(t') + m*sin(t'))\n        where t' equals to mod(t, 2PI/X);\n        */\n\n        case 'diamond':\n        case 'square':\n          // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n          // %28t%2C+PI%2F2%29%29%2Bsin%28mod+%28t%2C+PI%2F2%29%29%29%2C+t+%3D\n          // +0+..+2*PI\n          settings.shape = function shapeSquare(theta) {\n            var thetaPrime = theta % (2 * Math.PI / 4);\n            return 1 / (Math.cos(thetaPrime) + Math.sin(thetaPrime));\n          };\n          break;\n\n        case 'triangle-forward':\n          // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n          // %28t%2C+2*PI%2F3%29%29%2Bsqrt%283%29sin%28mod+%28t%2C+2*PI%2F3%29\n          // %29%29%2C+t+%3D+0+..+2*PI\n          settings.shape = function shapeTriangle(theta) {\n            var thetaPrime = theta % (2 * Math.PI / 3);\n            return 1 / (Math.cos(thetaPrime) +\n                        Math.sqrt(3) * Math.sin(thetaPrime));\n          };\n          break;\n\n        case 'triangle':\n        case 'triangle-upright':\n          settings.shape = function shapeTriangle(theta) {\n            var thetaPrime = (theta + Math.PI * 3 / 2) % (2 * Math.PI / 3);\n            return 1 / (Math.cos(thetaPrime) +\n                        Math.sqrt(3) * Math.sin(thetaPrime));\n          };\n          break;\n\n        case 'pentagon':\n          settings.shape = function shapePentagon(theta) {\n            var thetaPrime = (theta + 0.955) % (2 * Math.PI / 5);\n            return 1 / (Math.cos(thetaPrime) +\n                        0.726543 * Math.sin(thetaPrime));\n          };\n          break;\n\n        case 'star':\n          settings.shape = function shapeStar(theta) {\n            var thetaPrime = (theta + 0.955) % (2 * Math.PI / 10);\n            if ((theta + 0.955) % (2 * Math.PI / 5) - (2 * Math.PI / 10) >= 0) {\n              return 1 / (Math.cos((2 * Math.PI / 10) - thetaPrime) +\n                          3.07768 * Math.sin((2 * Math.PI / 10) - thetaPrime));\n            } else {\n              return 1 / (Math.cos(thetaPrime) +\n                          3.07768 * Math.sin(thetaPrime));\n            }\n          };\n          break;\n      }\n    }\n\n    /* Make sure gridSize is a whole number and is not smaller than 4px */\n    settings.gridSize = Math.max(Math.floor(settings.gridSize), 4);\n\n    /* shorthand */\n    var g = settings.gridSize;\n    var maskRectWidth = g - settings.maskGapWidth;\n\n    /* normalize rotation settings */\n    var rotationRange = Math.abs(settings.maxRotation - settings.minRotation);\n    var minRotation = Math.min(settings.maxRotation, settings.minRotation);\n    var rotationStep = settings.rotationStep;\n\n    /* information/object available to all functions, set when start() */\n    var grid, // 2d array containing filling information\n      ngx, ngy, // width and height of the grid\n      center, // position of the center of the cloud\n      maxRadius;\n\n    /* timestamp for measuring each putWord() action */\n    var escapeTime;\n\n    /* function for getting the color of the text */\n    var getTextColor;\n    function random_hsl_color(min, max) {\n      return 'hsl(' +\n        (Math.random() * 360).toFixed() + ',' +\n        (Math.random() * 30 + 70).toFixed() + '%,' +\n        (Math.random() * (max - min) + min).toFixed() + '%)';\n    }\n    switch (settings.color) {\n      case 'random-dark':\n        getTextColor = function getRandomDarkColor() {\n          return random_hsl_color(10, 50);\n        };\n        break;\n\n      case 'random-light':\n        getTextColor = function getRandomLightColor() {\n          return random_hsl_color(50, 90);\n        };\n        break;\n\n      default:\n        if (typeof settings.color === 'function') {\n          getTextColor = settings.color;\n        }\n        break;\n    }\n\n    /* function for getting the classes of the text */\n    var getTextClasses = null;\n    if (typeof settings.classes === 'function') {\n      getTextClasses = settings.classes;\n    }\n\n    /* Interactive */\n    var interactive = false;\n    var infoGrid = [];\n    var hovered;\n\n    var getInfoGridFromMouseTouchEvent =\n    function getInfoGridFromMouseTouchEvent(evt) {\n      var canvas = evt.currentTarget;\n      var rect = canvas.getBoundingClientRect();\n      var clientX;\n      var clientY;\n      /** Detect if touches are available */\n      if (evt.touches) {\n        clientX = evt.touches[0].clientX;\n        clientY = evt.touches[0].clientY;\n      } else {\n        clientX = evt.clientX;\n        clientY = evt.clientY;\n      }\n      var eventX = clientX - rect.left;\n      var eventY = clientY - rect.top;\n\n      var x = Math.floor(eventX * ((canvas.width / rect.width) || 1) / g);\n      var y = Math.floor(eventY * ((canvas.height / rect.height) || 1) / g);\n\n      return infoGrid[x][y];\n    };\n\n    var wordcloudhover = function wordcloudhover(evt) {\n      var info = getInfoGridFromMouseTouchEvent(evt);\n\n      if (hovered === info) {\n        return;\n      }\n\n      hovered = info;\n      if (!info) {\n        settings.hover(undefined, undefined, evt);\n\n        return;\n      }\n\n      settings.hover(info.item, info.dimension, evt);\n\n    };\n\n    var wordcloudclick = function wordcloudclick(evt) {\n      var info = getInfoGridFromMouseTouchEvent(evt);\n      if (!info) {\n        return;\n      }\n\n      settings.click(info.item, info.dimension, evt);\n      evt.preventDefault();\n    };\n\n    /* Get points on the grid for a given radius away from the center */\n    var pointsAtRadius = [];\n    var getPointsAtRadius = function getPointsAtRadius(radius) {\n      if (pointsAtRadius[radius]) {\n        return pointsAtRadius[radius];\n      }\n\n      // Look for these number of points on each radius\n      var T = radius * 8;\n\n      // Getting all the points at this radius\n      var t = T;\n      var points = [];\n\n      if (radius === 0) {\n        points.push([center[0], center[1], 0]);\n      }\n\n      while (t--) {\n        // distort the radius to put the cloud in shape\n        var rx = 1;\n        if (settings.shape !== 'circle') {\n          rx = settings.shape(t / T * 2 * Math.PI); // 0 to 1\n        }\n\n        // Push [x, y, t]; t is used solely for getTextColor()\n        points.push([\n          center[0] + radius * rx * Math.cos(-t / T * 2 * Math.PI),\n          center[1] + radius * rx * Math.sin(-t / T * 2 * Math.PI) *\n            settings.ellipticity,\n          t / T * 2 * Math.PI]);\n      }\n\n      pointsAtRadius[radius] = points;\n      return points;\n    };\n\n    /* Return true if we had spent too much time */\n    var exceedTime = function exceedTime() {\n      return ((settings.abortThreshold > 0) &&\n        ((new Date()).getTime() - escapeTime > settings.abortThreshold));\n    };\n\n    /* Get the deg of rotation according to settings, and luck. */\n    var getRotateDeg = function getRotateDeg() {\n      if (settings.rotateRatio === 0) {\n        return 0;\n      }\n\n      if (Math.random() > settings.rotateRatio) {\n        return 0;\n      }\n\n      if (rotationRange === 0) {\n        return minRotation;\n      }\n\n      return minRotation + Math.round(Math.random() * rotationRange / rotationStep) * rotationStep;\n    };\n\n    var getTextInfo = function getTextInfo(word, weight, rotateDeg) {\n      // calculate the acutal font size\n      // fontSize === 0 means weightFactor function wants the text skipped,\n      // and size < minSize means we cannot draw the text.\n      var debug = false;\n      var fontSize = settings.weightFactor(weight);\n      if (fontSize <= settings.minSize) {\n        return false;\n      }\n\n      // Scale factor here is to make sure fillText is not limited by\n      // the minium font size set by browser.\n      // It will always be 1 or 2n.\n      var mu = 1;\n      if (fontSize < minFontSize) {\n        mu = (function calculateScaleFactor() {\n          var mu = 2;\n          while (mu * fontSize < minFontSize) {\n            mu += 2;\n          }\n          return mu;\n        })();\n      }\n\n      var fcanvas = document.createElement('canvas');\n      var fctx = fcanvas.getContext('2d', { willReadFrequently: true });\n\n      fctx.font = settings.fontWeight + ' ' +\n        (fontSize * mu).toString(10) + 'px ' + settings.fontFamily;\n\n      // Estimate the dimension of the text with measureText().\n      var fw = fctx.measureText(word).width / mu;\n      var fh = Math.max(fontSize * mu,\n                        fctx.measureText('m').width,\n                        fctx.measureText('\\uFF37').width) / mu;\n\n      // Create a boundary box that is larger than our estimates,\n      // so text don't get cut of (it sill might)\n      var boxWidth = fw + fh * 2;\n      var boxHeight = fh * 3;\n      var fgw = Math.ceil(boxWidth / g);\n      var fgh = Math.ceil(boxHeight / g);\n      boxWidth = fgw * g;\n      boxHeight = fgh * g;\n\n      // Calculate the proper offsets to make the text centered at\n      // the preferred position.\n\n      // This is simply half of the width.\n      var fillTextOffsetX = - fw / 2;\n      // Instead of moving the box to the exact middle of the preferred\n      // position, for Y-offset we move 0.4 instead, so Latin alphabets look\n      // vertical centered.\n      var fillTextOffsetY = - fh * 0.4;\n\n      // Calculate the actual dimension of the canvas, considering the rotation.\n      var cgh = Math.ceil((boxWidth * Math.abs(Math.sin(rotateDeg)) +\n                           boxHeight * Math.abs(Math.cos(rotateDeg))) / g);\n      var cgw = Math.ceil((boxWidth * Math.abs(Math.cos(rotateDeg)) +\n                           boxHeight * Math.abs(Math.sin(rotateDeg))) / g);\n      var width = cgw * g;\n      var height = cgh * g;\n\n      fcanvas.setAttribute('width', width);\n      fcanvas.setAttribute('height', height);\n\n      if (debug) {\n        // Attach fcanvas to the DOM\n        document.body.appendChild(fcanvas);\n        // Save it's state so that we could restore and draw the grid correctly.\n        fctx.save();\n      }\n\n      // Scale the canvas with |mu|.\n      fctx.scale(1 / mu, 1 / mu);\n      fctx.translate(width * mu / 2, height * mu / 2);\n      fctx.rotate(- rotateDeg);\n\n      // Once the width/height is set, ctx info will be reset.\n      // Set it again here.\n      fctx.font = settings.fontWeight + ' ' +\n        (fontSize * mu).toString(10) + 'px ' + settings.fontFamily;\n\n      // Fill the text into the fcanvas.\n      // XXX: We cannot because textBaseline = 'top' here because\n      // Firefox and Chrome uses different default line-height for canvas.\n      // Please read https://bugzil.la/737852#c6.\n      // Here, we use textBaseline = 'middle' and draw the text at exactly\n      // 0.5 * fontSize lower.\n      fctx.fillStyle = '#000';\n      fctx.textBaseline = 'middle';\n      fctx.fillText(word, fillTextOffsetX * mu,\n                    (fillTextOffsetY + fontSize * 0.5) * mu);\n\n      // Get the pixels of the text\n      var imageData = fctx.getImageData(0, 0, width, height).data;\n\n      if (exceedTime()) {\n        return false;\n      }\n\n      if (debug) {\n        // Draw the box of the original estimation\n        fctx.strokeRect(fillTextOffsetX * mu,\n                        fillTextOffsetY, fw * mu, fh * mu);\n        fctx.restore();\n      }\n\n      // Read the pixels and save the information to the occupied array\n      var occupied = [];\n      var gx = cgw, gy, x, y;\n      var bounds = [cgh / 2, cgw / 2, cgh / 2, cgw / 2];\n      while (gx--) {\n        gy = cgh;\n        while (gy--) {\n          y = g;\n          singleGridLoop: {\n            while (y--) {\n              x = g;\n              while (x--) {\n                if (imageData[((gy * g + y) * width +\n                               (gx * g + x)) * 4 + 3]) {\n                  occupied.push([gx, gy]);\n\n                  if (gx < bounds[3]) {\n                    bounds[3] = gx;\n                  }\n                  if (gx > bounds[1]) {\n                    bounds[1] = gx;\n                  }\n                  if (gy < bounds[0]) {\n                    bounds[0] = gy;\n                  }\n                  if (gy > bounds[2]) {\n                    bounds[2] = gy;\n                  }\n\n                  if (debug) {\n                    fctx.fillStyle = 'rgba(255, 0, 0, 0.5)';\n                    fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n                  }\n                  break singleGridLoop;\n                }\n              }\n            }\n            if (debug) {\n              fctx.fillStyle = 'rgba(0, 0, 255, 0.5)';\n              fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n            }\n          }\n        }\n      }\n\n      if (debug) {\n        fctx.fillStyle = 'rgba(0, 255, 0, 0.5)';\n        fctx.fillRect(bounds[3] * g,\n                      bounds[0] * g,\n                      (bounds[1] - bounds[3] + 1) * g,\n                      (bounds[2] - bounds[0] + 1) * g);\n      }\n\n      // Return information needed to create the text on the real canvas\n      return {\n        mu: mu,\n        occupied: occupied,\n        bounds: bounds,\n        gw: cgw,\n        gh: cgh,\n        fillTextOffsetX: fillTextOffsetX,\n        fillTextOffsetY: fillTextOffsetY,\n        fillTextWidth: fw,\n        fillTextHeight: fh,\n        fontSize: fontSize\n      };\n    };\n\n    /* Determine if there is room available in the given dimension */\n    var canFitText = function canFitText(gx, gy, gw, gh, occupied) {\n      // Go through the occupied points,\n      // return false if the space is not available.\n      var i = occupied.length;\n      while (i--) {\n        var px = gx + occupied[i][0];\n        var py = gy + occupied[i][1];\n\n        if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n          if (!settings.drawOutOfBound) {\n            return false;\n          }\n          continue;\n        }\n\n        if (!grid[px][py]) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    /* Actually draw the text on the grid */\n    var drawText = function drawText(gx, gy, info, word, weight,\n                                     distance, theta, rotateDeg, attributes) {\n\n      var fontSize = info.fontSize;\n      var color;\n      if (getTextColor) {\n        color = getTextColor(word, weight, fontSize, distance, theta);\n      } else {\n        color = settings.color;\n      }\n\n      var classes;\n      if (getTextClasses) {\n        classes = getTextClasses(word, weight, fontSize, distance, theta);\n      } else {\n        classes = settings.classes;\n      }\n\n      var dimension;\n      var bounds = info.bounds;\n      dimension = {\n        x: (gx + bounds[3]) * g,\n        y: (gy + bounds[0]) * g,\n        w: (bounds[1] - bounds[3] + 1) * g,\n        h: (bounds[2] - bounds[0] + 1) * g\n      };\n\n      elements.forEach(function(el) {\n        if (el.getContext) {\n          var ctx = el.getContext('2d');\n          var mu = info.mu;\n\n          // Save the current state before messing it\n          ctx.save();\n          ctx.scale(1 / mu, 1 / mu);\n\n          ctx.font = settings.fontWeight + ' ' +\n                     (fontSize * mu).toString(10) + 'px ' + settings.fontFamily;\n          ctx.fillStyle = color;\n\n          // Translate the canvas position to the origin coordinate of where\n          // the text should be put.\n          ctx.translate((gx + info.gw / 2) * g * mu,\n                        (gy + info.gh / 2) * g * mu);\n\n          if (rotateDeg !== 0) {\n            ctx.rotate(- rotateDeg);\n          }\n\n          // Finally, fill the text.\n\n          // XXX: We cannot because textBaseline = 'top' here because\n          // Firefox and Chrome uses different default line-height for canvas.\n          // Please read https://bugzil.la/737852#c6.\n          // Here, we use textBaseline = 'middle' and draw the text at exactly\n          // 0.5 * fontSize lower.\n          ctx.textBaseline = 'middle';\n          ctx.fillText(word, info.fillTextOffsetX * mu,\n                             (info.fillTextOffsetY + fontSize * 0.5) * mu);\n\n          // The below box is always matches how <span>s are positioned\n          /* ctx.strokeRect(info.fillTextOffsetX, info.fillTextOffsetY,\n            info.fillTextWidth, info.fillTextHeight); */\n\n          // Restore the state.\n          ctx.restore();\n        } else {\n          // drawText on DIV element\n          var span = document.createElement('span');\n          var transformRule = '';\n          transformRule = 'rotate(' + (- rotateDeg / Math.PI * 180) + 'deg) ';\n          if (info.mu !== 1) {\n            transformRule +=\n              'translateX(-' + (info.fillTextWidth / 4) + 'px) ' +\n              'scale(' + (1 / info.mu) + ')';\n          }\n          var styleRules = {\n            'position': 'absolute',\n            'display': 'block',\n            'font': settings.fontWeight + ' ' +\n                    (fontSize * info.mu) + 'px ' + settings.fontFamily,\n            'left': ((gx + info.gw / 2) * g + info.fillTextOffsetX) + 'px',\n            'top': ((gy + info.gh / 2) * g + info.fillTextOffsetY) + 'px',\n            'width': info.fillTextWidth + 'px',\n            'height': info.fillTextHeight + 'px',\n            'lineHeight': fontSize + 'px',\n            'whiteSpace': 'nowrap',\n            'transform': transformRule,\n            'webkitTransform': transformRule,\n            'msTransform': transformRule,\n            'transformOrigin': '50% 40%',\n            'webkitTransformOrigin': '50% 40%',\n            'msTransformOrigin': '50% 40%'\n          };\n          if (color) {\n            styleRules.color = color;\n          }\n          span.textContent = word;\n          for (var cssProp in styleRules) {\n            span.style[cssProp] = styleRules[cssProp];\n          }\n          if (attributes) {\n            for (var attribute in attributes) {\n              span.setAttribute(attribute, attributes[attribute]);\n            }\n          }\n          if (classes) {\n            span.className += classes;\n          }\n          el.appendChild(span);\n        }\n      });\n    };\n\n    /* Help function to updateGrid */\n    var fillGridAt = function fillGridAt(x, y, drawMask, dimension, item) {\n      if (x >= ngx || y >= ngy || x < 0 || y < 0) {\n        return;\n      }\n\n      grid[x][y] = false;\n\n      if (drawMask) {\n        var ctx = elements[0].getContext('2d');\n        ctx.fillRect(x * g, y * g, maskRectWidth, maskRectWidth);\n      }\n\n      if (interactive) {\n        infoGrid[x][y] = { item: item, dimension: dimension };\n      }\n    };\n\n    /* Update the filling information of the given space with occupied points.\n       Draw the mask on the canvas if necessary. */\n    var updateGrid = function updateGrid(gx, gy, gw, gh, info, item) {\n      var occupied = info.occupied;\n      var drawMask = settings.drawMask;\n      var ctx;\n      if (drawMask) {\n        ctx = elements[0].getContext('2d');\n        ctx.save();\n        ctx.fillStyle = settings.maskColor;\n      }\n\n      var dimension;\n      if (interactive) {\n        var bounds = info.bounds;\n        dimension = {\n          x: (gx + bounds[3]) * g,\n          y: (gy + bounds[0]) * g,\n          w: (bounds[1] - bounds[3] + 1) * g,\n          h: (bounds[2] - bounds[0] + 1) * g\n        };\n      }\n\n      var i = occupied.length;\n      while (i--) {\n        var px = gx + occupied[i][0];\n        var py = gy + occupied[i][1];\n\n        if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n          continue;\n        }\n\n        fillGridAt(px, py, drawMask, dimension, item);\n      }\n\n      if (drawMask) {\n        ctx.restore();\n      }\n    };\n\n    /* putWord() processes each item on the list,\n       calculate it's size and determine it's position, and actually\n       put it on the canvas. */\n    var putWord = function putWord(item) {\n      var word, weight, attributes;\n      if (Array.isArray(item)) {\n        word = item[0];\n        weight = item[1];\n      } else {\n        word = item.word;\n        weight = item.weight;\n        attributes = item.attributes;\n      }\n      var rotateDeg = getRotateDeg();\n\n      // get info needed to put the text onto the canvas\n      var info = getTextInfo(word, weight, rotateDeg);\n\n      // not getting the info means we shouldn't be drawing this one.\n      if (!info) {\n        return false;\n      }\n\n      if (exceedTime()) {\n        return false;\n      }\n\n      // If drawOutOfBound is set to false,\n      // skip the loop if we have already know the bounding box of\n      // word is larger than the canvas.\n      if (!settings.drawOutOfBound) {\n        var bounds = info.bounds;\n        if ((bounds[1] - bounds[3] + 1) > ngx ||\n          (bounds[2] - bounds[0] + 1) > ngy) {\n          return false;\n        }\n      }\n\n      // Determine the position to put the text by\n      // start looking for the nearest points\n      var r = maxRadius + 1;\n\n      var tryToPutWordAtPoint = function(gxy) {\n        var gx = Math.floor(gxy[0] - info.gw / 2);\n        var gy = Math.floor(gxy[1] - info.gh / 2);\n        var gw = info.gw;\n        var gh = info.gh;\n\n        // If we cannot fit the text at this position, return false\n        // and go to the next position.\n        if (!canFitText(gx, gy, gw, gh, info.occupied)) {\n          return false;\n        }\n\n        // Actually put the text on the canvas\n        drawText(gx, gy, info, word, weight,\n                 (maxRadius - r), gxy[2], rotateDeg, attributes);\n\n        // Mark the spaces on the grid as filled\n        updateGrid(gx, gy, gw, gh, info, item);\n\n        return {\n          gx: gx,\n          gy: gy,\n          rot: rotateDeg,\n          info: info\n        };\n      };\n\n      while (r--) {\n        var points = getPointsAtRadius(maxRadius - r);\n\n        if (settings.shuffle) {\n          points = [].concat(points);\n          shuffleArray(points);\n        }\n\n        // Try to fit the words by looking at each point.\n        // array.some() will stop and return true\n        // when putWordAtPoint() returns true.\n        for (var i = 0; i < points.length; i++) {\n          var res = tryToPutWordAtPoint(points[i]);\n          if (res) {\n            return res;\n          }\n        }\n\n        // var drawn = points.some(tryToPutWordAtPoint);\n        // if (drawn) {\n        //   // leave putWord() and return true\n        //   return true;\n        // }\n      }\n      // we tried all distances but text won't fit, return null\n      return null;\n    };\n\n    /* Send DOM event to all elements. Will stop sending event and return\n       if the previous one is canceled (for cancelable events). */\n    var sendEvent = function sendEvent(type, cancelable, detail) {\n      if (cancelable) {\n        return !elements.some(function(el) {\n          var evt = document.createEvent('CustomEvent');\n          evt.initCustomEvent(type, true, cancelable, detail || {});\n          return !el.dispatchEvent(evt);\n        }, this);\n      } else {\n        elements.forEach(function(el) {\n          var evt = document.createEvent('CustomEvent');\n          evt.initCustomEvent(type, true, cancelable, detail || {});\n          el.dispatchEvent(evt);\n        }, this);\n      }\n    };\n\n    /* Start drawing on a canvas */\n    var start = function start() {\n      // For dimensions, clearCanvas etc.,\n      // we only care about the first element.\n      var canvas = elements[0];\n\n      if (canvas.getContext) {\n        ngx = Math.ceil(canvas.width / g);\n        ngy = Math.ceil(canvas.height / g);\n      } else {\n        var rect = canvas.getBoundingClientRect();\n        ngx = Math.ceil(rect.width / g);\n        ngy = Math.ceil(rect.height / g);\n      }\n\n      // Sending a wordcloudstart event which cause the previous loop to stop.\n      // Do nothing if the event is canceled.\n      if (!sendEvent('wordcloudstart', true)) {\n        return;\n      }\n\n      // Determine the center of the word cloud\n      center = (settings.origin) ?\n        [settings.origin[0]/g, settings.origin[1]/g] :\n        [ngx / 2, ngy / 2];\n\n      // Maxium radius to look for space\n      maxRadius = Math.floor(Math.sqrt(ngx * ngx + ngy * ngy));\n\n      /* Clear the canvas only if the clearCanvas is set,\n         if not, update the grid to the current canvas state */\n      grid = [];\n\n      var gx, gy, i;\n      if (!canvas.getContext || settings.clearCanvas) {\n        elements.forEach(function(el) {\n          if (el.getContext) {\n            var ctx = el.getContext('2d');\n            ctx.fillStyle = settings.backgroundColor;\n            ctx.clearRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n            ctx.fillRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n          } else {\n            el.textContent = '';\n            el.style.backgroundColor = settings.backgroundColor;\n            el.style.position = 'relative';\n          }\n        });\n\n        /* fill the grid with empty state */\n        gx = ngx;\n        while (gx--) {\n          grid[gx] = [];\n          gy = ngy;\n          while (gy--) {\n            grid[gx][gy] = true;\n          }\n        }\n      } else {\n        /* Determine bgPixel by creating\n           another canvas and fill the specified background color. */\n        var bctx = document.createElement('canvas').getContext('2d');\n\n        bctx.fillStyle = settings.backgroundColor;\n        bctx.fillRect(0, 0, 1, 1);\n        var bgPixel = bctx.getImageData(0, 0, 1, 1).data;\n\n        /* Read back the pixels of the canvas we got to tell which part of the\n           canvas is empty.\n           (no clearCanvas only works with a canvas, not divs) */\n        var imageData =\n          canvas.getContext('2d').getImageData(0, 0, ngx * g, ngy * g).data;\n\n        gx = ngx;\n        var x, y;\n        while (gx--) {\n          grid[gx] = [];\n          gy = ngy;\n          while (gy--) {\n            y = g;\n            singleGridLoop: while (y--) {\n              x = g;\n              while (x--) {\n                i = 4;\n                while (i--) {\n                  if (imageData[((gy * g + y) * ngx * g +\n                                 (gx * g + x)) * 4 + i] !== bgPixel[i]) {\n                    grid[gx][gy] = false;\n                    break singleGridLoop;\n                  }\n                }\n              }\n            }\n            if (grid[gx][gy] !== false) {\n              grid[gx][gy] = true;\n            }\n          }\n        }\n\n        imageData = bctx = bgPixel = undefined;\n      }\n\n      // fill the infoGrid with empty state if we need it\n      if (settings.hover || settings.click) {\n\n        interactive = true;\n\n        /* fill the grid with empty state */\n        gx = ngx + 1;\n        while (gx--) {\n          infoGrid[gx] = [];\n        }\n\n        if (settings.hover) {\n          canvas.addEventListener('mousemove', wordcloudhover);\n        }\n\n        if (settings.click) {\n          canvas.addEventListener('click', wordcloudclick);\n          canvas.addEventListener('touchstart', wordcloudclick);\n          canvas.addEventListener('touchend', function (e) {\n            e.preventDefault();\n          });\n          canvas.style.webkitTapHighlightColor = 'rgba(0, 0, 0, 0)';\n        }\n\n        canvas.addEventListener('wordcloudstart', function stopInteraction() {\n          canvas.removeEventListener('wordcloudstart', stopInteraction);\n\n          canvas.removeEventListener('mousemove', wordcloudhover);\n          canvas.removeEventListener('click', wordcloudclick);\n          hovered = undefined;\n        });\n      }\n\n      i = 0;\n      var loopingFunction, stoppingFunction;\n      var layouting = true;\n      if (!settings.layoutAnimation) {\n        loopingFunction = function (cb) {\n          cb();\n        }\n        stoppingFunction = function () {\n          layouting = false;\n        }\n      }\n      else if (settings.wait !== 0) {\n        loopingFunction = window.setTimeout;\n        stoppingFunction = window.clearTimeout;\n      } else {\n        loopingFunction = window.setImmediate;\n        stoppingFunction = window.clearImmediate;\n      }\n\n      var addEventListener = function addEventListener(type, listener) {\n        elements.forEach(function(el) {\n          el.addEventListener(type, listener);\n        }, this);\n      };\n\n      var removeEventListener = function removeEventListener(type, listener) {\n        elements.forEach(function(el) {\n          el.removeEventListener(type, listener);\n        }, this);\n      };\n\n      var anotherWordCloudStart = function anotherWordCloudStart() {\n        removeEventListener('wordcloudstart', anotherWordCloudStart);\n        stoppingFunction(timer);\n      };\n\n      addEventListener('wordcloudstart', anotherWordCloudStart);\n\n      // At least wait the following code before call the first iteration.\n      var timer = (settings.layoutAnimation ? loopingFunction : setTimeout)(function loop() {\n        if (!layouting) {\n          return;\n        }\n        if (i >= settings.list.length) {\n          stoppingFunction(timer);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n\n          return;\n        }\n        escapeTime = (new Date()).getTime();\n        var drawn = putWord(settings.list[i]);\n        var canceled = !sendEvent('wordclouddrawn', true, {\n          item: settings.list[i], drawn: drawn });\n        if (exceedTime() || canceled) {\n          stoppingFunction(timer);\n          settings.abort();\n          sendEvent('wordcloudabort', false);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          return;\n        }\n        i++;\n        timer = loopingFunction(loop, settings.wait);\n      }, settings.wait);\n    };\n\n    // All set, start the drawing\n    start();\n  };\n\n  WordCloud.isSupported = isSupported;\n  WordCloud.minFontSize = minFontSize;\n\n  export default WordCloud;", "import * as echarts from 'echarts/lib/echarts';\n\nimport './WordCloudSeries';\nimport './WordCloudView';\n\nimport wordCloudLayoutHelper from './layout';\n\nif (!wordCloudLayoutHelper.isSupported) {\n    throw new Error('Sorry your browser not support wordCloud');\n}\n\n// https://github.com/timdream/wordcloud2.js/blob/c236bee60436e048949f9becc4f0f67bd832dc5c/index.js#L233\nfunction updateCanvasMask(maskCanvas) {\n    var ctx = maskCanvas.getContext('2d');\n    var imageData = ctx.getImageData(\n        0, 0, maskCanvas.width, maskCanvas.height);\n    var newImageData = ctx.createImageData(imageData);\n\n    var toneSum = 0;\n    var toneCnt = 0;\n    for (var i = 0; i < imageData.data.length; i += 4) {\n        var alpha = imageData.data[i + 3];\n        if (alpha > 128) {\n            var tone = imageData.data[i]\n                + imageData.data[i + 1]\n                + imageData.data[i + 2];\n            toneSum += tone;\n            ++toneCnt;\n        }\n    }\n    var threshold = toneSum / toneCnt;\n\n    for (var i = 0; i < imageData.data.length; i += 4) {\n        var tone = imageData.data[i]\n            + imageData.data[i + 1]\n            + imageData.data[i + 2];\n        var alpha = imageData.data[i + 3];\n\n        if (alpha < 128 || tone > threshold) {\n            // Area not to draw\n            newImageData.data[i] = 0;\n            newImageData.data[i + 1] = 0;\n            newImageData.data[i + 2] = 0;\n            newImageData.data[i + 3] = 0;\n        }\n        else {\n            // Area to draw\n            // The color must be same with backgroundColor\n            newImageData.data[i] = 255;\n            newImageData.data[i + 1] = 255;\n            newImageData.data[i + 2] = 255;\n            newImageData.data[i + 3] = 255;\n        }\n    }\n\n    ctx.putImageData(newImageData, 0, 0);\n}\n\necharts.registerLayout(function (ecModel, api) {\n    ecModel.eachSeriesByType('wordCloud', function (seriesModel) {\n        var gridRect = echarts.helper.getLayoutRect(\n            seriesModel.getBoxLayoutParams(), {\n                width: api.getWidth(),\n                height: api.getHeight()\n            }\n        );\n        var data = seriesModel.getData();\n\n        var canvas = document.createElement('canvas');\n        canvas.width = gridRect.width;\n        canvas.height = gridRect.height;\n\n        var ctx = canvas.getContext('2d');\n        var maskImage = seriesModel.get('maskImage');\n        if (maskImage) {\n            try {\n                ctx.drawImage(maskImage, 0, 0, canvas.width, canvas.height);\n                updateCanvasMask(canvas);\n            }\n            catch (e) {\n                console.error('Invalid mask image');\n                console.error(e.toString());\n            }\n        }\n\n        var sizeRange = seriesModel.get('sizeRange');\n        var rotationRange = seriesModel.get('rotationRange');\n        var valueExtent = data.getDataExtent('value');\n\n        var DEGREE_TO_RAD = Math.PI / 180;\n        var gridSize = seriesModel.get('gridSize');\n        wordCloudLayoutHelper(canvas, {\n            list: data.mapArray('value', function (value, idx) {\n                var itemModel = data.getItemModel(idx);\n                return [\n                    data.getName(idx),\n                    itemModel.get('textStyle.fontSize', true)\n                        || echarts.number.linearMap(value, valueExtent, sizeRange),\n                    idx\n                ];\n            }).sort(function (a, b) {\n                // Sort from large to small in case there is no more room for more words\n                return b[1] - a[1];\n            }),\n            fontFamily: seriesModel.get('textStyle.fontFamily')\n                || seriesModel.get('emphasis.textStyle.fontFamily')\n                || ecModel.get('textStyle.fontFamily'),\n            fontWeight: seriesModel.get('textStyle.fontWeight')\n                || seriesModel.get('emphasis.textStyle.fontWeight')\n                || ecModel.get('textStyle.fontWeight'),\n\n            gridSize: gridSize,\n\n            ellipticity: gridRect.height / gridRect.width,\n\n            minRotation: rotationRange[0] * DEGREE_TO_RAD,\n            maxRotation: rotationRange[1] * DEGREE_TO_RAD,\n\n            clearCanvas: !maskImage,\n\n            rotateRatio: 1,\n\n            rotationStep: seriesModel.get('rotationStep') * DEGREE_TO_RAD,\n\n            drawOutOfBound: seriesModel.get('drawOutOfBound'),\n\n            layoutAnimation: seriesModel.get('layoutAnimation'),\n\n            shuffle: false,\n\n            shape: seriesModel.get('shape')\n        });\n\n        function onWordCloudDrawn(e) {\n            var item = e.detail.item;\n            if (e.detail.drawn && seriesModel.layoutInstance.ondraw) {\n                e.detail.drawn.gx += gridRect.x / gridSize;\n                e.detail.drawn.gy += gridRect.y / gridSize;\n                seriesModel.layoutInstance.ondraw(\n                    item[0], item[1], item[2], e.detail.drawn\n                );\n            }\n        }\n\n        canvas.addEventListener('wordclouddrawn', onWordCloudDrawn);\n\n        if (seriesModel.layoutInstance) {\n            // Dispose previous\n            seriesModel.layoutInstance.dispose();\n        }\n\n        seriesModel.layoutInstance = {\n            ondraw: null,\n\n            dispose: function () {\n                canvas.removeEventListener('wordclouddrawn', onWordCloudDrawn);\n                // Abort\n                canvas.addEventListener('wordclouddrawn', function (e) {\n                    // Prevent default to cancle the event and stop the loop\n                    e.preventDefault();\n                });\n            }\n        };\n    });\n});\n\necharts.registerPreprocessor(function (option) {\n    var series = (option || {}).series;\n    !echarts.util.isArray(series) && (series = series ? [series] : []);\n\n    var compats = ['shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\n\n    echarts.util.each(series, function (seriesItem) {\n        if (seriesItem && seriesItem.type === 'wordCloud') {\n            var textStyle = seriesItem.textStyle || {};\n\n            compatTextStyle(textStyle.normal);\n            compatTextStyle(textStyle.emphasis);\n        }\n    });\n\n    function compatTextStyle(textStyle) {\n        textStyle && echarts.util.each(compats, function (key) {\n            if (textStyle.hasOwnProperty(key)) {\n                textStyle['text' + echarts.format.capitalFirst(key)] = textStyle[key];\n            }\n        });\n    }\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAiDA,IAAI,CAAC,SAAgBA,QAAgB,CAAC;AAiBtC,IAAI,kBAAkB;;;AChEd,kBAAkB;AAAA,EAEtB,MAAM;AAAA,EAEN,uBAAuB;AAAA,EACvB,mBAAmB,SAAU,OAAO;AAChC,WAAO;AAAA,MACH,MAAM,MAAM,IAAI,OAAO;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,gBAAgB;AAAA,EAEhB,eAAe,WAAY;AACvB,QAAI,SAAS,KAAK;AAClB,WAAO,WAAW,KAAK,IAAI,KAAK,MAAM,OAAO,QAAQ,GAAG,CAAC;AAAA,EAC7D;AAAA,EAEA,gBAAgB,SAAU,QAAQ,SAAS;AACvC,QAAI,aAAqB,eAAO,iBAAiB,OAAO,MAAM;AAAA,MAC1D,iBAAiB,CAAC,OAAO;AAAA,IAC7B,CAAC;AACD,QAAI,OAAO,IAAY,mBAAK,YAAY,IAAI;AAC5C,SAAK,SAAS,OAAO,IAAI;AACzB,WAAO;AAAA,EACX;AAAA,EAGA,eAAe;AAAA,IAEX,WAAW;AAAA,IAGX,OAAO;AAAA,IAEP,MAAM;AAAA,IAEN,KAAK;AAAA,IAEL,OAAO;AAAA,IAEP,QAAQ;AAAA,IAER,WAAW,CAAC,IAAI,EAAE;AAAA,IAElB,eAAe,CAAC,KAAK,EAAE;AAAA,IAEvB,cAAc;AAAA,IAEd,UAAU;AAAA,IAEV,gBAAgB;AAAA,IAEhB,WAAW;AAAA,MACP,YAAY;AAAA,IAChB;AAAA,EACJ;AACJ,CAAC;;;ACxDO,gBAAgB;AAAA,EAEpB,MAAM;AAAA,EAEN,QAAQ,SAAU,aAAa,SAAS,KAAK;AACzC,QAAI,QAAQ,KAAK;AACjB,UAAM,UAAU;AAEhB,QAAI,OAAO,YAAY,QAAQ;AAE/B,QAAI,WAAW,YAAY,IAAI,UAAU;AAEzC,gBAAY,eAAe,SAAS,SAAU,MAAM,MAAM,SAAS,OAAO;AACtE,UAAI,YAAY,KAAK,aAAa,OAAO;AACzC,UAAI,iBAAiB,UAAU,SAAS,WAAW;AAEnD,UAAI,SAAS,IAAY,gBAAQ,KAAK;AAAA,QAClC,OAAe,eAAO,gBAAgB,cAAc;AAAA,QACpD,QAAQ,IAAI,MAAM,KAAK;AAAA,QACvB,QAAQ,IAAI,MAAM,KAAK;AAAA,QACvB,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACpC,IAAI,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,QACpC,UAAU,MAAM;AAAA,MACpB,CAAC;AACD,aAAO,SAAS;AAAA,QACZ,GAAG,MAAM,KAAK;AAAA,QACd,GAAG,MAAM,KAAK,kBAAkB,OAAO;AAAA,QACvC;AAAA,QACA,eAAe;AAAA,QACf,MAAM,KAAK,cAAc,SAAS,OAAO,EAAE;AAAA,QAC3C,UAAU;AAAA,MACd,CAAC;AAED,YAAM,IAAI,MAAM;AAEhB,WAAK,iBAAiB,SAAS,MAAM;AAErC,aAAO,YAAY,UAAU,EAAE,QAAgB,eAAO,gBAAgB,UAAU,SAAS,CAAC,YAAY,WAAW,CAAC,GAAG;AAAA,QACjH,OAAO;AAAA,MACX,CAAC;AACD,aAAO,YAAY,MAAM,EAAE,QAAgB,eAAO,gBAAgB,UAAU,SAAS,CAAC,QAAQ,WAAW,CAAC,GAAG;AAAA,QACzG,OAAO;AAAA,MACX,CAAC;AAED,MAAQ,eAAO;AAAA,QACX;AAAA,QACA,UAAU,IAAI,CAAC,YAAY,OAAO,CAAC;AAAA,QACnC,UAAU,IAAI,CAAC,YAAY,WAAW,CAAC;AAAA,MAC3C;AAEA,aAAO,kBAAkB;AAAA,QACrB,UAAU,YAAY,IAAI,WAAW,IAAI,YAAY,IAAI,CAAC,kBAAkB,UAAU,CAAC,IAAI;AAAA,QAC3F,QAAQ,YAAY,IAAI,CAAC,kBAAkB,QAAQ,CAAC;AAAA,MACxD;AAEA,aAAO,uBAAuB;AAAA,IAClC;AAEA,SAAK,SAAS;AAAA,EAClB;AAAA,EAEA,QAAQ,WAAY;AAChB,SAAK,MAAM,UAAU;AAErB,SAAK,OAAO,eAAe,QAAQ;AAAA,EACvC;AAAA,EAEA,SAAS,WAAY;AACjB,SAAK,OAAO,eAAe,QAAQ;AAAA,EACvC;AACJ,CAAC;;;AC7DD,IAAI,CAAC,OAAO,cAAc;AACxB,SAAO,eAAgB,SAAS,oBAAoB;AAClD,WAAO,OAAO,kBACd,OAAO,sBACP,OAAO,mBACP,OAAO,iBACN,SAAS,sBAAsB;AAC9B,UAAI,CAAC,OAAO,eAAe,CAAC,OAAO,kBAAkB;AACnD,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,CAAC,MAAS;AAC1B,UAAI,UAAU;AAKd,UAAI,iBAAiB,SAASC,gBAAe,UAAU;AACrD,YAAI,KAAK,UAAU;AACnB,kBAAU,KAAK,QAAQ;AACvB,eAAO,YAAY,UAAU,GAAG,SAAS,EAAE,GAAG,GAAG;AAEjD,eAAO;AAAA,MACT;AAEA,aAAO,iBAAiB,WAAW,SAAS,sBAAsB,KAAK;AAGrE,YAAI,OAAO,IAAI,SAAS,YACpB,IAAI,KAAK,OAAO,GAAG,QAAQ,MAAM,MAAM,SACb;AAC5B;AAAA,QACF;AAEA,YAAI,yBAAyB;AAE7B,YAAI,KAAK,SAAS,IAAI,KAAK,OAAO,QAAQ,MAAM,GAAG,EAAE;AACrD,YAAI,CAAC,UAAU,KAAK;AAClB;AAAA,QACF;AAEA,kBAAU,IAAI;AACd,kBAAU,MAAM;AAAA,MAClB,GAAG,IAAI;AAGP,aAAO,iBAAiB,SAAS,iBAAiB,IAAI;AACpD,YAAI,CAAC,UAAU,KAAK;AAClB;AAAA,QACF;AAEA,kBAAU,MAAM;AAAA,MAClB;AAEA,aAAO;AAAA,IACT,EAAG,KAEH,SAAS,qBAAqB,IAAI;AAChC,aAAO,WAAW,IAAI,CAAC;AAAA,IACzB;AAAA,EACF,EAAG;AACL;AAEA,IAAI,CAAC,OAAO,gBAAgB;AAC1B,SAAO,iBAAkB,SAAS,sBAAsB;AACtD,WAAO,OAAO,oBACd,OAAO,wBACP,OAAO,qBACP,OAAO,mBAGP,SAAS,uBAAuB,OAAO;AACrC,aAAO,aAAa,KAAK;AAAA,IAC3B;AAAA,EACF,EAAG;AACL;AAGE,IAAI,cAAe,SAASC,eAAc;AACxC,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,MAAI,CAAC,UAAU,CAAC,OAAO,YAAY;AACjC,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,OAAO,WAAW,IAAI;AAChC,MAAI,CAAC,IAAI,cAAc;AACrB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,IAAI,UAAU;AACjB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,UAAU,MAAM;AACzB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,MAAM,UAAU,MAAM;AACzB,WAAO;AAAA,EACT;AAEA,SAAO;AACT,EAAE;AAIF,IAAI,cAAe,SAAS,iBAAiB;AAC3C,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,MAAI,MAAM,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAG1D,MAAI,OAAO;AAGX,MAAI,UAAU;AAEd,SAAO,MAAM;AACX,QAAI,OAAO,KAAK,SAAS,EAAE,IAAI;AAC/B,QAAK,IAAI,YAAY,QAAQ,EAAE,UAAU,YACpC,IAAI,YAAY,GAAG,EAAE,UAAW,QAAQ;AAC3C,aAAQ,OAAO;AAAA,IACjB;AAEA,eAAW,IAAI,YAAY,QAAQ,EAAE;AACrC,aAAS,IAAI,YAAY,GAAG,EAAE;AAE9B;AAAA,EACF;AAEA,SAAO;AACT,EAAG;AAGH,IAAI,eAAe,SAASC,cAAa,KAAK;AAC5C,WAAS,GAAG,GAAG,IAAI,IAAI,QAAQ,GAC7B,IAAI,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,GAChC,IAAI,IAAI,EAAE,IAAI,IAAI,KAAK,IAAI,IAC3B,IAAI,KAAK,GAAG;AAAA,EAAC;AACf,SAAO;AACT;AAEA,IAAI,YAAY,SAASC,WAAU,UAAU,SAAS;AACpD,MAAI,CAAC,aAAa;AAChB;AAAA,EACF;AAEA,MAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,eAAW,CAAC,QAAQ;AAAA,EACtB;AAEA,WAAS,QAAQ,SAAS,IAAI,GAAG;AAC/B,QAAI,OAAO,OAAO,UAAU;AAC1B,eAAS,KAAK,SAAS,eAAe,EAAE;AACxC,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM;AAAA,MACR;AAAA,IACF,WAAW,CAAC,GAAG,WAAW,CAAC,GAAG,aAAa;AACzC,YAAM;AAAA,IACR;AAAA,EACF,CAAC;AAGD,MAAI,WAAW;AAAA,IACb,MAAM,CAAC;AAAA,IACP,YAAY;AAAA,IAEZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,cAAc;AAAA,IACd,aAAa;AAAA,IACb,iBAAiB;AAAA,IAEjB,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IAER,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc;AAAA,IAEd,iBAAiB;AAAA,IAEjB,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,OAAO,SAAS,OAAO;AAAA,IAAC;AAAA,IAExB,aAAa,CAAE,KAAK,KAAK;AAAA,IACzB,aAAa,KAAK,KAAK;AAAA,IACvB,cAAc;AAAA,IAEd,SAAS;AAAA,IACT,aAAa;AAAA,IAEb,OAAO;AAAA,IACP,aAAa;AAAA,IAEb,SAAS;AAAA,IAET,OAAO;AAAA,IACP,OAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACX,aAAS,OAAO,SAAS;AACvB,UAAI,OAAO,UAAU;AACnB,iBAAS,OAAO,QAAQ;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAGA,MAAI,OAAO,SAAS,iBAAiB,YAAY;AAC/C,QAAI,SAAS,SAAS;AACtB,aAAS,eAAe,SAAS,aAAa,IAAI;AAChD,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AAGA,MAAI,OAAO,SAAS,UAAU,YAAY;AACxC,YAAQ,SAAS;AAAA,WACV;AAAA;AAIH,iBAAS,QAAQ;AACjB;AAAA,WAEG;AACH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,iBAAO,IAAI,KAAK,IAAI,KAAK;AAAA,QAC3B;AACA;AAAA,WAWG;AAAA,WACA;AAIH,iBAAS,QAAQ,SAAS,YAAY,OAAO;AAC3C,cAAI,aAAa,SAAS,IAAI,KAAK,KAAK;AACxC,iBAAO,KAAK,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,UAAU;AAAA,QACxD;AACA;AAAA,WAEG;AAIH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,cAAI,aAAa,SAAS,IAAI,KAAK,KAAK;AACxC,iBAAO,KAAK,KAAK,IAAI,UAAU,IACnB,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,UAAU;AAAA,QAChD;AACA;AAAA,WAEG;AAAA,WACA;AACH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,cAAI,cAAc,QAAQ,KAAK,KAAK,IAAI,MAAM,IAAI,KAAK,KAAK;AAC5D,iBAAO,KAAK,KAAK,IAAI,UAAU,IACnB,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,UAAU;AAAA,QAChD;AACA;AAAA,WAEG;AACH,iBAAS,QAAQ,SAAS,cAAc,OAAO;AAC7C,cAAI,cAAc,QAAQ,UAAU,IAAI,KAAK,KAAK;AAClD,iBAAO,KAAK,KAAK,IAAI,UAAU,IACnB,WAAW,KAAK,IAAI,UAAU;AAAA,QAC5C;AACA;AAAA,WAEG;AACH,iBAAS,QAAQ,SAAS,UAAU,OAAO;AACzC,cAAI,cAAc,QAAQ,UAAU,IAAI,KAAK,KAAK;AAClD,eAAK,QAAQ,UAAU,IAAI,KAAK,KAAK,KAAM,IAAI,KAAK,KAAK,MAAO,GAAG;AACjE,mBAAO,KAAK,KAAK,IAAK,IAAI,KAAK,KAAK,KAAM,UAAU,IACxC,UAAU,KAAK,IAAK,IAAI,KAAK,KAAK,KAAM,UAAU;AAAA,UAChE,OAAO;AACL,mBAAO,KAAK,KAAK,IAAI,UAAU,IACnB,UAAU,KAAK,IAAI,UAAU;AAAA,UAC3C;AAAA,QACF;AACA;AAAA;AAAA,EAEN;AAGA,WAAS,WAAW,KAAK,IAAI,KAAK,MAAM,SAAS,QAAQ,GAAG,CAAC;AAG7D,MAAI,IAAI,SAAS;AACjB,MAAI,gBAAgB,IAAI,SAAS;AAGjC,MAAI,gBAAgB,KAAK,IAAI,SAAS,cAAc,SAAS,WAAW;AACxE,MAAI,cAAc,KAAK,IAAI,SAAS,aAAa,SAAS,WAAW;AACrE,MAAI,eAAe,SAAS;AAG5B,MAAI,MACF,KAAK,KACL,QACA;AAGF,MAAI;AAGJ,MAAI;AACJ,WAAS,iBAAiB,KAAK,KAAK;AAClC,WAAO,UACJ,KAAK,OAAO,IAAI,KAAK,QAAQ,IAAI,OACjC,KAAK,OAAO,IAAI,KAAK,IAAI,QAAQ,IAAI,QACrC,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,QAAQ,IAAI;AAAA,EACpD;AACA,UAAQ,SAAS;AAAA,SACV;AACH,qBAAe,SAAS,qBAAqB;AAC3C,eAAO,iBAAiB,IAAI,EAAE;AAAA,MAChC;AACA;AAAA,SAEG;AACH,qBAAe,SAAS,sBAAsB;AAC5C,eAAO,iBAAiB,IAAI,EAAE;AAAA,MAChC;AACA;AAAA;AAGA,UAAI,OAAO,SAAS,UAAU,YAAY;AACxC,uBAAe,SAAS;AAAA,MAC1B;AACA;AAAA;AAIJ,MAAI,iBAAiB;AACrB,MAAI,OAAO,SAAS,YAAY,YAAY;AAC1C,qBAAiB,SAAS;AAAA,EAC5B;AAGA,MAAI,cAAc;AAClB,MAAI,WAAW,CAAC;AAChB,MAAI;AAEJ,MAAI,iCACJ,SAASC,gCAA+B,KAAK;AAC3C,QAAI,SAAS,IAAI;AACjB,QAAI,OAAO,OAAO,sBAAsB;AACxC,QAAI;AACJ,QAAI;AAEJ,QAAI,IAAI,SAAS;AACf,gBAAU,IAAI,QAAQ,GAAG;AACzB,gBAAU,IAAI,QAAQ,GAAG;AAAA,IAC3B,OAAO;AACL,gBAAU,IAAI;AACd,gBAAU,IAAI;AAAA,IAChB;AACA,QAAI,SAAS,UAAU,KAAK;AAC5B,QAAI,SAAS,UAAU,KAAK;AAE5B,QAAI,IAAI,KAAK,MAAM,UAAW,OAAO,QAAQ,KAAK,SAAU,KAAK,CAAC;AAClE,QAAI,IAAI,KAAK,MAAM,UAAW,OAAO,SAAS,KAAK,UAAW,KAAK,CAAC;AAEpE,WAAO,SAAS,GAAG;AAAA,EACrB;AAEA,MAAI,iBAAiB,SAASC,gBAAe,KAAK;AAChD,QAAI,OAAO,+BAA+B,GAAG;AAE7C,QAAI,YAAY,MAAM;AACpB;AAAA,IACF;AAEA,cAAU;AACV,QAAI,CAAC,MAAM;AACT,eAAS,MAAM,QAAW,QAAW,GAAG;AAExC;AAAA,IACF;AAEA,aAAS,MAAM,KAAK,MAAM,KAAK,WAAW,GAAG;AAAA,EAE/C;AAEA,MAAI,iBAAiB,SAASC,gBAAe,KAAK;AAChD,QAAI,OAAO,+BAA+B,GAAG;AAC7C,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AAEA,aAAS,MAAM,KAAK,MAAM,KAAK,WAAW,GAAG;AAC7C,QAAI,eAAe;AAAA,EACrB;AAGA,MAAI,iBAAiB,CAAC;AACtB,MAAI,oBAAoB,SAASC,mBAAkB,QAAQ;AACzD,QAAI,eAAe,SAAS;AAC1B,aAAO,eAAe;AAAA,IACxB;AAGA,QAAI,IAAI,SAAS;AAGjB,QAAI,IAAI;AACR,QAAI,SAAS,CAAC;AAEd,QAAI,WAAW,GAAG;AAChB,aAAO,KAAK,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,CAAC;AAAA,IACvC;AAEA,WAAO,KAAK;AAEV,UAAI,KAAK;AACT,UAAI,SAAS,UAAU,UAAU;AAC/B,aAAK,SAAS,MAAM,IAAI,IAAI,IAAI,KAAK,EAAE;AAAA,MACzC;AAGA,aAAO,KAAK;AAAA,QACV,OAAO,KAAK,SAAS,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE;AAAA,QACvD,OAAO,KAAK,SAAS,KAAK,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IACrD,SAAS;AAAA,QACX,IAAI,IAAI,IAAI,KAAK;AAAA,MAAE,CAAC;AAAA,IACxB;AAEA,mBAAe,UAAU;AACzB,WAAO;AAAA,EACT;AAGA,MAAI,aAAa,SAASC,cAAa;AACrC,WAAS,SAAS,iBAAiB,KAC/B,IAAI,KAAK,EAAG,QAAQ,IAAI,aAAa,SAAS;AAAA,EACpD;AAGA,MAAI,eAAe,SAASC,gBAAe;AACzC,QAAI,SAAS,gBAAgB,GAAG;AAC9B,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,OAAO,IAAI,SAAS,aAAa;AACxC,aAAO;AAAA,IACT;AAEA,QAAI,kBAAkB,GAAG;AACvB,aAAO;AAAA,IACT;AAEA,WAAO,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,YAAY,IAAI;AAAA,EAClF;AAEA,MAAI,cAAc,SAASC,aAAY,MAAM,QAAQ,WAAW;AAI9D,QAAI,QAAQ;AACZ,QAAI,WAAW,SAAS,aAAa,MAAM;AAC3C,QAAI,YAAY,SAAS,SAAS;AAChC,aAAO;AAAA,IACT;AAKA,QAAI,KAAK;AACT,QAAI,WAAW,aAAa;AAC1B,WAAM,SAAS,uBAAuB;AACpC,YAAIC,MAAK;AACT,eAAOA,MAAK,WAAW,aAAa;AAClC,UAAAA,OAAM;AAAA,QACR;AACA,eAAOA;AAAA,MACT,EAAG;AAAA,IACL;AAEA,QAAI,UAAU,SAAS,cAAc,QAAQ;AAC7C,QAAI,OAAO,QAAQ,WAAW,MAAM,EAAE,oBAAoB,KAAK,CAAC;AAEhE,SAAK,OAAO,SAAS,aAAa,OAC/B,WAAW,IAAI,SAAS,EAAE,IAAI,QAAQ,SAAS;AAGlD,QAAI,KAAK,KAAK,YAAY,IAAI,EAAE,QAAQ;AACxC,QAAI,KAAK,KAAK;AAAA,MAAI,WAAW;AAAA,MACX,KAAK,YAAY,GAAG,EAAE;AAAA,MACtB,KAAK,YAAY,QAAQ,EAAE;AAAA,IAAK,IAAI;AAItD,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,YAAY,KAAK;AACrB,QAAI,MAAM,KAAK,KAAK,WAAW,CAAC;AAChC,QAAI,MAAM,KAAK,KAAK,YAAY,CAAC;AACjC,eAAW,MAAM;AACjB,gBAAY,MAAM;AAMlB,QAAI,kBAAkB,CAAE,KAAK;AAI7B,QAAI,kBAAkB,CAAE,KAAK;AAG7B,QAAI,MAAM,KAAK,MAAM,WAAW,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,IACvC,YAAY,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC;AACnE,QAAI,MAAM,KAAK,MAAM,WAAW,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,IACvC,YAAY,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC;AACnE,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AAEnB,YAAQ,aAAa,SAAS,KAAK;AACnC,YAAQ,aAAa,UAAU,MAAM;AAErC,QAAI,OAAO;AAET,eAAS,KAAK,YAAY,OAAO;AAEjC,WAAK,KAAK;AAAA,IACZ;AAGA,SAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AACzB,SAAK,UAAU,QAAQ,KAAK,GAAG,SAAS,KAAK,CAAC;AAC9C,SAAK,OAAO,CAAE,SAAS;AAIvB,SAAK,OAAO,SAAS,aAAa,OAC/B,WAAW,IAAI,SAAS,EAAE,IAAI,QAAQ,SAAS;AAQlD,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK;AAAA,MAAS;AAAA,MAAM,kBAAkB;AAAA,OACvB,kBAAkB,WAAW,OAAO;AAAA,IAAE;AAGrD,QAAI,YAAY,KAAK,aAAa,GAAG,GAAG,OAAO,MAAM,EAAE;AAEvD,QAAI,WAAW,GAAG;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,OAAO;AAET,WAAK;AAAA,QAAW,kBAAkB;AAAA,QAClB;AAAA,QAAiB,KAAK;AAAA,QAAI,KAAK;AAAA,MAAE;AACjD,WAAK,QAAQ;AAAA,IACf;AAGA,QAAI,WAAW,CAAC;AAChB,QAAI,KAAK,KAAK,IAAI,GAAG;AACrB,QAAI,SAAS,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAChD,WAAO,MAAM;AACX,WAAK;AACL,aAAO,MAAM;AACX,YAAI;AACJ,wBAAgB;AACd,iBAAO,KAAK;AACV,gBAAI;AACJ,mBAAO,KAAK;AACV,kBAAI,YAAY,KAAK,IAAI,KAAK,SACd,KAAK,IAAI,MAAM,IAAI,IAAI;AACrC,yBAAS,KAAK,CAAC,IAAI,EAAE,CAAC;AAEtB,oBAAI,KAAK,OAAO,IAAI;AAClB,yBAAO,KAAK;AAAA,gBACd;AACA,oBAAI,KAAK,OAAO,IAAI;AAClB,yBAAO,KAAK;AAAA,gBACd;AACA,oBAAI,KAAK,OAAO,IAAI;AAClB,yBAAO,KAAK;AAAA,gBACd;AACA,oBAAI,KAAK,OAAO,IAAI;AAClB,yBAAO,KAAK;AAAA,gBACd;AAEA,oBAAI,OAAO;AACT,uBAAK,YAAY;AACjB,uBAAK,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,gBAChD;AACA,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AACA,cAAI,OAAO;AACT,iBAAK,YAAY;AACjB,iBAAK,SAAS,KAAK,GAAG,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG;AAAA,UAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,OAAO;AACT,WAAK,YAAY;AACjB,WAAK;AAAA,QAAS,OAAO,KAAK;AAAA,QACZ,OAAO,KAAK;AAAA,SACX,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,SAC7B,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,MAAC;AAAA,IAC/C;AAGA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,MACA;AAAA,MACA,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAGA,MAAI,aAAa,SAASC,YAAW,IAAI,IAAI,IAAI,IAAI,UAAU;AAG7D,QAAI,IAAI,SAAS;AACjB,WAAO,KAAK;AACV,UAAI,KAAK,KAAK,SAAS,GAAG;AAC1B,UAAI,KAAK,KAAK,SAAS,GAAG;AAE1B,UAAI,MAAM,OAAO,MAAM,OAAO,KAAK,KAAK,KAAK,GAAG;AAC9C,YAAI,CAAC,SAAS,gBAAgB;AAC5B,iBAAO;AAAA,QACT;AACA;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,IAAI,KAAK;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAGA,MAAI,WAAW,SAASC,UAAS,IAAI,IAAI,MAAM,MAAM,QACpB,UAAU,OAAO,WAAW,YAAY;AAEvE,QAAI,WAAW,KAAK;AACpB,QAAI;AACJ,QAAI,cAAc;AAChB,cAAQ,aAAa,MAAM,QAAQ,UAAU,UAAU,KAAK;AAAA,IAC9D,OAAO;AACL,cAAQ,SAAS;AAAA,IACnB;AAEA,QAAI;AACJ,QAAI,gBAAgB;AAClB,gBAAU,eAAe,MAAM,QAAQ,UAAU,UAAU,KAAK;AAAA,IAClE,OAAO;AACL,gBAAU,SAAS;AAAA,IACrB;AAEA,QAAI;AACJ,QAAI,SAAS,KAAK;AAClB,gBAAY;AAAA,MACV,IAAI,KAAK,OAAO,MAAM;AAAA,MACtB,IAAI,KAAK,OAAO,MAAM;AAAA,MACtB,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,MACjC,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,IACnC;AAEA,aAAS,QAAQ,SAAS,IAAI;AAC5B,UAAI,GAAG,YAAY;AACjB,YAAI,MAAM,GAAG,WAAW,IAAI;AAC5B,YAAI,KAAK,KAAK;AAGd,YAAI,KAAK;AACT,YAAI,MAAM,IAAI,IAAI,IAAI,EAAE;AAExB,YAAI,OAAO,SAAS,aAAa,OACrB,WAAW,IAAI,SAAS,EAAE,IAAI,QAAQ,SAAS;AAC3D,YAAI,YAAY;AAIhB,YAAI;AAAA,WAAW,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,WACxB,KAAK,KAAK,KAAK,KAAK,IAAI;AAAA,QAAE;AAEzC,YAAI,cAAc,GAAG;AACnB,cAAI,OAAO,CAAE,SAAS;AAAA,QACxB;AASA,YAAI,eAAe;AACnB,YAAI;AAAA,UAAS;AAAA,UAAM,KAAK,kBAAkB;AAAA,WACtB,KAAK,kBAAkB,WAAW,OAAO;AAAA,QAAE;AAO/D,YAAI,QAAQ;AAAA,MACd,OAAO;AAEL,YAAI,OAAO,SAAS,cAAc,MAAM;AACxC,YAAI,gBAAgB;AACpB,wBAAgB,YAAa,CAAE,YAAY,KAAK,KAAK,MAAO;AAC5D,YAAI,KAAK,OAAO,GAAG;AACjB,2BACE,iBAAkB,KAAK,gBAAgB,IAAK,eAChC,IAAI,KAAK,KAAM;AAAA,QAC/B;AACA,YAAI,aAAa;AAAA,UACf,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ,SAAS,aAAa,MACrB,WAAW,KAAK,KAAM,QAAQ,SAAS;AAAA,UAChD,SAAU,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,kBAAmB;AAAA,UAC1D,QAAS,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,kBAAmB;AAAA,UACzD,SAAS,KAAK,gBAAgB;AAAA,UAC9B,UAAU,KAAK,iBAAiB;AAAA,UAChC,cAAc,WAAW;AAAA,UACzB,cAAc;AAAA,UACd,aAAa;AAAA,UACb,mBAAmB;AAAA,UACnB,eAAe;AAAA,UACf,mBAAmB;AAAA,UACnB,yBAAyB;AAAA,UACzB,qBAAqB;AAAA,QACvB;AACA,YAAI,OAAO;AACT,qBAAW,QAAQ;AAAA,QACrB;AACA,aAAK,cAAc;AACnB,iBAAS,WAAW,YAAY;AAC9B,eAAK,MAAM,WAAW,WAAW;AAAA,QACnC;AACA,YAAI,YAAY;AACd,mBAAS,aAAa,YAAY;AAChC,iBAAK,aAAa,WAAW,WAAW,UAAU;AAAA,UACpD;AAAA,QACF;AACA,YAAI,SAAS;AACX,eAAK,aAAa;AAAA,QACpB;AACA,WAAG,YAAY,IAAI;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MAAI,aAAa,SAASC,YAAW,GAAG,GAAG,UAAU,WAAW,MAAM;AACpE,QAAI,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,IAAI,GAAG;AAC1C;AAAA,IACF;AAEA,SAAK,GAAG,KAAK;AAEb,QAAI,UAAU;AACZ,UAAI,MAAM,SAAS,GAAG,WAAW,IAAI;AACrC,UAAI,SAAS,IAAI,GAAG,IAAI,GAAG,eAAe,aAAa;AAAA,IACzD;AAEA,QAAI,aAAa;AACf,eAAS,GAAG,KAAK,EAAE,MAAY,UAAqB;AAAA,IACtD;AAAA,EACF;AAIA,MAAI,aAAa,SAASC,YAAW,IAAI,IAAI,IAAI,IAAI,MAAM,MAAM;AAC/D,QAAI,WAAW,KAAK;AACpB,QAAI,WAAW,SAAS;AACxB,QAAI;AACJ,QAAI,UAAU;AACZ,YAAM,SAAS,GAAG,WAAW,IAAI;AACjC,UAAI,KAAK;AACT,UAAI,YAAY,SAAS;AAAA,IAC3B;AAEA,QAAI;AACJ,QAAI,aAAa;AACf,UAAI,SAAS,KAAK;AAClB,kBAAY;AAAA,QACV,IAAI,KAAK,OAAO,MAAM;AAAA,QACtB,IAAI,KAAK,OAAO,MAAM;AAAA,QACtB,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,QACjC,IAAI,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,MACnC;AAAA,IACF;AAEA,QAAI,IAAI,SAAS;AACjB,WAAO,KAAK;AACV,UAAI,KAAK,KAAK,SAAS,GAAG;AAC1B,UAAI,KAAK,KAAK,SAAS,GAAG;AAE1B,UAAI,MAAM,OAAO,MAAM,OAAO,KAAK,KAAK,KAAK,GAAG;AAC9C;AAAA,MACF;AAEA,iBAAW,IAAI,IAAI,UAAU,WAAW,IAAI;AAAA,IAC9C;AAEA,QAAI,UAAU;AACZ,UAAI,QAAQ;AAAA,IACd;AAAA,EACF;AAKA,MAAI,UAAU,SAASC,SAAQ,MAAM;AACnC,QAAI,MAAM,QAAQ;AAClB,QAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAO,KAAK;AACZ,eAAS,KAAK;AAAA,IAChB,OAAO;AACL,aAAO,KAAK;AACZ,eAAS,KAAK;AACd,mBAAa,KAAK;AAAA,IACpB;AACA,QAAI,YAAY,aAAa;AAG7B,QAAI,OAAO,YAAY,MAAM,QAAQ,SAAS;AAG9C,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AAEA,QAAI,WAAW,GAAG;AAChB,aAAO;AAAA,IACT;AAKA,QAAI,CAAC,SAAS,gBAAgB;AAC5B,UAAI,SAAS,KAAK;AAClB,UAAK,OAAO,KAAK,OAAO,KAAK,IAAK,OAC/B,OAAO,KAAK,OAAO,KAAK,IAAK,KAAK;AACnC,eAAO;AAAA,MACT;AAAA,IACF;AAIA,QAAI,IAAI,YAAY;AAEpB,QAAI,sBAAsB,SAAS,KAAK;AACtC,UAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,KAAK,CAAC;AACxC,UAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,KAAK,CAAC;AACxC,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AAId,UAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,GAAG;AAC9C,eAAO;AAAA,MACT;AAGA;AAAA,QAAS;AAAA,QAAI;AAAA,QAAI;AAAA,QAAM;AAAA,QAAM;AAAA,QACnB,YAAY;AAAA,QAAI,IAAI;AAAA,QAAI;AAAA,QAAW;AAAA,MAAU;AAGvD,iBAAW,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI;AAErC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,MACF;AAAA,IACF;AAEA,WAAO,KAAK;AACV,UAAI,SAAS,kBAAkB,YAAY,CAAC;AAE5C,UAAI,SAAS,SAAS;AACpB,iBAAS,CAAC,EAAE,OAAO,MAAM;AACzB,qBAAa,MAAM;AAAA,MACrB;AAKA,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,YAAI,MAAM,oBAAoB,OAAO,EAAE;AACvC,YAAI,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IAOF;AAEA,WAAO;AAAA,EACT;AAIA,MAAI,YAAY,SAASC,WAAU,MAAM,YAAY,QAAQ;AAC3D,QAAI,YAAY;AACd,aAAO,CAAC,SAAS,KAAK,SAAS,IAAI;AACjC,YAAI,MAAM,SAAS,YAAY,aAAa;AAC5C,YAAI,gBAAgB,MAAM,MAAM,YAAY,UAAU,CAAC,CAAC;AACxD,eAAO,CAAC,GAAG,cAAc,GAAG;AAAA,MAC9B,GAAG,IAAI;AAAA,IACT,OAAO;AACL,eAAS,QAAQ,SAAS,IAAI;AAC5B,YAAI,MAAM,SAAS,YAAY,aAAa;AAC5C,YAAI,gBAAgB,MAAM,MAAM,YAAY,UAAU,CAAC,CAAC;AACxD,WAAG,cAAc,GAAG;AAAA,MACtB,GAAG,IAAI;AAAA,IACT;AAAA,EACF;AAGA,MAAI,QAAQ,SAASC,SAAQ;AAG3B,QAAI,SAAS,SAAS;AAEtB,QAAI,OAAO,YAAY;AACrB,YAAM,KAAK,KAAK,OAAO,QAAQ,CAAC;AAChC,YAAM,KAAK,KAAK,OAAO,SAAS,CAAC;AAAA,IACnC,OAAO;AACL,UAAI,OAAO,OAAO,sBAAsB;AACxC,YAAM,KAAK,KAAK,KAAK,QAAQ,CAAC;AAC9B,YAAM,KAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IACjC;AAIA,QAAI,CAAC,UAAU,kBAAkB,IAAI,GAAG;AACtC;AAAA,IACF;AAGA,aAAU,SAAS,SACjB,CAAC,SAAS,OAAO,KAAG,GAAG,SAAS,OAAO,KAAG,CAAC,IAC3C,CAAC,MAAM,GAAG,MAAM,CAAC;AAGnB,gBAAY,KAAK,MAAM,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG,CAAC;AAIvD,WAAO,CAAC;AAER,QAAI,IAAI,IAAI;AACZ,QAAI,CAAC,OAAO,cAAc,SAAS,aAAa;AAC9C,eAAS,QAAQ,SAAS,IAAI;AAC5B,YAAI,GAAG,YAAY;AACjB,cAAI,MAAM,GAAG,WAAW,IAAI;AAC5B,cAAI,YAAY,SAAS;AACzB,cAAI,UAAU,GAAG,GAAG,OAAO,IAAI,IAAI,OAAO,IAAI,EAAE;AAChD,cAAI,SAAS,GAAG,GAAG,OAAO,IAAI,IAAI,OAAO,IAAI,EAAE;AAAA,QACjD,OAAO;AACL,aAAG,cAAc;AACjB,aAAG,MAAM,kBAAkB,SAAS;AACpC,aAAG,MAAM,WAAW;AAAA,QACtB;AAAA,MACF,CAAC;AAGD,WAAK;AACL,aAAO,MAAM;AACX,aAAK,MAAM,CAAC;AACZ,aAAK;AACL,eAAO,MAAM;AACX,eAAK,IAAI,MAAM;AAAA,QACjB;AAAA,MACF;AAAA,IACF,OAAO;AAGL,UAAI,OAAO,SAAS,cAAc,QAAQ,EAAE,WAAW,IAAI;AAE3D,WAAK,YAAY,SAAS;AAC1B,WAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,UAAI,UAAU,KAAK,aAAa,GAAG,GAAG,GAAG,CAAC,EAAE;AAK5C,UAAI,YACF,OAAO,WAAW,IAAI,EAAE,aAAa,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC,EAAE;AAE/D,WAAK;AACL,UAAI,GAAG;AACP,aAAO,MAAM;AACX,aAAK,MAAM,CAAC;AACZ,aAAK;AACL,eAAO,MAAM;AACX,cAAI;AACJ;AAAgB,mBAAO,KAAK;AAC1B,kBAAI;AACJ,qBAAO,KAAK;AACV,oBAAI;AACJ,uBAAO,KAAK;AACV,sBAAI,YAAY,KAAK,IAAI,KAAK,MAAM,KACpB,KAAK,IAAI,MAAM,IAAI,OAAO,QAAQ,IAAI;AACpD,yBAAK,IAAI,MAAM;AACf,0BAAM;AAAA,kBACR;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,cAAI,KAAK,IAAI,QAAQ,OAAO;AAC1B,iBAAK,IAAI,MAAM;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAEA,kBAAY,OAAO,UAAU;AAAA,IAC/B;AAGA,QAAI,SAAS,SAAS,SAAS,OAAO;AAEpC,oBAAc;AAGd,WAAK,MAAM;AACX,aAAO,MAAM;AACX,iBAAS,MAAM,CAAC;AAAA,MAClB;AAEA,UAAI,SAAS,OAAO;AAClB,eAAO,iBAAiB,aAAa,cAAc;AAAA,MACrD;AAEA,UAAI,SAAS,OAAO;AAClB,eAAO,iBAAiB,SAAS,cAAc;AAC/C,eAAO,iBAAiB,cAAc,cAAc;AACpD,eAAO,iBAAiB,YAAY,SAAU,GAAG;AAC/C,YAAE,eAAe;AAAA,QACnB,CAAC;AACD,eAAO,MAAM,0BAA0B;AAAA,MACzC;AAEA,aAAO,iBAAiB,kBAAkB,SAAS,kBAAkB;AACnE,eAAO,oBAAoB,kBAAkB,eAAe;AAE5D,eAAO,oBAAoB,aAAa,cAAc;AACtD,eAAO,oBAAoB,SAAS,cAAc;AAClD,kBAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,QAAI;AACJ,QAAI,iBAAiB;AACrB,QAAI,YAAY;AAChB,QAAI,CAAC,SAAS,iBAAiB;AAC7B,wBAAkB,SAAU,IAAI;AAC9B,WAAG;AAAA,MACL;AACA,yBAAmB,WAAY;AAC7B,oBAAY;AAAA,MACd;AAAA,IACF,WACS,SAAS,SAAS,GAAG;AAC5B,wBAAkB,OAAO;AACzB,yBAAmB,OAAO;AAAA,IAC5B,OAAO;AACL,wBAAkB,OAAO;AACzB,yBAAmB,OAAO;AAAA,IAC5B;AAEA,QAAI,mBAAmB,SAASC,kBAAiB,MAAM,UAAU;AAC/D,eAAS,QAAQ,SAAS,IAAI;AAC5B,WAAG,iBAAiB,MAAM,QAAQ;AAAA,MACpC,GAAG,IAAI;AAAA,IACT;AAEA,QAAI,sBAAsB,SAASC,qBAAoB,MAAM,UAAU;AACrE,eAAS,QAAQ,SAAS,IAAI;AAC5B,WAAG,oBAAoB,MAAM,QAAQ;AAAA,MACvC,GAAG,IAAI;AAAA,IACT;AAEA,QAAI,wBAAwB,SAASC,yBAAwB;AAC3D,0BAAoB,kBAAkBA,sBAAqB;AAC3D,uBAAiB,KAAK;AAAA,IACxB;AAEA,qBAAiB,kBAAkB,qBAAqB;AAGxD,QAAI,SAAS,SAAS,kBAAkB,kBAAkB,YAAY,SAAS,OAAO;AACpF,UAAI,CAAC,WAAW;AACd;AAAA,MACF;AACA,UAAI,KAAK,SAAS,KAAK,QAAQ;AAC7B,yBAAiB,KAAK;AACtB,kBAAU,iBAAiB,KAAK;AAChC,4BAAoB,kBAAkB,qBAAqB;AAE3D;AAAA,MACF;AACA,mBAAc,IAAI,KAAK,EAAG,QAAQ;AAClC,UAAI,QAAQ,QAAQ,SAAS,KAAK,EAAE;AACpC,UAAI,WAAW,CAAC,UAAU,kBAAkB,MAAM;AAAA,QAChD,MAAM,SAAS,KAAK;AAAA,QAAI;AAAA,MAAa,CAAC;AACxC,UAAI,WAAW,KAAK,UAAU;AAC5B,yBAAiB,KAAK;AACtB,iBAAS,MAAM;AACf,kBAAU,kBAAkB,KAAK;AACjC,kBAAU,iBAAiB,KAAK;AAChC,4BAAoB,kBAAkB,qBAAqB;AAC3D;AAAA,MACF;AACA;AACA,cAAQ,gBAAgB,MAAM,SAAS,IAAI;AAAA,IAC7C,GAAG,SAAS,IAAI;AAAA,EAClB;AAGA,QAAM;AACR;AAEA,UAAU,cAAc;AACxB,UAAU,cAAc;AAExB,IAAO,iBAAQ;;;ACjpCjB,IAAI,CAAC,eAAsB,aAAa;AACpC,QAAM,IAAI,MAAM,0CAA0C;AAC9D;AAGA,SAAS,iBAAiB,YAAY;AAClC,MAAI,MAAM,WAAW,WAAW,IAAI;AACpC,MAAI,YAAY,IAAI;AAAA,IAChB;AAAA,IAAG;AAAA,IAAG,WAAW;AAAA,IAAO,WAAW;AAAA,EAAM;AAC7C,MAAI,eAAe,IAAI,gBAAgB,SAAS;AAEhD,MAAI,UAAU;AACd,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,QAAQ,KAAK,GAAG;AAC/C,QAAI,QAAQ,UAAU,KAAK,IAAI;AAC/B,QAAI,QAAQ,KAAK;AACb,UAAI,OAAO,UAAU,KAAK,KACpB,UAAU,KAAK,IAAI,KACnB,UAAU,KAAK,IAAI;AACzB,iBAAW;AACX,QAAE;AAAA,IACN;AAAA,EACJ;AACA,MAAI,YAAY,UAAU;AAE1B,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK,QAAQ,KAAK,GAAG;AAC/C,QAAI,OAAO,UAAU,KAAK,KACpB,UAAU,KAAK,IAAI,KACnB,UAAU,KAAK,IAAI;AACzB,QAAI,QAAQ,UAAU,KAAK,IAAI;AAE/B,QAAI,QAAQ,OAAO,OAAO,WAAW;AAEjC,mBAAa,KAAK,KAAK;AACvB,mBAAa,KAAK,IAAI,KAAK;AAC3B,mBAAa,KAAK,IAAI,KAAK;AAC3B,mBAAa,KAAK,IAAI,KAAK;AAAA,IAC/B,OACK;AAGD,mBAAa,KAAK,KAAK;AACvB,mBAAa,KAAK,IAAI,KAAK;AAC3B,mBAAa,KAAK,IAAI,KAAK;AAC3B,mBAAa,KAAK,IAAI,KAAK;AAAA,IAC/B;AAAA,EACJ;AAEA,MAAI,aAAa,cAAc,GAAG,CAAC;AACvC;AAEQ,eAAe,SAAU,SAAS,KAAK;AAC3C,UAAQ,iBAAiB,aAAa,SAAU,aAAa;AACzD,QAAI,WAAmB,eAAO;AAAA,MAC1B,YAAY,mBAAmB;AAAA,MAAG;AAAA,QAC9B,OAAO,IAAI,SAAS;AAAA,QACpB,QAAQ,IAAI,UAAU;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,OAAO,YAAY,QAAQ;AAE/B,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,WAAO,QAAQ,SAAS;AACxB,WAAO,SAAS,SAAS;AAEzB,QAAI,MAAM,OAAO,WAAW,IAAI;AAChC,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,WAAW;AACX,UAAI;AACA,YAAI,UAAU,WAAW,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC1D,yBAAiB,MAAM;AAAA,MAC3B,SACO,GAAP;AACI,gBAAQ,MAAM,oBAAoB;AAClC,gBAAQ,MAAM,EAAE,SAAS,CAAC;AAAA,MAC9B;AAAA,IACJ;AAEA,QAAI,YAAY,YAAY,IAAI,WAAW;AAC3C,QAAI,gBAAgB,YAAY,IAAI,eAAe;AACnD,QAAI,cAAc,KAAK,cAAc,OAAO;AAE5C,QAAI,gBAAgB,KAAK,KAAK;AAC9B,QAAI,WAAW,YAAY,IAAI,UAAU;AACzC,mBAAsB,QAAQ;AAAA,MAC1B,MAAM,KAAK,SAAS,SAAS,SAAU,OAAO,KAAK;AAC/C,YAAI,YAAY,KAAK,aAAa,GAAG;AACrC,eAAO;AAAA,UACH,KAAK,QAAQ,GAAG;AAAA,UAChB,UAAU,IAAI,sBAAsB,IAAI,KACzB,eAAO,UAAU,OAAO,aAAa,SAAS;AAAA,UAC7D;AAAA,QACJ;AAAA,MACJ,CAAC,EAAE,KAAK,SAAU,GAAG,GAAG;AAEpB,eAAO,EAAE,KAAK,EAAE;AAAA,MACpB,CAAC;AAAA,MACD,YAAY,YAAY,IAAI,sBAAsB,KAC3C,YAAY,IAAI,+BAA+B,KAC/C,QAAQ,IAAI,sBAAsB;AAAA,MACzC,YAAY,YAAY,IAAI,sBAAsB,KAC3C,YAAY,IAAI,+BAA+B,KAC/C,QAAQ,IAAI,sBAAsB;AAAA,MAEzC;AAAA,MAEA,aAAa,SAAS,SAAS,SAAS;AAAA,MAExC,aAAa,cAAc,KAAK;AAAA,MAChC,aAAa,cAAc,KAAK;AAAA,MAEhC,aAAa,CAAC;AAAA,MAEd,aAAa;AAAA,MAEb,cAAc,YAAY,IAAI,cAAc,IAAI;AAAA,MAEhD,gBAAgB,YAAY,IAAI,gBAAgB;AAAA,MAEhD,iBAAiB,YAAY,IAAI,iBAAiB;AAAA,MAElD,SAAS;AAAA,MAET,OAAO,YAAY,IAAI,OAAO;AAAA,IAClC,CAAC;AAED,aAAS,iBAAiB,GAAG;AACzB,UAAI,OAAO,EAAE,OAAO;AACpB,UAAI,EAAE,OAAO,SAAS,YAAY,eAAe,QAAQ;AACrD,UAAE,OAAO,MAAM,MAAM,SAAS,IAAI;AAClC,UAAE,OAAO,MAAM,MAAM,SAAS,IAAI;AAClC,oBAAY,eAAe;AAAA,UACvB,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,KAAK;AAAA,UAAI,EAAE,OAAO;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO,iBAAiB,kBAAkB,gBAAgB;AAE1D,QAAI,YAAY,gBAAgB;AAE5B,kBAAY,eAAe,QAAQ;AAAA,IACvC;AAEA,gBAAY,iBAAiB;AAAA,MACzB,QAAQ;AAAA,MAER,SAAS,WAAY;AACjB,eAAO,oBAAoB,kBAAkB,gBAAgB;AAE7D,eAAO,iBAAiB,kBAAkB,SAAU,GAAG;AAEnD,YAAE,eAAe;AAAA,QACrB,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,CAAC;AACL,CAAC;AAEO,qBAAqB,SAAU,QAAQ;AAC3C,MAAI,UAAU,UAAU,CAAC,GAAG;AAC5B,GAAS,aAAK,QAAQ,MAAM,MAAM,SAAS,SAAS,CAAC,MAAM,IAAI,CAAC;AAEhE,MAAI,UAAU,CAAC,eAAe,cAAc,iBAAiB,eAAe;AAE5E,EAAQ,aAAK,KAAK,QAAQ,SAAU,YAAY;AAC5C,QAAI,cAAc,WAAW,SAAS,aAAa;AAC/C,UAAI,YAAY,WAAW,aAAa,CAAC;AAEzC,sBAAgB,UAAU,MAAM;AAChC,sBAAgB,UAAU,QAAQ;AAAA,IACtC;AAAA,EACJ,CAAC;AAED,WAAS,gBAAgB,WAAW;AAChC,iBAAqB,aAAK,KAAK,SAAS,SAAU,KAAK;AACnD,UAAI,UAAU,eAAe,GAAG,GAAG;AAC/B,kBAAU,SAAiB,eAAO,aAAa,GAAG,KAAK,UAAU;AAAA,MACrE;AAAA,IACJ,CAAC;AAAA,EACL;AACJ,CAAC;", "names": ["install", "setZeroTimeout", "isSupported", "shuffle<PERSON><PERSON><PERSON>", "WordCloud", "getInfoGridFromMouseTouchEvent", "wordcloudhover", "wordcloudclick", "getPointsAtRadius", "exceedTime", "getRotateDeg", "getTextInfo", "mu", "canFitText", "drawText", "fillGridAt", "updateGrid", "putWord", "sendEvent", "start", "addEventListener", "removeEventListener", "anotherWordCloudStart"]}