<template>
    <div class="system-edit-dept-container">
        <el-dialog title="转移项目设备" v-model="isShowDialog" width="769px">
            <el-form ref="formRef" :model="ruleForm" :rules="rules" size="default" label-width="90px">
                <el-row :gutter="35">

                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="源项目" prop="name">
                            <el-input v-model="ruleForm.name"  :disabled="true"></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="目标项目" prop="targetId">
                            <el-select v-model="targetProject" placeholder="请选择目标项目" filterable clearable value-key="id" @change="updateTargetProject" style="width:100%;">
                                <el-option v-for="item in projectData" :key="item.id" :label="item.name" :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" size="default">确认转移</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';
import api from '/@/api/project';
import { ElMessageBox, ElMessage } from 'element-plus';

interface RuleFormState {
    id: string;
    name: string;
    targetId: string;
    targetName: string;
}
interface AllProjectList {
    id: string;
    name: string;
}
interface ProjectSate {
    isShowDialog: boolean;
    ruleForm: RuleFormState;
    projectData: AllProjectList[];
    targetProject?: AllProjectList;
    rules: object;
}

const baseForm: RuleFormState = {
    id: '',
    name: '',
    targetId: '',
    targetName: ''
};

export default defineComponent({
    name: 'TransferInstance',
    setup(prop, { emit }) {
        const formRef = ref<HTMLElement | null>(null);
        const state = reactive<ProjectSate>({
            isShowDialog: false,
            ruleForm: {
                ...baseForm,
            },
            projectData: [], // 项目数据
            targetProject: {
                id: '',
                name: ''
            },
            rules: {
                name: [{ required: true, message: '源项目不能为空', trigger: 'blur' }],
                targetId: [{ required: true, message: '目标项目不能为空', trigger: 'blur' }]
            },
        });

        // 打开弹窗
        const openDialog = (row: RuleFormState) => {
            resetForm();
            if (row && typeof row === 'object') {
                state.ruleForm = row;
            }
            api.project.getOptionList({ rejectId: state.ruleForm.id }).then((res: any) => {
				state.projectData = res.project || [];
			});
            state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };
        const updateTargetProject = () => {
            if (state.targetProject != null) {
                state.ruleForm.targetId = state.targetProject.id;
                state.ruleForm.targetName = state.targetProject.name;
            } else {
                state.ruleForm.targetId = '';
                state.ruleForm.targetName = '';
            }
        };
        // 确认转移
        const onSubmit = () => {
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.validate((valid: boolean) => {
                if (valid) {
                    ElMessageBox.confirm(`此操作将把项目（${state.ruleForm.name}）的所有设备转移到项目（${state.ruleForm.targetName}）,是否继续？`, '提示', {
                        confirmButtonText: '确认转移',
                        cancelButtonText: '取消',
                        type: 'warning',
                    }).then(() => {
                        api.project.transfer(state.ruleForm).then(() => {
                            ElMessage.success('项目设备转移成功');
                            closeDialog(); // 关闭弹窗
                            emit('getProjectList');
                        });
                    }).catch(() => {

                    });
                }
            });
        };
        const resetForm = () => {
            state.ruleForm = {
                ...baseForm,
            };
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.resetFields();
        };
        return {
            updateTargetProject,
            openDialog,
            closeDialog,
            onCancel,
            onSubmit,
            formRef,
            ...toRefs(state),
        };
    },
});
</script>
