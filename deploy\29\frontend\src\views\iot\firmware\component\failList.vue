<template>
    <div class="system-edit-dept-container">
        <el-dialog title="失败设备列表" v-model="isShowDialog" width="769px">
            <el-table ref="tableRef" :border="true" v-loading="tableData.loading" :data="tableData.data" width="100%">
                <el-table-column prop="name" label="设备名称"></el-table-column>
                <el-table-column prop="imei" label="IMEI" min-width="120"></el-table-column>
                <el-table-column prop="manufactor" label="厂家"  align="center"></el-table-column>
                <el-table-column prop="model" label="型号" align="center"></el-table-column>
                <el-table-column prop="tac" label="tac码" align="center"></el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="getFailInstanceList" />

            <template #footer>
                <span class="dialog-footer">
                    <el-button type="success" size="default" @click="onReRelease" v-if="tableData.total>0 && tableData.canReRelease">重新升级失败设备</el-button>
                    <el-button @click="onCancel" size="default">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
  
<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import api from '/@/api/firmware';

interface TableDataRow {
    id: string;
    name: string;
    imei: string;
    manufactor: string;
    model: string;
    tac: string;
}

interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        total: number;
        loading: boolean;
        canReRelease: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            logId: string;
        }
    },
    isShowDialog: boolean;
}

export default defineComponent({
    name: 'failList',
    setup(prop, { emit }) {
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                total: 0,
                loading: false,
                canReRelease: false,
                param: {
                    pageNum: 1,
                    pageSize: 10,
                    logId: ''
                },
            },
            isShowDialog: false
        });

        const getFailInstanceList = () => {
            state.tableData.loading = true;
            api.firmware.getFailList(state.tableData.param).then((res: any) => {
                state.tableData.data = res.failLog;
                state.tableData.total = res.total;
                state.tableData.canReRelease = res.canReRelease == 1
            }).finally(() => (state.tableData.loading = false));
        };

        // 打开弹窗
        const openDialog = (row: any) => {
            if (row && typeof row === 'object') {
                state.tableData.param.logId = row.id;
            }
            getFailInstanceList();
            state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        //重新升级失败设备
        const onReRelease = () => {
            ElMessageBox.confirm(`此操作将重新升级列表中的设备, 是否继续?`, '提示', {
                confirmButtonText: '重新升级',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.firmware.reRelease(state.tableData.param.logId).then(() => {
                    ElMessage.success('升级完成');
                    state.isShowDialog = false;
                    emit('getUpgradeList');
                });
            }).catch(() => {
                //ElMessage.success('发布失败');
            });
        }
        // 取消
        const onCancel = () => {
            closeDialog();
        };

        return {
            getFailInstanceList,
            openDialog,
            closeDialog,
            onCancel,
            onReRelease,
            ...toRefs(state),
        };
    },
});
</script>
  