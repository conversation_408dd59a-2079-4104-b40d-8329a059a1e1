/* 深色模式样式
------------------------------- */
[data-theme='dark'] {
	// 变量(自定义时，只需修改这里的值)
	--next-bg-main               : #1f1f1f;
	--next-color-white           : #ffffff;
	--next-color-disabled        : #191919;
	--next-color-bar             : #dadada;
	--next-color-primary         : #303030;
	--next-border-color          : #424242;
	--next-border-black          : #333333;
	--next-border-columns        : #2a2a2a;
	--next-color-seting          : #110d0d;
	--next-text-color-regular    : #9b9da1;
	--next-text-color-placeholder: #7a7a7a;
	--next-color-hover           : #3c3c3c;
	--next-color-hover-rgba      : #000000;


	// root
	--next-bg-main-color         : var(--next-bg-main) !important;
	--next-bg-topBar             : var(--next-color-disabled) !important;
	--next-bg-topBarColor        : var(--next-color-bar) !important;
	--next-bg-menuBar            : var(--next-color-disabled) !important;
	--next-bg-menuBarColor       : var(--next-color-bar) !important;
	--next-bg-columnsMenuBar     : var(--next-color-disabled) !important;
	--next-bg-columnsMenuBarColor: var(--next-color-bar) !important;
	--next-border-color-light    : var(--next-border-black) !important;
	--next-color-primary-lighter : var(--next-color-primary) !important;
	--next-bg-color              : var(--next-color-primary) !important;
	--next-color-dark-hover      : var(--next-color-hover) !important;
	--next-color-menu-hover      : var(--next-color-hover-rgba) !important;
	--next-color-menu-hover-blue : var(--next-color-hover-rgba) !important;
	--next-color-menu-text-blue: #1967D2;
	// --next-color-menu-text-blue  : var(--next-color-hover-rgba) !important;
	--next-color-user-hover      : var(--next-color-hover-rgba) !important;
	--next-color-seting-main     : var(--next-color-seting) !important;
	--next-color-seting-aside    : var(--next-color-hover) !important;
	--next-color-seting-header   : var(--next-color-primary) !important;
	--next-bg-menuBar-black      : var(--next-color-white) !important;
	--next-bg-menuBar-light      : var(--next-bg-main) !important;

	// element plus
	--el-color-white             : var(--next-color-disabled) !important;
	--el-text-color-primary      : var(--next-color-bar) !important;
	--el-border-color-base       : var(--next-border-black) !important;
	--el-border-color-light      : var(--next-border-black) !important;
	--el-text-color-regular      : var(--next-text-color-regular) !important;
	--el-bg-color                : var(--next-color-hover-rgba) !important;
	--el-color-success-lighter   : var(--next-color-primary) !important;
	--el-color-warning-lighter   : var(--next-color-primary) !important;
	--el-color-danger-lighter    : var(--next-color-primary) !important;
	--el-color-primary-lighter   : var(--next-color-primary) !important;
	--el-color-primary-light-9   : var(--next-color-hover) !important;
	--el-text-color-disabled-base: var(--el-color-primary) !important;
	--el-text-color-disabled     : var(--next-text-color-placeholder) !important;
	--el-border-color-lighter    : var(--next-border-black) !important;
	--el-text-color-placeholder  : var(--next-text-color-placeholder) !important;
	--el-disabled-bg-color       : var(--next-color-disabled) !important;
	--el-fill-base               : var(--next-color-white) !important;

	// button
	.el-button {
		&:hover {
			border-color: var(--next-border-color) !important;
		}
	}

	// 高亮时
	.el-menu-item.is-active {
		color: var(--next-color-menu-text-blue) !important;
	}
	.el-button--primary,
	.el-button--info,
	.el-button--danger,
	.el-button--success,
	.el-button--warning {
		--el-button-text-color         : var(--next-color-white) !important;
		--el-button-hover-text-color   : var(--next-color-white) !important;
		--el-button-disabled-text-color: var(--next-color-white) !important;

		&:hover {
			border-color: var(--el-button-hover-border-color, var(--el-button-hover-bg-color)) !important;
		}
	}

	.el-button--text,
	.link-type,
	.link-type:focus {
		color: var(--next-color-white) !important;

		&:hover {
			border-color: var(--el-color-white) !important;
		}
	}

	// drawer
	.el-divider__text {
		background-color: var(--el-color-white) !important;
	}

	.el-drawer {
		border-left: 1px solid var(--next-border-color-light) !important;
	}

	// tabs
	.el-tabs--border-card {
		background-color: var(--el-color-white) !important;
		border-color    : var(--next-border-color-light) !important;
	}

	.el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
		background  : var(--next-color-primary-lighter);
		border-color: var(--next-border-color-light) !important;
	}

	.el-table__header,
	.el-table__body {
		border-collapse: collapse !important;
	}

	.el-divider--horizontal {
		border-color: var(--next-border-color-light) !important;
	}

	.el-loading-mask {
		background: var(--next-color-primary-lighter) !important;
	}

	// 
	.el-drawer.rtl.open {
		background: var(--el-color-white) !important;
	}

	// 
	.box-content {
		border-color: var(--next-border-color-light) !important;
	}

	.el-cascader__tags .el-tag--info {
		background: var(--next-border-color-light) !important;
	}

	input:-webkit-autofill {
		-webkit-box-shadow: 0 0 0 1000px var(--next-border-color-light) inset !important;
	}

	// 组态管理-组态设计
	.page-wrapper>iframe {
		background: var(--next-color-primary-lighter) !important;
	}

	// alert / notice-bar
	.home-card-item {
		border: 1px solid var(--next-border-color-light) !important;
	}

	.el-card {
		background-color: var(--el-color-white) !important;
		color           : var(--el-text-color-primary) !important;
		border          : 1px solid var(--next-border-color-light) !important;
	}

	.el-button.is-text:not(.is-disabled):focus,
	.el-button.is-text:not(.is-disabled):hover {
		background-color: transparent;
	}

	.el-scrollbar__view {
		background-color: var(--el-color-white) !important;
	}

	.pagination-container {
		background-color: var(--el-color-white) !important;
	}

	// .el-tag {
	// 	background-color: var(--el-color-white) !important;
	// }
	.el-collapse {
		--el-collapse-header-bg-color: var(--el-color-white) !important;
	}

	// .el-switch__core {
	// 	background-color: var(--el-text-color-primary) !important;
	// }
	.el-select-dropdown__item.selected {
		background-color: var(--el-color-white) !important;
	}

	.el-select-dropdown__item.hover,
	.el-select-dropdown__item:hover {
		background-color: var(--el-bg-color) !important;
	}

	.el-dialog {
		background-color: var(--el-color-white) !important;
	}

	.el-dialog__body {
		border-top-color: var(--next-border-color-light) !important;
	}

	.el-textarea__inner {
		background-color: var(--el-color-white) !important;
	}

	.el-textarea {
		--el-input-border-color: var(--next-border-color-light) !important;
	}

	.el-button--large {
		background-color: var(--el-color-white) !important;
		border          : 1px solid var(--next-border-color-light) !important;
	}

	.el-input-number__decrease,
	.el-input-number__increase {
		border-color: var(--next-border-color-light) !important;
	}

	.el-collapse-item__wrap {
		background-color: var(--el-color-white) !important;
	}

	.el-input__wrapper {
		background-color: var(--el-color-white) !important;
		color           : var(--el-text-color-primary) !important;
	}

	.el-input {
		--el-input-border-color: var(--next-border-color-light) !important;
		--el-input-hover-border: var(--el-text-color-disabled) !important;

	}

	.el-date-editor {
		--el-input-border-color      : var(--next-border-color-light) !important;
		--el-input-hover-border-color: var(--el-text-color-disabled) !important;
	}

	.el-date-range-picker {
		--el-datepicker-inrange-bg-color: var(--el-bg-color) !important;
	}

	.el-date-table td.in-range .el-date-table-cell:hover {
		background-color: var(--next-border-color-light) !important;
	}

	.el-tree {
		background-color             : var(--el-color-white) !important;
		--el-tree-node-hover-bg-color: var(--el-color-white) !important;

	}

	.el-tree-node__content .el-select-dropdown__item.hover {
		background-color: var(--el-color-white) !important;
	}

	.el-tree-node__content:hover {
		background-color: var(--el-color-white) !important;
	}

	.el-table th.el-table__cell,
	.el-table tr {
		background-color: var(--el-color-white) !important;
		color           : var(--el-text-color-primary) !important;

		.cell {
			color: var(--el-text-color-primary) !important;
		}
	}

	.el-scrollbar__view .el-table__body tr:hover>td.el-table__cell,
	.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
		background-color: var(--el-bg-color) !important;
	}

	.el-table__body-wrapper tr td.el-table-fixed-column--left,
	.el-table__body-wrapper tr td.el-table-fixed-column--right,
	.el-table__body-wrapper tr th.el-table-fixed-column--left,
	.el-table__body-wrapper tr th.el-table-fixed-column--right,
	.el-table__footer-wrapper tr td.el-table-fixed-column--left,
	.el-table__footer-wrapper tr td.el-table-fixed-column--right,
	.el-table__footer-wrapper tr th.el-table-fixed-column--left,
	.el-table__footer-wrapper tr th.el-table-fixed-column--right,
	.el-table__header-wrapper tr td.el-table-fixed-column--left,
	.el-table__header-wrapper tr td.el-table-fixed-column--right,
	.el-table__header-wrapper tr th.el-table-fixed-column--left,
	.el-table__header-wrapper tr th.el-table-fixed-column--right {
		background-color: var(--el-color-white) !important;
	}

	.add-flag-container {
		border-bottom-color: var(--next-border-color-light) !important;
	}

	.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
		background-color: var(--el-color-white) !important;
	}

	.help-wrap .help-item .help-item-label {
		background-color: var(--next-border-color-light) !important;
	}

	.table-wrap .table-item-wrap .label {
		background-color: var(--el-color-white) !important;
	}

	.el-alert,
	.notice-bar {
		border          : 1px solid var(--next-border-color) !important;
		background-color: var(--next-color-disabled) !important;
	}

	.system-dic-container {

		.content,
		.content-box {
			background-color: var(--next-color-disabled) !important;
			color           : var(--el-text-color-primary) !important;
		}

		.ant-descriptions-row {
			border-bottom-color: var(--next-border-color-light) !important;
		}

		.ant-descriptions-view {
			border-color: var(--next-border-color-light) !important;
		}

		.ant-descriptions-item-label,
		.ant-descriptions-item-content {
			background-color: var(--next-color-disabled) !important;
			color           : var(--el-text-color-primary) !important;
			border-color    : var(--next-border-color-light) !important;
		}
	}

	.el-cascader-node:not(.is-disabled):focus,
	.el-cascader-node:not(.is-disabled):hover {
		background-color: var(--el-bg-color) !important;
	}

	.el-tabs--border-card>.el-tabs__header {
		background-color: var(--next-color-disabled) !important;
	}

	.el-upload--picture-card {
		background-color: var(--next-color-disabled) !important;

	}

	.wu-box {
		border-color: var(--next-border-color-light) !important;

		.wu-title {
			border-color: var(--next-border-color-light) !important;
		}
	}

	// menu
	.layout-aside {
		border-right: 1px solid var(--next-border-color-light) !important;
	}

	// colorPicker
	.el-color-picker__mask {
		background: unset !important;
	}

	.el-color-picker__trigger {
		border: 1px solid var(--next-border-color-light) !important;
	}

	// popper / dropdown
	.el-popper {
		border: 1px solid var(--next-border-color) !important;
		color : var(--el-text-color-primary) !important;

		.el-popper__arrow:before {
			background: var(--el-color-white) !important;
			border    : 1px solid var(--next-border-color);
		}

		a {
			color: var(--el-text-color-primary) !important;
		}
	}

	.el-popper,
	.el-dropdown-menu {
		background: var(--el-color-white) !important;
	}

	.el-dropdown-menu__item:hover:not(.is-disabled) {
		background: var(--el-bg-color) !important;
	}

	.el-dropdown-menu__item.is-disabled {
		font-weight: 700 !important;
	}

	// input
	.el-input-group__append,
	.el-input-group__prepend {
		border      : var(--el-input-border) !important;
		border-right: none !important;
		background  : var(--next-color-disabled) !important;
		border-left : 0 !important;
	}

	.el-input-number__decrease,
	.el-input-number__increase {
		background: var(--next-color-disabled) !important;
	}

	// tag
	.el-select .el-select__tags .el-tag {
		background-color: var(--next-bg-color) !important;
	}

	// pagination
	.el-pagination.is-background .el-pager li:not(.disabled).active {
		color: var(--next-color-white) !important;
	}

	.el-pagination.is-background .btn-next,
	.el-pagination.is-background .btn-prev,
	.el-pagination.is-background .el-pager li {
		background-color: var(--next-bg-color);
	}

	// radio
	.el-radio-button:not(.is-active) .el-radio-button__inner {
		border     : 1px solid var(--next-border-color-light) !important;
		border-left: 0 !important;
	}

	.el-radio-button.is-active .el-radio-button__inner {
		color: var(--next-color-white) !important;
	}

	// countup
	.countup-card-item-flex {
		color: var(--el-text-color-primary) !important;
	}

	// editor
	.editor-container {
		.w-e-toolbar {
			background: var(--el-color-white) !important;
			border    : 1px solid var(--next-border-color-light) !important;

			.w-e-menu:hover {
				background: var(--next-color-user-hover) !important;

				i {
					color: var(--el-text-color-primary) !important;
				}
			}
		}

		.w-e-text-container {
			border    : 1px solid var(--next-border-color-light) !important;
			border-top: none !important;

			.w-e-text {
				background: var(--el-color-white) !important;
			}
		}
	}

	// date-picker
	.el-picker-panel {
		background: var(--el-color-white) !important;
	}

	// dialog
	.el-dialog {
		border: 1px solid var(--el-border-color-lighter);

		.el-dialog__header {
			color: var(--el-text-color-primary) !important;
		}
	}

	// columns
	.layout-columns-aside ul .layout-columns-active {
		color: var(--next-color-white) !important;
	}

	.layout-columns-aside {
		border-right: 1px solid var(--next-border-columns);
	}

	.data-overview .home-card-one .home-card-item *span {
		color: var(--next-color-white) !important;
	}

	.monitor-weather {
		background-color: var(--el-color-white) !important;
	}

	.monitor-weather .left .city-weather-data-overview-wrap section {
		color: #fff !important;

	}

	.monitor-weather .left .city-weather-data-overview-wrap,
	.monitor-weather .left .city-weather-data-overview-wrap section span:nth-child(2) {
		border-color: var(--next-border-color-light) !important;
	}

	.monitor-weather .left .city-weather-data-overview-wrap section:nth-child(2n+1) {
		background-color: var(--el-color-white) !important;
	}

	.monitor-weather .left .city-weather-data-overview-wrap section:nth-child(2n) {
		background-color: var(--next-border-color-light) !important;
	}

	.monitor-weather .left .city-weather-data-overview-wrap section.active {
		background-color: #1d3f4b !important;
	}
}