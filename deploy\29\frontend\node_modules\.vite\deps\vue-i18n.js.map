{"version": 3, "sources": ["../../@intlify/shared/dist/shared.esm-bundler.js", "../../@intlify/message-resolver/dist/message-resolver.esm-bundler.js", "../../@intlify/runtime/dist/runtime.esm-bundler.js", "../../@intlify/message-compiler/dist/message-compiler.esm-bundler.js", "../../@intlify/devtools-if/dist/devtools-if.esm-bundler.js", "../../@intlify/core-base/dist/core-base.esm-bundler.js", "../../vue-i18n/dist/vue-i18n.cjs.js", "dep:vue-i18n"], "sourcesContent": ["/*!\n  * @intlify/shared v9.1.10\n  * (c) 2022 kazuya kawa<PERSON>\n  * Released under the MIT License.\n  */\n/**\r\n * Original Utilities\r\n * written by ka<PERSON>ya kawa<PERSON>\r\n */\r\nconst inBrowser = typeof window !== 'undefined';\r\nlet mark;\r\nlet measure;\r\nif ((process.env.NODE_ENV !== 'production')) {\r\n    const perf = inBrowser && window.performance;\r\n    if (perf &&\r\n        perf.mark &&\r\n        perf.measure &&\r\n        perf.clearMarks &&\r\n        perf.clearMeasures) {\r\n        mark = (tag) => perf.mark(tag);\r\n        measure = (name, startTag, endTag) => {\r\n            perf.measure(name, startTag, endTag);\r\n            perf.clearMarks(startTag);\r\n            perf.clearMarks(endTag);\r\n        };\r\n    }\r\n}\r\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\r\n/* eslint-disable */\r\nfunction format(message, ...args) {\r\n    if (args.length === 1 && isObject(args[0])) {\r\n        args = args[0];\r\n    }\r\n    if (!args || !args.hasOwnProperty) {\r\n        args = {};\r\n    }\r\n    return message.replace(RE_ARGS, (match, identifier) => {\r\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\r\n    });\r\n}\r\nconst hasSymbol = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\r\nconst makeSymbol = (name) => hasSymbol ? Symbol(name) : name;\r\nconst generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });\r\nconst friendlyJSONstringify = (json) => JSON.stringify(json)\r\n    .replace(/\\u2028/g, '\\\\u2028')\r\n    .replace(/\\u2029/g, '\\\\u2029')\r\n    .replace(/\\u0027/g, '\\\\u0027');\r\nconst isNumber = (val) => typeof val === 'number' && isFinite(val);\r\nconst isDate = (val) => toTypeString(val) === '[object Date]';\r\nconst isRegExp = (val) => toTypeString(val) === '[object RegExp]';\r\nconst isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;\r\nfunction warn(msg, err) {\r\n    if (typeof console !== 'undefined') {\r\n        console.warn(`[intlify] ` + msg);\r\n        /* istanbul ignore if */\r\n        if (err) {\r\n            console.warn(err.stack);\r\n        }\r\n    }\r\n}\r\nconst assign = Object.assign;\r\nlet _globalThis;\r\nconst getGlobalThis = () => {\r\n    // prettier-ignore\r\n    return (_globalThis ||\r\n        (_globalThis =\r\n            typeof globalThis !== 'undefined'\r\n                ? globalThis\r\n                : typeof self !== 'undefined'\r\n                    ? self\r\n                    : typeof window !== 'undefined'\r\n                        ? window\r\n                        : typeof global !== 'undefined'\r\n                            ? global\r\n                            : {}));\r\n};\r\nfunction escapeHtml(rawText) {\r\n    return rawText\r\n        .replace(/</g, '&lt;')\r\n        .replace(/>/g, '&gt;')\r\n        .replace(/\"/g, '&quot;')\r\n        .replace(/'/g, '&apos;');\r\n}\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nfunction hasOwn(obj, key) {\r\n    return hasOwnProperty.call(obj, key);\r\n}\r\n/* eslint-enable */\r\n/**\r\n * Useful Utilities By Evan you\r\n * Modified by kazuya kawaguchi\r\n * MIT License\r\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts\r\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts\r\n */\r\nconst isArray = Array.isArray;\r\nconst isFunction = (val) => typeof val === 'function';\r\nconst isString = (val) => typeof val === 'string';\r\nconst isBoolean = (val) => typeof val === 'boolean';\r\nconst isSymbol = (val) => typeof val === 'symbol';\r\nconst isObject = (val) => // eslint-disable-line\r\n val !== null && typeof val === 'object';\r\nconst isPromise = (val) => {\r\n    return isObject(val) && isFunction(val.then) && isFunction(val.catch);\r\n};\r\nconst objectToString = Object.prototype.toString;\r\nconst toTypeString = (value) => objectToString.call(value);\r\nconst isPlainObject = (val) => toTypeString(val) === '[object Object]';\r\n// for converting list and named values to displayed strings.\r\nconst toDisplayString = (val) => {\r\n    return val == null\r\n        ? ''\r\n        : isArray(val) || (isPlainObject(val) && val.toString === objectToString)\r\n            ? JSON.stringify(val, null, 2)\r\n            : String(val);\r\n};\r\nconst RANGE = 2;\r\nfunction generateCodeFrame(source, start = 0, end = source.length) {\r\n    const lines = source.split(/\\r?\\n/);\r\n    let count = 0;\r\n    const res = [];\r\n    for (let i = 0; i < lines.length; i++) {\r\n        count += lines[i].length + 1;\r\n        if (count >= start) {\r\n            for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {\r\n                if (j < 0 || j >= lines.length)\r\n                    continue;\r\n                const line = j + 1;\r\n                res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);\r\n                const lineLength = lines[j].length;\r\n                if (j === i) {\r\n                    // push underline\r\n                    const pad = start - (count - lineLength) + 1;\r\n                    const length = Math.max(1, end > count ? lineLength - pad : end - start);\r\n                    res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));\r\n                }\r\n                else if (j > i) {\r\n                    if (end > count) {\r\n                        const length = Math.max(Math.min(end - count, lineLength), 1);\r\n                        res.push(`   |  ` + '^'.repeat(length));\r\n                    }\r\n                    count += lineLength + 1;\r\n                }\r\n            }\r\n            break;\r\n        }\r\n    }\r\n    return res.join('\\n');\r\n}\n\n/**\r\n * Event emitter, forked from the below:\r\n * - original repository url: https://github.com/developit/mitt\r\n * - code url: https://github.com/developit/mitt/blob/master/src/index.ts\r\n * - author: Jason Miller (https://github.com/developit)\r\n * - license: MIT\r\n */\r\n/**\r\n * Create a event emitter\r\n *\r\n * @returns An event emitter\r\n */\r\nfunction createEmitter() {\r\n    const events = new Map();\r\n    const emitter = {\r\n        events,\r\n        on(event, handler) {\r\n            const handlers = events.get(event);\r\n            const added = handlers && handlers.push(handler);\r\n            if (!added) {\r\n                events.set(event, [handler]);\r\n            }\r\n        },\r\n        off(event, handler) {\r\n            const handlers = events.get(event);\r\n            if (handlers) {\r\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\r\n            }\r\n        },\r\n        emit(event, payload) {\r\n            (events.get(event) || [])\r\n                .slice()\r\n                .map(handler => handler(payload));\r\n            (events.get('*') || [])\r\n                .slice()\r\n                .map(handler => handler(event, payload));\r\n        }\r\n    };\r\n    return emitter;\r\n}\n\nexport { assign, createEmitter, escapeHtml, format, friendlyJSONstringify, generateCodeFrame, generateFormatCacheKey, getGlobalThis, hasOwn, inBrowser, isArray, isBoolean, isDate, isEmptyObject, isFunction, isNumber, isObject, isPlainObject, isPromise, isRegExp, isString, isSymbol, makeSymbol, mark, measure, objectToString, toDisplayString, toTypeString, warn };\n", "/*!\n  * @intlify/message-resolver v9.1.10\n  * (c) 2022 kazuya kawa<PERSON>\n  * Released under the MIT License.\n  */\n/**\r\n * Original Utilities\r\n * written by kazuya kawaguchi\r\n */\r\nif ((process.env.NODE_ENV !== 'production')) ;\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\nfunction hasOwn(obj, key) {\r\n    return hasOwnProperty.call(obj, key);\r\n}\r\nconst isObject = (val) => // eslint-disable-line\r\n val !== null && typeof val === 'object';\n\nconst pathStateMachine = [];\r\npathStateMachine[0 /* BEFORE_PATH */] = {\r\n    [\"w\" /* WORKSPACE */]: [0 /* BEFORE_PATH */],\r\n    [\"i\" /* IDENT */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"[\" /* LEFT_BRACKET */]: [4 /* IN_SUB_PATH */],\r\n    [\"o\" /* END_OF_FAIL */]: [7 /* AFTER_PATH */]\r\n};\r\npathStateMachine[1 /* IN_PATH */] = {\r\n    [\"w\" /* WORKSPACE */]: [1 /* IN_PATH */],\r\n    [\".\" /* DOT */]: [2 /* BEFORE_IDENT */],\r\n    [\"[\" /* LEFT_BRACKET */]: [4 /* IN_SUB_PATH */],\r\n    [\"o\" /* END_OF_FAIL */]: [7 /* AFTER_PATH */]\r\n};\r\npathStateMachine[2 /* BEFORE_IDENT */] = {\r\n    [\"w\" /* WORKSPACE */]: [2 /* BEFORE_IDENT */],\r\n    [\"i\" /* IDENT */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"0\" /* ZERO */]: [3 /* IN_IDENT */, 0 /* APPEND */]\r\n};\r\npathStateMachine[3 /* IN_IDENT */] = {\r\n    [\"i\" /* IDENT */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"0\" /* ZERO */]: [3 /* IN_IDENT */, 0 /* APPEND */],\r\n    [\"w\" /* WORKSPACE */]: [1 /* IN_PATH */, 1 /* PUSH */],\r\n    [\".\" /* DOT */]: [2 /* BEFORE_IDENT */, 1 /* PUSH */],\r\n    [\"[\" /* LEFT_BRACKET */]: [4 /* IN_SUB_PATH */, 1 /* PUSH */],\r\n    [\"o\" /* END_OF_FAIL */]: [7 /* AFTER_PATH */, 1 /* PUSH */]\r\n};\r\npathStateMachine[4 /* IN_SUB_PATH */] = {\r\n    [\"'\" /* SINGLE_QUOTE */]: [5 /* IN_SINGLE_QUOTE */, 0 /* APPEND */],\r\n    [\"\\\"\" /* DOUBLE_QUOTE */]: [6 /* IN_DOUBLE_QUOTE */, 0 /* APPEND */],\r\n    [\"[\" /* LEFT_BRACKET */]: [\r\n        4 /* IN_SUB_PATH */,\r\n        2 /* INC_SUB_PATH_DEPTH */\r\n    ],\r\n    [\"]\" /* RIGHT_BRACKET */]: [1 /* IN_PATH */, 3 /* PUSH_SUB_PATH */],\r\n    [\"o\" /* END_OF_FAIL */]: 8 /* ERROR */,\r\n    [\"l\" /* ELSE */]: [4 /* IN_SUB_PATH */, 0 /* APPEND */]\r\n};\r\npathStateMachine[5 /* IN_SINGLE_QUOTE */] = {\r\n    [\"'\" /* SINGLE_QUOTE */]: [4 /* IN_SUB_PATH */, 0 /* APPEND */],\r\n    [\"o\" /* END_OF_FAIL */]: 8 /* ERROR */,\r\n    [\"l\" /* ELSE */]: [5 /* IN_SINGLE_QUOTE */, 0 /* APPEND */]\r\n};\r\npathStateMachine[6 /* IN_DOUBLE_QUOTE */] = {\r\n    [\"\\\"\" /* DOUBLE_QUOTE */]: [4 /* IN_SUB_PATH */, 0 /* APPEND */],\r\n    [\"o\" /* END_OF_FAIL */]: 8 /* ERROR */,\r\n    [\"l\" /* ELSE */]: [6 /* IN_DOUBLE_QUOTE */, 0 /* APPEND */]\r\n};\r\n/**\r\n * Check if an expression is a literal value.\r\n */\r\nconst literalValueRE = /^\\s?(?:true|false|-?[\\d.]+|'[^']*'|\"[^\"]*\")\\s?$/;\r\nfunction isLiteral(exp) {\r\n    return literalValueRE.test(exp);\r\n}\r\n/**\r\n * Strip quotes from a string\r\n */\r\nfunction stripQuotes(str) {\r\n    const a = str.charCodeAt(0);\r\n    const b = str.charCodeAt(str.length - 1);\r\n    return a === b && (a === 0x22 || a === 0x27) ? str.slice(1, -1) : str;\r\n}\r\n/**\r\n * Determine the type of a character in a keypath.\r\n */\r\nfunction getPathCharType(ch) {\r\n    if (ch === undefined || ch === null) {\r\n        return \"o\" /* END_OF_FAIL */;\r\n    }\r\n    const code = ch.charCodeAt(0);\r\n    switch (code) {\r\n        case 0x5b: // [\r\n        case 0x5d: // ]\r\n        case 0x2e: // .\r\n        case 0x22: // \"\r\n        case 0x27: // '\r\n            return ch;\r\n        case 0x5f: // _\r\n        case 0x24: // $\r\n        case 0x2d: // -\r\n            return \"i\" /* IDENT */;\r\n        case 0x09: // Tab (HT)\r\n        case 0x0a: // Newline (LF)\r\n        case 0x0d: // Return (CR)\r\n        case 0xa0: // No-break space (NBSP)\r\n        case 0xfeff: // Byte Order Mark (BOM)\r\n        case 0x2028: // Line Separator (LS)\r\n        case 0x2029: // Paragraph Separator (PS)\r\n            return \"w\" /* WORKSPACE */;\r\n    }\r\n    return \"i\" /* IDENT */;\r\n}\r\n/**\r\n * Format a subPath, return its plain form if it is\r\n * a literal string or number. Otherwise prepend the\r\n * dynamic indicator (*).\r\n */\r\nfunction formatSubPath(path) {\r\n    const trimmed = path.trim();\r\n    // invalid leading 0\r\n    if (path.charAt(0) === '0' && isNaN(parseInt(path))) {\r\n        return false;\r\n    }\r\n    return isLiteral(trimmed)\r\n        ? stripQuotes(trimmed)\r\n        : \"*\" /* ASTARISK */ + trimmed;\r\n}\r\n/**\r\n * Parse a string path into an array of segments\r\n */\r\nfunction parse(path) {\r\n    const keys = [];\r\n    let index = -1;\r\n    let mode = 0 /* BEFORE_PATH */;\r\n    let subPathDepth = 0;\r\n    let c;\r\n    let key; // eslint-disable-line\r\n    let newChar;\r\n    let type;\r\n    let transition;\r\n    let action;\r\n    let typeMap;\r\n    const actions = [];\r\n    actions[0 /* APPEND */] = () => {\r\n        if (key === undefined) {\r\n            key = newChar;\r\n        }\r\n        else {\r\n            key += newChar;\r\n        }\r\n    };\r\n    actions[1 /* PUSH */] = () => {\r\n        if (key !== undefined) {\r\n            keys.push(key);\r\n            key = undefined;\r\n        }\r\n    };\r\n    actions[2 /* INC_SUB_PATH_DEPTH */] = () => {\r\n        actions[0 /* APPEND */]();\r\n        subPathDepth++;\r\n    };\r\n    actions[3 /* PUSH_SUB_PATH */] = () => {\r\n        if (subPathDepth > 0) {\r\n            subPathDepth--;\r\n            mode = 4 /* IN_SUB_PATH */;\r\n            actions[0 /* APPEND */]();\r\n        }\r\n        else {\r\n            subPathDepth = 0;\r\n            if (key === undefined) {\r\n                return false;\r\n            }\r\n            key = formatSubPath(key);\r\n            if (key === false) {\r\n                return false;\r\n            }\r\n            else {\r\n                actions[1 /* PUSH */]();\r\n            }\r\n        }\r\n    };\r\n    function maybeUnescapeQuote() {\r\n        const nextChar = path[index + 1];\r\n        if ((mode === 5 /* IN_SINGLE_QUOTE */ &&\r\n            nextChar === \"'\" /* SINGLE_QUOTE */) ||\r\n            (mode === 6 /* IN_DOUBLE_QUOTE */ &&\r\n                nextChar === \"\\\"\" /* DOUBLE_QUOTE */)) {\r\n            index++;\r\n            newChar = '\\\\' + nextChar;\r\n            actions[0 /* APPEND */]();\r\n            return true;\r\n        }\r\n    }\r\n    while (mode !== null) {\r\n        index++;\r\n        c = path[index];\r\n        if (c === '\\\\' && maybeUnescapeQuote()) {\r\n            continue;\r\n        }\r\n        type = getPathCharType(c);\r\n        typeMap = pathStateMachine[mode];\r\n        transition = typeMap[type] || typeMap[\"l\" /* ELSE */] || 8 /* ERROR */;\r\n        // check parse error\r\n        if (transition === 8 /* ERROR */) {\r\n            return;\r\n        }\r\n        mode = transition[0];\r\n        if (transition[1] !== undefined) {\r\n            action = actions[transition[1]];\r\n            if (action) {\r\n                newChar = c;\r\n                if (action() === false) {\r\n                    return;\r\n                }\r\n            }\r\n        }\r\n        // check parse finish\r\n        if (mode === 7 /* AFTER_PATH */) {\r\n            return keys;\r\n        }\r\n    }\r\n}\r\n// path token cache\r\nconst cache = new Map();\r\nfunction resolveValue(obj, path) {\r\n    // check object\r\n    if (!isObject(obj)) {\r\n        return null;\r\n    }\r\n    // parse path\r\n    let hit = cache.get(path);\r\n    if (!hit) {\r\n        hit = parse(path);\r\n        if (hit) {\r\n            cache.set(path, hit);\r\n        }\r\n    }\r\n    // check hit\r\n    if (!hit) {\r\n        return null;\r\n    }\r\n    // resolve path value\r\n    const len = hit.length;\r\n    let last = obj;\r\n    let i = 0;\r\n    while (i < len) {\r\n        const val = last[hit[i]];\r\n        if (val === undefined) {\r\n            return null;\r\n        }\r\n        last = val;\r\n        i++;\r\n    }\r\n    return last;\r\n}\r\n/**\r\n * Transform flat json in obj to normal json in obj\r\n */\r\nfunction handleFlatJson(obj) {\r\n    // check obj\r\n    if (!isObject(obj)) {\r\n        return obj;\r\n    }\r\n    for (const key in obj) {\r\n        // check key\r\n        if (!hasOwn(obj, key)) {\r\n            continue;\r\n        }\r\n        // handle for normal json\r\n        if (!key.includes(\".\" /* DOT */)) {\r\n            // recursive process value if value is also a object\r\n            if (isObject(obj[key])) {\r\n                handleFlatJson(obj[key]);\r\n            }\r\n        }\r\n        // handle for flat json, transform to normal json\r\n        else {\r\n            // go to the last object\r\n            const subKeys = key.split(\".\" /* DOT */);\r\n            const lastIndex = subKeys.length - 1;\r\n            let currentObj = obj;\r\n            for (let i = 0; i < lastIndex; i++) {\r\n                if (!(subKeys[i] in currentObj)) {\r\n                    currentObj[subKeys[i]] = {};\r\n                }\r\n                currentObj = currentObj[subKeys[i]];\r\n            }\r\n            // update last object value, delete old property\r\n            currentObj[subKeys[lastIndex]] = obj[key];\r\n            delete obj[key];\r\n            // recursive process value if value is also a object\r\n            if (isObject(currentObj[subKeys[lastIndex]])) {\r\n                handleFlatJson(currentObj[subKeys[lastIndex]]);\r\n            }\r\n        }\r\n    }\r\n    return obj;\r\n}\n\nexport { handleFlatJson, parse, resolveValue };\n", "/*!\n  * @intlify/runtime v9.1.10\n  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { isNumber, isObject, isString, isFunction, isPlainObject, toDisplayString } from '@intlify/shared';\n\nconst DEFAULT_MODIFIER = (str) => str;\r\nconst DEFAULT_MESSAGE = (ctx) => ''; // eslint-disable-line\r\nconst DEFAULT_MESSAGE_DATA_TYPE = 'text';\r\nconst DEFAULT_NORMALIZE = (values) => values.length === 0 ? '' : values.join('');\r\nconst DEFAULT_INTERPOLATE = toDisplayString;\r\nfunction pluralDefault(choice, choicesLength) {\r\n    choice = Math.abs(choice);\r\n    if (choicesLength === 2) {\r\n        // prettier-ignore\r\n        return choice\r\n            ? choice > 1\r\n                ? 1\r\n                : 0\r\n            : 1;\r\n    }\r\n    return choice ? Math.min(choice, 2) : 0;\r\n}\r\nfunction getPluralIndex(options) {\r\n    // prettier-ignore\r\n    const index = isNumber(options.pluralIndex)\r\n        ? options.pluralIndex\r\n        : -1;\r\n    // prettier-ignore\r\n    return options.named && (isNumber(options.named.count) || isNumber(options.named.n))\r\n        ? isNumber(options.named.count)\r\n            ? options.named.count\r\n            : isNumber(options.named.n)\r\n                ? options.named.n\r\n                : index\r\n        : index;\r\n}\r\nfunction normalizeNamed(pluralIndex, props) {\r\n    if (!props.count) {\r\n        props.count = pluralIndex;\r\n    }\r\n    if (!props.n) {\r\n        props.n = pluralIndex;\r\n    }\r\n}\r\nfunction createMessageContext(options = {}) {\r\n    const locale = options.locale;\r\n    const pluralIndex = getPluralIndex(options);\r\n    const pluralRule = isObject(options.pluralRules) &&\r\n        isString(locale) &&\r\n        isFunction(options.pluralRules[locale])\r\n        ? options.pluralRules[locale]\r\n        : pluralDefault;\r\n    const orgPluralRule = isObject(options.pluralRules) &&\r\n        isString(locale) &&\r\n        isFunction(options.pluralRules[locale])\r\n        ? pluralDefault\r\n        : undefined;\r\n    const plural = (messages) => messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];\r\n    const _list = options.list || [];\r\n    const list = (index) => _list[index];\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    const _named = options.named || {};\r\n    isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);\r\n    const named = (key) => _named[key];\r\n    // TODO: need to design resolve message function?\r\n    function message(key) {\r\n        // prettier-ignore\r\n        const msg = isFunction(options.messages)\r\n            ? options.messages(key)\r\n            : isObject(options.messages)\r\n                ? options.messages[key]\r\n                : false;\r\n        return !msg\r\n            ? options.parent\r\n                ? options.parent.message(key) // resolve from parent messages\r\n                : DEFAULT_MESSAGE\r\n            : msg;\r\n    }\r\n    const _modifier = (name) => options.modifiers\r\n        ? options.modifiers[name]\r\n        : DEFAULT_MODIFIER;\r\n    const normalize = isPlainObject(options.processor) && isFunction(options.processor.normalize)\r\n        ? options.processor.normalize\r\n        : DEFAULT_NORMALIZE;\r\n    const interpolate = isPlainObject(options.processor) &&\r\n        isFunction(options.processor.interpolate)\r\n        ? options.processor.interpolate\r\n        : DEFAULT_INTERPOLATE;\r\n    const type = isPlainObject(options.processor) && isString(options.processor.type)\r\n        ? options.processor.type\r\n        : DEFAULT_MESSAGE_DATA_TYPE;\r\n    const ctx = {\r\n        [\"list\" /* LIST */]: list,\r\n        [\"named\" /* NAMED */]: named,\r\n        [\"plural\" /* PLURAL */]: plural,\r\n        [\"linked\" /* LINKED */]: (key, modifier) => {\r\n            // TODO: should check `key`\r\n            const msg = message(key)(ctx);\r\n            return isString(modifier) ? _modifier(modifier)(msg) : msg;\r\n        },\r\n        [\"message\" /* MESSAGE */]: message,\r\n        [\"type\" /* TYPE */]: type,\r\n        [\"interpolate\" /* INTERPOLATE */]: interpolate,\r\n        [\"normalize\" /* NORMALIZE */]: normalize\r\n    };\r\n    return ctx;\r\n}\n\nexport { DEFAULT_MESSAGE_DATA_TYPE, createMessageContext };\n", "/*!\n  * @intlify/message-compiler v9.1.10\n  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { format, assign, isString } from '@intlify/shared';\n\n/** @internal */\r\nconst errorMessages = {\r\n    // tokenizer error messages\r\n    [0 /* EXPECTED_TOKEN */]: `Expected token: '{0}'`,\r\n    [1 /* INVALID_TOKEN_IN_PLACEHOLDER */]: `Invalid token in placeholder: '{0}'`,\r\n    [2 /* UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER */]: `Unterminated single quote in placeholder`,\r\n    [3 /* UNKNOWN_ESCAPE_SEQUENCE */]: `Unknown escape sequence: \\\\{0}`,\r\n    [4 /* INVALID_UNICODE_ESCAPE_SEQUENCE */]: `Invalid unicode escape sequence: {0}`,\r\n    [5 /* UNBALANCED_CLOSING_BRACE */]: `Unbalanced closing brace`,\r\n    [6 /* UNTERMINATED_CLOSING_BRACE */]: `Unterminated closing brace`,\r\n    [7 /* EMPTY_PLACEHOLDER */]: `Empty placeholder`,\r\n    [8 /* NOT_ALLOW_NEST_PLACEHOLDER */]: `Not allowed nest placeholder`,\r\n    [9 /* INVALID_LINKED_FORMAT */]: `Invalid linked format`,\r\n    // parser error messages\r\n    [10 /* MUST_HAVE_MESSAGES_IN_PLURAL */]: `Plural must have messages`,\r\n    [11 /* UNEXPECTED_EMPTY_LINKED_MODIFIER */]: `Unexpected empty linked modifier`,\r\n    [12 /* UNEXPECTED_EMPTY_LINKED_KEY */]: `Unexpected empty linked key`,\r\n    [13 /* UNEXPECTED_LEXICAL_ANALYSIS */]: `Unexpected lexical analysis in token: '{0}'`\r\n};\r\nfunction createCompileError(code, loc, options = {}) {\r\n    const { domain, messages, args } = options;\r\n    const msg = (process.env.NODE_ENV !== 'production')\r\n        ? format((messages || errorMessages)[code] || '', ...(args || []))\r\n        : code;\r\n    const error = new SyntaxError(String(msg));\r\n    error.code = code;\r\n    if (loc) {\r\n        error.location = loc;\r\n    }\r\n    error.domain = domain;\r\n    return error;\r\n}\r\n/** @internal */\r\nfunction defaultOnError(error) {\r\n    throw error;\r\n}\n\nconst LocationStub = {\r\n    start: { line: 1, column: 1, offset: 0 },\r\n    end: { line: 1, column: 1, offset: 0 }\r\n};\r\nfunction createPosition(line, column, offset) {\r\n    return { line, column, offset };\r\n}\r\nfunction createLocation(start, end, source) {\r\n    const loc = { start, end };\r\n    if (source != null) {\r\n        loc.source = source;\r\n    }\r\n    return loc;\r\n}\n\nconst CHAR_SP = ' ';\r\nconst CHAR_CR = '\\r';\r\nconst CHAR_LF = '\\n';\r\nconst CHAR_LS = String.fromCharCode(0x2028);\r\nconst CHAR_PS = String.fromCharCode(0x2029);\r\nfunction createScanner(str) {\r\n    const _buf = str;\r\n    let _index = 0;\r\n    let _line = 1;\r\n    let _column = 1;\r\n    let _peekOffset = 0;\r\n    const isCRLF = (index) => _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\r\n    const isLF = (index) => _buf[index] === CHAR_LF;\r\n    const isPS = (index) => _buf[index] === CHAR_PS;\r\n    const isLS = (index) => _buf[index] === CHAR_LS;\r\n    const isLineEnd = (index) => isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\r\n    const index = () => _index;\r\n    const line = () => _line;\r\n    const column = () => _column;\r\n    const peekOffset = () => _peekOffset;\r\n    const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\r\n    const currentChar = () => charAt(_index);\r\n    const currentPeek = () => charAt(_index + _peekOffset);\r\n    function next() {\r\n        _peekOffset = 0;\r\n        if (isLineEnd(_index)) {\r\n            _line++;\r\n            _column = 0;\r\n        }\r\n        if (isCRLF(_index)) {\r\n            _index++;\r\n        }\r\n        _index++;\r\n        _column++;\r\n        return _buf[_index];\r\n    }\r\n    function peek() {\r\n        if (isCRLF(_index + _peekOffset)) {\r\n            _peekOffset++;\r\n        }\r\n        _peekOffset++;\r\n        return _buf[_index + _peekOffset];\r\n    }\r\n    function reset() {\r\n        _index = 0;\r\n        _line = 1;\r\n        _column = 1;\r\n        _peekOffset = 0;\r\n    }\r\n    function resetPeek(offset = 0) {\r\n        _peekOffset = offset;\r\n    }\r\n    function skipToPeek() {\r\n        const target = _index + _peekOffset;\r\n        // eslint-disable-next-line no-unmodified-loop-condition\r\n        while (target !== _index) {\r\n            next();\r\n        }\r\n        _peekOffset = 0;\r\n    }\r\n    return {\r\n        index,\r\n        line,\r\n        column,\r\n        peekOffset,\r\n        charAt,\r\n        currentChar,\r\n        currentPeek,\r\n        next,\r\n        peek,\r\n        reset,\r\n        resetPeek,\r\n        skipToPeek\r\n    };\r\n}\n\nconst EOF = undefined;\r\nconst LITERAL_DELIMITER = \"'\";\r\nconst ERROR_DOMAIN$1 = 'tokenizer';\r\nfunction createTokenizer(source, options = {}) {\r\n    const location = options.location !== false;\r\n    const _scnr = createScanner(source);\r\n    const currentOffset = () => _scnr.index();\r\n    const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());\r\n    const _initLoc = currentPosition();\r\n    const _initOffset = currentOffset();\r\n    const _context = {\r\n        currentType: 14 /* EOF */,\r\n        offset: _initOffset,\r\n        startLoc: _initLoc,\r\n        endLoc: _initLoc,\r\n        lastType: 14 /* EOF */,\r\n        lastOffset: _initOffset,\r\n        lastStartLoc: _initLoc,\r\n        lastEndLoc: _initLoc,\r\n        braceNest: 0,\r\n        inLinked: false,\r\n        text: ''\r\n    };\r\n    const context = () => _context;\r\n    const { onError } = options;\r\n    function emitError(code, pos, offset, ...args) {\r\n        const ctx = context();\r\n        pos.column += offset;\r\n        pos.offset += offset;\r\n        if (onError) {\r\n            const loc = createLocation(ctx.startLoc, pos);\r\n            const err = createCompileError(code, loc, {\r\n                domain: ERROR_DOMAIN$1,\r\n                args\r\n            });\r\n            onError(err);\r\n        }\r\n    }\r\n    function getToken(context, type, value) {\r\n        context.endLoc = currentPosition();\r\n        context.currentType = type;\r\n        const token = { type };\r\n        if (location) {\r\n            token.loc = createLocation(context.startLoc, context.endLoc);\r\n        }\r\n        if (value != null) {\r\n            token.value = value;\r\n        }\r\n        return token;\r\n    }\r\n    const getEndToken = (context) => getToken(context, 14 /* EOF */);\r\n    function eat(scnr, ch) {\r\n        if (scnr.currentChar() === ch) {\r\n            scnr.next();\r\n            return ch;\r\n        }\r\n        else {\r\n            emitError(0 /* EXPECTED_TOKEN */, currentPosition(), 0, ch);\r\n            return '';\r\n        }\r\n    }\r\n    function peekSpaces(scnr) {\r\n        let buf = '';\r\n        while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\r\n            buf += scnr.currentPeek();\r\n            scnr.peek();\r\n        }\r\n        return buf;\r\n    }\r\n    function skipSpaces(scnr) {\r\n        const buf = peekSpaces(scnr);\r\n        scnr.skipToPeek();\r\n        return buf;\r\n    }\r\n    function isIdentifierStart(ch) {\r\n        if (ch === EOF) {\r\n            return false;\r\n        }\r\n        const cc = ch.charCodeAt(0);\r\n        return ((cc >= 97 && cc <= 122) || // a-z\r\n            (cc >= 65 && cc <= 90) || // A-Z\r\n            cc === 95 // _\r\n        );\r\n    }\r\n    function isNumberStart(ch) {\r\n        if (ch === EOF) {\r\n            return false;\r\n        }\r\n        const cc = ch.charCodeAt(0);\r\n        return cc >= 48 && cc <= 57; // 0-9\r\n    }\r\n    function isNamedIdentifierStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 2 /* BraceLeft */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = isIdentifierStart(scnr.currentPeek());\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isListIdentifierStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 2 /* BraceLeft */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\r\n        const ret = isNumberStart(ch);\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLiteralStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 2 /* BraceLeft */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === LITERAL_DELIMITER;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedDotStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 8 /* LinkedAlias */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \".\" /* LinkedDot */;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedModifierStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 9 /* LinkedDot */) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = isIdentifierStart(scnr.currentPeek());\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedDelimiterStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (!(currentType === 8 /* LinkedAlias */ ||\r\n            currentType === 12 /* LinkedModifier */)) {\r\n            return false;\r\n        }\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \":\" /* LinkedDelimiter */;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isLinkedReferStart(scnr, context) {\r\n        const { currentType } = context;\r\n        if (currentType !== 10 /* LinkedDelimiter */) {\r\n            return false;\r\n        }\r\n        const fn = () => {\r\n            const ch = scnr.currentPeek();\r\n            if (ch === \"{\" /* BraceLeft */) {\r\n                return isIdentifierStart(scnr.peek());\r\n            }\r\n            else if (ch === \"@\" /* LinkedAlias */ ||\r\n                ch === \"%\" /* Modulo */ ||\r\n                ch === \"|\" /* Pipe */ ||\r\n                ch === \":\" /* LinkedDelimiter */ ||\r\n                ch === \".\" /* LinkedDot */ ||\r\n                ch === CHAR_SP ||\r\n                !ch) {\r\n                return false;\r\n            }\r\n            else if (ch === CHAR_LF) {\r\n                scnr.peek();\r\n                return fn();\r\n            }\r\n            else {\r\n                // other characters\r\n                return isIdentifierStart(ch);\r\n            }\r\n        };\r\n        const ret = fn();\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isPluralStart(scnr) {\r\n        peekSpaces(scnr);\r\n        const ret = scnr.currentPeek() === \"|\" /* Pipe */;\r\n        scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function isTextStart(scnr, reset = true) {\r\n        const fn = (hasSpace = false, prev = '', detectModulo = false) => {\r\n            const ch = scnr.currentPeek();\r\n            if (ch === \"{\" /* BraceLeft */) {\r\n                return prev === \"%\" /* Modulo */ ? false : hasSpace;\r\n            }\r\n            else if (ch === \"@\" /* LinkedAlias */ || !ch) {\r\n                return prev === \"%\" /* Modulo */ ? true : hasSpace;\r\n            }\r\n            else if (ch === \"%\" /* Modulo */) {\r\n                scnr.peek();\r\n                return fn(hasSpace, \"%\" /* Modulo */, true);\r\n            }\r\n            else if (ch === \"|\" /* Pipe */) {\r\n                return prev === \"%\" /* Modulo */ || detectModulo\r\n                    ? true\r\n                    : !(prev === CHAR_SP || prev === CHAR_LF);\r\n            }\r\n            else if (ch === CHAR_SP) {\r\n                scnr.peek();\r\n                return fn(true, CHAR_SP, detectModulo);\r\n            }\r\n            else if (ch === CHAR_LF) {\r\n                scnr.peek();\r\n                return fn(true, CHAR_LF, detectModulo);\r\n            }\r\n            else {\r\n                return true;\r\n            }\r\n        };\r\n        const ret = fn();\r\n        reset && scnr.resetPeek();\r\n        return ret;\r\n    }\r\n    function takeChar(scnr, fn) {\r\n        const ch = scnr.currentChar();\r\n        if (ch === EOF) {\r\n            return EOF;\r\n        }\r\n        if (fn(ch)) {\r\n            scnr.next();\r\n            return ch;\r\n        }\r\n        return null;\r\n    }\r\n    function takeIdentifierChar(scnr) {\r\n        const closure = (ch) => {\r\n            const cc = ch.charCodeAt(0);\r\n            return ((cc >= 97 && cc <= 122) || // a-z\r\n                (cc >= 65 && cc <= 90) || // A-Z\r\n                (cc >= 48 && cc <= 57) || // 0-9\r\n                cc === 95 || // _\r\n                cc === 36 // $\r\n            );\r\n        };\r\n        return takeChar(scnr, closure);\r\n    }\r\n    function takeDigit(scnr) {\r\n        const closure = (ch) => {\r\n            const cc = ch.charCodeAt(0);\r\n            return cc >= 48 && cc <= 57; // 0-9\r\n        };\r\n        return takeChar(scnr, closure);\r\n    }\r\n    function takeHexDigit(scnr) {\r\n        const closure = (ch) => {\r\n            const cc = ch.charCodeAt(0);\r\n            return ((cc >= 48 && cc <= 57) || // 0-9\r\n                (cc >= 65 && cc <= 70) || // A-F\r\n                (cc >= 97 && cc <= 102)); // a-f\r\n        };\r\n        return takeChar(scnr, closure);\r\n    }\r\n    function getDigits(scnr) {\r\n        let ch = '';\r\n        let num = '';\r\n        while ((ch = takeDigit(scnr))) {\r\n            num += ch;\r\n        }\r\n        return num;\r\n    }\r\n    function readText(scnr) {\r\n        let buf = '';\r\n        while (true) {\r\n            const ch = scnr.currentChar();\r\n            if (ch === \"{\" /* BraceLeft */ ||\r\n                ch === \"}\" /* BraceRight */ ||\r\n                ch === \"@\" /* LinkedAlias */ ||\r\n                ch === \"|\" /* Pipe */ ||\r\n                !ch) {\r\n                break;\r\n            }\r\n            else if (ch === \"%\" /* Modulo */) {\r\n                if (isTextStart(scnr)) {\r\n                    buf += ch;\r\n                    scnr.next();\r\n                }\r\n                else {\r\n                    break;\r\n                }\r\n            }\r\n            else if (ch === CHAR_SP || ch === CHAR_LF) {\r\n                if (isTextStart(scnr)) {\r\n                    buf += ch;\r\n                    scnr.next();\r\n                }\r\n                else if (isPluralStart(scnr)) {\r\n                    break;\r\n                }\r\n                else {\r\n                    buf += ch;\r\n                    scnr.next();\r\n                }\r\n            }\r\n            else {\r\n                buf += ch;\r\n                scnr.next();\r\n            }\r\n        }\r\n        return buf;\r\n    }\r\n    function readNamedIdentifier(scnr) {\r\n        skipSpaces(scnr);\r\n        let ch = '';\r\n        let name = '';\r\n        while ((ch = takeIdentifierChar(scnr))) {\r\n            name += ch;\r\n        }\r\n        if (scnr.currentChar() === EOF) {\r\n            emitError(6 /* UNTERMINATED_CLOSING_BRACE */, currentPosition(), 0);\r\n        }\r\n        return name;\r\n    }\r\n    function readListIdentifier(scnr) {\r\n        skipSpaces(scnr);\r\n        let value = '';\r\n        if (scnr.currentChar() === '-') {\r\n            scnr.next();\r\n            value += `-${getDigits(scnr)}`;\r\n        }\r\n        else {\r\n            value += getDigits(scnr);\r\n        }\r\n        if (scnr.currentChar() === EOF) {\r\n            emitError(6 /* UNTERMINATED_CLOSING_BRACE */, currentPosition(), 0);\r\n        }\r\n        return value;\r\n    }\r\n    function readLiteral(scnr) {\r\n        skipSpaces(scnr);\r\n        eat(scnr, `\\'`);\r\n        let ch = '';\r\n        let literal = '';\r\n        const fn = (x) => x !== LITERAL_DELIMITER && x !== CHAR_LF;\r\n        while ((ch = takeChar(scnr, fn))) {\r\n            if (ch === '\\\\') {\r\n                literal += readEscapeSequence(scnr);\r\n            }\r\n            else {\r\n                literal += ch;\r\n            }\r\n        }\r\n        const current = scnr.currentChar();\r\n        if (current === CHAR_LF || current === EOF) {\r\n            emitError(2 /* UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER */, currentPosition(), 0);\r\n            // TODO: Is it correct really?\r\n            if (current === CHAR_LF) {\r\n                scnr.next();\r\n                eat(scnr, `\\'`);\r\n            }\r\n            return literal;\r\n        }\r\n        eat(scnr, `\\'`);\r\n        return literal;\r\n    }\r\n    function readEscapeSequence(scnr) {\r\n        const ch = scnr.currentChar();\r\n        switch (ch) {\r\n            case '\\\\':\r\n            case `\\'`:\r\n                scnr.next();\r\n                return `\\\\${ch}`;\r\n            case 'u':\r\n                return readUnicodeEscapeSequence(scnr, ch, 4);\r\n            case 'U':\r\n                return readUnicodeEscapeSequence(scnr, ch, 6);\r\n            default:\r\n                emitError(3 /* UNKNOWN_ESCAPE_SEQUENCE */, currentPosition(), 0, ch);\r\n                return '';\r\n        }\r\n    }\r\n    function readUnicodeEscapeSequence(scnr, unicode, digits) {\r\n        eat(scnr, unicode);\r\n        let sequence = '';\r\n        for (let i = 0; i < digits; i++) {\r\n            const ch = takeHexDigit(scnr);\r\n            if (!ch) {\r\n                emitError(4 /* INVALID_UNICODE_ESCAPE_SEQUENCE */, currentPosition(), 0, `\\\\${unicode}${sequence}${scnr.currentChar()}`);\r\n                break;\r\n            }\r\n            sequence += ch;\r\n        }\r\n        return `\\\\${unicode}${sequence}`;\r\n    }\r\n    function readInvalidIdentifier(scnr) {\r\n        skipSpaces(scnr);\r\n        let ch = '';\r\n        let identifiers = '';\r\n        const closure = (ch) => ch !== \"{\" /* BraceLeft */ &&\r\n            ch !== \"}\" /* BraceRight */ &&\r\n            ch !== CHAR_SP &&\r\n            ch !== CHAR_LF;\r\n        while ((ch = takeChar(scnr, closure))) {\r\n            identifiers += ch;\r\n        }\r\n        return identifiers;\r\n    }\r\n    function readLinkedModifier(scnr) {\r\n        let ch = '';\r\n        let name = '';\r\n        while ((ch = takeIdentifierChar(scnr))) {\r\n            name += ch;\r\n        }\r\n        return name;\r\n    }\r\n    function readLinkedRefer(scnr) {\r\n        const fn = (detect = false, buf) => {\r\n            const ch = scnr.currentChar();\r\n            if (ch === \"{\" /* BraceLeft */ ||\r\n                ch === \"%\" /* Modulo */ ||\r\n                ch === \"@\" /* LinkedAlias */ ||\r\n                ch === \"|\" /* Pipe */ ||\r\n                !ch) {\r\n                return buf;\r\n            }\r\n            else if (ch === CHAR_SP) {\r\n                return buf;\r\n            }\r\n            else if (ch === CHAR_LF) {\r\n                buf += ch;\r\n                scnr.next();\r\n                return fn(detect, buf);\r\n            }\r\n            else {\r\n                buf += ch;\r\n                scnr.next();\r\n                return fn(true, buf);\r\n            }\r\n        };\r\n        return fn(false, '');\r\n    }\r\n    function readPlural(scnr) {\r\n        skipSpaces(scnr);\r\n        const plural = eat(scnr, \"|\" /* Pipe */);\r\n        skipSpaces(scnr);\r\n        return plural;\r\n    }\r\n    // TODO: We need refactoring of token parsing ...\r\n    function readTokenInPlaceholder(scnr, context) {\r\n        let token = null;\r\n        const ch = scnr.currentChar();\r\n        switch (ch) {\r\n            case \"{\" /* BraceLeft */:\r\n                if (context.braceNest >= 1) {\r\n                    emitError(8 /* NOT_ALLOW_NEST_PLACEHOLDER */, currentPosition(), 0);\r\n                }\r\n                scnr.next();\r\n                token = getToken(context, 2 /* BraceLeft */, \"{\" /* BraceLeft */);\r\n                skipSpaces(scnr);\r\n                context.braceNest++;\r\n                return token;\r\n            case \"}\" /* BraceRight */:\r\n                if (context.braceNest > 0 &&\r\n                    context.currentType === 2 /* BraceLeft */) {\r\n                    emitError(7 /* EMPTY_PLACEHOLDER */, currentPosition(), 0);\r\n                }\r\n                scnr.next();\r\n                token = getToken(context, 3 /* BraceRight */, \"}\" /* BraceRight */);\r\n                context.braceNest--;\r\n                context.braceNest > 0 && skipSpaces(scnr);\r\n                if (context.inLinked && context.braceNest === 0) {\r\n                    context.inLinked = false;\r\n                }\r\n                return token;\r\n            case \"@\" /* LinkedAlias */:\r\n                if (context.braceNest > 0) {\r\n                    emitError(6 /* UNTERMINATED_CLOSING_BRACE */, currentPosition(), 0);\r\n                }\r\n                token = readTokenInLinked(scnr, context) || getEndToken(context);\r\n                context.braceNest = 0;\r\n                return token;\r\n            default:\r\n                let validNamedIdentifier = true;\r\n                let validListIdentifier = true;\r\n                let validLiteral = true;\r\n                if (isPluralStart(scnr)) {\r\n                    if (context.braceNest > 0) {\r\n                        emitError(6 /* UNTERMINATED_CLOSING_BRACE */, currentPosition(), 0);\r\n                    }\r\n                    token = getToken(context, 1 /* Pipe */, readPlural(scnr));\r\n                    // reset\r\n                    context.braceNest = 0;\r\n                    context.inLinked = false;\r\n                    return token;\r\n                }\r\n                if (context.braceNest > 0 &&\r\n                    (context.currentType === 5 /* Named */ ||\r\n                        context.currentType === 6 /* List */ ||\r\n                        context.currentType === 7 /* Literal */)) {\r\n                    emitError(6 /* UNTERMINATED_CLOSING_BRACE */, currentPosition(), 0);\r\n                    context.braceNest = 0;\r\n                    return readToken(scnr, context);\r\n                }\r\n                if ((validNamedIdentifier = isNamedIdentifierStart(scnr, context))) {\r\n                    token = getToken(context, 5 /* Named */, readNamedIdentifier(scnr));\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                if ((validListIdentifier = isListIdentifierStart(scnr, context))) {\r\n                    token = getToken(context, 6 /* List */, readListIdentifier(scnr));\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                if ((validLiteral = isLiteralStart(scnr, context))) {\r\n                    token = getToken(context, 7 /* Literal */, readLiteral(scnr));\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\r\n                    // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\r\n                    token = getToken(context, 13 /* InvalidPlace */, readInvalidIdentifier(scnr));\r\n                    emitError(1 /* INVALID_TOKEN_IN_PLACEHOLDER */, currentPosition(), 0, token.value);\r\n                    skipSpaces(scnr);\r\n                    return token;\r\n                }\r\n                break;\r\n        }\r\n        return token;\r\n    }\r\n    // TODO: We need refactoring of token parsing ...\r\n    function readTokenInLinked(scnr, context) {\r\n        const { currentType } = context;\r\n        let token = null;\r\n        const ch = scnr.currentChar();\r\n        if ((currentType === 8 /* LinkedAlias */ ||\r\n            currentType === 9 /* LinkedDot */ ||\r\n            currentType === 12 /* LinkedModifier */ ||\r\n            currentType === 10 /* LinkedDelimiter */) &&\r\n            (ch === CHAR_LF || ch === CHAR_SP)) {\r\n            emitError(9 /* INVALID_LINKED_FORMAT */, currentPosition(), 0);\r\n        }\r\n        switch (ch) {\r\n            case \"@\" /* LinkedAlias */:\r\n                scnr.next();\r\n                token = getToken(context, 8 /* LinkedAlias */, \"@\" /* LinkedAlias */);\r\n                context.inLinked = true;\r\n                return token;\r\n            case \".\" /* LinkedDot */:\r\n                skipSpaces(scnr);\r\n                scnr.next();\r\n                return getToken(context, 9 /* LinkedDot */, \".\" /* LinkedDot */);\r\n            case \":\" /* LinkedDelimiter */:\r\n                skipSpaces(scnr);\r\n                scnr.next();\r\n                return getToken(context, 10 /* LinkedDelimiter */, \":\" /* LinkedDelimiter */);\r\n            default:\r\n                if (isPluralStart(scnr)) {\r\n                    token = getToken(context, 1 /* Pipe */, readPlural(scnr));\r\n                    // reset\r\n                    context.braceNest = 0;\r\n                    context.inLinked = false;\r\n                    return token;\r\n                }\r\n                if (isLinkedDotStart(scnr, context) ||\r\n                    isLinkedDelimiterStart(scnr, context)) {\r\n                    skipSpaces(scnr);\r\n                    return readTokenInLinked(scnr, context);\r\n                }\r\n                if (isLinkedModifierStart(scnr, context)) {\r\n                    skipSpaces(scnr);\r\n                    return getToken(context, 12 /* LinkedModifier */, readLinkedModifier(scnr));\r\n                }\r\n                if (isLinkedReferStart(scnr, context)) {\r\n                    skipSpaces(scnr);\r\n                    if (ch === \"{\" /* BraceLeft */) {\r\n                        // scan the placeholder\r\n                        return readTokenInPlaceholder(scnr, context) || token;\r\n                    }\r\n                    else {\r\n                        return getToken(context, 11 /* LinkedKey */, readLinkedRefer(scnr));\r\n                    }\r\n                }\r\n                if (currentType === 8 /* LinkedAlias */) {\r\n                    emitError(9 /* INVALID_LINKED_FORMAT */, currentPosition(), 0);\r\n                }\r\n                context.braceNest = 0;\r\n                context.inLinked = false;\r\n                return readToken(scnr, context);\r\n        }\r\n    }\r\n    // TODO: We need refactoring of token parsing ...\r\n    function readToken(scnr, context) {\r\n        let token = { type: 14 /* EOF */ };\r\n        if (context.braceNest > 0) {\r\n            return readTokenInPlaceholder(scnr, context) || getEndToken(context);\r\n        }\r\n        if (context.inLinked) {\r\n            return readTokenInLinked(scnr, context) || getEndToken(context);\r\n        }\r\n        const ch = scnr.currentChar();\r\n        switch (ch) {\r\n            case \"{\" /* BraceLeft */:\r\n                return readTokenInPlaceholder(scnr, context) || getEndToken(context);\r\n            case \"}\" /* BraceRight */:\r\n                emitError(5 /* UNBALANCED_CLOSING_BRACE */, currentPosition(), 0);\r\n                scnr.next();\r\n                return getToken(context, 3 /* BraceRight */, \"}\" /* BraceRight */);\r\n            case \"@\" /* LinkedAlias */:\r\n                return readTokenInLinked(scnr, context) || getEndToken(context);\r\n            default:\r\n                if (isPluralStart(scnr)) {\r\n                    token = getToken(context, 1 /* Pipe */, readPlural(scnr));\r\n                    // reset\r\n                    context.braceNest = 0;\r\n                    context.inLinked = false;\r\n                    return token;\r\n                }\r\n                if (isTextStart(scnr)) {\r\n                    return getToken(context, 0 /* Text */, readText(scnr));\r\n                }\r\n                if (ch === \"%\" /* Modulo */) {\r\n                    scnr.next();\r\n                    return getToken(context, 4 /* Modulo */, \"%\" /* Modulo */);\r\n                }\r\n                break;\r\n        }\r\n        return token;\r\n    }\r\n    function nextToken() {\r\n        const { currentType, offset, startLoc, endLoc } = _context;\r\n        _context.lastType = currentType;\r\n        _context.lastOffset = offset;\r\n        _context.lastStartLoc = startLoc;\r\n        _context.lastEndLoc = endLoc;\r\n        _context.offset = currentOffset();\r\n        _context.startLoc = currentPosition();\r\n        if (_scnr.currentChar() === EOF) {\r\n            return getToken(_context, 14 /* EOF */);\r\n        }\r\n        return readToken(_scnr, _context);\r\n    }\r\n    return {\r\n        nextToken,\r\n        currentOffset,\r\n        currentPosition,\r\n        context\r\n    };\r\n}\n\nconst ERROR_DOMAIN = 'parser';\r\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\r\nconst KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\r\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\r\n    switch (match) {\r\n        case `\\\\\\\\`:\r\n            return `\\\\`;\r\n        case `\\\\\\'`:\r\n            return `\\'`;\r\n        default: {\r\n            const codePoint = parseInt(codePoint4 || codePoint6, 16);\r\n            if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\r\n                return String.fromCodePoint(codePoint);\r\n            }\r\n            // invalid ...\r\n            // Replace them with U+FFFD REPLACEMENT CHARACTER.\r\n            return '�';\r\n        }\r\n    }\r\n}\r\nfunction createParser(options = {}) {\r\n    const location = options.location !== false;\r\n    const { onError } = options;\r\n    function emitError(tokenzer, code, start, offset, ...args) {\r\n        const end = tokenzer.currentPosition();\r\n        end.offset += offset;\r\n        end.column += offset;\r\n        if (onError) {\r\n            const loc = createLocation(start, end);\r\n            const err = createCompileError(code, loc, {\r\n                domain: ERROR_DOMAIN,\r\n                args\r\n            });\r\n            onError(err);\r\n        }\r\n    }\r\n    function startNode(type, offset, loc) {\r\n        const node = {\r\n            type,\r\n            start: offset,\r\n            end: offset\r\n        };\r\n        if (location) {\r\n            node.loc = { start: loc, end: loc };\r\n        }\r\n        return node;\r\n    }\r\n    function endNode(node, offset, pos, type) {\r\n        node.end = offset;\r\n        if (type) {\r\n            node.type = type;\r\n        }\r\n        if (location && node.loc) {\r\n            node.loc.end = pos;\r\n        }\r\n    }\r\n    function parseText(tokenizer, value) {\r\n        const context = tokenizer.context();\r\n        const node = startNode(3 /* Text */, context.offset, context.startLoc);\r\n        node.value = value;\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseList(tokenizer, index) {\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\r\n        const node = startNode(5 /* List */, offset, loc);\r\n        node.index = parseInt(index, 10);\r\n        tokenizer.nextToken(); // skip brach right\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseNamed(tokenizer, key) {\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\r\n        const node = startNode(4 /* Named */, offset, loc);\r\n        node.key = key;\r\n        tokenizer.nextToken(); // skip brach right\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseLiteral(tokenizer, value) {\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\r\n        const node = startNode(9 /* Literal */, offset, loc);\r\n        node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\r\n        tokenizer.nextToken(); // skip brach right\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseLinkedModifier(tokenizer) {\r\n        const token = tokenizer.nextToken();\r\n        const context = tokenizer.context();\r\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get linked dot loc\r\n        const node = startNode(8 /* LinkedModifier */, offset, loc);\r\n        if (token.type !== 12 /* LinkedModifier */) {\r\n            // empty modifier\r\n            emitError(tokenizer, 11 /* UNEXPECTED_EMPTY_LINKED_MODIFIER */, context.lastStartLoc, 0);\r\n            node.value = '';\r\n            endNode(node, offset, loc);\r\n            return {\r\n                nextConsumeToken: token,\r\n                node\r\n            };\r\n        }\r\n        // check token\r\n        if (token.value == null) {\r\n            emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n        }\r\n        node.value = token.value || '';\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return {\r\n            node\r\n        };\r\n    }\r\n    function parseLinkedKey(tokenizer, value) {\r\n        const context = tokenizer.context();\r\n        const node = startNode(7 /* LinkedKey */, context.offset, context.startLoc);\r\n        node.value = value;\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseLinked(tokenizer) {\r\n        const context = tokenizer.context();\r\n        const linkedNode = startNode(6 /* Linked */, context.offset, context.startLoc);\r\n        let token = tokenizer.nextToken();\r\n        if (token.type === 9 /* LinkedDot */) {\r\n            const parsed = parseLinkedModifier(tokenizer);\r\n            linkedNode.modifier = parsed.node;\r\n            token = parsed.nextConsumeToken || tokenizer.nextToken();\r\n        }\r\n        // asset check token\r\n        if (token.type !== 10 /* LinkedDelimiter */) {\r\n            emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n        }\r\n        token = tokenizer.nextToken();\r\n        // skip brace left\r\n        if (token.type === 2 /* BraceLeft */) {\r\n            token = tokenizer.nextToken();\r\n        }\r\n        switch (token.type) {\r\n            case 11 /* LinkedKey */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\r\n                break;\r\n            case 5 /* Named */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseNamed(tokenizer, token.value || '');\r\n                break;\r\n            case 6 /* List */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseList(tokenizer, token.value || '');\r\n                break;\r\n            case 7 /* Literal */:\r\n                if (token.value == null) {\r\n                    emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                }\r\n                linkedNode.key = parseLiteral(tokenizer, token.value || '');\r\n                break;\r\n            default:\r\n                // empty key\r\n                emitError(tokenizer, 12 /* UNEXPECTED_EMPTY_LINKED_KEY */, context.lastStartLoc, 0);\r\n                const nextContext = tokenizer.context();\r\n                const emptyLinkedKeyNode = startNode(7 /* LinkedKey */, nextContext.offset, nextContext.startLoc);\r\n                emptyLinkedKeyNode.value = '';\r\n                endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\r\n                linkedNode.key = emptyLinkedKeyNode;\r\n                endNode(linkedNode, nextContext.offset, nextContext.startLoc);\r\n                return {\r\n                    nextConsumeToken: token,\r\n                    node: linkedNode\r\n                };\r\n        }\r\n        endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return {\r\n            node: linkedNode\r\n        };\r\n    }\r\n    function parseMessage(tokenizer) {\r\n        const context = tokenizer.context();\r\n        const startOffset = context.currentType === 1 /* Pipe */\r\n            ? tokenizer.currentOffset()\r\n            : context.offset;\r\n        const startLoc = context.currentType === 1 /* Pipe */\r\n            ? context.endLoc\r\n            : context.startLoc;\r\n        const node = startNode(2 /* Message */, startOffset, startLoc);\r\n        node.items = [];\r\n        let nextToken = null;\r\n        do {\r\n            const token = nextToken || tokenizer.nextToken();\r\n            nextToken = null;\r\n            switch (token.type) {\r\n                case 0 /* Text */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseText(tokenizer, token.value || ''));\r\n                    break;\r\n                case 6 /* List */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseList(tokenizer, token.value || ''));\r\n                    break;\r\n                case 5 /* Named */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseNamed(tokenizer, token.value || ''));\r\n                    break;\r\n                case 7 /* Literal */:\r\n                    if (token.value == null) {\r\n                        emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, getTokenCaption(token));\r\n                    }\r\n                    node.items.push(parseLiteral(tokenizer, token.value || ''));\r\n                    break;\r\n                case 8 /* LinkedAlias */:\r\n                    const parsed = parseLinked(tokenizer);\r\n                    node.items.push(parsed.node);\r\n                    nextToken = parsed.nextConsumeToken || null;\r\n                    break;\r\n            }\r\n        } while (context.currentType !== 14 /* EOF */ &&\r\n            context.currentType !== 1 /* Pipe */);\r\n        // adjust message node loc\r\n        const endOffset = context.currentType === 1 /* Pipe */\r\n            ? context.lastOffset\r\n            : tokenizer.currentOffset();\r\n        const endLoc = context.currentType === 1 /* Pipe */\r\n            ? context.lastEndLoc\r\n            : tokenizer.currentPosition();\r\n        endNode(node, endOffset, endLoc);\r\n        return node;\r\n    }\r\n    function parsePlural(tokenizer, offset, loc, msgNode) {\r\n        const context = tokenizer.context();\r\n        let hasEmptyMessage = msgNode.items.length === 0;\r\n        const node = startNode(1 /* Plural */, offset, loc);\r\n        node.cases = [];\r\n        node.cases.push(msgNode);\r\n        do {\r\n            const msg = parseMessage(tokenizer);\r\n            if (!hasEmptyMessage) {\r\n                hasEmptyMessage = msg.items.length === 0;\r\n            }\r\n            node.cases.push(msg);\r\n        } while (context.currentType !== 14 /* EOF */);\r\n        if (hasEmptyMessage) {\r\n            emitError(tokenizer, 10 /* MUST_HAVE_MESSAGES_IN_PLURAL */, loc, 0);\r\n        }\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    function parseResource(tokenizer) {\r\n        const context = tokenizer.context();\r\n        const { offset, startLoc } = context;\r\n        const msgNode = parseMessage(tokenizer);\r\n        if (context.currentType === 14 /* EOF */) {\r\n            return msgNode;\r\n        }\r\n        else {\r\n            return parsePlural(tokenizer, offset, startLoc, msgNode);\r\n        }\r\n    }\r\n    function parse(source) {\r\n        const tokenizer = createTokenizer(source, assign({}, options));\r\n        const context = tokenizer.context();\r\n        const node = startNode(0 /* Resource */, context.offset, context.startLoc);\r\n        if (location && node.loc) {\r\n            node.loc.source = source;\r\n        }\r\n        node.body = parseResource(tokenizer);\r\n        // assert whether achieved to EOF\r\n        if (context.currentType !== 14 /* EOF */) {\r\n            emitError(tokenizer, 13 /* UNEXPECTED_LEXICAL_ANALYSIS */, context.lastStartLoc, 0, source[context.offset] || '');\r\n        }\r\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\r\n        return node;\r\n    }\r\n    return { parse };\r\n}\r\nfunction getTokenCaption(token) {\r\n    if (token.type === 14 /* EOF */) {\r\n        return 'EOF';\r\n    }\r\n    const name = (token.value || '').replace(/\\r?\\n/gu, '\\\\n');\r\n    return name.length > 10 ? name.slice(0, 9) + '…' : name;\r\n}\n\nfunction createTransformer(ast, options = {} // eslint-disable-line\r\n) {\r\n    const _context = {\r\n        ast,\r\n        helpers: new Set()\r\n    };\r\n    const context = () => _context;\r\n    const helper = (name) => {\r\n        _context.helpers.add(name);\r\n        return name;\r\n    };\r\n    return { context, helper };\r\n}\r\nfunction traverseNodes(nodes, transformer) {\r\n    for (let i = 0; i < nodes.length; i++) {\r\n        traverseNode(nodes[i], transformer);\r\n    }\r\n}\r\nfunction traverseNode(node, transformer) {\r\n    // TODO: if we need pre-hook of transform, should be implemented to here\r\n    switch (node.type) {\r\n        case 1 /* Plural */:\r\n            traverseNodes(node.cases, transformer);\r\n            transformer.helper(\"plural\" /* PLURAL */);\r\n            break;\r\n        case 2 /* Message */:\r\n            traverseNodes(node.items, transformer);\r\n            break;\r\n        case 6 /* Linked */:\r\n            const linked = node;\r\n            traverseNode(linked.key, transformer);\r\n            transformer.helper(\"linked\" /* LINKED */);\r\n            break;\r\n        case 5 /* List */:\r\n            transformer.helper(\"interpolate\" /* INTERPOLATE */);\r\n            transformer.helper(\"list\" /* LIST */);\r\n            break;\r\n        case 4 /* Named */:\r\n            transformer.helper(\"interpolate\" /* INTERPOLATE */);\r\n            transformer.helper(\"named\" /* NAMED */);\r\n            break;\r\n    }\r\n    // TODO: if we need post-hook of transform, should be implemented to here\r\n}\r\n// transform AST\r\nfunction transform(ast, options = {} // eslint-disable-line\r\n) {\r\n    const transformer = createTransformer(ast);\r\n    transformer.helper(\"normalize\" /* NORMALIZE */);\r\n    // traverse\r\n    ast.body && traverseNode(ast.body, transformer);\r\n    // set meta information\r\n    const context = transformer.context();\r\n    ast.helpers = Array.from(context.helpers);\r\n}\n\nfunction createCodeGenerator(ast, options) {\r\n    const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;\r\n    const _context = {\r\n        source: ast.loc.source,\r\n        filename,\r\n        code: '',\r\n        column: 1,\r\n        line: 1,\r\n        offset: 0,\r\n        map: undefined,\r\n        breakLineCode,\r\n        needIndent: _needIndent,\r\n        indentLevel: 0\r\n    };\r\n    const context = () => _context;\r\n    function push(code, node) {\r\n        _context.code += code;\r\n    }\r\n    function _newline(n, withBreakLine = true) {\r\n        const _breakLineCode = withBreakLine ? breakLineCode : '';\r\n        push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);\r\n    }\r\n    function indent(withNewLine = true) {\r\n        const level = ++_context.indentLevel;\r\n        withNewLine && _newline(level);\r\n    }\r\n    function deindent(withNewLine = true) {\r\n        const level = --_context.indentLevel;\r\n        withNewLine && _newline(level);\r\n    }\r\n    function newline() {\r\n        _newline(_context.indentLevel);\r\n    }\r\n    const helper = (key) => `_${key}`;\r\n    const needIndent = () => _context.needIndent;\r\n    return {\r\n        context,\r\n        push,\r\n        indent,\r\n        deindent,\r\n        newline,\r\n        helper,\r\n        needIndent\r\n    };\r\n}\r\nfunction generateLinkedNode(generator, node) {\r\n    const { helper } = generator;\r\n    generator.push(`${helper(\"linked\" /* LINKED */)}(`);\r\n    generateNode(generator, node.key);\r\n    if (node.modifier) {\r\n        generator.push(`, `);\r\n        generateNode(generator, node.modifier);\r\n    }\r\n    generator.push(`)`);\r\n}\r\nfunction generateMessageNode(generator, node) {\r\n    const { helper, needIndent } = generator;\r\n    generator.push(`${helper(\"normalize\" /* NORMALIZE */)}([`);\r\n    generator.indent(needIndent());\r\n    const length = node.items.length;\r\n    for (let i = 0; i < length; i++) {\r\n        generateNode(generator, node.items[i]);\r\n        if (i === length - 1) {\r\n            break;\r\n        }\r\n        generator.push(', ');\r\n    }\r\n    generator.deindent(needIndent());\r\n    generator.push('])');\r\n}\r\nfunction generatePluralNode(generator, node) {\r\n    const { helper, needIndent } = generator;\r\n    if (node.cases.length > 1) {\r\n        generator.push(`${helper(\"plural\" /* PLURAL */)}([`);\r\n        generator.indent(needIndent());\r\n        const length = node.cases.length;\r\n        for (let i = 0; i < length; i++) {\r\n            generateNode(generator, node.cases[i]);\r\n            if (i === length - 1) {\r\n                break;\r\n            }\r\n            generator.push(', ');\r\n        }\r\n        generator.deindent(needIndent());\r\n        generator.push(`])`);\r\n    }\r\n}\r\nfunction generateResource(generator, node) {\r\n    if (node.body) {\r\n        generateNode(generator, node.body);\r\n    }\r\n    else {\r\n        generator.push('null');\r\n    }\r\n}\r\nfunction generateNode(generator, node) {\r\n    const { helper } = generator;\r\n    switch (node.type) {\r\n        case 0 /* Resource */:\r\n            generateResource(generator, node);\r\n            break;\r\n        case 1 /* Plural */:\r\n            generatePluralNode(generator, node);\r\n            break;\r\n        case 2 /* Message */:\r\n            generateMessageNode(generator, node);\r\n            break;\r\n        case 6 /* Linked */:\r\n            generateLinkedNode(generator, node);\r\n            break;\r\n        case 8 /* LinkedModifier */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        case 7 /* LinkedKey */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        case 5 /* List */:\r\n            generator.push(`${helper(\"interpolate\" /* INTERPOLATE */)}(${helper(\"list\" /* LIST */)}(${node.index}))`, node);\r\n            break;\r\n        case 4 /* Named */:\r\n            generator.push(`${helper(\"interpolate\" /* INTERPOLATE */)}(${helper(\"named\" /* NAMED */)}(${JSON.stringify(node.key)}))`, node);\r\n            break;\r\n        case 9 /* Literal */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        case 3 /* Text */:\r\n            generator.push(JSON.stringify(node.value), node);\r\n            break;\r\n        default:\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                throw new Error(`unhandled codegen node type: ${node.type}`);\r\n            }\r\n    }\r\n}\r\n// generate code from AST\r\nconst generate = (ast, options = {} // eslint-disable-line\r\n) => {\r\n    const mode = isString(options.mode) ? options.mode : 'normal';\r\n    const filename = isString(options.filename)\r\n        ? options.filename\r\n        : 'message.intl';\r\n    const sourceMap = !!options.sourceMap;\r\n    // prettier-ignore\r\n    const breakLineCode = options.breakLineCode != null\r\n        ? options.breakLineCode\r\n        : mode === 'arrow'\r\n            ? ';'\r\n            : '\\n';\r\n    const needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\r\n    const helpers = ast.helpers || [];\r\n    const generator = createCodeGenerator(ast, {\r\n        mode,\r\n        filename,\r\n        sourceMap,\r\n        breakLineCode,\r\n        needIndent\r\n    });\r\n    generator.push(mode === 'normal' ? `function __msg__ (ctx) {` : `(ctx) => {`);\r\n    generator.indent(needIndent);\r\n    if (helpers.length > 0) {\r\n        generator.push(`const { ${helpers.map(s => `${s}: _${s}`).join(', ')} } = ctx`);\r\n        generator.newline();\r\n    }\r\n    generator.push(`return `);\r\n    generateNode(generator, ast);\r\n    generator.deindent(needIndent);\r\n    generator.push(`}`);\r\n    const { code, map } = generator.context();\r\n    return {\r\n        ast,\r\n        code,\r\n        map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    };\r\n};\n\nfunction baseCompile(source, options = {}) {\r\n    const assignedOptions = assign({}, options);\r\n    // parse source codes\r\n    const parser = createParser(assignedOptions);\r\n    const ast = parser.parse(source);\r\n    // transform ASTs\r\n    transform(ast, assignedOptions);\r\n    // generate javascript codes\r\n    return generate(ast, assignedOptions);\r\n}\n\nexport { ERROR_DOMAIN, LocationStub, baseCompile, createCompileError, createLocation, createParser, createPosition, defaultOnError, errorMessages };\n", "/*!\n  * @intlify/devtools-if v9.1.10\n  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nconst IntlifyDevToolsHooks = {\r\n    I18nInit: 'i18n:init',\r\n    FunctionTranslate: 'function:translate'\r\n};\n\nexport { IntlifyDevToolsHooks };\n", "/*!\n  * @intlify/core-base v9.1.10\n  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { format, isString, isArray, isPlainObject, assign, isFunction, isBoolean, isRegExp, warn, isObject, escapeHtml, inBrowser, mark, measure, generateCode<PERSON>rame, generateFormatCacheKey, isNumber, isEmptyObject, isDate, getGlobalThis } from '@intlify/shared';\nimport { resolveValue } from '@intlify/message-resolver';\nexport * from '@intlify/message-resolver';\nimport { createMessageContext } from '@intlify/runtime';\nexport * from '@intlify/runtime';\nimport { defaultOnError, baseCompile, createCompileError } from '@intlify/message-compiler';\nexport { createCompileError } from '@intlify/message-compiler';\nimport { IntlifyDevToolsHooks } from '@intlify/devtools-if';\n\nlet devtools = null;\r\nfunction setDevToolsHook(hook) {\r\n    devtools = hook;\r\n}\r\nfunction getDevToolsHook() {\r\n    return devtools;\r\n}\r\nfunction initI18nDevTools(i18n, version, meta) {\r\n    // TODO: queue if devtools is undefined\r\n    devtools &&\r\n        devtools.emit(IntlifyDevToolsHooks.I18nInit, {\r\n            timestamp: Date.now(),\r\n            i18n,\r\n            version,\r\n            meta\r\n        });\r\n}\r\nconst translateDevTools = /* #__PURE__*/ createDevToolsHook(IntlifyDevToolsHooks.FunctionTranslate);\r\nfunction createDevToolsHook(hook) {\r\n    return (payloads) => devtools && devtools.emit(hook, payloads);\r\n}\n\n/** @internal */\r\nconst warnMessages = {\r\n    [0 /* NOT_FOUND_KEY */]: `Not found '{key}' key in '{locale}' locale messages.`,\r\n    [1 /* FALLBACK_TO_TRANSLATE */]: `Fall back to translate '{key}' key with '{target}' locale.`,\r\n    [2 /* CANNOT_FORMAT_NUMBER */]: `Cannot format a number value due to not supported Intl.NumberFormat.`,\r\n    [3 /* FALLBACK_TO_NUMBER_FORMAT */]: `Fall back to number format '{key}' key with '{target}' locale.`,\r\n    [4 /* CANNOT_FORMAT_DATE */]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,\r\n    [5 /* FALLBACK_TO_DATE_FORMAT */]: `Fall back to datetime format '{key}' key with '{target}' locale.`\r\n};\r\nfunction getWarnMessage(code, ...args) {\r\n    return format(warnMessages[code], ...args);\r\n}\n\n/**\r\n * Intlify core-base version\r\n * @internal\r\n */\r\nconst VERSION = '9.1.10';\r\nconst NOT_REOSLVED = -1;\r\nconst MISSING_RESOLVE_VALUE = '';\r\nfunction getDefaultLinkedModifiers() {\r\n    return {\r\n        upper: (val) => (isString(val) ? val.toUpperCase() : val),\r\n        lower: (val) => (isString(val) ? val.toLowerCase() : val),\r\n        // prettier-ignore\r\n        capitalize: (val) => (isString(val)\r\n            ? `${val.charAt(0).toLocaleUpperCase()}${val.substr(1)}`\r\n            : val)\r\n    };\r\n}\r\nlet _compiler;\r\nfunction registerMessageCompiler(compiler) {\r\n    _compiler = compiler;\r\n}\r\n// Additional Meta for Intlify DevTools\r\nlet _additionalMeta = null;\r\nconst setAdditionalMeta = /* #__PURE__*/ (meta) => {\r\n    _additionalMeta = meta;\r\n};\r\nconst getAdditionalMeta = /* #__PURE__*/ () => _additionalMeta;\r\n// ID for CoreContext\r\nlet _cid = 0;\r\nfunction createCoreContext(options = {}) {\r\n    // setup options\r\n    const version = isString(options.version) ? options.version : VERSION;\r\n    const locale = isString(options.locale) ? options.locale : 'en-US';\r\n    const fallbackLocale = isArray(options.fallbackLocale) ||\r\n        isPlainObject(options.fallbackLocale) ||\r\n        isString(options.fallbackLocale) ||\r\n        options.fallbackLocale === false\r\n        ? options.fallbackLocale\r\n        : locale;\r\n    const messages = isPlainObject(options.messages)\r\n        ? options.messages\r\n        : { [locale]: {} };\r\n    const datetimeFormats = isPlainObject(options.datetimeFormats)\r\n        ? options.datetimeFormats\r\n        : { [locale]: {} };\r\n    const numberFormats = isPlainObject(options.numberFormats)\r\n        ? options.numberFormats\r\n        : { [locale]: {} };\r\n    const modifiers = assign({}, options.modifiers || {}, getDefaultLinkedModifiers());\r\n    const pluralRules = options.pluralRules || {};\r\n    const missing = isFunction(options.missing) ? options.missing : null;\r\n    const missingWarn = isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\r\n        ? options.missingWarn\r\n        : true;\r\n    const fallbackWarn = isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : true;\r\n    const fallbackFormat = !!options.fallbackFormat;\r\n    const unresolving = !!options.unresolving;\r\n    const postTranslation = isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : null;\r\n    const processor = isPlainObject(options.processor) ? options.processor : null;\r\n    const warnHtmlMessage = isBoolean(options.warnHtmlMessage)\r\n        ? options.warnHtmlMessage\r\n        : true;\r\n    const escapeParameter = !!options.escapeParameter;\r\n    const messageCompiler = isFunction(options.messageCompiler)\r\n        ? options.messageCompiler\r\n        : _compiler;\r\n    const onWarn = isFunction(options.onWarn) ? options.onWarn : warn;\r\n    // setup internal options\r\n    const internalOptions = options;\r\n    const __datetimeFormatters = isObject(internalOptions.__datetimeFormatters)\r\n        ? internalOptions.__datetimeFormatters\r\n        : new Map();\r\n    const __numberFormatters = isObject(internalOptions.__numberFormatters)\r\n        ? internalOptions.__numberFormatters\r\n        : new Map();\r\n    const __meta = isObject(internalOptions.__meta) ? internalOptions.__meta : {};\r\n    _cid++;\r\n    const context = {\r\n        version,\r\n        cid: _cid,\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        datetimeFormats,\r\n        numberFormats,\r\n        modifiers,\r\n        pluralRules,\r\n        missing,\r\n        missingWarn,\r\n        fallbackWarn,\r\n        fallbackFormat,\r\n        unresolving,\r\n        postTranslation,\r\n        processor,\r\n        warnHtmlMessage,\r\n        escapeParameter,\r\n        messageCompiler,\r\n        onWarn,\r\n        __datetimeFormatters,\r\n        __numberFormatters,\r\n        __meta\r\n    };\r\n    // for vue-devtools timeline event\r\n    if ((process.env.NODE_ENV !== 'production')) {\r\n        context.__v_emitter =\r\n            internalOptions.__v_emitter != null\r\n                ? internalOptions.__v_emitter\r\n                : undefined;\r\n    }\r\n    // NOTE: experimental !!\r\n    if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\r\n        initI18nDevTools(context, version, __meta);\r\n    }\r\n    return context;\r\n}\r\n/** @internal */\r\nfunction isTranslateFallbackWarn(fallback, key) {\r\n    return fallback instanceof RegExp ? fallback.test(key) : fallback;\r\n}\r\n/** @internal */\r\nfunction isTranslateMissingWarn(missing, key) {\r\n    return missing instanceof RegExp ? missing.test(key) : missing;\r\n}\r\n/** @internal */\r\nfunction handleMissing(context, key, locale, missingWarn, type) {\r\n    const { missing, onWarn } = context;\r\n    // for vue-devtools timeline event\r\n    if ((process.env.NODE_ENV !== 'production')) {\r\n        const emitter = context.__v_emitter;\r\n        if (emitter) {\r\n            emitter.emit(\"missing\" /* MISSING */, {\r\n                locale,\r\n                key,\r\n                type,\r\n                groupId: `${type}:${key}`\r\n            });\r\n        }\r\n    }\r\n    if (missing !== null) {\r\n        const ret = missing(context, locale, key, type);\r\n        return isString(ret) ? ret : key;\r\n    }\r\n    else {\r\n        if ((process.env.NODE_ENV !== 'production') && isTranslateMissingWarn(missingWarn, key)) {\r\n            onWarn(getWarnMessage(0 /* NOT_FOUND_KEY */, { key, locale }));\r\n        }\r\n        return key;\r\n    }\r\n}\r\n/** @internal */\r\nfunction getLocaleChain(ctx, fallback, start) {\r\n    const context = ctx;\r\n    if (!context.__localeChainCache) {\r\n        context.__localeChainCache = new Map();\r\n    }\r\n    let chain = context.__localeChainCache.get(start);\r\n    if (!chain) {\r\n        chain = [];\r\n        // first block defined by start\r\n        let block = [start];\r\n        // while any intervening block found\r\n        while (isArray(block)) {\r\n            block = appendBlockToChain(chain, block, fallback);\r\n        }\r\n        // prettier-ignore\r\n        // last block defined by default\r\n        const defaults = isArray(fallback)\r\n            ? fallback\r\n            : isPlainObject(fallback)\r\n                ? fallback['default']\r\n                    ? fallback['default']\r\n                    : null\r\n                : fallback;\r\n        // convert defaults to array\r\n        block = isString(defaults) ? [defaults] : defaults;\r\n        if (isArray(block)) {\r\n            appendBlockToChain(chain, block, false);\r\n        }\r\n        context.__localeChainCache.set(start, chain);\r\n    }\r\n    return chain;\r\n}\r\nfunction appendBlockToChain(chain, block, blocks) {\r\n    let follow = true;\r\n    for (let i = 0; i < block.length && isBoolean(follow); i++) {\r\n        const locale = block[i];\r\n        if (isString(locale)) {\r\n            follow = appendLocaleToChain(chain, block[i], blocks);\r\n        }\r\n    }\r\n    return follow;\r\n}\r\nfunction appendLocaleToChain(chain, locale, blocks) {\r\n    let follow;\r\n    const tokens = locale.split('-');\r\n    do {\r\n        const target = tokens.join('-');\r\n        follow = appendItemToChain(chain, target, blocks);\r\n        tokens.splice(-1, 1);\r\n    } while (tokens.length && follow === true);\r\n    return follow;\r\n}\r\nfunction appendItemToChain(chain, target, blocks) {\r\n    let follow = false;\r\n    if (!chain.includes(target)) {\r\n        follow = true;\r\n        if (target) {\r\n            follow = target[target.length - 1] !== '!';\r\n            const locale = target.replace(/!/g, '');\r\n            chain.push(locale);\r\n            if ((isArray(blocks) || isPlainObject(blocks)) &&\r\n                blocks[locale] // eslint-disable-line @typescript-eslint/no-explicit-any\r\n            ) {\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                follow = blocks[locale];\r\n            }\r\n        }\r\n    }\r\n    return follow;\r\n}\r\n/** @internal */\r\nfunction updateFallbackLocale(ctx, locale, fallback) {\r\n    const context = ctx;\r\n    context.__localeChainCache = new Map();\r\n    getLocaleChain(ctx, fallback, locale);\r\n}\n\nconst RE_HTML_TAG = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\r\nconst WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;\r\nfunction checkHtmlMessage(source, options) {\r\n    const warnHtmlMessage = isBoolean(options.warnHtmlMessage)\r\n        ? options.warnHtmlMessage\r\n        : true;\r\n    if (warnHtmlMessage && RE_HTML_TAG.test(source)) {\r\n        warn(format(WARN_MESSAGE, { source }));\r\n    }\r\n}\r\nconst defaultOnCacheKey = (source) => source;\r\nlet compileCache = Object.create(null);\r\nfunction clearCompileCache() {\r\n    compileCache = Object.create(null);\r\n}\r\nfunction compileToFunction(source, options = {}) {\r\n    {\r\n        // check HTML message\r\n        (process.env.NODE_ENV !== 'production') && checkHtmlMessage(source, options);\r\n        // check caches\r\n        const onCacheKey = options.onCacheKey || defaultOnCacheKey;\r\n        const key = onCacheKey(source);\r\n        const cached = compileCache[key];\r\n        if (cached) {\r\n            return cached;\r\n        }\r\n        // compile error detecting\r\n        let occurred = false;\r\n        const onError = options.onError || defaultOnError;\r\n        options.onError = (err) => {\r\n            occurred = true;\r\n            onError(err);\r\n        };\r\n        // compile\r\n        const { code } = baseCompile(source, options);\r\n        // evaluate function\r\n        const msg = new Function(`return ${code}`)();\r\n        // if occurred compile error, don't cache\r\n        return !occurred ? (compileCache[key] = msg) : msg;\r\n    }\r\n}\n\nfunction createCoreError(code) {\r\n    return createCompileError(code, null, (process.env.NODE_ENV !== 'production') ? { messages: errorMessages } : undefined);\r\n}\r\n/** @internal */\r\nconst errorMessages = {\r\n    [14 /* INVALID_ARGUMENT */]: 'Invalid arguments',\r\n    [15 /* INVALID_DATE_ARGUMENT */]: 'The date provided is an invalid Date object.' +\r\n        'Make sure your Date represents a valid date.',\r\n    [16 /* INVALID_ISO_DATE_ARGUMENT */]: 'The argument provided is not a valid ISO date string'\r\n};\n\nconst NOOP_MESSAGE_FUNCTION = () => '';\r\nconst isMessageFunction = (val) => isFunction(val);\r\n// implementation of `translate` function\r\nfunction translate(context, ...args) {\r\n    const { fallbackFormat, postTranslation, unresolving, fallbackLocale, messages } = context;\r\n    const [key, options] = parseTranslateArgs(...args);\r\n    const missingWarn = isBoolean(options.missingWarn)\r\n        ? options.missingWarn\r\n        : context.missingWarn;\r\n    const fallbackWarn = isBoolean(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : context.fallbackWarn;\r\n    const escapeParameter = isBoolean(options.escapeParameter)\r\n        ? options.escapeParameter\r\n        : context.escapeParameter;\r\n    const resolvedMessage = !!options.resolvedMessage;\r\n    // prettier-ignore\r\n    const defaultMsgOrKey = isString(options.default) || isBoolean(options.default) // default by function option\r\n        ? !isBoolean(options.default)\r\n            ? options.default\r\n            : key\r\n        : fallbackFormat // default by `fallbackFormat` option\r\n            ? key\r\n            : '';\r\n    const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== '';\r\n    const locale = isString(options.locale) ? options.locale : context.locale;\r\n    // escape params\r\n    escapeParameter && escapeParams(options);\r\n    // resolve message format\r\n    // eslint-disable-next-line prefer-const\r\n    let [format, targetLocale, message] = !resolvedMessage\r\n        ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn)\r\n        : [\r\n            key,\r\n            locale,\r\n            messages[locale] || {}\r\n        ];\r\n    // if you use default message, set it as message format!\r\n    let cacheBaseKey = key;\r\n    if (!resolvedMessage &&\r\n        !(isString(format) || isMessageFunction(format))) {\r\n        if (enableDefaultMsg) {\r\n            format = defaultMsgOrKey;\r\n            cacheBaseKey = format;\r\n        }\r\n    }\r\n    // checking message format and target locale\r\n    if (!resolvedMessage &&\r\n        (!(isString(format) || isMessageFunction(format)) ||\r\n            !isString(targetLocale))) {\r\n        return unresolving ? NOT_REOSLVED : key;\r\n    }\r\n    if ((process.env.NODE_ENV !== 'production') && isString(format) && context.messageCompiler == null) {\r\n        warn(`The message format compilation is not supported in this build. ` +\r\n            `Because message compiler isn't included. ` +\r\n            `You need to pre-compilation all message format. ` +\r\n            `So translate function return '${key}'.`);\r\n        return key;\r\n    }\r\n    // setup compile error detecting\r\n    let occurred = false;\r\n    const errorDetector = () => {\r\n        occurred = true;\r\n    };\r\n    // compile message format\r\n    const msg = !isMessageFunction(format)\r\n        ? compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, errorDetector)\r\n        : format;\r\n    // if occurred compile error, return the message format\r\n    if (occurred) {\r\n        return format;\r\n    }\r\n    // evaluate message with context\r\n    const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);\r\n    const msgContext = createMessageContext(ctxOptions);\r\n    const messaged = evaluateMessage(context, msg, msgContext);\r\n    // if use post translation option, proceed it with handler\r\n    const ret = postTranslation ? postTranslation(messaged) : messaged;\r\n    // NOTE: experimental !!\r\n    if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\r\n        // prettier-ignore\r\n        const payloads = {\r\n            timestamp: Date.now(),\r\n            key: isString(key)\r\n                ? key\r\n                : isMessageFunction(format)\r\n                    ? format.key\r\n                    : '',\r\n            locale: targetLocale || (isMessageFunction(format)\r\n                ? format.locale\r\n                : ''),\r\n            format: isString(format)\r\n                ? format\r\n                : isMessageFunction(format)\r\n                    ? format.source\r\n                    : '',\r\n            message: ret\r\n        };\r\n        payloads.meta = assign({}, context.__meta, getAdditionalMeta() || {});\r\n        translateDevTools(payloads);\r\n    }\r\n    return ret;\r\n}\r\nfunction escapeParams(options) {\r\n    if (isArray(options.list)) {\r\n        options.list = options.list.map(item => isString(item) ? escapeHtml(item) : item);\r\n    }\r\n    else if (isObject(options.named)) {\r\n        Object.keys(options.named).forEach(key => {\r\n            if (isString(options.named[key])) {\r\n                options.named[key] = escapeHtml(options.named[key]);\r\n            }\r\n        });\r\n    }\r\n}\r\nfunction resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {\r\n    const { messages, onWarn } = context;\r\n    const locales = getLocaleChain(context, fallbackLocale, locale);\r\n    let message = {};\r\n    let targetLocale;\r\n    let format = null;\r\n    let from = locale;\r\n    let to = null;\r\n    const type = 'translate';\r\n    for (let i = 0; i < locales.length; i++) {\r\n        targetLocale = to = locales[i];\r\n        if ((process.env.NODE_ENV !== 'production') &&\r\n            locale !== targetLocale &&\r\n            isTranslateFallbackWarn(fallbackWarn, key)) {\r\n            onWarn(getWarnMessage(1 /* FALLBACK_TO_TRANSLATE */, {\r\n                key,\r\n                target: targetLocale\r\n            }));\r\n        }\r\n        // for vue-devtools timeline event\r\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\r\n            const emitter = context.__v_emitter;\r\n            if (emitter) {\r\n                emitter.emit(\"fallback\" /* FALBACK */, {\r\n                    type,\r\n                    key,\r\n                    from,\r\n                    to,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n        }\r\n        message =\r\n            messages[targetLocale] || {};\r\n        // for vue-devtools timeline event\r\n        let start = null;\r\n        let startTag;\r\n        let endTag;\r\n        if ((process.env.NODE_ENV !== 'production') && inBrowser) {\r\n            start = window.performance.now();\r\n            startTag = 'intlify-message-resolve-start';\r\n            endTag = 'intlify-message-resolve-end';\r\n            mark && mark(startTag);\r\n        }\r\n        if ((format = resolveValue(message, key)) === null) {\r\n            // if null, resolve with object key path\r\n            format = message[key]; // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        }\r\n        // for vue-devtools timeline event\r\n        if ((process.env.NODE_ENV !== 'production') && inBrowser) {\r\n            const end = window.performance.now();\r\n            const emitter = context.__v_emitter;\r\n            if (emitter && start && format) {\r\n                emitter.emit(\"message-resolve\" /* MESSAGE_RESOLVE */, {\r\n                    type: \"message-resolve\" /* MESSAGE_RESOLVE */,\r\n                    key,\r\n                    message: format,\r\n                    time: end - start,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n            if (startTag && endTag && mark && measure) {\r\n                mark(endTag);\r\n                measure('intlify message resolve', startTag, endTag);\r\n            }\r\n        }\r\n        if (isString(format) || isFunction(format))\r\n            break;\r\n        const missingRet = handleMissing(context, key, targetLocale, missingWarn, type);\r\n        if (missingRet !== key) {\r\n            format = missingRet;\r\n        }\r\n        from = to;\r\n    }\r\n    return [format, targetLocale, message];\r\n}\r\nfunction compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, errorDetector) {\r\n    const { messageCompiler, warnHtmlMessage } = context;\r\n    if (isMessageFunction(format)) {\r\n        const msg = format;\r\n        msg.locale = msg.locale || targetLocale;\r\n        msg.key = msg.key || key;\r\n        return msg;\r\n    }\r\n    // for vue-devtools timeline event\r\n    let start = null;\r\n    let startTag;\r\n    let endTag;\r\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\r\n        start = window.performance.now();\r\n        startTag = 'intlify-message-compilation-start';\r\n        endTag = 'intlify-message-compilation-end';\r\n        mark && mark(startTag);\r\n    }\r\n    const msg = messageCompiler(format, getCompileOptions(context, targetLocale, cacheBaseKey, format, warnHtmlMessage, errorDetector));\r\n    // for vue-devtools timeline event\r\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\r\n        const end = window.performance.now();\r\n        const emitter = context.__v_emitter;\r\n        if (emitter && start) {\r\n            emitter.emit(\"message-compilation\" /* MESSAGE_COMPILATION */, {\r\n                type: \"message-compilation\" /* MESSAGE_COMPILATION */,\r\n                message: format,\r\n                time: end - start,\r\n                groupId: `${'translate'}:${key}`\r\n            });\r\n        }\r\n        if (startTag && endTag && mark && measure) {\r\n            mark(endTag);\r\n            measure('intlify message compilation', startTag, endTag);\r\n        }\r\n    }\r\n    msg.locale = targetLocale;\r\n    msg.key = key;\r\n    msg.source = format;\r\n    return msg;\r\n}\r\nfunction evaluateMessage(context, msg, msgCtx) {\r\n    // for vue-devtools timeline event\r\n    let start = null;\r\n    let startTag;\r\n    let endTag;\r\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\r\n        start = window.performance.now();\r\n        startTag = 'intlify-message-evaluation-start';\r\n        endTag = 'intlify-message-evaluation-end';\r\n        mark && mark(startTag);\r\n    }\r\n    const messaged = msg(msgCtx);\r\n    // for vue-devtools timeline event\r\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\r\n        const end = window.performance.now();\r\n        const emitter = context.__v_emitter;\r\n        if (emitter && start) {\r\n            emitter.emit(\"message-evaluation\" /* MESSAGE_EVALUATION */, {\r\n                type: \"message-evaluation\" /* MESSAGE_EVALUATION */,\r\n                value: messaged,\r\n                time: end - start,\r\n                groupId: `${'translate'}:${msg.key}`\r\n            });\r\n        }\r\n        if (startTag && endTag && mark && measure) {\r\n            mark(endTag);\r\n            measure('intlify message evaluation', startTag, endTag);\r\n        }\r\n    }\r\n    return messaged;\r\n}\r\n/** @internal */\r\nfunction parseTranslateArgs(...args) {\r\n    const [arg1, arg2, arg3] = args;\r\n    const options = {};\r\n    if (!isString(arg1) && !isNumber(arg1) && !isMessageFunction(arg1)) {\r\n        throw createCoreError(14 /* INVALID_ARGUMENT */);\r\n    }\r\n    // prettier-ignore\r\n    const key = isNumber(arg1)\r\n        ? String(arg1)\r\n        : isMessageFunction(arg1)\r\n            ? arg1\r\n            : arg1;\r\n    if (isNumber(arg2)) {\r\n        options.plural = arg2;\r\n    }\r\n    else if (isString(arg2)) {\r\n        options.default = arg2;\r\n    }\r\n    else if (isPlainObject(arg2) && !isEmptyObject(arg2)) {\r\n        options.named = arg2;\r\n    }\r\n    else if (isArray(arg2)) {\r\n        options.list = arg2;\r\n    }\r\n    if (isNumber(arg3)) {\r\n        options.plural = arg3;\r\n    }\r\n    else if (isString(arg3)) {\r\n        options.default = arg3;\r\n    }\r\n    else if (isPlainObject(arg3)) {\r\n        assign(options, arg3);\r\n    }\r\n    return [key, options];\r\n}\r\nfunction getCompileOptions(context, locale, key, source, warnHtmlMessage, errorDetector) {\r\n    return {\r\n        warnHtmlMessage,\r\n        onError: (err) => {\r\n            errorDetector && errorDetector(err);\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                const message = `Message compilation error: ${err.message}`;\r\n                const codeFrame = err.location &&\r\n                    generateCodeFrame(source, err.location.start.offset, err.location.end.offset);\r\n                const emitter = context\r\n                    .__v_emitter;\r\n                if (emitter) {\r\n                    emitter.emit(\"compile-error\" /* COMPILE_ERROR */, {\r\n                        message: source,\r\n                        error: err.message,\r\n                        start: err.location && err.location.start.offset,\r\n                        end: err.location && err.location.end.offset,\r\n                        groupId: `${'translate'}:${key}`\r\n                    });\r\n                }\r\n                console.error(codeFrame ? `${message}\\n${codeFrame}` : message);\r\n            }\r\n            else {\r\n                throw err;\r\n            }\r\n        },\r\n        onCacheKey: (source) => generateFormatCacheKey(locale, key, source)\r\n    };\r\n}\r\nfunction getMessageContextOptions(context, locale, message, options) {\r\n    const { modifiers, pluralRules } = context;\r\n    const resolveMessage = (key) => {\r\n        const val = resolveValue(message, key);\r\n        if (isString(val)) {\r\n            let occurred = false;\r\n            const errorDetector = () => {\r\n                occurred = true;\r\n            };\r\n            const msg = compileMessageFormat(context, key, locale, val, key, errorDetector);\r\n            return !occurred\r\n                ? msg\r\n                : NOOP_MESSAGE_FUNCTION;\r\n        }\r\n        else if (isMessageFunction(val)) {\r\n            return val;\r\n        }\r\n        else {\r\n            // TODO: should be implemented warning message\r\n            return NOOP_MESSAGE_FUNCTION;\r\n        }\r\n    };\r\n    const ctxOptions = {\r\n        locale,\r\n        modifiers,\r\n        pluralRules,\r\n        messages: resolveMessage\r\n    };\r\n    if (context.processor) {\r\n        ctxOptions.processor = context.processor;\r\n    }\r\n    if (options.list) {\r\n        ctxOptions.list = options.list;\r\n    }\r\n    if (options.named) {\r\n        ctxOptions.named = options.named;\r\n    }\r\n    if (isNumber(options.plural)) {\r\n        ctxOptions.pluralIndex = options.plural;\r\n    }\r\n    return ctxOptions;\r\n}\n\nconst intlDefined = typeof Intl !== 'undefined';\r\nconst Availabilities = {\r\n    dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',\r\n    numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'\r\n};\n\n// implementation of `datetime` function\r\nfunction datetime(context, ...args) {\r\n    const { datetimeFormats, unresolving, fallbackLocale, onWarn } = context;\r\n    const { __datetimeFormatters } = context;\r\n    if ((process.env.NODE_ENV !== 'production') && !Availabilities.dateTimeFormat) {\r\n        onWarn(getWarnMessage(4 /* CANNOT_FORMAT_DATE */));\r\n        return MISSING_RESOLVE_VALUE;\r\n    }\r\n    const [key, value, options, overrides] = parseDateTimeArgs(...args);\r\n    const missingWarn = isBoolean(options.missingWarn)\r\n        ? options.missingWarn\r\n        : context.missingWarn;\r\n    const fallbackWarn = isBoolean(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : context.fallbackWarn;\r\n    const part = !!options.part;\r\n    const locale = isString(options.locale) ? options.locale : context.locale;\r\n    const locales = getLocaleChain(context, fallbackLocale, locale);\r\n    if (!isString(key) || key === '') {\r\n        return new Intl.DateTimeFormat(locale).format(value);\r\n    }\r\n    // resolve format\r\n    let datetimeFormat = {};\r\n    let targetLocale;\r\n    let format = null;\r\n    let from = locale;\r\n    let to = null;\r\n    const type = 'datetime format';\r\n    for (let i = 0; i < locales.length; i++) {\r\n        targetLocale = to = locales[i];\r\n        if ((process.env.NODE_ENV !== 'production') &&\r\n            locale !== targetLocale &&\r\n            isTranslateFallbackWarn(fallbackWarn, key)) {\r\n            onWarn(getWarnMessage(5 /* FALLBACK_TO_DATE_FORMAT */, {\r\n                key,\r\n                target: targetLocale\r\n            }));\r\n        }\r\n        // for vue-devtools timeline event\r\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\r\n            const emitter = context.__v_emitter;\r\n            if (emitter) {\r\n                emitter.emit(\"fallback\" /* FALBACK */, {\r\n                    type,\r\n                    key,\r\n                    from,\r\n                    to,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n        }\r\n        datetimeFormat =\r\n            datetimeFormats[targetLocale] || {};\r\n        format = datetimeFormat[key];\r\n        if (isPlainObject(format))\r\n            break;\r\n        handleMissing(context, key, targetLocale, missingWarn, type);\r\n        from = to;\r\n    }\r\n    // checking format and target locale\r\n    if (!isPlainObject(format) || !isString(targetLocale)) {\r\n        return unresolving ? NOT_REOSLVED : key;\r\n    }\r\n    let id = `${targetLocale}__${key}`;\r\n    if (!isEmptyObject(overrides)) {\r\n        id = `${id}__${JSON.stringify(overrides)}`;\r\n    }\r\n    let formatter = __datetimeFormatters.get(id);\r\n    if (!formatter) {\r\n        formatter = new Intl.DateTimeFormat(targetLocale, assign({}, format, overrides));\r\n        __datetimeFormatters.set(id, formatter);\r\n    }\r\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\r\n}\r\n/** @internal */\r\nfunction parseDateTimeArgs(...args) {\r\n    const [arg1, arg2, arg3, arg4] = args;\r\n    let options = {};\r\n    let overrides = {};\r\n    let value;\r\n    if (isString(arg1)) {\r\n        // Only allow ISO strings - other date formats are often supported,\r\n        // but may cause different results in different browsers.\r\n        if (!/\\d{4}-\\d{2}-\\d{2}(T.*)?/.test(arg1)) {\r\n            throw createCoreError(16 /* INVALID_ISO_DATE_ARGUMENT */);\r\n        }\r\n        value = new Date(arg1);\r\n        try {\r\n            // This will fail if the date is not valid\r\n            value.toISOString();\r\n        }\r\n        catch (e) {\r\n            throw createCoreError(16 /* INVALID_ISO_DATE_ARGUMENT */);\r\n        }\r\n    }\r\n    else if (isDate(arg1)) {\r\n        if (isNaN(arg1.getTime())) {\r\n            throw createCoreError(15 /* INVALID_DATE_ARGUMENT */);\r\n        }\r\n        value = arg1;\r\n    }\r\n    else if (isNumber(arg1)) {\r\n        value = arg1;\r\n    }\r\n    else {\r\n        throw createCoreError(14 /* INVALID_ARGUMENT */);\r\n    }\r\n    if (isString(arg2)) {\r\n        options.key = arg2;\r\n    }\r\n    else if (isPlainObject(arg2)) {\r\n        options = arg2;\r\n    }\r\n    if (isString(arg3)) {\r\n        options.locale = arg3;\r\n    }\r\n    else if (isPlainObject(arg3)) {\r\n        overrides = arg3;\r\n    }\r\n    if (isPlainObject(arg4)) {\r\n        overrides = arg4;\r\n    }\r\n    return [options.key || '', value, options, overrides];\r\n}\r\n/** @internal */\r\nfunction clearDateTimeFormat(ctx, locale, format) {\r\n    const context = ctx;\r\n    for (const key in format) {\r\n        const id = `${locale}__${key}`;\r\n        if (!context.__datetimeFormatters.has(id)) {\r\n            continue;\r\n        }\r\n        context.__datetimeFormatters.delete(id);\r\n    }\r\n}\n\n// implementation of `number` function\r\nfunction number(context, ...args) {\r\n    const { numberFormats, unresolving, fallbackLocale, onWarn } = context;\r\n    const { __numberFormatters } = context;\r\n    if ((process.env.NODE_ENV !== 'production') && !Availabilities.numberFormat) {\r\n        onWarn(getWarnMessage(2 /* CANNOT_FORMAT_NUMBER */));\r\n        return MISSING_RESOLVE_VALUE;\r\n    }\r\n    const [key, value, options, overrides] = parseNumberArgs(...args);\r\n    const missingWarn = isBoolean(options.missingWarn)\r\n        ? options.missingWarn\r\n        : context.missingWarn;\r\n    const fallbackWarn = isBoolean(options.fallbackWarn)\r\n        ? options.fallbackWarn\r\n        : context.fallbackWarn;\r\n    const part = !!options.part;\r\n    const locale = isString(options.locale) ? options.locale : context.locale;\r\n    const locales = getLocaleChain(context, fallbackLocale, locale);\r\n    if (!isString(key) || key === '') {\r\n        return new Intl.NumberFormat(locale).format(value);\r\n    }\r\n    // resolve format\r\n    let numberFormat = {};\r\n    let targetLocale;\r\n    let format = null;\r\n    let from = locale;\r\n    let to = null;\r\n    const type = 'number format';\r\n    for (let i = 0; i < locales.length; i++) {\r\n        targetLocale = to = locales[i];\r\n        if ((process.env.NODE_ENV !== 'production') &&\r\n            locale !== targetLocale &&\r\n            isTranslateFallbackWarn(fallbackWarn, key)) {\r\n            onWarn(getWarnMessage(3 /* FALLBACK_TO_NUMBER_FORMAT */, {\r\n                key,\r\n                target: targetLocale\r\n            }));\r\n        }\r\n        // for vue-devtools timeline event\r\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\r\n            const emitter = context.__v_emitter;\r\n            if (emitter) {\r\n                emitter.emit(\"fallback\" /* FALBACK */, {\r\n                    type,\r\n                    key,\r\n                    from,\r\n                    to,\r\n                    groupId: `${type}:${key}`\r\n                });\r\n            }\r\n        }\r\n        numberFormat =\r\n            numberFormats[targetLocale] || {};\r\n        format = numberFormat[key];\r\n        if (isPlainObject(format))\r\n            break;\r\n        handleMissing(context, key, targetLocale, missingWarn, type);\r\n        from = to;\r\n    }\r\n    // checking format and target locale\r\n    if (!isPlainObject(format) || !isString(targetLocale)) {\r\n        return unresolving ? NOT_REOSLVED : key;\r\n    }\r\n    let id = `${targetLocale}__${key}`;\r\n    if (!isEmptyObject(overrides)) {\r\n        id = `${id}__${JSON.stringify(overrides)}`;\r\n    }\r\n    let formatter = __numberFormatters.get(id);\r\n    if (!formatter) {\r\n        formatter = new Intl.NumberFormat(targetLocale, assign({}, format, overrides));\r\n        __numberFormatters.set(id, formatter);\r\n    }\r\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\r\n}\r\n/** @internal */\r\nfunction parseNumberArgs(...args) {\r\n    const [arg1, arg2, arg3, arg4] = args;\r\n    let options = {};\r\n    let overrides = {};\r\n    if (!isNumber(arg1)) {\r\n        throw createCoreError(14 /* INVALID_ARGUMENT */);\r\n    }\r\n    const value = arg1;\r\n    if (isString(arg2)) {\r\n        options.key = arg2;\r\n    }\r\n    else if (isPlainObject(arg2)) {\r\n        options = arg2;\r\n    }\r\n    if (isString(arg3)) {\r\n        options.locale = arg3;\r\n    }\r\n    else if (isPlainObject(arg3)) {\r\n        overrides = arg3;\r\n    }\r\n    if (isPlainObject(arg4)) {\r\n        overrides = arg4;\r\n    }\r\n    return [options.key || '', value, options, overrides];\r\n}\r\n/** @internal */\r\nfunction clearNumberFormat(ctx, locale, format) {\r\n    const context = ctx;\r\n    for (const key in format) {\r\n        const id = `${locale}__${key}`;\r\n        if (!context.__numberFormatters.has(id)) {\r\n            continue;\r\n        }\r\n        context.__numberFormatters.delete(id);\r\n    }\r\n}\n\n{\r\n    if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\r\n        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\r\n    }\r\n}\n\nexport { MISSING_RESOLVE_VALUE, NOT_REOSLVED, VERSION, clearCompileCache, clearDateTimeFormat, clearNumberFormat, compileToFunction, createCoreContext, createCoreError, datetime, getAdditionalMeta, getDevToolsHook, getLocaleChain, getWarnMessage, handleMissing, initI18nDevTools, isMessageFunction, isTranslateFallbackWarn, isTranslateMissingWarn, number, parseDateTimeArgs, parseNumberArgs, parseTranslateArgs, registerMessageCompiler, setAdditionalMeta, setDevToolsHook, translate, translateDevTools, updateFallbackLocale };\n", "/*!\n  * vue-i18n v9.1.10\n  * (c) 2022 kazuya kawa<PERSON>\n  * Released under the MIT License.\n  */\n'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar shared = require('@intlify/shared');\nvar coreBase = require('@intlify/core-base');\nvar vue = require('vue');\n\n/**\r\n * Vue I18n Version\r\n *\r\n * @remarks\r\n * Semver format. Same format as the package.json `version` field.\r\n *\r\n * @VueI18nGeneral\r\n */\r\nconst VERSION = '9.1.10';\n\nconst warnMessages = {\r\n    [6 /* FALLBACK_TO_ROOT */]: `Fall back to {type} '{key}' with root locale.`,\r\n    [7 /* NOT_SUPPORTED_PRESERVE */]: `Not supported 'preserve'.`,\r\n    [8 /* NOT_SUPPORTED_FORMATTER */]: `Not supported 'formatter'.`,\r\n    [9 /* NOT_SUPPORTED_PRESERVE_DIRECTIVE */]: `Not supported 'preserveDirectiveContent'.`,\r\n    [10 /* NOT_SUPPORTED_GET_CHOICE_INDEX */]: `Not supported 'getChoiceIndex'.`,\r\n    [11 /* COMPONENT_NAME_LEGACY_COMPATIBLE */]: `Component name legacy compatible: '{name}' -> 'i18n'`,\r\n    [12 /* NOT_FOUND_PARENT_SCOPE */]: `Not found parent scope. use the global scope.`\r\n};\r\nfunction getWarnMessage(code, ...args) {\r\n    return shared.format(warnMessages[code], ...args);\r\n}\n\nfunction createI18nError(code, ...args) {\r\n    return coreBase.createCompileError(code, null, { messages: errorMessages, args } );\r\n}\r\nconst errorMessages = {\r\n    [14 /* UNEXPECTED_RETURN_TYPE */]: 'Unexpected return type in composer',\r\n    [15 /* INVALID_ARGUMENT */]: 'Invalid argument',\r\n    [16 /* MUST_BE_CALL_SETUP_TOP */]: 'Must be called at the top of a `setup` function',\r\n    [17 /* NOT_INSLALLED */]: 'Need to install with `app.use` function',\r\n    [22 /* UNEXPECTED_ERROR */]: 'Unexpected error',\r\n    [18 /* NOT_AVAILABLE_IN_LEGACY_MODE */]: 'Not available in legacy mode',\r\n    [19 /* REQUIRED_VALUE */]: `Required in value: {0}`,\r\n    [20 /* INVALID_VALUE */]: `Invalid value`,\r\n    [21 /* CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN */]: `Cannot setup vue-devtools plugin`\r\n};\n\nconst DEVTOOLS_META = '__INTLIFY_META__';\r\nconst TransrateVNodeSymbol = shared.makeSymbol('__transrateVNode');\r\nconst DatetimePartsSymbol = shared.makeSymbol('__datetimeParts');\r\nconst NumberPartsSymbol = shared.makeSymbol('__numberParts');\r\nconst EnableEmitter = shared.makeSymbol('__enableEmitter');\r\nconst DisableEmitter = shared.makeSymbol('__disableEmitter');\r\nconst SetPluralRulesSymbol = shared.makeSymbol('__setPluralRules');\r\nshared.makeSymbol('__intlifyMeta');\r\nconst InejctWithOption = shared.makeSymbol('__injectWithOption');\r\nlet composerID = 0;\r\nfunction defineCoreMissingHandler(missing) {\r\n    return ((ctx, locale, key, type) => {\r\n        return missing(locale, key, vue.getCurrentInstance() || undefined, type);\r\n    });\r\n}\r\nfunction getLocaleMessages(locale, options) {\r\n    const { messages, __i18n } = options;\r\n    // prettier-ignore\r\n    const ret = shared.isPlainObject(messages)\r\n        ? messages\r\n        : shared.isArray(__i18n)\r\n            ? {}\r\n            : { [locale]: {} };\r\n    // merge locale messages of i18n custom block\r\n    if (shared.isArray(__i18n)) {\r\n        __i18n.forEach(({ locale, resource }) => {\r\n            if (locale) {\r\n                ret[locale] = ret[locale] || {};\r\n                deepCopy(resource, ret[locale]);\r\n            }\r\n            else {\r\n                deepCopy(resource, ret);\r\n            }\r\n        });\r\n    }\r\n    // handle messages for flat json\r\n    if (options.flatJson) {\r\n        for (const key in ret) {\r\n            if (shared.hasOwn(ret, key)) {\r\n                coreBase.handleFlatJson(ret[key]);\r\n            }\r\n        }\r\n    }\r\n    return ret;\r\n}\r\nconst isNotObjectOrIsArray = (val) => !shared.isObject(val) || shared.isArray(val);\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nfunction deepCopy(src, des) {\r\n    // src and des should both be objects, and non of then can be a array\r\n    if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {\r\n        throw createI18nError(20 /* INVALID_VALUE */);\r\n    }\r\n    for (const key in src) {\r\n        if (shared.hasOwn(src, key)) {\r\n            if (isNotObjectOrIsArray(src[key]) || isNotObjectOrIsArray(des[key])) {\r\n                // replace with src[key] when:\r\n                // src[key] or des[key] is not a object, or\r\n                // src[key] or des[key] is a array\r\n                des[key] = src[key];\r\n            }\r\n            else {\r\n                // src[key] and des[key] are both object, merge them\r\n                deepCopy(src[key], des[key]);\r\n            }\r\n        }\r\n    }\r\n}\r\n// for Intlify DevTools\r\nconst getMetaInfo = /* #__PURE__*/ () => {\r\n    const instance = vue.getCurrentInstance();\r\n    return instance && instance.type[DEVTOOLS_META] // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        ? { [DEVTOOLS_META]: instance.type[DEVTOOLS_META] } // eslint-disable-line @typescript-eslint/no-explicit-any\r\n        : null;\r\n};\r\n/**\r\n * Create composer interface factory\r\n *\r\n * @internal\r\n */\r\nfunction createComposer(options = {}) {\r\n    const { __root } = options;\r\n    const _isGlobal = __root === undefined;\r\n    let _inheritLocale = shared.isBoolean(options.inheritLocale)\r\n        ? options.inheritLocale\r\n        : true;\r\n    const _locale = vue.ref(\r\n    // prettier-ignore\r\n    __root && _inheritLocale\r\n        ? __root.locale.value\r\n        : shared.isString(options.locale)\r\n            ? options.locale\r\n            : 'en-US');\r\n    const _fallbackLocale = vue.ref(\r\n    // prettier-ignore\r\n    __root && _inheritLocale\r\n        ? __root.fallbackLocale.value\r\n        : shared.isString(options.fallbackLocale) ||\r\n            shared.isArray(options.fallbackLocale) ||\r\n            shared.isPlainObject(options.fallbackLocale) ||\r\n            options.fallbackLocale === false\r\n            ? options.fallbackLocale\r\n            : _locale.value);\r\n    const _messages = vue.ref(getLocaleMessages(_locale.value, options));\r\n    const _datetimeFormats = vue.ref(shared.isPlainObject(options.datetimeFormats)\r\n        ? options.datetimeFormats\r\n        : { [_locale.value]: {} });\r\n    const _numberFormats = vue.ref(shared.isPlainObject(options.numberFormats)\r\n        ? options.numberFormats\r\n        : { [_locale.value]: {} });\r\n    // warning suppress options\r\n    // prettier-ignore\r\n    let _missingWarn = __root\r\n        ? __root.missingWarn\r\n        : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn)\r\n            ? options.missingWarn\r\n            : true;\r\n    // prettier-ignore\r\n    let _fallbackWarn = __root\r\n        ? __root.fallbackWarn\r\n        : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn)\r\n            ? options.fallbackWarn\r\n            : true;\r\n    // prettier-ignore\r\n    let _fallbackRoot = __root\r\n        ? __root.fallbackRoot\r\n        : shared.isBoolean(options.fallbackRoot)\r\n            ? options.fallbackRoot\r\n            : true;\r\n    // configure fall back to root\r\n    let _fallbackFormat = !!options.fallbackFormat;\r\n    // runtime missing\r\n    let _missing = shared.isFunction(options.missing) ? options.missing : null;\r\n    let _runtimeMissing = shared.isFunction(options.missing)\r\n        ? defineCoreMissingHandler(options.missing)\r\n        : null;\r\n    // postTranslation handler\r\n    let _postTranslation = shared.isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : null;\r\n    let _warnHtmlMessage = shared.isBoolean(options.warnHtmlMessage)\r\n        ? options.warnHtmlMessage\r\n        : true;\r\n    let _escapeParameter = !!options.escapeParameter;\r\n    // custom linked modifiers\r\n    // prettier-ignore\r\n    const _modifiers = __root\r\n        ? __root.modifiers\r\n        : shared.isPlainObject(options.modifiers)\r\n            ? options.modifiers\r\n            : {};\r\n    // pluralRules\r\n    let _pluralRules = options.pluralRules || (__root && __root.pluralRules);\r\n    // runtime context\r\n    // eslint-disable-next-line prefer-const\r\n    let _context;\r\n    function getCoreContext() {\r\n        return coreBase.createCoreContext({\r\n            version: VERSION,\r\n            locale: _locale.value,\r\n            fallbackLocale: _fallbackLocale.value,\r\n            messages: _messages.value,\r\n            datetimeFormats: _datetimeFormats.value,\r\n            numberFormats: _numberFormats.value,\r\n            modifiers: _modifiers,\r\n            pluralRules: _pluralRules,\r\n            missing: _runtimeMissing === null ? undefined : _runtimeMissing,\r\n            missingWarn: _missingWarn,\r\n            fallbackWarn: _fallbackWarn,\r\n            fallbackFormat: _fallbackFormat,\r\n            unresolving: true,\r\n            postTranslation: _postTranslation === null ? undefined : _postTranslation,\r\n            warnHtmlMessage: _warnHtmlMessage,\r\n            escapeParameter: _escapeParameter,\r\n            __datetimeFormatters: shared.isPlainObject(_context)\r\n                ? _context.__datetimeFormatters\r\n                : undefined,\r\n            __numberFormatters: shared.isPlainObject(_context)\r\n                ? _context.__numberFormatters\r\n                : undefined,\r\n            __v_emitter: shared.isPlainObject(_context)\r\n                ? _context.__v_emitter\r\n                : undefined,\r\n            __meta: { framework: 'vue' }\r\n        });\r\n    }\r\n    _context = getCoreContext();\r\n    coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n    // track reactivity\r\n    function trackReactivityValues() {\r\n        return [\r\n            _locale.value,\r\n            _fallbackLocale.value,\r\n            _messages.value,\r\n            _datetimeFormats.value,\r\n            _numberFormats.value\r\n        ];\r\n    }\r\n    // locale\r\n    const locale = vue.computed({\r\n        get: () => _locale.value,\r\n        set: val => {\r\n            _locale.value = val;\r\n            _context.locale = _locale.value;\r\n        }\r\n    });\r\n    // fallbackLocale\r\n    const fallbackLocale = vue.computed({\r\n        get: () => _fallbackLocale.value,\r\n        set: val => {\r\n            _fallbackLocale.value = val;\r\n            _context.fallbackLocale = _fallbackLocale.value;\r\n            coreBase.updateFallbackLocale(_context, _locale.value, val);\r\n        }\r\n    });\r\n    // messages\r\n    const messages = vue.computed(() => _messages.value);\r\n    // datetimeFormats\r\n    const datetimeFormats = vue.computed(() => _datetimeFormats.value);\r\n    // numberFormats\r\n    const numberFormats = vue.computed(() => _numberFormats.value);\r\n    // getPostTranslationHandler\r\n    function getPostTranslationHandler() {\r\n        return shared.isFunction(_postTranslation) ? _postTranslation : null;\r\n    }\r\n    // setPostTranslationHandler\r\n    function setPostTranslationHandler(handler) {\r\n        _postTranslation = handler;\r\n        _context.postTranslation = handler;\r\n    }\r\n    // getMissingHandler\r\n    function getMissingHandler() {\r\n        return _missing;\r\n    }\r\n    // setMissingHandler\r\n    function setMissingHandler(handler) {\r\n        if (handler !== null) {\r\n            _runtimeMissing = defineCoreMissingHandler(handler);\r\n        }\r\n        _missing = handler;\r\n        _context.missing = _runtimeMissing;\r\n    }\r\n    function isResolvedTranslateMessage(type, arg // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    ) {\r\n        return type !== 'translate' || !!arg.resolvedMessage === false;\r\n    }\r\n    function wrapWithDeps(fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) {\r\n        trackReactivityValues(); // track reactive dependency\r\n        // NOTE: experimental !!\r\n        let ret;\r\n        {\r\n            try {\r\n                coreBase.setAdditionalMeta(getMetaInfo());\r\n                ret = fn(_context);\r\n            }\r\n            finally {\r\n                coreBase.setAdditionalMeta(null);\r\n            }\r\n        }\r\n        if (shared.isNumber(ret) && ret === coreBase.NOT_REOSLVED) {\r\n            const [key, arg2] = argumentParser();\r\n            if (__root &&\r\n                shared.isString(key) &&\r\n                isResolvedTranslateMessage(warnType, arg2)) {\r\n                if (_fallbackRoot &&\r\n                    (coreBase.isTranslateFallbackWarn(_fallbackWarn, key) ||\r\n                        coreBase.isTranslateMissingWarn(_missingWarn, key))) {\r\n                    shared.warn(getWarnMessage(6 /* FALLBACK_TO_ROOT */, {\r\n                        key,\r\n                        type: warnType\r\n                    }));\r\n                }\r\n                // for vue-devtools timeline event\r\n                {\r\n                    const { __v_emitter: emitter } = _context;\r\n                    if (emitter && _fallbackRoot) {\r\n                        emitter.emit(\"fallback\" /* FALBACK */, {\r\n                            type: warnType,\r\n                            key,\r\n                            to: 'global',\r\n                            groupId: `${warnType}:${key}`\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n            return __root && _fallbackRoot\r\n                ? fallbackSuccess(__root)\r\n                : fallbackFail(key);\r\n        }\r\n        else if (successCondition(ret)) {\r\n            return ret;\r\n        }\r\n        else {\r\n            /* istanbul ignore next */\r\n            throw createI18nError(14 /* UNEXPECTED_RETURN_TYPE */);\r\n        }\r\n    }\r\n    // t\r\n    function t(...args) {\r\n        return wrapWithDeps(context => coreBase.translate(context, ...args), () => coreBase.parseTranslateArgs(...args), 'translate', root => root.t(...args), key => key, val => shared.isString(val));\r\n    }\r\n    // rt\r\n    function rt(...args) {\r\n        const [arg1, arg2, arg3] = args;\r\n        if (arg3 && !shared.isObject(arg3)) {\r\n            throw createI18nError(15 /* INVALID_ARGUMENT */);\r\n        }\r\n        return t(...[arg1, arg2, shared.assign({ resolvedMessage: true }, arg3 || {})]);\r\n    }\r\n    // d\r\n    function d(...args) {\r\n        return wrapWithDeps(context => coreBase.datetime(context, ...args), () => coreBase.parseDateTimeArgs(...args), 'datetime format', root => root.d(...args), () => coreBase.MISSING_RESOLVE_VALUE, val => shared.isString(val));\r\n    }\r\n    // n\r\n    function n(...args) {\r\n        return wrapWithDeps(context => coreBase.number(context, ...args), () => coreBase.parseNumberArgs(...args), 'number format', root => root.n(...args), () => coreBase.MISSING_RESOLVE_VALUE, val => shared.isString(val));\r\n    }\r\n    // for custom processor\r\n    function normalize(values) {\r\n        return values.map(val => shared.isString(val) ? vue.createVNode(vue.Text, null, val, 0) : val);\r\n    }\r\n    const interpolate = (val) => val;\r\n    const processor = {\r\n        normalize,\r\n        interpolate,\r\n        type: 'vnode'\r\n    };\r\n    // transrateVNode, using for `i18n-t` component\r\n    function transrateVNode(...args) {\r\n        return wrapWithDeps(context => {\r\n            let ret;\r\n            const _context = context;\r\n            try {\r\n                _context.processor = processor;\r\n                ret = coreBase.translate(_context, ...args);\r\n            }\r\n            finally {\r\n                _context.processor = null;\r\n            }\r\n            return ret;\r\n        }, () => coreBase.parseTranslateArgs(...args), 'translate', \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        root => root[TransrateVNodeSymbol](...args), key => [vue.createVNode(vue.Text, null, key, 0)], val => shared.isArray(val));\r\n    }\r\n    // numberParts, using for `i18n-n` component\r\n    function numberParts(...args) {\r\n        return wrapWithDeps(context => coreBase.number(context, ...args), () => coreBase.parseNumberArgs(...args), 'number format', \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        root => root[NumberPartsSymbol](...args), () => [], val => shared.isString(val) || shared.isArray(val));\r\n    }\r\n    // datetimeParts, using for `i18n-d` component\r\n    function datetimeParts(...args) {\r\n        return wrapWithDeps(context => coreBase.datetime(context, ...args), () => coreBase.parseDateTimeArgs(...args), 'datetime format', \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        root => root[DatetimePartsSymbol](...args), () => [], val => shared.isString(val) || shared.isArray(val));\r\n    }\r\n    function setPluralRules(rules) {\r\n        _pluralRules = rules;\r\n        _context.pluralRules = _pluralRules;\r\n    }\r\n    // te\r\n    function te(key, locale) {\r\n        const targetLocale = shared.isString(locale) ? locale : _locale.value;\r\n        const message = getLocaleMessage(targetLocale);\r\n        return coreBase.resolveValue(message, key) !== null;\r\n    }\r\n    function resolveMessages(key) {\r\n        let messages = null;\r\n        const locales = coreBase.getLocaleChain(_context, _fallbackLocale.value, _locale.value);\r\n        for (let i = 0; i < locales.length; i++) {\r\n            const targetLocaleMessages = _messages.value[locales[i]] || {};\r\n            const messageValue = coreBase.resolveValue(targetLocaleMessages, key);\r\n            if (messageValue != null) {\r\n                messages = messageValue;\r\n                break;\r\n            }\r\n        }\r\n        return messages;\r\n    }\r\n    // tm\r\n    function tm(key) {\r\n        const messages = resolveMessages(key);\r\n        // prettier-ignore\r\n        return messages != null\r\n            ? messages\r\n            : __root\r\n                ? __root.tm(key) || {}\r\n                : {};\r\n    }\r\n    // getLocaleMessage\r\n    function getLocaleMessage(locale) {\r\n        return (_messages.value[locale] || {});\r\n    }\r\n    // setLocaleMessage\r\n    function setLocaleMessage(locale, message) {\r\n        _messages.value[locale] = message;\r\n        _context.messages = _messages.value;\r\n    }\r\n    // mergeLocaleMessage\r\n    function mergeLocaleMessage(locale, message) {\r\n        _messages.value[locale] = _messages.value[locale] || {};\r\n        deepCopy(message, _messages.value[locale]);\r\n        _context.messages = _messages.value;\r\n    }\r\n    // getDateTimeFormat\r\n    function getDateTimeFormat(locale) {\r\n        return _datetimeFormats.value[locale] || {};\r\n    }\r\n    // setDateTimeFormat\r\n    function setDateTimeFormat(locale, format) {\r\n        _datetimeFormats.value[locale] = format;\r\n        _context.datetimeFormats = _datetimeFormats.value;\r\n        coreBase.clearDateTimeFormat(_context, locale, format);\r\n    }\r\n    // mergeDateTimeFormat\r\n    function mergeDateTimeFormat(locale, format) {\r\n        _datetimeFormats.value[locale] = shared.assign(_datetimeFormats.value[locale] || {}, format);\r\n        _context.datetimeFormats = _datetimeFormats.value;\r\n        coreBase.clearDateTimeFormat(_context, locale, format);\r\n    }\r\n    // getNumberFormat\r\n    function getNumberFormat(locale) {\r\n        return _numberFormats.value[locale] || {};\r\n    }\r\n    // setNumberFormat\r\n    function setNumberFormat(locale, format) {\r\n        _numberFormats.value[locale] = format;\r\n        _context.numberFormats = _numberFormats.value;\r\n        coreBase.clearNumberFormat(_context, locale, format);\r\n    }\r\n    // mergeNumberFormat\r\n    function mergeNumberFormat(locale, format) {\r\n        _numberFormats.value[locale] = shared.assign(_numberFormats.value[locale] || {}, format);\r\n        _context.numberFormats = _numberFormats.value;\r\n        coreBase.clearNumberFormat(_context, locale, format);\r\n    }\r\n    // for debug\r\n    composerID++;\r\n    // watch root locale & fallbackLocale\r\n    if (__root) {\r\n        vue.watch(__root.locale, (val) => {\r\n            if (_inheritLocale) {\r\n                _locale.value = val;\r\n                _context.locale = val;\r\n                coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n            }\r\n        });\r\n        vue.watch(__root.fallbackLocale, (val) => {\r\n            if (_inheritLocale) {\r\n                _fallbackLocale.value = val;\r\n                _context.fallbackLocale = val;\r\n                coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n            }\r\n        });\r\n    }\r\n    // define composition API!\r\n    const composer = {\r\n        id: composerID,\r\n        locale,\r\n        fallbackLocale,\r\n        get inheritLocale() {\r\n            return _inheritLocale;\r\n        },\r\n        set inheritLocale(val) {\r\n            _inheritLocale = val;\r\n            if (val && __root) {\r\n                _locale.value = __root.locale.value;\r\n                _fallbackLocale.value = __root.fallbackLocale.value;\r\n                coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\r\n            }\r\n        },\r\n        get availableLocales() {\r\n            return Object.keys(_messages.value).sort();\r\n        },\r\n        messages,\r\n        datetimeFormats,\r\n        numberFormats,\r\n        get modifiers() {\r\n            return _modifiers;\r\n        },\r\n        get pluralRules() {\r\n            return _pluralRules || {};\r\n        },\r\n        get isGlobal() {\r\n            return _isGlobal;\r\n        },\r\n        get missingWarn() {\r\n            return _missingWarn;\r\n        },\r\n        set missingWarn(val) {\r\n            _missingWarn = val;\r\n            _context.missingWarn = _missingWarn;\r\n        },\r\n        get fallbackWarn() {\r\n            return _fallbackWarn;\r\n        },\r\n        set fallbackWarn(val) {\r\n            _fallbackWarn = val;\r\n            _context.fallbackWarn = _fallbackWarn;\r\n        },\r\n        get fallbackRoot() {\r\n            return _fallbackRoot;\r\n        },\r\n        set fallbackRoot(val) {\r\n            _fallbackRoot = val;\r\n        },\r\n        get fallbackFormat() {\r\n            return _fallbackFormat;\r\n        },\r\n        set fallbackFormat(val) {\r\n            _fallbackFormat = val;\r\n            _context.fallbackFormat = _fallbackFormat;\r\n        },\r\n        get warnHtmlMessage() {\r\n            return _warnHtmlMessage;\r\n        },\r\n        set warnHtmlMessage(val) {\r\n            _warnHtmlMessage = val;\r\n            _context.warnHtmlMessage = val;\r\n        },\r\n        get escapeParameter() {\r\n            return _escapeParameter;\r\n        },\r\n        set escapeParameter(val) {\r\n            _escapeParameter = val;\r\n            _context.escapeParameter = val;\r\n        },\r\n        t,\r\n        rt,\r\n        d,\r\n        n,\r\n        te,\r\n        tm,\r\n        getLocaleMessage,\r\n        setLocaleMessage,\r\n        mergeLocaleMessage,\r\n        getDateTimeFormat,\r\n        setDateTimeFormat,\r\n        mergeDateTimeFormat,\r\n        getNumberFormat,\r\n        setNumberFormat,\r\n        mergeNumberFormat,\r\n        getPostTranslationHandler,\r\n        setPostTranslationHandler,\r\n        getMissingHandler,\r\n        setMissingHandler,\r\n        [TransrateVNodeSymbol]: transrateVNode,\r\n        [NumberPartsSymbol]: numberParts,\r\n        [DatetimePartsSymbol]: datetimeParts,\r\n        [SetPluralRulesSymbol]: setPluralRules,\r\n        [InejctWithOption]: options.__injectWithOption // eslint-disable-line @typescript-eslint/no-explicit-any\r\n    };\r\n    // for vue-devtools timeline event\r\n    {\r\n        composer[EnableEmitter] = (emitter) => {\r\n            _context.__v_emitter = emitter;\r\n        };\r\n        composer[DisableEmitter] = () => {\r\n            _context.__v_emitter = undefined;\r\n        };\r\n    }\r\n    return composer;\r\n}\n\n/**\r\n * Convert to I18n Composer Options from VueI18n Options\r\n *\r\n * @internal\r\n */\r\nfunction convertComposerOptions(options) {\r\n    const locale = shared.isString(options.locale) ? options.locale : 'en-US';\r\n    const fallbackLocale = shared.isString(options.fallbackLocale) ||\r\n        shared.isArray(options.fallbackLocale) ||\r\n        shared.isPlainObject(options.fallbackLocale) ||\r\n        options.fallbackLocale === false\r\n        ? options.fallbackLocale\r\n        : locale;\r\n    const missing = shared.isFunction(options.missing) ? options.missing : undefined;\r\n    const missingWarn = shared.isBoolean(options.silentTranslationWarn) ||\r\n        shared.isRegExp(options.silentTranslationWarn)\r\n        ? !options.silentTranslationWarn\r\n        : true;\r\n    const fallbackWarn = shared.isBoolean(options.silentFallbackWarn) ||\r\n        shared.isRegExp(options.silentFallbackWarn)\r\n        ? !options.silentFallbackWarn\r\n        : true;\r\n    const fallbackRoot = shared.isBoolean(options.fallbackRoot)\r\n        ? options.fallbackRoot\r\n        : true;\r\n    const fallbackFormat = !!options.formatFallbackMessages;\r\n    const modifiers = shared.isPlainObject(options.modifiers) ? options.modifiers : {};\r\n    const pluralizationRules = options.pluralizationRules;\r\n    const postTranslation = shared.isFunction(options.postTranslation)\r\n        ? options.postTranslation\r\n        : undefined;\r\n    const warnHtmlMessage = shared.isString(options.warnHtmlInMessage)\r\n        ? options.warnHtmlInMessage !== 'off'\r\n        : true;\r\n    const escapeParameter = !!options.escapeParameterHtml;\r\n    const inheritLocale = shared.isBoolean(options.sync) ? options.sync : true;\r\n    if (options.formatter) {\r\n        shared.warn(getWarnMessage(8 /* NOT_SUPPORTED_FORMATTER */));\r\n    }\r\n    if (options.preserveDirectiveContent) {\r\n        shared.warn(getWarnMessage(9 /* NOT_SUPPORTED_PRESERVE_DIRECTIVE */));\r\n    }\r\n    let messages = options.messages;\r\n    if (shared.isPlainObject(options.sharedMessages)) {\r\n        const sharedMessages = options.sharedMessages;\r\n        const locales = Object.keys(sharedMessages);\r\n        messages = locales.reduce((messages, locale) => {\r\n            const message = messages[locale] || (messages[locale] = {});\r\n            shared.assign(message, sharedMessages[locale]);\r\n            return messages;\r\n        }, (messages || {}));\r\n    }\r\n    const { __i18n, __root, __injectWithOption } = options;\r\n    const datetimeFormats = options.datetimeFormats;\r\n    const numberFormats = options.numberFormats;\r\n    const flatJson = options.flatJson;\r\n    return {\r\n        locale,\r\n        fallbackLocale,\r\n        messages,\r\n        flatJson,\r\n        datetimeFormats,\r\n        numberFormats,\r\n        missing,\r\n        missingWarn,\r\n        fallbackWarn,\r\n        fallbackRoot,\r\n        fallbackFormat,\r\n        modifiers,\r\n        pluralRules: pluralizationRules,\r\n        postTranslation,\r\n        warnHtmlMessage,\r\n        escapeParameter,\r\n        inheritLocale,\r\n        __i18n,\r\n        __root,\r\n        __injectWithOption\r\n    };\r\n}\r\n/**\r\n * create VueI18n interface factory\r\n *\r\n * @internal\r\n */\r\nfunction createVueI18n(options = {}) {\r\n    const composer = createComposer(convertComposerOptions(options));\r\n    // defines VueI18n\r\n    const vueI18n = {\r\n        // id\r\n        id: composer.id,\r\n        // locale\r\n        get locale() {\r\n            return composer.locale.value;\r\n        },\r\n        set locale(val) {\r\n            composer.locale.value = val;\r\n        },\r\n        // fallbackLocale\r\n        get fallbackLocale() {\r\n            return composer.fallbackLocale.value;\r\n        },\r\n        set fallbackLocale(val) {\r\n            composer.fallbackLocale.value = val;\r\n        },\r\n        // messages\r\n        get messages() {\r\n            return composer.messages.value;\r\n        },\r\n        // datetimeFormats\r\n        get datetimeFormats() {\r\n            return composer.datetimeFormats.value;\r\n        },\r\n        // numberFormats\r\n        get numberFormats() {\r\n            return composer.numberFormats.value;\r\n        },\r\n        // availableLocales\r\n        get availableLocales() {\r\n            return composer.availableLocales;\r\n        },\r\n        // formatter\r\n        get formatter() {\r\n            shared.warn(getWarnMessage(8 /* NOT_SUPPORTED_FORMATTER */));\r\n            // dummy\r\n            return {\r\n                interpolate() {\r\n                    return [];\r\n                }\r\n            };\r\n        },\r\n        set formatter(val) {\r\n            shared.warn(getWarnMessage(8 /* NOT_SUPPORTED_FORMATTER */));\r\n        },\r\n        // missing\r\n        get missing() {\r\n            return composer.getMissingHandler();\r\n        },\r\n        set missing(handler) {\r\n            composer.setMissingHandler(handler);\r\n        },\r\n        // silentTranslationWarn\r\n        get silentTranslationWarn() {\r\n            return shared.isBoolean(composer.missingWarn)\r\n                ? !composer.missingWarn\r\n                : composer.missingWarn;\r\n        },\r\n        set silentTranslationWarn(val) {\r\n            composer.missingWarn = shared.isBoolean(val) ? !val : val;\r\n        },\r\n        // silentFallbackWarn\r\n        get silentFallbackWarn() {\r\n            return shared.isBoolean(composer.fallbackWarn)\r\n                ? !composer.fallbackWarn\r\n                : composer.fallbackWarn;\r\n        },\r\n        set silentFallbackWarn(val) {\r\n            composer.fallbackWarn = shared.isBoolean(val) ? !val : val;\r\n        },\r\n        // modifiers\r\n        get modifiers() {\r\n            return composer.modifiers;\r\n        },\r\n        // formatFallbackMessages\r\n        get formatFallbackMessages() {\r\n            return composer.fallbackFormat;\r\n        },\r\n        set formatFallbackMessages(val) {\r\n            composer.fallbackFormat = val;\r\n        },\r\n        // postTranslation\r\n        get postTranslation() {\r\n            return composer.getPostTranslationHandler();\r\n        },\r\n        set postTranslation(handler) {\r\n            composer.setPostTranslationHandler(handler);\r\n        },\r\n        // sync\r\n        get sync() {\r\n            return composer.inheritLocale;\r\n        },\r\n        set sync(val) {\r\n            composer.inheritLocale = val;\r\n        },\r\n        // warnInHtmlMessage\r\n        get warnHtmlInMessage() {\r\n            return composer.warnHtmlMessage ? 'warn' : 'off';\r\n        },\r\n        set warnHtmlInMessage(val) {\r\n            composer.warnHtmlMessage = val !== 'off';\r\n        },\r\n        // escapeParameterHtml\r\n        get escapeParameterHtml() {\r\n            return composer.escapeParameter;\r\n        },\r\n        set escapeParameterHtml(val) {\r\n            composer.escapeParameter = val;\r\n        },\r\n        // preserveDirectiveContent\r\n        get preserveDirectiveContent() {\r\n            shared.warn(getWarnMessage(9 /* NOT_SUPPORTED_PRESERVE_DIRECTIVE */));\r\n            return true;\r\n        },\r\n        set preserveDirectiveContent(val) {\r\n            shared.warn(getWarnMessage(9 /* NOT_SUPPORTED_PRESERVE_DIRECTIVE */));\r\n        },\r\n        // pluralizationRules\r\n        get pluralizationRules() {\r\n            return composer.pluralRules || {};\r\n        },\r\n        // for internal\r\n        __composer: composer,\r\n        // t\r\n        t(...args) {\r\n            const [arg1, arg2, arg3] = args;\r\n            const options = {};\r\n            let list = null;\r\n            let named = null;\r\n            if (!shared.isString(arg1)) {\r\n                throw createI18nError(15 /* INVALID_ARGUMENT */);\r\n            }\r\n            const key = arg1;\r\n            if (shared.isString(arg2)) {\r\n                options.locale = arg2;\r\n            }\r\n            else if (shared.isArray(arg2)) {\r\n                list = arg2;\r\n            }\r\n            else if (shared.isPlainObject(arg2)) {\r\n                named = arg2;\r\n            }\r\n            if (shared.isArray(arg3)) {\r\n                list = arg3;\r\n            }\r\n            else if (shared.isPlainObject(arg3)) {\r\n                named = arg3;\r\n            }\r\n            return composer.t(key, list || named || {}, options);\r\n        },\r\n        rt(...args) {\r\n            return composer.rt(...args);\r\n        },\r\n        // tc\r\n        tc(...args) {\r\n            const [arg1, arg2, arg3] = args;\r\n            const options = { plural: 1 };\r\n            let list = null;\r\n            let named = null;\r\n            if (!shared.isString(arg1)) {\r\n                throw createI18nError(15 /* INVALID_ARGUMENT */);\r\n            }\r\n            const key = arg1;\r\n            if (shared.isString(arg2)) {\r\n                options.locale = arg2;\r\n            }\r\n            else if (shared.isNumber(arg2)) {\r\n                options.plural = arg2;\r\n            }\r\n            else if (shared.isArray(arg2)) {\r\n                list = arg2;\r\n            }\r\n            else if (shared.isPlainObject(arg2)) {\r\n                named = arg2;\r\n            }\r\n            if (shared.isString(arg3)) {\r\n                options.locale = arg3;\r\n            }\r\n            else if (shared.isArray(arg3)) {\r\n                list = arg3;\r\n            }\r\n            else if (shared.isPlainObject(arg3)) {\r\n                named = arg3;\r\n            }\r\n            return composer.t(key, list || named || {}, options);\r\n        },\r\n        // te\r\n        te(key, locale) {\r\n            return composer.te(key, locale);\r\n        },\r\n        // tm\r\n        tm(key) {\r\n            return composer.tm(key);\r\n        },\r\n        // getLocaleMessage\r\n        getLocaleMessage(locale) {\r\n            return composer.getLocaleMessage(locale);\r\n        },\r\n        // setLocaleMessage\r\n        setLocaleMessage(locale, message) {\r\n            composer.setLocaleMessage(locale, message);\r\n        },\r\n        // mergeLocaleMessage\r\n        mergeLocaleMessage(locale, message) {\r\n            composer.mergeLocaleMessage(locale, message);\r\n        },\r\n        // d\r\n        d(...args) {\r\n            return composer.d(...args);\r\n        },\r\n        // getDateTimeFormat\r\n        getDateTimeFormat(locale) {\r\n            return composer.getDateTimeFormat(locale);\r\n        },\r\n        // setDateTimeFormat\r\n        setDateTimeFormat(locale, format) {\r\n            composer.setDateTimeFormat(locale, format);\r\n        },\r\n        // mergeDateTimeFormat\r\n        mergeDateTimeFormat(locale, format) {\r\n            composer.mergeDateTimeFormat(locale, format);\r\n        },\r\n        // n\r\n        n(...args) {\r\n            return composer.n(...args);\r\n        },\r\n        // getNumberFormat\r\n        getNumberFormat(locale) {\r\n            return composer.getNumberFormat(locale);\r\n        },\r\n        // setNumberFormat\r\n        setNumberFormat(locale, format) {\r\n            composer.setNumberFormat(locale, format);\r\n        },\r\n        // mergeNumberFormat\r\n        mergeNumberFormat(locale, format) {\r\n            composer.mergeNumberFormat(locale, format);\r\n        },\r\n        // getChoiceIndex\r\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n        getChoiceIndex(choice, choicesLength) {\r\n            shared.warn(getWarnMessage(10 /* NOT_SUPPORTED_GET_CHOICE_INDEX */));\r\n            return -1;\r\n        },\r\n        // for internal\r\n        __onComponentInstanceCreated(target) {\r\n            const { componentInstanceCreatedListener } = options;\r\n            if (componentInstanceCreatedListener) {\r\n                componentInstanceCreatedListener(target, vueI18n);\r\n            }\r\n        }\r\n    };\r\n    // for vue-devtools timeline event\r\n    {\r\n        vueI18n.__enableEmitter = (emitter) => {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            const __composer = composer;\r\n            __composer[EnableEmitter] && __composer[EnableEmitter](emitter);\r\n        };\r\n        vueI18n.__disableEmitter = () => {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            const __composer = composer;\r\n            __composer[DisableEmitter] && __composer[DisableEmitter]();\r\n        };\r\n    }\r\n    return vueI18n;\r\n}\n\nconst baseFormatProps = {\r\n    tag: {\r\n        type: [String, Object]\r\n    },\r\n    locale: {\r\n        type: String\r\n    },\r\n    scope: {\r\n        type: String,\r\n        validator: (val) => val === 'parent' || val === 'global',\r\n        default: 'parent'\r\n    },\r\n    i18n: {\r\n        type: Object\r\n    }\r\n};\n\n/**\r\n * Translation Component\r\n *\r\n * @remarks\r\n * See the following items for property about details\r\n *\r\n * @VueI18nSee [TranslationProps](component#translationprops)\r\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\r\n * @VueI18nSee [Component Interpolation](../guide/advanced/component)\r\n *\r\n * @example\r\n * ```html\r\n * <div id=\"app\">\r\n *   <!-- ... -->\r\n *   <i18n path=\"term\" tag=\"label\" for=\"tos\">\r\n *     <a :href=\"url\" target=\"_blank\">{{ $t('tos') }}</a>\r\n *   </i18n>\r\n *   <!-- ... -->\r\n * </div>\r\n * ```\r\n * ```js\r\n * import { createApp } from 'vue'\r\n * import { createI18n } from 'vue-i18n'\r\n *\r\n * const messages = {\r\n *   en: {\r\n *     tos: 'Term of Service',\r\n *     term: 'I accept xxx {0}.'\r\n *   },\r\n *   ja: {\r\n *     tos: '利用規約',\r\n *     term: '私は xxx の{0}に同意します。'\r\n *   }\r\n * }\r\n *\r\n * const i18n = createI18n({\r\n *   locale: 'en',\r\n *   messages\r\n * })\r\n *\r\n * const app = createApp({\r\n *   data: {\r\n *     url: '/term'\r\n *   }\r\n * }).use(i18n).mount('#app')\r\n * ```\r\n *\r\n * @VueI18nComponent\r\n */\r\nconst Translation = {\r\n    /* eslint-disable */\r\n    name: 'i18n-t',\r\n    props: shared.assign({\r\n        keypath: {\r\n            type: String,\r\n            required: true\r\n        },\r\n        plural: {\r\n            type: [Number, String],\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            validator: (val) => shared.isNumber(val) || !isNaN(val)\r\n        }\r\n    }, baseFormatProps),\r\n    /* eslint-enable */\r\n    setup(props, context) {\r\n        const { slots, attrs } = context;\r\n        const i18n = props.i18n ||\r\n            useI18n({\r\n                useScope: props.scope,\r\n                __useComponent: true\r\n            });\r\n        const keys = Object.keys(slots).filter(key => key !== '_');\r\n        return () => {\r\n            const options = {};\r\n            if (props.locale) {\r\n                options.locale = props.locale;\r\n            }\r\n            if (props.plural !== undefined) {\r\n                options.plural = shared.isString(props.plural) ? +props.plural : props.plural;\r\n            }\r\n            const arg = getInterpolateArg(context, keys);\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            const children = i18n[TransrateVNodeSymbol](props.keypath, arg, options);\r\n            const assignedAttrs = shared.assign({}, attrs);\r\n            // prettier-ignore\r\n            return shared.isString(props.tag)\r\n                ? vue.h(props.tag, assignedAttrs, children)\r\n                : shared.isObject(props.tag)\r\n                    ? vue.h(props.tag, assignedAttrs, children)\r\n                    : vue.h(vue.Fragment, assignedAttrs, children);\r\n        };\r\n    }\r\n};\r\nfunction getInterpolateArg({ slots }, keys) {\r\n    if (keys.length === 1 && keys[0] === 'default') {\r\n        // default slot only\r\n        return slots.default ? slots.default() : [];\r\n    }\r\n    else {\r\n        // named slots\r\n        return keys.reduce((arg, key) => {\r\n            const slot = slots[key];\r\n            if (slot) {\r\n                arg[key] = slot();\r\n            }\r\n            return arg;\r\n        }, {});\r\n    }\r\n}\n\nfunction renderFormatter(props, context, slotKeys, partFormatter) {\r\n    const { slots, attrs } = context;\r\n    return () => {\r\n        const options = { part: true };\r\n        let overrides = {};\r\n        if (props.locale) {\r\n            options.locale = props.locale;\r\n        }\r\n        if (shared.isString(props.format)) {\r\n            options.key = props.format;\r\n        }\r\n        else if (shared.isObject(props.format)) {\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            if (shared.isString(props.format.key)) {\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                options.key = props.format.key;\r\n            }\r\n            // Filter out number format options only\r\n            overrides = Object.keys(props.format).reduce((options, prop) => {\r\n                return slotKeys.includes(prop)\r\n                    ? shared.assign({}, options, { [prop]: props.format[prop] }) // eslint-disable-line @typescript-eslint/no-explicit-any\r\n                    : options;\r\n            }, {});\r\n        }\r\n        const parts = partFormatter(...[props.value, options, overrides]);\r\n        let children = [options.key];\r\n        if (shared.isArray(parts)) {\r\n            children = parts.map((part, index) => {\r\n                const slot = slots[part.type];\r\n                return slot\r\n                    ? slot({ [part.type]: part.value, index, parts })\r\n                    : [part.value];\r\n            });\r\n        }\r\n        else if (shared.isString(parts)) {\r\n            children = [parts];\r\n        }\r\n        const assignedAttrs = shared.assign({}, attrs);\r\n        // prettier-ignore\r\n        return shared.isString(props.tag)\r\n            ? vue.h(props.tag, assignedAttrs, children)\r\n            : shared.isObject(props.tag)\r\n                ? vue.h(props.tag, assignedAttrs, children)\r\n                : vue.h(vue.Fragment, assignedAttrs, children);\r\n    };\r\n}\n\nconst NUMBER_FORMAT_KEYS = [\r\n    'localeMatcher',\r\n    'style',\r\n    'unit',\r\n    'unitDisplay',\r\n    'currency',\r\n    'currencyDisplay',\r\n    'useGrouping',\r\n    'numberingSystem',\r\n    'minimumIntegerDigits',\r\n    'minimumFractionDigits',\r\n    'maximumFractionDigits',\r\n    'minimumSignificantDigits',\r\n    'maximumSignificantDigits',\r\n    'notation',\r\n    'formatMatcher'\r\n];\r\n/**\r\n * Number Format Component\r\n *\r\n * @remarks\r\n * See the following items for property about details\r\n *\r\n * @VueI18nSee [FormattableProps](component#formattableprops)\r\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\r\n * @VueI18nSee [Custom Formatting](../guide/essentials/number#custom-formatting)\r\n *\r\n * @VueI18nDanger\r\n * Not supported IE, due to no support `Intl.NumberFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/formatToParts)\r\n *\r\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-numberformat)\r\n *\r\n * @VueI18nComponent\r\n */\r\nconst NumberFormat = {\r\n    /* eslint-disable */\r\n    name: 'i18n-n',\r\n    props: shared.assign({\r\n        value: {\r\n            type: Number,\r\n            required: true\r\n        },\r\n        format: {\r\n            type: [String, Object]\r\n        }\r\n    }, baseFormatProps),\r\n    /* eslint-enable */\r\n    setup(props, context) {\r\n        const i18n = props.i18n ||\r\n            useI18n({ useScope: 'parent', __useComponent: true });\r\n        return renderFormatter(props, context, NUMBER_FORMAT_KEYS, (...args) => \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        i18n[NumberPartsSymbol](...args));\r\n    }\r\n};\n\nconst DATETIME_FORMAT_KEYS = [\r\n    'dateStyle',\r\n    'timeStyle',\r\n    'fractionalSecondDigits',\r\n    'calendar',\r\n    'dayPeriod',\r\n    'numberingSystem',\r\n    'localeMatcher',\r\n    'timeZone',\r\n    'hour12',\r\n    'hourCycle',\r\n    'formatMatcher',\r\n    'weekday',\r\n    'era',\r\n    'year',\r\n    'month',\r\n    'day',\r\n    'hour',\r\n    'minute',\r\n    'second',\r\n    'timeZoneName'\r\n];\r\n/**\r\n * Datetime Format Component\r\n *\r\n * @remarks\r\n * See the following items for property about details\r\n *\r\n * @VueI18nSee [FormattableProps](component#formattableprops)\r\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\r\n * @VueI18nSee [Custom Formatting](../guide/essentials/datetime#custom-formatting)\r\n *\r\n * @VueI18nDanger\r\n * Not supported IE, due to no support `Intl.DateTimeFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts)\r\n *\r\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-datetimeformat)\r\n *\r\n * @VueI18nComponent\r\n */\r\nconst DatetimeFormat = {\r\n    /* eslint-disable */\r\n    name: 'i18n-d',\r\n    props: shared.assign({\r\n        value: {\r\n            type: [Number, Date],\r\n            required: true\r\n        },\r\n        format: {\r\n            type: [String, Object]\r\n        }\r\n    }, baseFormatProps),\r\n    /* eslint-enable */\r\n    setup(props, context) {\r\n        const i18n = props.i18n ||\r\n            useI18n({ useScope: 'parent', __useComponent: true });\r\n        return renderFormatter(props, context, DATETIME_FORMAT_KEYS, (...args) => \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        i18n[DatetimePartsSymbol](...args));\r\n    }\r\n};\n\nfunction getComposer$1(i18n, instance) {\r\n    const i18nInternal = i18n;\r\n    if (i18n.mode === 'composition') {\r\n        return (i18nInternal.__getInstance(instance) || i18n.global);\r\n    }\r\n    else {\r\n        const vueI18n = i18nInternal.__getInstance(instance);\r\n        return vueI18n != null\r\n            ? vueI18n.__composer\r\n            : i18n.global.__composer;\r\n    }\r\n}\r\nfunction vTDirective(i18n) {\r\n    const bind = (el, { instance, value, modifiers }) => {\r\n        /* istanbul ignore if */\r\n        if (!instance || !instance.$) {\r\n            throw createI18nError(22 /* UNEXPECTED_ERROR */);\r\n        }\r\n        const composer = getComposer$1(i18n, instance.$);\r\n        if (modifiers.preserve) {\r\n            shared.warn(getWarnMessage(7 /* NOT_SUPPORTED_PRESERVE */));\r\n        }\r\n        const parsedValue = parseValue(value);\r\n        el.textContent = composer.t(...makeParams(parsedValue));\r\n    };\r\n    return {\r\n        beforeMount: bind,\r\n        beforeUpdate: bind\r\n    };\r\n}\r\nfunction parseValue(value) {\r\n    if (shared.isString(value)) {\r\n        return { path: value };\r\n    }\r\n    else if (shared.isPlainObject(value)) {\r\n        if (!('path' in value)) {\r\n            throw createI18nError(19 /* REQUIRED_VALUE */, 'path');\r\n        }\r\n        return value;\r\n    }\r\n    else {\r\n        throw createI18nError(20 /* INVALID_VALUE */);\r\n    }\r\n}\r\nfunction makeParams(value) {\r\n    const { path, locale, args, choice, plural } = value;\r\n    const options = {};\r\n    const named = args || {};\r\n    if (shared.isString(locale)) {\r\n        options.locale = locale;\r\n    }\r\n    if (shared.isNumber(choice)) {\r\n        options.plural = choice;\r\n    }\r\n    if (shared.isNumber(plural)) {\r\n        options.plural = plural;\r\n    }\r\n    return [path, named, options];\r\n}\n\nfunction apply(app, i18n, ...options) {\r\n    const pluginOptions = shared.isPlainObject(options[0])\r\n        ? options[0]\r\n        : {};\r\n    const useI18nComponentName = !!pluginOptions.useI18nComponentName;\r\n    const globalInstall = shared.isBoolean(pluginOptions.globalInstall)\r\n        ? pluginOptions.globalInstall\r\n        : true;\r\n    if (globalInstall && useI18nComponentName) {\r\n        shared.warn(getWarnMessage(11 /* COMPONENT_NAME_LEGACY_COMPATIBLE */, {\r\n            name: Translation.name\r\n        }));\r\n    }\r\n    if (globalInstall) {\r\n        // install components\r\n        app.component(!useI18nComponentName ? Translation.name : 'i18n', Translation);\r\n        app.component(NumberFormat.name, NumberFormat);\r\n        app.component(DatetimeFormat.name, DatetimeFormat);\r\n    }\r\n    // install directive\r\n    app.directive('t', vTDirective(i18n));\r\n}\n\n// supports compatibility for legacy vue-i18n APIs\r\nfunction defineMixin(vuei18n, composer, i18n) {\r\n    return {\r\n        beforeCreate() {\r\n            const instance = vue.getCurrentInstance();\r\n            /* istanbul ignore if */\r\n            if (!instance) {\r\n                throw createI18nError(22 /* UNEXPECTED_ERROR */);\r\n            }\r\n            const options = this.$options;\r\n            if (options.i18n) {\r\n                const optionsI18n = options.i18n;\r\n                if (options.__i18n) {\r\n                    optionsI18n.__i18n = options.__i18n;\r\n                }\r\n                optionsI18n.__root = composer;\r\n                if (this === this.$root) {\r\n                    this.$i18n = mergeToRoot(vuei18n, optionsI18n);\r\n                }\r\n                else {\r\n                    optionsI18n.__injectWithOption = true;\r\n                    this.$i18n = createVueI18n(optionsI18n);\r\n                }\r\n            }\r\n            else if (options.__i18n) {\r\n                if (this === this.$root) {\r\n                    this.$i18n = mergeToRoot(vuei18n, options);\r\n                }\r\n                else {\r\n                    this.$i18n = createVueI18n({\r\n                        __i18n: options.__i18n,\r\n                        __injectWithOption: true,\r\n                        __root: composer\r\n                    });\r\n                }\r\n            }\r\n            else {\r\n                // set global\r\n                this.$i18n = vuei18n;\r\n            }\r\n            vuei18n.__onComponentInstanceCreated(this.$i18n);\r\n            i18n.__setInstance(instance, this.$i18n);\r\n            // defines vue-i18n legacy APIs\r\n            this.$t = (...args) => this.$i18n.t(...args);\r\n            this.$rt = (...args) => this.$i18n.rt(...args);\r\n            this.$tc = (...args) => this.$i18n.tc(...args);\r\n            this.$te = (key, locale) => this.$i18n.te(key, locale);\r\n            this.$d = (...args) => this.$i18n.d(...args);\r\n            this.$n = (...args) => this.$i18n.n(...args);\r\n            this.$tm = (key) => this.$i18n.tm(key);\r\n        },\r\n        mounted() {\r\n        },\r\n        beforeUnmount() {\r\n            const instance = vue.getCurrentInstance();\r\n            /* istanbul ignore if */\r\n            if (!instance) {\r\n                throw createI18nError(22 /* UNEXPECTED_ERROR */);\r\n            }\r\n            delete this.$t;\r\n            delete this.$rt;\r\n            delete this.$tc;\r\n            delete this.$te;\r\n            delete this.$d;\r\n            delete this.$n;\r\n            delete this.$tm;\r\n            i18n.__deleteInstance(instance);\r\n            delete this.$i18n;\r\n        }\r\n    };\r\n}\r\nfunction mergeToRoot(root, options) {\r\n    root.locale = options.locale || root.locale;\r\n    root.fallbackLocale = options.fallbackLocale || root.fallbackLocale;\r\n    root.missing = options.missing || root.missing;\r\n    root.silentTranslationWarn =\r\n        options.silentTranslationWarn || root.silentFallbackWarn;\r\n    root.silentFallbackWarn =\r\n        options.silentFallbackWarn || root.silentFallbackWarn;\r\n    root.formatFallbackMessages =\r\n        options.formatFallbackMessages || root.formatFallbackMessages;\r\n    root.postTranslation = options.postTranslation || root.postTranslation;\r\n    root.warnHtmlInMessage = options.warnHtmlInMessage || root.warnHtmlInMessage;\r\n    root.escapeParameterHtml =\r\n        options.escapeParameterHtml || root.escapeParameterHtml;\r\n    root.sync = options.sync || root.sync;\r\n    root.__composer[SetPluralRulesSymbol](options.pluralizationRules || root.pluralizationRules);\r\n    const messages = getLocaleMessages(root.locale, {\r\n        messages: options.messages,\r\n        __i18n: options.__i18n\r\n    });\r\n    Object.keys(messages).forEach(locale => root.mergeLocaleMessage(locale, messages[locale]));\r\n    if (options.datetimeFormats) {\r\n        Object.keys(options.datetimeFormats).forEach(locale => root.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));\r\n    }\r\n    if (options.numberFormats) {\r\n        Object.keys(options.numberFormats).forEach(locale => root.mergeNumberFormat(locale, options.numberFormats[locale]));\r\n    }\r\n    return root;\r\n}\n\n/**\r\n * Vue I18n factory\r\n *\r\n * @param options - An options, see the {@link I18nOptions}\r\n *\r\n * @returns {@link I18n} instance\r\n *\r\n * @remarks\r\n * If you use Legacy API mode, you need toto specify {@link VueI18nOptions} and `legacy: true` option.\r\n *\r\n * If you use composition API mode, you need to specify {@link ComposerOptions}.\r\n *\r\n * @VueI18nSee [Getting Started](../guide/)\r\n * @VueI18nSee [Composition API](../guide/advanced/composition)\r\n *\r\n * @example\r\n * case: for Legacy API\r\n * ```js\r\n * import { createApp } from 'vue'\r\n * import { createI18n } from 'vue-i18n'\r\n *\r\n * // call with I18n option\r\n * const i18n = createI18n({\r\n *   locale: 'ja',\r\n *   messages: {\r\n *     en: { ... },\r\n *     ja: { ... }\r\n *   }\r\n * })\r\n *\r\n * const App = {\r\n *   // ...\r\n * }\r\n *\r\n * const app = createApp(App)\r\n *\r\n * // install!\r\n * app.use(i18n)\r\n * app.mount('#app')\r\n * ```\r\n *\r\n * @example\r\n * case: for composition API\r\n * ```js\r\n * import { createApp } from 'vue'\r\n * import { createI18n, useI18n } from 'vue-i18n'\r\n *\r\n * // call with I18n option\r\n * const i18n = createI18n({\r\n *   legacy: false, // you must specify 'legacy: false' option\r\n *   locale: 'ja',\r\n *   messages: {\r\n *     en: { ... },\r\n *     ja: { ... }\r\n *   }\r\n * })\r\n *\r\n * const App = {\r\n *   setup() {\r\n *     // ...\r\n *     const { t } = useI18n({ ... })\r\n *     return { ... , t }\r\n *   }\r\n * }\r\n *\r\n * const app = createApp(App)\r\n *\r\n * // install!\r\n * app.use(i18n)\r\n * app.mount('#app')\r\n * ```\r\n *\r\n * @VueI18nGeneral\r\n */\r\nfunction createI18n(options = {}) {\r\n    // prettier-ignore\r\n    const __legacyMode = shared.isBoolean(options.legacy)\r\n        ? options.legacy\r\n        : true;\r\n    const __globalInjection = !!options.globalInjection;\r\n    const __instances = new Map();\r\n    // prettier-ignore\r\n    const __global = __legacyMode\r\n        ? createVueI18n(options)\r\n        : createComposer(options);\r\n    const symbol = shared.makeSymbol('vue-i18n' );\r\n    const i18n = {\r\n        // mode\r\n        get mode() {\r\n            // prettier-ignore\r\n            return __legacyMode\r\n                    ? 'legacy'\r\n                    : 'composition'\r\n                ;\r\n        },\r\n        // install plugin\r\n        async install(app, ...options) {\r\n            // setup global provider\r\n            app.__VUE_I18N_SYMBOL__ = symbol;\r\n            app.provide(app.__VUE_I18N_SYMBOL__, i18n);\r\n            // global method and properties injection for Composition API\r\n            if (!__legacyMode && __globalInjection) {\r\n                injectGlobalFields(app, i18n.global);\r\n            }\r\n            // install built-in components and directive\r\n            {\r\n                apply(app, i18n, ...options);\r\n            }\r\n            // setup mixin for Legacy API\r\n            if (__legacyMode) {\r\n                app.mixin(defineMixin(__global, __global.__composer, i18n));\r\n            }\r\n        },\r\n        // global accessor\r\n        get global() {\r\n            return __global;\r\n        },\r\n        // @internal\r\n        __instances,\r\n        // @internal\r\n        __getInstance(component) {\r\n            return __instances.get(component) || null;\r\n        },\r\n        // @internal\r\n        __setInstance(component, instance) {\r\n            __instances.set(component, instance);\r\n        },\r\n        // @internal\r\n        __deleteInstance(component) {\r\n            __instances.delete(component);\r\n        }\r\n    };\r\n    return i18n;\r\n}\r\n/**\r\n * Use Composition API for Vue I18n\r\n *\r\n * @param options - An options, see {@link UseI18nOptions}\r\n *\r\n * @returns {@link Composer} instance\r\n *\r\n * @remarks\r\n * This function is mainly used by `setup`.\r\n *\r\n * If options are specified, Composer instance is created for each component and you can be localized on the component.\r\n *\r\n * If options are not specified, you can be localized using the global Composer.\r\n *\r\n * @example\r\n * case: Component resource base localization\r\n * ```html\r\n * <template>\r\n *   <form>\r\n *     <label>{{ t('language') }}</label>\r\n *     <select v-model=\"locale\">\r\n *       <option value=\"en\">en</option>\r\n *       <option value=\"ja\">ja</option>\r\n *     </select>\r\n *   </form>\r\n *   <p>message: {{ t('hello') }}</p>\r\n * </template>\r\n *\r\n * <script>\r\n * import { useI18n } from 'vue-i18n'\r\n *\r\n * export default {\r\n *  setup() {\r\n *    const { t, locale } = useI18n({\r\n *      locale: 'ja',\r\n *      messages: {\r\n *        en: { ... },\r\n *        ja: { ... }\r\n *      }\r\n *    })\r\n *    // Something to do ...\r\n *\r\n *    return { ..., t, locale }\r\n *  }\r\n * }\r\n * </script>\r\n * ```\r\n *\r\n * @VueI18nComposition\r\n */\r\nfunction useI18n(options = {}) {\r\n    const instance = vue.getCurrentInstance();\r\n    if (instance == null) {\r\n        throw createI18nError(16 /* MUST_BE_CALL_SETUP_TOP */);\r\n    }\r\n    if (!instance.appContext.app.__VUE_I18N_SYMBOL__) {\r\n        throw createI18nError(17 /* NOT_INSLALLED */);\r\n    }\r\n    const i18n = vue.inject(instance.appContext.app.__VUE_I18N_SYMBOL__);\r\n    /* istanbul ignore if */\r\n    if (!i18n) {\r\n        throw createI18nError(22 /* UNEXPECTED_ERROR */);\r\n    }\r\n    // prettier-ignore\r\n    const global = i18n.mode === 'composition'\r\n        ? i18n.global\r\n        : i18n.global.__composer;\r\n    // prettier-ignore\r\n    const scope = shared.isEmptyObject(options)\r\n        ? ('__i18n' in instance.type)\r\n            ? 'local'\r\n            : 'global'\r\n        : !options.useScope\r\n            ? 'local'\r\n            : options.useScope;\r\n    if (scope === 'global') {\r\n        let messages = shared.isObject(options.messages) ? options.messages : {};\r\n        if ('__i18nGlobal' in instance.type) {\r\n            messages = getLocaleMessages(global.locale.value, {\r\n                messages,\r\n                __i18n: instance.type.__i18nGlobal\r\n            });\r\n        }\r\n        // merge locale messages\r\n        const locales = Object.keys(messages);\r\n        if (locales.length) {\r\n            locales.forEach(locale => {\r\n                global.mergeLocaleMessage(locale, messages[locale]);\r\n            });\r\n        }\r\n        // merge datetime formats\r\n        if (shared.isObject(options.datetimeFormats)) {\r\n            const locales = Object.keys(options.datetimeFormats);\r\n            if (locales.length) {\r\n                locales.forEach(locale => {\r\n                    global.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);\r\n                });\r\n            }\r\n        }\r\n        // merge number formats\r\n        if (shared.isObject(options.numberFormats)) {\r\n            const locales = Object.keys(options.numberFormats);\r\n            if (locales.length) {\r\n                locales.forEach(locale => {\r\n                    global.mergeNumberFormat(locale, options.numberFormats[locale]);\r\n                });\r\n            }\r\n        }\r\n        return global;\r\n    }\r\n    if (scope === 'parent') {\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        let composer = getComposer(i18n, instance, options.__useComponent);\r\n        if (composer == null) {\r\n            {\r\n                shared.warn(getWarnMessage(12 /* NOT_FOUND_PARENT_SCOPE */));\r\n            }\r\n            composer = global;\r\n        }\r\n        return composer;\r\n    }\r\n    // scope 'local' case\r\n    if (i18n.mode === 'legacy') {\r\n        throw createI18nError(18 /* NOT_AVAILABLE_IN_LEGACY_MODE */);\r\n    }\r\n    const i18nInternal = i18n;\r\n    let composer = i18nInternal.__getInstance(instance);\r\n    if (composer == null) {\r\n        const type = instance.type;\r\n        const composerOptions = shared.assign({}, options);\r\n        if (type.__i18n) {\r\n            composerOptions.__i18n = type.__i18n;\r\n        }\r\n        if (global) {\r\n            composerOptions.__root = global;\r\n        }\r\n        composer = createComposer(composerOptions);\r\n        setupLifeCycle(i18nInternal, instance);\r\n        i18nInternal.__setInstance(instance, composer);\r\n    }\r\n    return composer;\r\n}\r\nfunction getComposer(i18n, target, useComponent = false) {\r\n    let composer = null;\r\n    const root = target.root;\r\n    let current = target.parent;\r\n    while (current != null) {\r\n        const i18nInternal = i18n;\r\n        if (i18n.mode === 'composition') {\r\n            composer = i18nInternal.__getInstance(current);\r\n        }\r\n        else {\r\n            const vueI18n = i18nInternal.__getInstance(current);\r\n            if (vueI18n != null) {\r\n                composer = vueI18n\r\n                    .__composer;\r\n            }\r\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n            if (useComponent && composer && !composer[InejctWithOption]) {\r\n                composer = null;\r\n            }\r\n        }\r\n        if (composer != null) {\r\n            break;\r\n        }\r\n        if (root === current) {\r\n            break;\r\n        }\r\n        current = current.parent;\r\n    }\r\n    return composer;\r\n}\r\nfunction setupLifeCycle(i18n, target, composer) {\r\n    vue.onMounted(() => {\r\n    }, target);\r\n    vue.onUnmounted(() => {\r\n        i18n.__deleteInstance(target);\r\n    }, target);\r\n}\r\nconst globalExportProps = [\r\n    'locale',\r\n    'fallbackLocale',\r\n    'availableLocales'\r\n];\r\nconst globalExportMethods = ['t', 'rt', 'd', 'n', 'tm'];\r\nfunction injectGlobalFields(app, composer) {\r\n    const i18n = Object.create(null);\r\n    globalExportProps.forEach(prop => {\r\n        const desc = Object.getOwnPropertyDescriptor(composer, prop);\r\n        if (!desc) {\r\n            throw createI18nError(22 /* UNEXPECTED_ERROR */);\r\n        }\r\n        const wrap = vue.isRef(desc.value) // check computed props\r\n            ? {\r\n                get() {\r\n                    return desc.value.value;\r\n                },\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                set(val) {\r\n                    desc.value.value = val;\r\n                }\r\n            }\r\n            : {\r\n                get() {\r\n                    return desc.get && desc.get();\r\n                }\r\n            };\r\n        Object.defineProperty(i18n, prop, wrap);\r\n    });\r\n    app.config.globalProperties.$i18n = i18n;\r\n    globalExportMethods.forEach(method => {\r\n        const desc = Object.getOwnPropertyDescriptor(composer, method);\r\n        if (!desc || !desc.value) {\r\n            throw createI18nError(22 /* UNEXPECTED_ERROR */);\r\n        }\r\n        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);\r\n    });\r\n}\n\n// register message compiler at vue-i18n\r\ncoreBase.registerMessageCompiler(coreBase.compileToFunction);\r\n// NOTE: experimental !!\r\n{\r\n    const target = shared.getGlobalThis();\r\n    target.__INTLIFY__ = true;\r\n    coreBase.setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);\r\n}\n\nexports.DatetimeFormat = DatetimeFormat;\nexports.NumberFormat = NumberFormat;\nexports.Translation = Translation;\nexports.VERSION = VERSION;\nexports.createI18n = createI18n;\nexports.useI18n = useI18n;\nexports.vTDirective = vTDirective;\n", "export default require(\"./node_modules/vue-i18n/dist/vue-i18n.cjs.js\");"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,SAAS,OAAO,YAAY,MAAM;AAC9B,MAAI,KAAK,WAAW,KAAK,SAAS,KAAK,EAAE,GAAG;AACxC,WAAO,KAAK;AAAA,EAChB;AACA,MAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,QAAQ,QAAQ,SAAS,CAAC,OAAO,eAAe;AACnD,WAAO,KAAK,eAAe,UAAU,IAAI,KAAK,cAAc;AAAA,EAChE,CAAC;AACL;AAYA,SAAS,KAAK,KAAK,KAAK;AACpB,MAAI,OAAO,YAAY,aAAa;AAChC,YAAQ,KAAK,eAAe,GAAG;AAE/B,QAAI,KAAK;AACL,cAAQ,KAAK,IAAI,KAAK;AAAA,IAC1B;AAAA,EACJ;AACJ;AAiBA,SAAS,WAAW,SAAS;AACzB,SAAO,QACF,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ;AAC/B;AAEA,SAAS,OAAO,KAAK,KAAK;AACtB,SAAO,eAAe,KAAK,KAAK,GAAG;AACvC;AA+BA,SAAS,kBAAkB,QAAQ,QAAQ,GAAG,MAAM,OAAO,QAAQ;AAC/D,QAAM,QAAQ,OAAO,MAAM,OAAO;AAClC,MAAI,QAAQ;AACZ,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,aAAS,MAAM,GAAG,SAAS;AAC3B,QAAI,SAAS,OAAO;AAChB,eAAS,IAAI,IAAI,OAAO,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK;AACxD,YAAI,IAAI,KAAK,KAAK,MAAM;AACpB;AACJ,cAAM,OAAO,IAAI;AACjB,YAAI,KAAK,GAAG,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM,OAAO,MAAM,IAAI;AACtE,cAAM,aAAa,MAAM,GAAG;AAC5B,YAAI,MAAM,GAAG;AAET,gBAAM,MAAM,SAAS,QAAQ,cAAc;AAC3C,gBAAM,SAAS,KAAK,IAAI,GAAG,MAAM,QAAQ,aAAa,MAAM,MAAM,KAAK;AACvE,cAAI,KAAK,WAAW,IAAI,OAAO,GAAG,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,QAC5D,WACS,IAAI,GAAG;AACZ,cAAI,MAAM,OAAO;AACb,kBAAM,SAAS,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;AAC5D,gBAAI,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC;AAAA,UAC1C;AACA,mBAAS,aAAa;AAAA,QAC1B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,IAAI,KAAK,IAAI;AACxB;AAcA,SAAS,gBAAgB;AACrB,QAAM,SAAS,oBAAI,IAAI;AACvB,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,GAAG,OAAO,SAAS;AACf,YAAM,WAAW,OAAO,IAAI,KAAK;AACjC,YAAM,QAAQ,YAAY,SAAS,KAAK,OAAO;AAC/C,UAAI,CAAC,OAAO;AACR,eAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,IAAI,OAAO,SAAS;AAChB,YAAM,WAAW,OAAO,IAAI,KAAK;AACjC,UAAI,UAAU;AACV,iBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,KAAK,OAAO,SAAS;AACjB,OAAC,OAAO,IAAI,KAAK,KAAK,CAAC,GAClB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,CAAC;AACpC,OAAC,OAAO,IAAI,GAAG,KAAK,CAAC,GAChB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,IAC/C;AAAA,EACJ;AACA,SAAO;AACX;AA7LA,IASM,WACF,MACA,SAgBE,SAaA,WACA,YACA,wBACA,uBAIA,UACA,QACA,UACA,eAUA,QACF,aACE,eAqBA,gBAYA,SACA,YACA,UACA,WACA,UACA,UAEA,WAGA,gBACA,cACA,eAEA,iBAOA;AApHN;AAAA;AASA,IAAM,YAAY,OAAO,WAAW;AAGpC,QAAK,MAAwC;AACzC,YAAM,OAAO,aAAa,OAAO;AACjC,UAAI,QACA,KAAK,QACL,KAAK,WACL,KAAK,cACL,KAAK,eAAe;AACpB,eAAO,CAAC,QAAQ,KAAK,KAAK,GAAG;AAC7B,kBAAU,CAAC,MAAM,UAAU,WAAW;AAClC,eAAK,QAAQ,MAAM,UAAU,MAAM;AACnC,eAAK,WAAW,QAAQ;AACxB,eAAK,WAAW,MAAM;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AACA,IAAM,UAAU;AAahB,IAAM,YAAY,OAAO,WAAW,cAAc,OAAO,OAAO,gBAAgB;AAChF,IAAM,aAAa,CAAC,SAAS,YAAY,OAAO,IAAI,IAAI;AACxD,IAAM,yBAAyB,CAAC,QAAQ,KAAK,WAAW,sBAAsB,EAAE,GAAG,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC;AAC9G,IAAM,wBAAwB,CAAC,SAAS,KAAK,UAAU,IAAI,EACtD,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS;AACjC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,SAAS,GAAG;AACjE,IAAM,SAAS,CAAC,QAAQ,aAAa,GAAG,MAAM;AAC9C,IAAM,WAAW,CAAC,QAAQ,aAAa,GAAG,MAAM;AAChD,IAAM,gBAAgB,CAAC,QAAQ,cAAc,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW;AAUjF,IAAM,SAAS,OAAO;AAEtB,IAAM,gBAAgB,MAAM;AAExB,aAAQ,gBACH,cACG,OAAO,eAAe,cAChB,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,CAAC;AAAA,IAC/B;AAQA,IAAM,iBAAiB,OAAO,UAAU;AAYxC,IAAM,UAAU,MAAM;AACtB,IAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAC3C,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ;AAC1C,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,WAAW,CAAC,QACjB,QAAQ,QAAQ,OAAO,QAAQ;AAChC,IAAM,YAAY,CAAC,QAAQ;AACvB,aAAO,SAAS,GAAG,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AAAA,IACxE;AACA,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,eAAe,CAAC,UAAU,eAAe,KAAK,KAAK;AACzD,IAAM,gBAAgB,CAAC,QAAQ,aAAa,GAAG,MAAM;AAErD,IAAM,kBAAkB,CAAC,QAAQ;AAC7B,aAAO,OAAO,OACR,KACA,QAAQ,GAAG,KAAM,cAAc,GAAG,KAAK,IAAI,aAAa,iBACpD,KAAK,UAAU,KAAK,MAAM,CAAC,IAC3B,OAAO,GAAG;AAAA,IACxB;AACA,IAAM,QAAQ;AAAA;AAAA;;;ACzGd,SAASA,QAAO,KAAK,KAAK;AACtB,SAAOC,gBAAe,KAAK,KAAK,GAAG;AACvC;AAuDA,SAAS,UAAU,KAAK;AACpB,SAAO,eAAe,KAAK,GAAG;AAClC;AAIA,SAAS,YAAY,KAAK;AACtB,QAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,QAAM,IAAI,IAAI,WAAW,IAAI,SAAS,CAAC;AACvC,SAAO,MAAM,MAAM,MAAM,MAAQ,MAAM,MAAQ,IAAI,MAAM,GAAG,EAAE,IAAI;AACtE;AAIA,SAAS,gBAAgB,IAAI;AACzB,MAAI,OAAO,UAAa,OAAO,MAAM;AACjC,WAAO;AAAA,EACX;AACA,QAAM,OAAO,GAAG,WAAW,CAAC;AAC5B,UAAQ;AAAA,SACC;AAAA,SACA;AAAA,SACA;AAAA,SACA;AAAA,SACA;AACD,aAAO;AAAA,SACN;AAAA,SACA;AAAA,SACA;AACD,aAAO;AAAA,SACN;AAAA,SACA;AAAA,SACA;AAAA,SACA;AAAA,SACA;AAAA,SACA;AAAA,SACA;AACD,aAAO;AAAA;AAEf,SAAO;AACX;AAMA,SAAS,cAAc,MAAM;AACzB,QAAM,UAAU,KAAK,KAAK;AAE1B,MAAI,KAAK,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS,IAAI,CAAC,GAAG;AACjD,WAAO;AAAA,EACX;AACA,SAAO,UAAU,OAAO,IAClB,YAAY,OAAO,IACnB,MAAqB;AAC/B;AAIA,SAAS,MAAM,MAAM;AACjB,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,eAAe;AACnB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,CAAC;AACjB,UAAQ,KAAkB,MAAM;AAC5B,QAAI,QAAQ,QAAW;AACnB,YAAM;AAAA,IACV,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,UAAQ,KAAgB,MAAM;AAC1B,QAAI,QAAQ,QAAW;AACnB,WAAK,KAAK,GAAG;AACb,YAAM;AAAA,IACV;AAAA,EACJ;AACA,UAAQ,KAA8B,MAAM;AACxC,YAAQ,GAAgB;AACxB;AAAA,EACJ;AACA,UAAQ,KAAyB,MAAM;AACnC,QAAI,eAAe,GAAG;AAClB;AACA,aAAO;AACP,cAAQ,GAAgB;AAAA,IAC5B,OACK;AACD,qBAAe;AACf,UAAI,QAAQ,QAAW;AACnB,eAAO;AAAA,MACX;AACA,YAAM,cAAc,GAAG;AACvB,UAAI,QAAQ,OAAO;AACf,eAAO;AAAA,MACX,OACK;AACD,gBAAQ,GAAc;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,qBAAqB;AAC1B,UAAM,WAAW,KAAK,QAAQ;AAC9B,QAAK,SAAS,KACV,aAAa,OACZ,SAAS,KACN,aAAa,KAA0B;AAC3C;AACA,gBAAU,OAAO;AACjB,cAAQ,GAAgB;AACxB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB;AACA,QAAI,KAAK;AACT,QAAI,MAAM,QAAQ,mBAAmB,GAAG;AACpC;AAAA,IACJ;AACA,WAAO,gBAAgB,CAAC;AACxB,cAAU,iBAAiB;AAC3B,iBAAa,QAAQ,SAAS,QAAQ,QAAmB;AAEzD,QAAI,eAAe,GAAe;AAC9B;AAAA,IACJ;AACA,WAAO,WAAW;AAClB,QAAI,WAAW,OAAO,QAAW;AAC7B,eAAS,QAAQ,WAAW;AAC5B,UAAI,QAAQ;AACR,kBAAU;AACV,YAAI,OAAO,MAAM,OAAO;AACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,SAAS,GAAoB;AAC7B,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAGA,SAAS,aAAa,KAAK,MAAM;AAE7B,MAAI,CAACC,UAAS,GAAG,GAAG;AAChB,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,MAAM,IAAI,IAAI;AACxB,MAAI,CAAC,KAAK;AACN,UAAM,MAAM,IAAI;AAChB,QAAI,KAAK;AACL,YAAM,IAAI,MAAM,GAAG;AAAA,IACvB;AAAA,EACJ;AAEA,MAAI,CAAC,KAAK;AACN,WAAO;AAAA,EACX;AAEA,QAAM,MAAM,IAAI;AAChB,MAAI,OAAO;AACX,MAAI,IAAI;AACR,SAAO,IAAI,KAAK;AACZ,UAAM,MAAM,KAAK,IAAI;AACrB,QAAI,QAAQ,QAAW;AACnB,aAAO;AAAA,IACX;AACA,WAAO;AACP;AAAA,EACJ;AACA,SAAO;AACX;AAIA,SAAS,eAAe,KAAK;AAEzB,MAAI,CAACA,UAAS,GAAG,GAAG;AAChB,WAAO;AAAA,EACX;AACA,aAAW,OAAO,KAAK;AAEnB,QAAI,CAACF,QAAO,KAAK,GAAG,GAAG;AACnB;AAAA,IACJ;AAEA,QAAI,CAAC,IAAI,SAAS,GAAa,GAAG;AAE9B,UAAIE,UAAS,IAAI,IAAI,GAAG;AACpB,uBAAe,IAAI,IAAI;AAAA,MAC3B;AAAA,IACJ,OAEK;AAED,YAAM,UAAU,IAAI,MAAM,GAAa;AACvC,YAAM,YAAY,QAAQ,SAAS;AACnC,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,YAAI,EAAE,QAAQ,MAAM,aAAa;AAC7B,qBAAW,QAAQ,MAAM,CAAC;AAAA,QAC9B;AACA,qBAAa,WAAW,QAAQ;AAAA,MACpC;AAEA,iBAAW,QAAQ,cAAc,IAAI;AACrC,aAAO,IAAI;AAEX,UAAIA,UAAS,WAAW,QAAQ,WAAW,GAAG;AAC1C,uBAAe,WAAW,QAAQ,WAAW;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAtSA,IAUMD,iBAIAC,WAGA,kBAkDA,gBAyJA;AA5NN;AAAA;AASA,QAAK;AAAwC;AAC7C,IAAMD,kBAAiB,OAAO,UAAU;AAIxC,IAAMC,YAAW,CAAC,QACjB,QAAQ,QAAQ,OAAO,QAAQ;AAEhC,IAAM,mBAAmB,CAAC;AAC1B,qBAAiB,KAAuB;AAAA,MACpC,CAAC,MAAsB,CAAC,CAAmB;AAAA,MAC3C,CAAC,MAAkB,CAAC,GAAkB,CAAc;AAAA,MACpD,CAAC,MAAyB,CAAC,CAAmB;AAAA,MAC9C,CAAC,MAAwB,CAAC,CAAkB;AAAA,IAChD;AACA,qBAAiB,KAAmB;AAAA,MAChC,CAAC,MAAsB,CAAC,CAAe;AAAA,MACvC,CAAC,MAAgB,CAAC,CAAoB;AAAA,MACtC,CAAC,MAAyB,CAAC,CAAmB;AAAA,MAC9C,CAAC,MAAwB,CAAC,CAAkB;AAAA,IAChD;AACA,qBAAiB,KAAwB;AAAA,MACrC,CAAC,MAAsB,CAAC,CAAoB;AAAA,MAC5C,CAAC,MAAkB,CAAC,GAAkB,CAAc;AAAA,MACpD,CAAC,MAAiB,CAAC,GAAkB,CAAc;AAAA,IACvD;AACA,qBAAiB,KAAoB;AAAA,MACjC,CAAC,MAAkB,CAAC,GAAkB,CAAc;AAAA,MACpD,CAAC,MAAiB,CAAC,GAAkB,CAAc;AAAA,MACnD,CAAC,MAAsB,CAAC,GAAiB,CAAY;AAAA,MACrD,CAAC,MAAgB,CAAC,GAAsB,CAAY;AAAA,MACpD,CAAC,MAAyB,CAAC,GAAqB,CAAY;AAAA,MAC5D,CAAC,MAAwB,CAAC,GAAoB,CAAY;AAAA,IAC9D;AACA,qBAAiB,KAAuB;AAAA,MACpC,CAAC,MAAyB,CAAC,GAAyB,CAAc;AAAA,MAClE,CAAC,MAA0B,CAAC,GAAyB,CAAc;AAAA,MACnE,CAAC,MAAyB;AAAA,QACtB;AAAA,QACA;AAAA,MACJ;AAAA,MACA,CAAC,MAA0B,CAAC,GAAiB,CAAqB;AAAA,MAClE,CAAC,MAAwB;AAAA,MACzB,CAAC,MAAiB,CAAC,GAAqB,CAAc;AAAA,IAC1D;AACA,qBAAiB,KAA2B;AAAA,MACxC,CAAC,MAAyB,CAAC,GAAqB,CAAc;AAAA,MAC9D,CAAC,MAAwB;AAAA,MACzB,CAAC,MAAiB,CAAC,GAAyB,CAAc;AAAA,IAC9D;AACA,qBAAiB,KAA2B;AAAA,MACxC,CAAC,MAA0B,CAAC,GAAqB,CAAc;AAAA,MAC/D,CAAC,MAAwB;AAAA,MACzB,CAAC,MAAiB,CAAC,GAAyB,CAAc;AAAA,IAC9D;AAIA,IAAM,iBAAiB;AAyJvB,IAAM,QAAQ,oBAAI,IAAI;AAAA;AAAA;;;AChNtB,SAAS,cAAc,QAAQ,eAAe;AAC1C,WAAS,KAAK,IAAI,MAAM;AACxB,MAAI,kBAAkB,GAAG;AAErB,WAAO,SACD,SAAS,IACL,IACA,IACJ;AAAA,EACV;AACA,SAAO,SAAS,KAAK,IAAI,QAAQ,CAAC,IAAI;AAC1C;AACA,SAAS,eAAe,SAAS;AAE7B,QAAM,QAAQ,SAAS,QAAQ,WAAW,IACpC,QAAQ,cACR;AAEN,SAAO,QAAQ,UAAU,SAAS,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,MAAM,CAAC,KAC5E,SAAS,QAAQ,MAAM,KAAK,IACxB,QAAQ,MAAM,QACd,SAAS,QAAQ,MAAM,CAAC,IACpB,QAAQ,MAAM,IACd,QACR;AACV;AACA,SAAS,eAAe,aAAa,OAAO;AACxC,MAAI,CAAC,MAAM,OAAO;AACd,UAAM,QAAQ;AAAA,EAClB;AACA,MAAI,CAAC,MAAM,GAAG;AACV,UAAM,IAAI;AAAA,EACd;AACJ;AACA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AACxC,QAAM,SAAS,QAAQ;AACvB,QAAM,cAAc,eAAe,OAAO;AAC1C,QAAM,aAAa,SAAS,QAAQ,WAAW,KAC3C,SAAS,MAAM,KACf,WAAW,QAAQ,YAAY,OAAO,IACpC,QAAQ,YAAY,UACpB;AACN,QAAM,gBAAgB,SAAS,QAAQ,WAAW,KAC9C,SAAS,MAAM,KACf,WAAW,QAAQ,YAAY,OAAO,IACpC,gBACA;AACN,QAAM,SAAS,CAAC,aAAa,SAAS,WAAW,aAAa,SAAS,QAAQ,aAAa;AAC5F,QAAM,QAAQ,QAAQ,QAAQ,CAAC;AAC/B,QAAM,OAAO,CAAC,UAAU,MAAM;AAE9B,QAAM,SAAS,QAAQ,SAAS,CAAC;AACjC,WAAS,QAAQ,WAAW,KAAK,eAAe,aAAa,MAAM;AACnE,QAAM,QAAQ,CAAC,QAAQ,OAAO;AAE9B,WAAS,QAAQ,KAAK;AAElB,UAAM,MAAM,WAAW,QAAQ,QAAQ,IACjC,QAAQ,SAAS,GAAG,IACpB,SAAS,QAAQ,QAAQ,IACrB,QAAQ,SAAS,OACjB;AACV,WAAO,CAAC,MACF,QAAQ,SACJ,QAAQ,OAAO,QAAQ,GAAG,IAC1B,kBACJ;AAAA,EACV;AACA,QAAM,YAAY,CAAC,SAAS,QAAQ,YAC9B,QAAQ,UAAU,QAClB;AACN,QAAM,YAAY,cAAc,QAAQ,SAAS,KAAK,WAAW,QAAQ,UAAU,SAAS,IACtF,QAAQ,UAAU,YAClB;AACN,QAAM,cAAc,cAAc,QAAQ,SAAS,KAC/C,WAAW,QAAQ,UAAU,WAAW,IACtC,QAAQ,UAAU,cAClB;AACN,QAAM,OAAO,cAAc,QAAQ,SAAS,KAAK,SAAS,QAAQ,UAAU,IAAI,IAC1E,QAAQ,UAAU,OAClB;AACN,QAAM,MAAM;AAAA,IACR,CAAC,SAAoB;AAAA,IACrB,CAAC,UAAsB;AAAA,IACvB,CAAC,WAAwB;AAAA,IACzB,CAAC,WAAwB,CAAC,KAAK,aAAa;AAExC,YAAM,MAAM,QAAQ,GAAG,EAAE,GAAG;AAC5B,aAAO,SAAS,QAAQ,IAAI,UAAU,QAAQ,EAAE,GAAG,IAAI;AAAA,IAC3D;AAAA,IACA,CAAC,YAA0B;AAAA,IAC3B,CAAC,SAAoB;AAAA,IACrB,CAAC,gBAAkC;AAAA,IACnC,CAAC,cAA8B;AAAA,EACnC;AACA,SAAO;AACX;AA5GA,IAOM,kBACA,iBACA,2BACA,mBACA;AAXN;AAAA;AAKA;AAEA,IAAM,mBAAmB,CAAC,QAAQ;AAClC,IAAM,kBAAkB,CAAC,QAAQ;AACjC,IAAM,4BAA4B;AAClC,IAAM,oBAAoB,CAAC,WAAW,OAAO,WAAW,IAAI,KAAK,OAAO,KAAK,EAAE;AAC/E,IAAM,sBAAsB;AAAA;AAAA;;;ACe5B,SAAS,mBAAmB,MAAM,KAAK,UAAU,CAAC,GAAG;AACjD,QAAM,EAAE,QAAQ,UAAU,KAAK,IAAI;AACnC,QAAM,MAAO,OACP,QAAQ,YAAY,eAAe,SAAS,IAAI,GAAI,QAAQ,CAAC,CAAE,IAC/D;AACN,QAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,QAAM,OAAO;AACb,MAAI,KAAK;AACL,UAAM,WAAW;AAAA,EACrB;AACA,QAAM,SAAS;AACf,SAAO;AACX;AAEA,SAAS,eAAe,OAAO;AAC3B,QAAM;AACV;AAMA,SAAS,eAAe,MAAM,QAAQ,QAAQ;AAC1C,SAAO,EAAE,MAAM,QAAQ,OAAO;AAClC;AACA,SAAS,eAAe,OAAO,KAAK,QAAQ;AACxC,QAAM,MAAM,EAAE,OAAO,IAAI;AACzB,MAAI,UAAU,MAAM;AAChB,QAAI,SAAS;AAAA,EACjB;AACA,SAAO;AACX;AAOA,SAAS,cAAc,KAAK;AACxB,QAAM,OAAO;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,UAAU;AACd,MAAI,cAAc;AAClB,QAAM,SAAS,CAACC,WAAU,KAAKA,YAAW,WAAW,KAAKA,SAAQ,OAAO;AACzE,QAAM,OAAO,CAACA,WAAU,KAAKA,YAAW;AACxC,QAAM,OAAO,CAACA,WAAU,KAAKA,YAAW;AACxC,QAAM,OAAO,CAACA,WAAU,KAAKA,YAAW;AACxC,QAAM,YAAY,CAACA,WAAU,OAAOA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK;AACtF,QAAM,QAAQ,MAAM;AACpB,QAAM,OAAO,MAAM;AACnB,QAAM,SAAS,MAAM;AACrB,QAAM,aAAa,MAAM;AACzB,QAAM,SAAS,CAAC,WAAW,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,KAAK;AAC3F,QAAM,cAAc,MAAM,OAAO,MAAM;AACvC,QAAM,cAAc,MAAM,OAAO,SAAS,WAAW;AACrD,WAAS,OAAO;AACZ,kBAAc;AACd,QAAI,UAAU,MAAM,GAAG;AACnB;AACA,gBAAU;AAAA,IACd;AACA,QAAI,OAAO,MAAM,GAAG;AAChB;AAAA,IACJ;AACA;AACA;AACA,WAAO,KAAK;AAAA,EAChB;AACA,WAAS,OAAO;AACZ,QAAI,OAAO,SAAS,WAAW,GAAG;AAC9B;AAAA,IACJ;AACA;AACA,WAAO,KAAK,SAAS;AAAA,EACzB;AACA,WAAS,QAAQ;AACb,aAAS;AACT,YAAQ;AACR,cAAU;AACV,kBAAc;AAAA,EAClB;AACA,WAAS,UAAU,SAAS,GAAG;AAC3B,kBAAc;AAAA,EAClB;AACA,WAAS,aAAa;AAClB,UAAM,SAAS,SAAS;AAExB,WAAO,WAAW,QAAQ;AACtB,WAAK;AAAA,IACT;AACA,kBAAc;AAAA,EAClB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAKA,SAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,QAAQ,cAAc,MAAM;AAClC,QAAM,gBAAgB,MAAM,MAAM,MAAM;AACxC,QAAM,kBAAkB,MAAM,eAAe,MAAM,KAAK,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;AACxF,QAAM,WAAW,gBAAgB;AACjC,QAAM,cAAc,cAAc;AAClC,QAAM,WAAW;AAAA,IACb,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,EACV;AACA,QAAM,UAAU,MAAM;AACtB,QAAM,EAAE,QAAQ,IAAI;AACpB,WAAS,UAAU,MAAM,KAAK,WAAW,MAAM;AAC3C,UAAM,MAAM,QAAQ;AACpB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,eAAe,IAAI,UAAU,GAAG;AAC5C,YAAM,MAAM,mBAAmB,MAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,cAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACA,WAAS,SAASC,UAAS,MAAM,OAAO;AACpC,IAAAA,SAAQ,SAAS,gBAAgB;AACjC,IAAAA,SAAQ,cAAc;AACtB,UAAM,QAAQ,EAAE,KAAK;AACrB,QAAI,UAAU;AACV,YAAM,MAAM,eAAeA,SAAQ,UAAUA,SAAQ,MAAM;AAAA,IAC/D;AACA,QAAI,SAAS,MAAM;AACf,YAAM,QAAQ;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,QAAM,cAAc,CAACA,aAAY,SAASA,UAAS,EAAY;AAC/D,WAAS,IAAI,MAAM,IAAI;AACnB,QAAI,KAAK,YAAY,MAAM,IAAI;AAC3B,WAAK,KAAK;AACV,aAAO;AAAA,IACX,OACK;AACD,gBAAU,GAAwB,gBAAgB,GAAG,GAAG,EAAE;AAC1D,aAAO;AAAA,IACX;AAAA,EACJ;AACA,WAAS,WAAW,MAAM;AACtB,QAAI,MAAM;AACV,WAAO,KAAK,YAAY,MAAM,WAAW,KAAK,YAAY,MAAM,SAAS;AACrE,aAAO,KAAK,YAAY;AACxB,WAAK,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW,MAAM;AACtB,UAAM,MAAM,WAAW,IAAI;AAC3B,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,WAAS,kBAAkB,IAAI;AAC3B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM,OACtB,MAAM,MAAM,MAAM,MACnB,OAAO;AAAA,EAEf;AACA,WAAS,cAAc,IAAI;AACvB,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B;AACA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAmB;AACnC,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAmB;AACnC,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,KAAK,KAAK,YAAY,MAAM,MAAM,KAAK,KAAK,IAAI,KAAK,YAAY;AACvE,UAAM,MAAM,cAAc,EAAE;AAC5B,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,eAAe,MAAMA,UAAS;AACnC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAmB;AACnC,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,iBAAiB,MAAMA,UAAS;AACrC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAqB;AACrC,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAmB;AACnC,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,EAAE,gBAAgB,KAClB,gBAAgB,KAA0B;AAC1C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAMA,UAAS;AACvC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,IAA0B;AAC1C,aAAO;AAAA,IACX;AACA,UAAM,KAAK,MAAM;AACb,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,KAAqB;AAC5B,eAAO,kBAAkB,KAAK,KAAK,CAAC;AAAA,MACxC,WACS,OAAO,OACZ,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,WACP,CAAC,IAAI;AACL,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG;AAAA,MACd,OACK;AAED,eAAO,kBAAkB,EAAE;AAAA,MAC/B;AAAA,IACJ;AACA,UAAM,MAAM,GAAG;AACf,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,cAAc,MAAM;AACzB,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,YAAY,MAAM,QAAQ,MAAM;AACrC,UAAM,KAAK,CAAC,WAAW,OAAO,OAAO,IAAI,eAAe,UAAU;AAC9D,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,KAAqB;AAC5B,eAAO,SAAS,MAAmB,QAAQ;AAAA,MAC/C,WACS,OAAO,OAAyB,CAAC,IAAI;AAC1C,eAAO,SAAS,MAAmB,OAAO;AAAA,MAC9C,WACS,OAAO,KAAkB;AAC9B,aAAK,KAAK;AACV,eAAO,GAAG,UAAU,KAAkB,IAAI;AAAA,MAC9C,WACS,OAAO,KAAgB;AAC5B,eAAO,SAAS,OAAoB,eAC9B,OACA,EAAE,SAAS,WAAW,SAAS;AAAA,MACzC,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,SAAS,YAAY;AAAA,MACzC,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,SAAS,YAAY;AAAA,MACzC,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,UAAM,MAAM,GAAG;AACf,aAAS,KAAK,UAAU;AACxB,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM,IAAI;AACxB,UAAM,KAAK,KAAK,YAAY;AAC5B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,QAAI,GAAG,EAAE,GAAG;AACR,WAAK,KAAK;AACV,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,UAAM,UAAU,CAAC,OAAO;AACpB,YAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,aAAS,MAAM,MAAM,MAAM,OACtB,MAAM,MAAM,MAAM,MAClB,MAAM,MAAM,MAAM,MACnB,OAAO,MACP,OAAO;AAAA,IAEf;AACA,WAAO,SAAS,MAAM,OAAO;AAAA,EACjC;AACA,WAAS,UAAU,MAAM;AACrB,UAAM,UAAU,CAAC,OAAO;AACpB,YAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,aAAO,MAAM,MAAM,MAAM;AAAA,IAC7B;AACA,WAAO,SAAS,MAAM,OAAO;AAAA,EACjC;AACA,WAAS,aAAa,MAAM;AACxB,UAAM,UAAU,CAAC,OAAO;AACpB,YAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,aAAS,MAAM,MAAM,MAAM,MACtB,MAAM,MAAM,MAAM,MAClB,MAAM,MAAM,MAAM;AAAA,IAC3B;AACA,WAAO,SAAS,MAAM,OAAO;AAAA,EACjC;AACA,WAAS,UAAU,MAAM;AACrB,QAAI,KAAK;AACT,QAAI,MAAM;AACV,WAAQ,KAAK,UAAU,IAAI,GAAI;AAC3B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM;AACpB,QAAI,MAAM;AACV,WAAO,MAAM;AACT,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL;AAAA,MACJ,WACS,OAAO,KAAkB;AAC9B,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAK;AAAA,QACd,OACK;AACD;AAAA,QACJ;AAAA,MACJ,WACS,OAAO,WAAW,OAAO,SAAS;AACvC,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAK;AAAA,QACd,WACS,cAAc,IAAI,GAAG;AAC1B;AAAA,QACJ,OACK;AACD,iBAAO;AACP,eAAK,KAAK;AAAA,QACd;AAAA,MACJ,OACK;AACD,eAAO;AACP,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB,MAAM;AAC/B,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,cAAQ;AAAA,IACZ;AACA,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,gBAAU,GAAoC,gBAAgB,GAAG,CAAC;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,eAAW,IAAI;AACf,QAAI,QAAQ;AACZ,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,WAAK,KAAK;AACV,eAAS,IAAI,UAAU,IAAI;AAAA,IAC/B,OACK;AACD,eAAS,UAAU,IAAI;AAAA,IAC3B;AACA,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,gBAAU,GAAoC,gBAAgB,GAAG,CAAC;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AACA,WAAS,YAAY,MAAM;AACvB,eAAW,IAAI;AACf,QAAI,MAAM,GAAI;AACd,QAAI,KAAK;AACT,QAAI,UAAU;AACd,UAAM,KAAK,CAAC,MAAM,MAAM,qBAAqB,MAAM;AACnD,WAAQ,KAAK,SAAS,MAAM,EAAE,GAAI;AAC9B,UAAI,OAAO,MAAM;AACb,mBAAW,mBAAmB,IAAI;AAAA,MACtC,OACK;AACD,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,YAAY,WAAW,YAAY,KAAK;AACxC,gBAAU,GAAkD,gBAAgB,GAAG,CAAC;AAEhF,UAAI,YAAY,SAAS;AACrB,aAAK,KAAK;AACV,YAAI,MAAM,GAAI;AAAA,MAClB;AACA,aAAO;AAAA,IACX;AACA,QAAI,MAAM,GAAI;AACd,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ;AAAA,WACC;AAAA,WACA;AACD,aAAK,KAAK;AACV,eAAO,KAAK;AAAA,WACX;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,WAC3C;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA;AAE5C,kBAAU,GAAiC,gBAAgB,GAAG,GAAG,EAAE;AACnE,eAAO;AAAA;AAAA,EAEnB;AACA,WAAS,0BAA0B,MAAM,SAAS,QAAQ;AACtD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAM,KAAK,aAAa,IAAI;AAC5B,UAAI,CAAC,IAAI;AACL,kBAAU,GAAyC,gBAAgB,GAAG,GAAG,KAAK,UAAU,WAAW,KAAK,YAAY,GAAG;AACvH;AAAA,MACJ;AACA,kBAAY;AAAA,IAChB;AACA,WAAO,KAAK,UAAU;AAAA,EAC1B;AACA,WAAS,sBAAsB,MAAM;AACjC,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,cAAc;AAClB,UAAM,UAAU,CAACC,QAAOA,QAAO,OAC3BA,QAAO,OACPA,QAAO,WACPA,QAAO;AACX,WAAQ,KAAK,SAAS,MAAM,OAAO,GAAI;AACnC,qBAAe;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,cAAQ;AAAA,IACZ;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,MAAM;AAC3B,UAAM,KAAK,CAAC,SAAS,OAAO,QAAQ;AAChC,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,eAAO;AACP,aAAK,KAAK;AACV,eAAO,GAAG,QAAQ,GAAG;AAAA,MACzB,OACK;AACD,eAAO;AACP,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,GAAG;AAAA,MACvB;AAAA,IACJ;AACA,WAAO,GAAG,OAAO,EAAE;AAAA,EACvB;AACA,WAAS,WAAW,MAAM;AACtB,eAAW,IAAI;AACf,UAAM,SAAS,IAAI,MAAM,GAAc;AACvC,eAAW,IAAI;AACf,WAAO;AAAA,EACX;AAEA,WAAS,uBAAuB,MAAMD,UAAS;AAC3C,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ;AAAA,WACC;AACD,YAAIA,SAAQ,aAAa,GAAG;AACxB,oBAAU,GAAoC,gBAAgB,GAAG,CAAC;AAAA,QACtE;AACA,aAAK,KAAK;AACV,gBAAQ,SAASA,UAAS,GAAmB,GAAmB;AAChE,mBAAW,IAAI;AACf,QAAAA,SAAQ;AACR,eAAO;AAAA,WACN;AACD,YAAIA,SAAQ,YAAY,KACpBA,SAAQ,gBAAgB,GAAmB;AAC3C,oBAAU,GAA2B,gBAAgB,GAAG,CAAC;AAAA,QAC7D;AACA,aAAK,KAAK;AACV,gBAAQ,SAASA,UAAS,GAAoB,GAAoB;AAClE,QAAAA,SAAQ;AACR,QAAAA,SAAQ,YAAY,KAAK,WAAW,IAAI;AACxC,YAAIA,SAAQ,YAAYA,SAAQ,cAAc,GAAG;AAC7C,UAAAA,SAAQ,WAAW;AAAA,QACvB;AACA,eAAO;AAAA,WACN;AACD,YAAIA,SAAQ,YAAY,GAAG;AACvB,oBAAU,GAAoC,gBAAgB,GAAG,CAAC;AAAA,QACtE;AACA,gBAAQ,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAC/D,QAAAA,SAAQ,YAAY;AACpB,eAAO;AAAA;AAEP,YAAI,uBAAuB;AAC3B,YAAI,sBAAsB;AAC1B,YAAI,eAAe;AACnB,YAAI,cAAc,IAAI,GAAG;AACrB,cAAIA,SAAQ,YAAY,GAAG;AACvB,sBAAU,GAAoC,gBAAgB,GAAG,CAAC;AAAA,UACtE;AACA,kBAAQ,SAASA,UAAS,GAAc,WAAW,IAAI,CAAC;AAExD,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAIA,SAAQ,YAAY,MACnBA,SAAQ,gBAAgB,KACrBA,SAAQ,gBAAgB,KACxBA,SAAQ,gBAAgB,IAAkB;AAC9C,oBAAU,GAAoC,gBAAgB,GAAG,CAAC;AAClE,UAAAA,SAAQ,YAAY;AACpB,iBAAO,UAAU,MAAMA,QAAO;AAAA,QAClC;AACA,YAAK,uBAAuB,uBAAuB,MAAMA,QAAO,GAAI;AAChE,kBAAQ,SAASA,UAAS,GAAe,oBAAoB,IAAI,CAAC;AAClE,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAK,sBAAsB,sBAAsB,MAAMA,QAAO,GAAI;AAC9D,kBAAQ,SAASA,UAAS,GAAc,mBAAmB,IAAI,CAAC;AAChE,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAK,eAAe,eAAe,MAAMA,QAAO,GAAI;AAChD,kBAAQ,SAASA,UAAS,GAAiB,YAAY,IAAI,CAAC;AAC5D,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,cAAc;AAEhE,kBAAQ,SAASA,UAAS,IAAuB,sBAAsB,IAAI,CAAC;AAC5E,oBAAU,GAAsC,gBAAgB,GAAG,GAAG,MAAM,KAAK;AACjF,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA;AAAA;AAER,WAAO;AAAA,EACX;AAEA,WAAS,kBAAkB,MAAMA,UAAS;AACtC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK,YAAY;AAC5B,SAAK,gBAAgB,KACjB,gBAAgB,KAChB,gBAAgB,MAChB,gBAAgB,QACf,OAAO,WAAW,OAAO,UAAU;AACpC,gBAAU,GAA+B,gBAAgB,GAAG,CAAC;AAAA,IACjE;AACA,YAAQ;AAAA,WACC;AACD,aAAK,KAAK;AACV,gBAAQ,SAASA,UAAS,GAAqB,GAAqB;AACpE,QAAAA,SAAQ,WAAW;AACnB,eAAO;AAAA,WACN;AACD,mBAAW,IAAI;AACf,aAAK,KAAK;AACV,eAAO,SAASA,UAAS,GAAmB,GAAmB;AAAA,WAC9D;AACD,mBAAW,IAAI;AACf,aAAK,KAAK;AACV,eAAO,SAASA,UAAS,IAA0B,GAAyB;AAAA;AAE5E,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAc,WAAW,IAAI,CAAC;AAExD,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,MAAMA,QAAO,KAC9B,uBAAuB,MAAMA,QAAO,GAAG;AACvC,qBAAW,IAAI;AACf,iBAAO,kBAAkB,MAAMA,QAAO;AAAA,QAC1C;AACA,YAAI,sBAAsB,MAAMA,QAAO,GAAG;AACtC,qBAAW,IAAI;AACf,iBAAO,SAASA,UAAS,IAAyB,mBAAmB,IAAI,CAAC;AAAA,QAC9E;AACA,YAAI,mBAAmB,MAAMA,QAAO,GAAG;AACnC,qBAAW,IAAI;AACf,cAAI,OAAO,KAAqB;AAE5B,mBAAO,uBAAuB,MAAMA,QAAO,KAAK;AAAA,UACpD,OACK;AACD,mBAAO,SAASA,UAAS,IAAoB,gBAAgB,IAAI,CAAC;AAAA,UACtE;AAAA,QACJ;AACA,YAAI,gBAAgB,GAAqB;AACrC,oBAAU,GAA+B,gBAAgB,GAAG,CAAC;AAAA,QACjE;AACA,QAAAA,SAAQ,YAAY;AACpB,QAAAA,SAAQ,WAAW;AACnB,eAAO,UAAU,MAAMA,QAAO;AAAA;AAAA,EAE1C;AAEA,WAAS,UAAU,MAAMA,UAAS;AAC9B,QAAI,QAAQ,EAAE,MAAM,GAAa;AACjC,QAAIA,SAAQ,YAAY,GAAG;AACvB,aAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IACvE;AACA,QAAIA,SAAQ,UAAU;AAClB,aAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IAClE;AACA,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ;AAAA,WACC;AACD,eAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,WAClE;AACD,kBAAU,GAAkC,gBAAgB,GAAG,CAAC;AAChE,aAAK,KAAK;AACV,eAAO,SAASA,UAAS,GAAoB,GAAoB;AAAA,WAChE;AACD,eAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA;AAE9D,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAc,WAAW,IAAI,CAAC;AAExD,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO,SAASA,UAAS,GAAc,SAAS,IAAI,CAAC;AAAA,QACzD;AACA,YAAI,OAAO,KAAkB;AACzB,eAAK,KAAK;AACV,iBAAO,SAASA,UAAS,GAAgB,GAAgB;AAAA,QAC7D;AACA;AAAA;AAER,WAAO;AAAA,EACX;AACA,WAAS,YAAY;AACjB,UAAM,EAAE,aAAa,QAAQ,UAAU,OAAO,IAAI;AAClD,aAAS,WAAW;AACpB,aAAS,aAAa;AACtB,aAAS,eAAe;AACxB,aAAS,aAAa;AACtB,aAAS,SAAS,cAAc;AAChC,aAAS,WAAW,gBAAgB;AACpC,QAAI,MAAM,YAAY,MAAM,KAAK;AAC7B,aAAO,SAAS,UAAU,EAAY;AAAA,IAC1C;AACA,WAAO,UAAU,OAAO,QAAQ;AAAA,EACpC;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAKA,SAAS,mBAAmB,OAAO,YAAY,YAAY;AACvD,UAAQ;AAAA,SACC;AACD,aAAO;AAAA,SACN;AACD,aAAO;AAAA,aACF;AACL,YAAM,YAAY,SAAS,cAAc,YAAY,EAAE;AACvD,UAAI,aAAa,SAAU,aAAa,OAAQ;AAC5C,eAAO,OAAO,cAAc,SAAS;AAAA,MACzC;AAGA,aAAO;AAAA,IACX;AAAA;AAER;AACA,SAAS,aAAa,UAAU,CAAC,GAAG;AAChC,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,EAAE,QAAQ,IAAI;AACpB,WAAS,UAAU,UAAU,MAAM,OAAO,WAAW,MAAM;AACvD,UAAM,MAAM,SAAS,gBAAgB;AACrC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,eAAe,OAAO,GAAG;AACrC,YAAM,MAAM,mBAAmB,MAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,cAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACA,WAAS,UAAU,MAAM,QAAQ,KAAK;AAClC,UAAM,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,KAAK;AAAA,IACT;AACA,QAAI,UAAU;AACV,WAAK,MAAM,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IACtC;AACA,WAAO;AAAA,EACX;AACA,WAAS,QAAQ,MAAM,QAAQ,KAAK,MAAM;AACtC,SAAK,MAAM;AACX,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IAChB;AACA,QAAI,YAAY,KAAK,KAAK;AACtB,WAAK,IAAI,MAAM;AAAA,IACnB;AAAA,EACJ;AACA,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAAc,QAAQ,QAAQ,QAAQ,QAAQ;AACrE,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAc,QAAQ,GAAG;AAChD,SAAK,QAAQ,SAAS,OAAO,EAAE;AAC/B,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,WAAW,WAAW,KAAK;AAChC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAe,QAAQ,GAAG;AACjD,SAAK,MAAM;AACX,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,aAAa,WAAW,OAAO;AACpC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAiB,QAAQ,GAAG;AACnD,SAAK,QAAQ,MAAM,QAAQ,eAAe,kBAAkB;AAC5D,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB,WAAW;AACpC,UAAM,QAAQ,UAAU,UAAU;AAClC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAwB,QAAQ,GAAG;AAC1D,QAAI,MAAM,SAAS,IAAyB;AAExC,gBAAU,WAAW,IAA2C,QAAQ,cAAc,CAAC;AACvF,WAAK,QAAQ;AACb,cAAQ,MAAM,QAAQ,GAAG;AACzB,aAAO;AAAA,QACH,kBAAkB;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,MAAM,SAAS,MAAM;AACrB,gBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IAC9G;AACA,SAAK,QAAQ,MAAM,SAAS;AAC5B,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,MACH;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,eAAe,WAAW,OAAO;AACtC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAAmB,QAAQ,QAAQ,QAAQ,QAAQ;AAC1E,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,YAAY,WAAW;AAC5B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,aAAa,UAAU,GAAgB,QAAQ,QAAQ,QAAQ,QAAQ;AAC7E,QAAI,QAAQ,UAAU,UAAU;AAChC,QAAI,MAAM,SAAS,GAAmB;AAClC,YAAM,SAAS,oBAAoB,SAAS;AAC5C,iBAAW,WAAW,OAAO;AAC7B,cAAQ,OAAO,oBAAoB,UAAU,UAAU;AAAA,IAC3D;AAEA,QAAI,MAAM,SAAS,IAA0B;AACzC,gBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IAC9G;AACA,YAAQ,UAAU,UAAU;AAE5B,QAAI,MAAM,SAAS,GAAmB;AAClC,cAAQ,UAAU,UAAU;AAAA,IAChC;AACA,YAAQ,MAAM;AAAA,WACL;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QAC9G;AACA,mBAAW,MAAM,eAAe,WAAW,MAAM,SAAS,EAAE;AAC5D;AAAA,WACC;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QAC9G;AACA,mBAAW,MAAM,WAAW,WAAW,MAAM,SAAS,EAAE;AACxD;AAAA,WACC;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QAC9G;AACA,mBAAW,MAAM,UAAU,WAAW,MAAM,SAAS,EAAE;AACvD;AAAA,WACC;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QAC9G;AACA,mBAAW,MAAM,aAAa,WAAW,MAAM,SAAS,EAAE;AAC1D;AAAA;AAGA,kBAAU,WAAW,IAAsC,QAAQ,cAAc,CAAC;AAClF,cAAM,cAAc,UAAU,QAAQ;AACtC,cAAM,qBAAqB,UAAU,GAAmB,YAAY,QAAQ,YAAY,QAAQ;AAChG,2BAAmB,QAAQ;AAC3B,gBAAQ,oBAAoB,YAAY,QAAQ,YAAY,QAAQ;AACpE,mBAAW,MAAM;AACjB,gBAAQ,YAAY,YAAY,QAAQ,YAAY,QAAQ;AAC5D,eAAO;AAAA,UACH,kBAAkB;AAAA,UAClB,MAAM;AAAA,QACV;AAAA;AAER,YAAQ,YAAY,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AAC1E,WAAO;AAAA,MACH,MAAM;AAAA,IACV;AAAA,EACJ;AACA,WAAS,aAAa,WAAW;AAC7B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,cAAc,QAAQ,gBAAgB,IACtC,UAAU,cAAc,IACxB,QAAQ;AACd,UAAM,WAAW,QAAQ,gBAAgB,IACnC,QAAQ,SACR,QAAQ;AACd,UAAM,OAAO,UAAU,GAAiB,aAAa,QAAQ;AAC7D,SAAK,QAAQ,CAAC;AACd,QAAI,YAAY;AAChB,OAAG;AACC,YAAM,QAAQ,aAAa,UAAU,UAAU;AAC/C,kBAAY;AACZ,cAAQ,MAAM;AAAA,aACL;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UAC9G;AACA,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,aACC;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UAC9G;AACA,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,aACC;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UAC9G;AACA,eAAK,MAAM,KAAK,WAAW,WAAW,MAAM,SAAS,EAAE,CAAC;AACxD;AAAA,aACC;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UAC9G;AACA,eAAK,MAAM,KAAK,aAAa,WAAW,MAAM,SAAS,EAAE,CAAC;AAC1D;AAAA,aACC;AACD,gBAAM,SAAS,YAAY,SAAS;AACpC,eAAK,MAAM,KAAK,OAAO,IAAI;AAC3B,sBAAY,OAAO,oBAAoB;AACvC;AAAA;AAAA,IAEZ,SAAS,QAAQ,gBAAgB,MAC7B,QAAQ,gBAAgB;AAE5B,UAAM,YAAY,QAAQ,gBAAgB,IACpC,QAAQ,aACR,UAAU,cAAc;AAC9B,UAAM,SAAS,QAAQ,gBAAgB,IACjC,QAAQ,aACR,UAAU,gBAAgB;AAChC,YAAQ,MAAM,WAAW,MAAM;AAC/B,WAAO;AAAA,EACX;AACA,WAAS,YAAY,WAAW,QAAQ,KAAK,SAAS;AAClD,UAAM,UAAU,UAAU,QAAQ;AAClC,QAAI,kBAAkB,QAAQ,MAAM,WAAW;AAC/C,UAAM,OAAO,UAAU,GAAgB,QAAQ,GAAG;AAClD,SAAK,QAAQ,CAAC;AACd,SAAK,MAAM,KAAK,OAAO;AACvB,OAAG;AACC,YAAM,MAAM,aAAa,SAAS;AAClC,UAAI,CAAC,iBAAiB;AAClB,0BAAkB,IAAI,MAAM,WAAW;AAAA,MAC3C;AACA,WAAK,MAAM,KAAK,GAAG;AAAA,IACvB,SAAS,QAAQ,gBAAgB;AACjC,QAAI,iBAAiB;AACjB,gBAAU,WAAW,IAAuC,KAAK,CAAC;AAAA,IACtE;AACA,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,cAAc,WAAW;AAC9B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,UAAM,UAAU,aAAa,SAAS;AACtC,QAAI,QAAQ,gBAAgB,IAAc;AACtC,aAAO;AAAA,IACX,OACK;AACD,aAAO,YAAY,WAAW,QAAQ,UAAU,OAAO;AAAA,IAC3D;AAAA,EACJ;AACA,WAASE,OAAM,QAAQ;AACnB,UAAM,YAAY,gBAAgB,QAAQ,OAAO,CAAC,GAAG,OAAO,CAAC;AAC7D,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAAkB,QAAQ,QAAQ,QAAQ,QAAQ;AACzE,QAAI,YAAY,KAAK,KAAK;AACtB,WAAK,IAAI,SAAS;AAAA,IACtB;AACA,SAAK,OAAO,cAAc,SAAS;AAEnC,QAAI,QAAQ,gBAAgB,IAAc;AACtC,gBAAU,WAAW,IAAsC,QAAQ,cAAc,GAAG,OAAO,QAAQ,WAAW,EAAE;AAAA,IACpH;AACA,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,SAAO,EAAE,OAAAA,OAAM;AACnB;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI,MAAM,SAAS,IAAc;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,MAAM,SAAS,IAAI,QAAQ,WAAW,KAAK;AACzD,SAAO,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,CAAC,IAAI,WAAM;AACvD;AAEA,SAAS,kBAAkB,KAAK,UAAU,CAAC,GACzC;AACE,QAAM,WAAW;AAAA,IACb;AAAA,IACA,SAAS,oBAAI,IAAI;AAAA,EACrB;AACA,QAAM,UAAU,MAAM;AACtB,QAAM,SAAS,CAAC,SAAS;AACrB,aAAS,QAAQ,IAAI,IAAI;AACzB,WAAO;AAAA,EACX;AACA,SAAO,EAAE,SAAS,OAAO;AAC7B;AACA,SAAS,cAAc,OAAO,aAAa;AACvC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,iBAAa,MAAM,IAAI,WAAW;AAAA,EACtC;AACJ;AACA,SAAS,aAAa,MAAM,aAAa;AAErC,UAAQ,KAAK;AAAA,SACJ;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC,kBAAY,OAAO,QAAqB;AACxC;AAAA,SACC;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC;AAAA,SACC;AACD,YAAM,SAAS;AACf,mBAAa,OAAO,KAAK,WAAW;AACpC,kBAAY,OAAO,QAAqB;AACxC;AAAA,SACC;AACD,kBAAY,OAAO,aAA+B;AAClD,kBAAY,OAAO,MAAiB;AACpC;AAAA,SACC;AACD,kBAAY,OAAO,aAA+B;AAClD,kBAAY,OAAO,OAAmB;AACtC;AAAA;AAGZ;AAEA,SAAS,UAAU,KAAK,UAAU,CAAC,GACjC;AACE,QAAM,cAAc,kBAAkB,GAAG;AACzC,cAAY,OAAO,WAA2B;AAE9C,MAAI,QAAQ,aAAa,IAAI,MAAM,WAAW;AAE9C,QAAM,UAAU,YAAY,QAAQ;AACpC,MAAI,UAAU,MAAM,KAAK,QAAQ,OAAO;AAC5C;AAEA,SAAS,oBAAoB,KAAK,SAAS;AACvC,QAAM,EAAE,WAAW,UAAU,eAAe,YAAY,YAAY,IAAI;AACxE,QAAM,WAAW;AAAA,IACb,QAAQ,IAAI,IAAI;AAAA,IAChB;AAAA,IACA,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACA,QAAM,UAAU,MAAM;AACtB,WAAS,KAAK,MAAM,MAAM;AACtB,aAAS,QAAQ;AAAA,EACrB;AACA,WAAS,SAAS,GAAG,gBAAgB,MAAM;AACvC,UAAM,iBAAiB,gBAAgB,gBAAgB;AACvD,SAAK,cAAc,iBAAiB,KAAK,OAAO,CAAC,IAAI,cAAc;AAAA,EACvE;AACA,WAAS,OAAO,cAAc,MAAM;AAChC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EACjC;AACA,WAAS,SAAS,cAAc,MAAM;AAClC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EACjC;AACA,WAAS,UAAU;AACf,aAAS,SAAS,WAAW;AAAA,EACjC;AACA,QAAM,SAAS,CAAC,QAAQ,IAAI;AAC5B,QAAM,aAAa,MAAM,SAAS;AAClC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,OAAO,IAAI;AACnB,YAAU,KAAK,GAAG,OAAO,QAAqB,IAAI;AAClD,eAAa,WAAW,KAAK,GAAG;AAChC,MAAI,KAAK,UAAU;AACf,cAAU,KAAK,IAAI;AACnB,iBAAa,WAAW,KAAK,QAAQ;AAAA,EACzC;AACA,YAAU,KAAK,GAAG;AACtB;AACA,SAAS,oBAAoB,WAAW,MAAM;AAC1C,QAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,YAAU,KAAK,GAAG,OAAO,WAA2B,KAAK;AACzD,YAAU,OAAO,WAAW,CAAC;AAC7B,QAAM,SAAS,KAAK,MAAM;AAC1B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,iBAAa,WAAW,KAAK,MAAM,EAAE;AACrC,QAAI,MAAM,SAAS,GAAG;AAClB;AAAA,IACJ;AACA,cAAU,KAAK,IAAI;AAAA,EACvB;AACA,YAAU,SAAS,WAAW,CAAC;AAC/B,YAAU,KAAK,IAAI;AACvB;AACA,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,MAAI,KAAK,MAAM,SAAS,GAAG;AACvB,cAAU,KAAK,GAAG,OAAO,QAAqB,KAAK;AACnD,cAAU,OAAO,WAAW,CAAC;AAC7B,UAAM,SAAS,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,mBAAa,WAAW,KAAK,MAAM,EAAE;AACrC,UAAI,MAAM,SAAS,GAAG;AAClB;AAAA,MACJ;AACA,gBAAU,KAAK,IAAI;AAAA,IACvB;AACA,cAAU,SAAS,WAAW,CAAC;AAC/B,cAAU,KAAK,IAAI;AAAA,EACvB;AACJ;AACA,SAAS,iBAAiB,WAAW,MAAM;AACvC,MAAI,KAAK,MAAM;AACX,iBAAa,WAAW,KAAK,IAAI;AAAA,EACrC,OACK;AACD,cAAU,KAAK,MAAM;AAAA,EACzB;AACJ;AACA,SAAS,aAAa,WAAW,MAAM;AACnC,QAAM,EAAE,OAAO,IAAI;AACnB,UAAQ,KAAK;AAAA,SACJ;AACD,uBAAiB,WAAW,IAAI;AAChC;AAAA,SACC;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,SACC;AACD,0BAAoB,WAAW,IAAI;AACnC;AAAA,SACC;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,SACC;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,SACC;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,SACC;AACD,gBAAU,KAAK,GAAG,OAAO,aAA+B,KAAK,OAAO,MAAiB,KAAK,KAAK,WAAW,IAAI;AAC9G;AAAA,SACC;AACD,gBAAU,KAAK,GAAG,OAAO,aAA+B,KAAK,OAAO,OAAmB,KAAK,KAAK,UAAU,KAAK,GAAG,OAAO,IAAI;AAC9H;AAAA,SACC;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,SACC;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA;AAEA,UAAK,MAAwC;AACzC,cAAM,IAAI,MAAM,gCAAgC,KAAK,MAAM;AAAA,MAC/D;AAAA;AAEZ;AA0CA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACvC,QAAM,kBAAkB,OAAO,CAAC,GAAG,OAAO;AAE1C,QAAM,SAAS,aAAa,eAAe;AAC3C,QAAM,MAAM,OAAO,MAAM,MAAM;AAE/B,YAAU,KAAK,eAAe;AAE9B,SAAO,SAAS,KAAK,eAAe;AACxC;AAzyCA,IAQM,eAmDA,SACA,SACA,SACA,SACA,SAwEA,KACA,mBACA,gBAwoBA,cAEA,eAqeA;AAxvCN;AAAA;AAKA;AAGA,IAAM,gBAAgB;AAAA,MAElB,CAAC,IAAyB;AAAA,MAC1B,CAAC,IAAuC;AAAA,MACxC,CAAC,IAAmD;AAAA,MACpD,CAAC,IAAkC;AAAA,MACnC,CAAC,IAA0C;AAAA,MAC3C,CAAC,IAAmC;AAAA,MACpC,CAAC,IAAqC;AAAA,MACtC,CAAC,IAA4B;AAAA,MAC7B,CAAC,IAAqC;AAAA,MACtC,CAAC,IAAgC;AAAA,MAEjC,CAAC,KAAwC;AAAA,MACzC,CAAC,KAA4C;AAAA,MAC7C,CAAC,KAAuC;AAAA,MACxC,CAAC,KAAuC;AAAA,IAC5C;AAkCA,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,IAAM,UAAU,OAAO,aAAa,IAAM;AAwE1C,IAAM,MAAM;AACZ,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AAwoBvB,IAAM,eAAe;AAErB,IAAM,gBAAgB;AAqetB,IAAM,WAAW,CAAC,KAAK,UAAU,CAAC,MAC7B;AACD,YAAM,OAAO,SAAS,QAAQ,IAAI,IAAI,QAAQ,OAAO;AACrD,YAAM,WAAW,SAAS,QAAQ,QAAQ,IACpC,QAAQ,WACR;AACN,YAAM,YAAY,CAAC,CAAC,QAAQ;AAE5B,YAAM,gBAAgB,QAAQ,iBAAiB,OACzC,QAAQ,gBACR,SAAS,UACL,MACA;AACV,YAAM,aAAa,QAAQ,aAAa,QAAQ,aAAa,SAAS;AACtE,YAAM,UAAU,IAAI,WAAW,CAAC;AAChC,YAAM,YAAY,oBAAoB,KAAK;AAAA,QACvC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,gBAAU,KAAK,SAAS,WAAW,6BAA6B,YAAY;AAC5E,gBAAU,OAAO,UAAU;AAC3B,UAAI,QAAQ,SAAS,GAAG;AACpB,kBAAU,KAAK,WAAW,QAAQ,IAAI,OAAK,GAAG,OAAO,GAAG,EAAE,KAAK,IAAI,WAAW;AAC9E,kBAAU,QAAQ;AAAA,MACtB;AACA,gBAAU,KAAK,SAAS;AACxB,mBAAa,WAAW,GAAG;AAC3B,gBAAU,SAAS,UAAU;AAC7B,gBAAU,KAAK,GAAG;AAClB,YAAM,EAAE,MAAM,IAAI,IAAI,UAAU,QAAQ;AACxC,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,KAAK,MAAM,IAAI,OAAO,IAAI;AAAA,MAC9B;AAAA,IACJ;AAAA;AAAA;;;AC9xCA,IAKM;AALN;AAAA;AAKA,IAAM,uBAAuB;AAAA,MACzB,UAAU;AAAA,MACV,mBAAmB;AAAA,IACvB;AAAA;AAAA;;;ACRA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,SAAS,gBAAgB,MAAM;AAC3B,aAAW;AACf;AACA,SAAS,kBAAkB;AACvB,SAAO;AACX;AACA,SAAS,iBAAiB,MAAM,SAAS,MAAM;AAE3C,cACI,SAAS,KAAK,qBAAqB,UAAU;AAAA,IACzC,WAAW,KAAK,IAAI;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACT;AAEA,SAAS,mBAAmB,MAAM;AAC9B,SAAO,CAAC,aAAa,YAAY,SAAS,KAAK,MAAM,QAAQ;AACjE;AAWA,SAAS,eAAe,SAAS,MAAM;AACnC,SAAO,OAAO,aAAa,OAAO,GAAG,IAAI;AAC7C;AASA,SAAS,4BAA4B;AACjC,SAAO;AAAA,IACH,OAAO,CAAC,QAAS,SAAS,GAAG,IAAI,IAAI,YAAY,IAAI;AAAA,IACrD,OAAO,CAAC,QAAS,SAAS,GAAG,IAAI,IAAI,YAAY,IAAI;AAAA,IAErD,YAAY,CAAC,QAAS,SAAS,GAAG,IAC5B,GAAG,IAAI,OAAO,CAAC,EAAE,kBAAkB,IAAI,IAAI,OAAO,CAAC,MACnD;AAAA,EACV;AACJ;AAEA,SAAS,wBAAwB,UAAU;AACvC,cAAY;AAChB;AASA,SAAS,kBAAkB,UAAU,CAAC,GAAG;AAErC,QAAM,UAAU,SAAS,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC9D,QAAM,SAAS,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAC3D,QAAM,iBAAiB,QAAQ,QAAQ,cAAc,KACjD,cAAc,QAAQ,cAAc,KACpC,SAAS,QAAQ,cAAc,KAC/B,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,QAAM,WAAW,cAAc,QAAQ,QAAQ,IACzC,QAAQ,WACR,EAAE,CAAC,SAAS,CAAC,EAAE;AACrB,QAAM,kBAAkB,cAAc,QAAQ,eAAe,IACvD,QAAQ,kBACR,EAAE,CAAC,SAAS,CAAC,EAAE;AACrB,QAAM,gBAAgB,cAAc,QAAQ,aAAa,IACnD,QAAQ,gBACR,EAAE,CAAC,SAAS,CAAC,EAAE;AACrB,QAAM,YAAY,OAAO,CAAC,GAAG,QAAQ,aAAa,CAAC,GAAG,0BAA0B,CAAC;AACjF,QAAM,cAAc,QAAQ,eAAe,CAAC;AAC5C,QAAM,UAAU,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAChE,QAAM,cAAc,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC5E,QAAQ,cACR;AACN,QAAM,eAAe,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC/E,QAAQ,eACR;AACN,QAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,QAAM,cAAc,CAAC,CAAC,QAAQ;AAC9B,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,QAAM,YAAY,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY;AACzE,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AACN,QAAM,kBAAkB,CAAC,CAAC,QAAQ;AAClC,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,QAAM,SAAS,WAAW,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAE7D,QAAM,kBAAkB;AACxB,QAAM,uBAAuB,SAAS,gBAAgB,oBAAoB,IACpE,gBAAgB,uBAChB,oBAAI,IAAI;AACd,QAAM,qBAAqB,SAAS,gBAAgB,kBAAkB,IAChE,gBAAgB,qBAChB,oBAAI,IAAI;AACd,QAAM,SAAS,SAAS,gBAAgB,MAAM,IAAI,gBAAgB,SAAS,CAAC;AAC5E;AACA,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,MAAK,MAAwC;AACzC,YAAQ,cACJ,gBAAgB,eAAe,OACzB,gBAAgB,cAChB;AAAA,EACd;AAEA,MAAK,MAAqE;AACtE,qBAAiB,SAAS,SAAS,MAAM;AAAA,EAC7C;AACA,SAAO;AACX;AAEA,SAAS,wBAAwB,UAAU,KAAK;AAC5C,SAAO,oBAAoB,SAAS,SAAS,KAAK,GAAG,IAAI;AAC7D;AAEA,SAAS,uBAAuB,SAAS,KAAK;AAC1C,SAAO,mBAAmB,SAAS,QAAQ,KAAK,GAAG,IAAI;AAC3D;AAEA,SAAS,cAAc,SAAS,KAAK,QAAQ,aAAa,MAAM;AAC5D,QAAM,EAAE,SAAS,OAAO,IAAI;AAE5B,MAAK,MAAwC;AACzC,UAAM,UAAU,QAAQ;AACxB,QAAI,SAAS;AACT,cAAQ,KAAK,WAAyB;AAAA,QAClC;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,GAAG,QAAQ;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,YAAY,MAAM;AAClB,UAAM,MAAM,QAAQ,SAAS,QAAQ,KAAK,IAAI;AAC9C,WAAO,SAAS,GAAG,IAAI,MAAM;AAAA,EACjC,OACK;AACD,QAA+C,uBAAuB,aAAa,GAAG,GAAG;AACrF,aAAO,eAAe,GAAuB,EAAE,KAAK,OAAO,CAAC,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,eAAe,KAAK,UAAU,OAAO;AAC1C,QAAM,UAAU;AAChB,MAAI,CAAC,QAAQ,oBAAoB;AAC7B,YAAQ,qBAAqB,oBAAI,IAAI;AAAA,EACzC;AACA,MAAI,QAAQ,QAAQ,mBAAmB,IAAI,KAAK;AAChD,MAAI,CAAC,OAAO;AACR,YAAQ,CAAC;AAET,QAAI,QAAQ,CAAC,KAAK;AAElB,WAAO,QAAQ,KAAK,GAAG;AACnB,cAAQ,mBAAmB,OAAO,OAAO,QAAQ;AAAA,IACrD;AAGA,UAAM,WAAW,QAAQ,QAAQ,IAC3B,WACA,cAAc,QAAQ,IAClB,SAAS,aACL,SAAS,aACT,OACJ;AAEV,YAAQ,SAAS,QAAQ,IAAI,CAAC,QAAQ,IAAI;AAC1C,QAAI,QAAQ,KAAK,GAAG;AAChB,yBAAmB,OAAO,OAAO,KAAK;AAAA,IAC1C;AACA,YAAQ,mBAAmB,IAAI,OAAO,KAAK;AAAA,EAC/C;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,OAAO,QAAQ;AAC9C,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,UAAU,UAAU,MAAM,GAAG,KAAK;AACxD,UAAM,SAAS,MAAM;AACrB,QAAI,SAAS,MAAM,GAAG;AAClB,eAAS,oBAAoB,OAAO,MAAM,IAAI,MAAM;AAAA,IACxD;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,OAAO,QAAQ,QAAQ;AAChD,MAAI;AACJ,QAAM,SAAS,OAAO,MAAM,GAAG;AAC/B,KAAG;AACC,UAAM,SAAS,OAAO,KAAK,GAAG;AAC9B,aAAS,kBAAkB,OAAO,QAAQ,MAAM;AAChD,WAAO,OAAO,IAAI,CAAC;AAAA,EACvB,SAAS,OAAO,UAAU,WAAW;AACrC,SAAO;AACX;AACA,SAAS,kBAAkB,OAAO,QAAQ,QAAQ;AAC9C,MAAI,SAAS;AACb,MAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AACzB,aAAS;AACT,QAAI,QAAQ;AACR,eAAS,OAAO,OAAO,SAAS,OAAO;AACvC,YAAM,SAAS,OAAO,QAAQ,MAAM,EAAE;AACtC,YAAM,KAAK,MAAM;AACjB,WAAK,QAAQ,MAAM,KAAK,cAAc,MAAM,MACxC,OAAO,SACT;AAEE,iBAAS,OAAO;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,qBAAqB,KAAK,QAAQ,UAAU;AACjD,QAAM,UAAU;AAChB,UAAQ,qBAAqB,oBAAI,IAAI;AACrC,iBAAe,KAAK,UAAU,MAAM;AACxC;AAIA,SAAS,iBAAiB,QAAQ,SAAS;AACvC,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AACN,MAAI,mBAAmB,YAAY,KAAK,MAAM,GAAG;AAC7C,SAAK,OAAO,cAAc,EAAE,OAAO,CAAC,CAAC;AAAA,EACzC;AACJ;AAGA,SAAS,oBAAoB;AACzB,iBAAe,uBAAO,OAAO,IAAI;AACrC;AACA,SAAS,kBAAkB,QAAQ,UAAU,CAAC,GAAG;AAC7C;AAEI,IAA2C,iBAAiB,QAAQ,OAAO;AAE3E,UAAM,aAAa,QAAQ,cAAc;AACzC,UAAM,MAAM,WAAW,MAAM;AAC7B,UAAM,SAAS,aAAa;AAC5B,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AAEA,QAAI,WAAW;AACf,UAAM,UAAU,QAAQ,WAAW;AACnC,YAAQ,UAAU,CAAC,QAAQ;AACvB,iBAAW;AACX,cAAQ,GAAG;AAAA,IACf;AAEA,UAAM,EAAE,KAAK,IAAI,YAAY,QAAQ,OAAO;AAE5C,UAAM,MAAM,IAAI,SAAS,UAAU,MAAM,EAAE;AAE3C,WAAO,CAAC,WAAY,aAAa,OAAO,MAAO;AAAA,EACnD;AACJ;AAEA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,mBAAmB,MAAM,MAAO,OAAyC,EAAE,UAAUC,eAAc,IAAI,MAAS;AAC3H;AAYA,SAAS,UAAU,YAAY,MAAM;AACjC,QAAM,EAAE,gBAAgB,iBAAiB,aAAa,gBAAgB,SAAS,IAAI;AACnF,QAAM,CAAC,KAAK,OAAO,IAAI,mBAAmB,GAAG,IAAI;AACjD,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR,QAAQ;AACd,QAAM,kBAAkB,CAAC,CAAC,QAAQ;AAElC,QAAM,kBAAkB,SAAS,QAAQ,OAAO,KAAK,UAAU,QAAQ,OAAO,IACxE,CAAC,UAAU,QAAQ,OAAO,IACtB,QAAQ,UACR,MACJ,iBACI,MACA;AACV,QAAM,mBAAmB,kBAAkB,oBAAoB;AAC/D,QAAM,SAAS,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,QAAQ;AAEnE,qBAAmB,aAAa,OAAO;AAGvC,MAAI,CAACC,SAAQ,cAAc,OAAO,IAAI,CAAC,kBACjC,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,WAAW,IACpF;AAAA,IACE;AAAA,IACA;AAAA,IACA,SAAS,WAAW,CAAC;AAAA,EACzB;AAEJ,MAAI,eAAe;AACnB,MAAI,CAAC,mBACD,EAAE,SAASA,OAAM,KAAK,kBAAkBA,OAAM,IAAI;AAClD,QAAI,kBAAkB;AAClB,MAAAA,UAAS;AACT,qBAAeA;AAAA,IACnB;AAAA,EACJ;AAEA,MAAI,CAAC,oBACA,EAAE,SAASA,OAAM,KAAK,kBAAkBA,OAAM,MAC3C,CAAC,SAAS,YAAY,IAAI;AAC9B,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAA+C,SAASA,OAAM,KAAK,QAAQ,mBAAmB,MAAM;AAChG,SAAK,yLAGgC,OAAO;AAC5C,WAAO;AAAA,EACX;AAEA,MAAI,WAAW;AACf,QAAM,gBAAgB,MAAM;AACxB,eAAW;AAAA,EACf;AAEA,QAAM,MAAM,CAAC,kBAAkBA,OAAM,IAC/B,qBAAqB,SAAS,KAAK,cAAcA,SAAQ,cAAc,aAAa,IACpFA;AAEN,MAAI,UAAU;AACV,WAAOA;AAAA,EACX;AAEA,QAAM,aAAa,yBAAyB,SAAS,cAAc,SAAS,OAAO;AACnF,QAAM,aAAa,qBAAqB,UAAU;AAClD,QAAM,WAAW,gBAAgB,SAAS,KAAK,UAAU;AAEzD,QAAM,MAAM,kBAAkB,gBAAgB,QAAQ,IAAI;AAE1D,MAAK,MAAqE;AAEtE,UAAM,WAAW;AAAA,MACb,WAAW,KAAK,IAAI;AAAA,MACpB,KAAK,SAAS,GAAG,IACX,MACA,kBAAkBA,OAAM,IACpBA,QAAO,MACP;AAAA,MACV,QAAQ,iBAAiB,kBAAkBA,OAAM,IAC3CA,QAAO,SACP;AAAA,MACN,QAAQ,SAASA,OAAM,IACjBA,UACA,kBAAkBA,OAAM,IACpBA,QAAO,SACP;AAAA,MACV,SAAS;AAAA,IACb;AACA,aAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,kBAAkB,KAAK,CAAC,CAAC;AACpE,sBAAkB,QAAQ;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,aAAa,SAAS;AAC3B,MAAI,QAAQ,QAAQ,IAAI,GAAG;AACvB,YAAQ,OAAO,QAAQ,KAAK,IAAI,UAAQ,SAAS,IAAI,IAAI,WAAW,IAAI,IAAI,IAAI;AAAA,EACpF,WACS,SAAS,QAAQ,KAAK,GAAG;AAC9B,WAAO,KAAK,QAAQ,KAAK,EAAE,QAAQ,SAAO;AACtC,UAAI,SAAS,QAAQ,MAAM,IAAI,GAAG;AAC9B,gBAAQ,MAAM,OAAO,WAAW,QAAQ,MAAM,IAAI;AAAA,MACtD;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,aAAa;AAC3F,QAAM,EAAE,UAAU,OAAO,IAAI;AAC7B,QAAM,UAAU,eAAe,SAAS,gBAAgB,MAAM;AAC9D,MAAI,UAAU,CAAC;AACf,MAAI;AACJ,MAAIA,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,mBAAe,KAAK,QAAQ;AAC5B,QACI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,aAAO,eAAe,GAA+B;AAAA,QACjD;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAEA,QAA+C,WAAW,cAAc;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAA0B;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,QAAQ;AAAA,QACxB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,cACI,SAAS,iBAAiB,CAAC;AAE/B,QAAI,QAAQ;AACZ,QAAI;AACJ,QAAI;AACJ,QAA+C,WAAW;AACtD,cAAQ,OAAO,YAAY,IAAI;AAC/B,iBAAW;AACX,eAAS;AACT,cAAQ,KAAK,QAAQ;AAAA,IACzB;AACA,SAAKA,UAAS,aAAa,SAAS,GAAG,OAAO,MAAM;AAEhD,MAAAA,UAAS,QAAQ;AAAA,IACrB;AAEA,QAA+C,WAAW;AACtD,YAAM,MAAM,OAAO,YAAY,IAAI;AACnC,YAAM,UAAU,QAAQ;AACxB,UAAI,WAAW,SAASA,SAAQ;AAC5B,gBAAQ,KAAK,mBAAyC;AAAA,UAClD,MAAM;AAAA,UACN;AAAA,UACA,SAASA;AAAA,UACT,MAAM,MAAM;AAAA,UACZ,SAAS,GAAG,QAAQ;AAAA,QACxB,CAAC;AAAA,MACL;AACA,UAAI,YAAY,UAAU,QAAQ,SAAS;AACvC,aAAK,MAAM;AACX,gBAAQ,2BAA2B,UAAU,MAAM;AAAA,MACvD;AAAA,IACJ;AACA,QAAI,SAASA,OAAM,KAAK,WAAWA,OAAM;AACrC;AACJ,UAAM,aAAa,cAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC9E,QAAI,eAAe,KAAK;AACpB,MAAAA,UAAS;AAAA,IACb;AACA,WAAO;AAAA,EACX;AACA,SAAO,CAACA,SAAQ,cAAc,OAAO;AACzC;AACA,SAAS,qBAAqB,SAAS,KAAK,cAAcA,SAAQ,cAAc,eAAe;AAC3F,QAAM,EAAE,iBAAiB,gBAAgB,IAAI;AAC7C,MAAI,kBAAkBA,OAAM,GAAG;AAC3B,UAAMC,OAAMD;AACZ,IAAAC,KAAI,SAASA,KAAI,UAAU;AAC3B,IAAAA,KAAI,MAAMA,KAAI,OAAO;AACrB,WAAOA;AAAA,EACX;AAEA,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAA+C,WAAW;AACtD,YAAQ,OAAO,YAAY,IAAI;AAC/B,eAAW;AACX,aAAS;AACT,YAAQ,KAAK,QAAQ;AAAA,EACzB;AACA,QAAM,MAAM,gBAAgBD,SAAQ,kBAAkB,SAAS,cAAc,cAAcA,SAAQ,iBAAiB,aAAa,CAAC;AAElI,MAA+C,WAAW;AACtD,UAAM,MAAM,OAAO,YAAY,IAAI;AACnC,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,OAAO;AAClB,cAAQ,KAAK,uBAAiD;AAAA,QAC1D,MAAM;AAAA,QACN,SAASA;AAAA,QACT,MAAM,MAAM;AAAA,QACZ,SAAS,GAAG,eAAe;AAAA,MAC/B,CAAC;AAAA,IACL;AACA,QAAI,YAAY,UAAU,QAAQ,SAAS;AACvC,WAAK,MAAM;AACX,cAAQ,+BAA+B,UAAU,MAAM;AAAA,IAC3D;AAAA,EACJ;AACA,MAAI,SAAS;AACb,MAAI,MAAM;AACV,MAAI,SAASA;AACb,SAAO;AACX;AACA,SAAS,gBAAgB,SAAS,KAAK,QAAQ;AAE3C,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAA+C,WAAW;AACtD,YAAQ,OAAO,YAAY,IAAI;AAC/B,eAAW;AACX,aAAS;AACT,YAAQ,KAAK,QAAQ;AAAA,EACzB;AACA,QAAM,WAAW,IAAI,MAAM;AAE3B,MAA+C,WAAW;AACtD,UAAM,MAAM,OAAO,YAAY,IAAI;AACnC,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,OAAO;AAClB,cAAQ,KAAK,sBAA+C;AAAA,QACxD,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,MAAM;AAAA,QACZ,SAAS,GAAG,eAAe,IAAI;AAAA,MACnC,CAAC;AAAA,IACL;AACA,QAAI,YAAY,UAAU,QAAQ,SAAS;AACvC,WAAK,MAAM;AACX,cAAQ,8BAA8B,UAAU,MAAM;AAAA,IAC1D;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,sBAAsB,MAAM;AACjC,QAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,QAAM,UAAU,CAAC;AACjB,MAAI,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,kBAAkB,IAAI,GAAG;AAChE,UAAM,gBAAgB,EAAyB;AAAA,EACnD;AAEA,QAAM,MAAM,SAAS,IAAI,IACnB,OAAO,IAAI,IACX,kBAAkB,IAAI,IAClB,OACA;AACV,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,SAAS,IAAI,GAAG;AACrB,YAAQ,UAAU;AAAA,EACtB,WACS,cAAc,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AAClD,YAAQ,QAAQ;AAAA,EACpB,WACS,QAAQ,IAAI,GAAG;AACpB,YAAQ,OAAO;AAAA,EACnB;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,SAAS,IAAI,GAAG;AACrB,YAAQ,UAAU;AAAA,EACtB,WACS,cAAc,IAAI,GAAG;AAC1B,WAAO,SAAS,IAAI;AAAA,EACxB;AACA,SAAO,CAAC,KAAK,OAAO;AACxB;AACA,SAAS,kBAAkB,SAAS,QAAQ,KAAK,QAAQ,iBAAiB,eAAe;AACrF,SAAO;AAAA,IACH;AAAA,IACA,SAAS,CAAC,QAAQ;AACd,uBAAiB,cAAc,GAAG;AAClC,UAAK,MAAwC;AACzC,cAAM,UAAU,8BAA8B,IAAI;AAClD,cAAM,YAAY,IAAI,YAClB,kBAAkB,QAAQ,IAAI,SAAS,MAAM,QAAQ,IAAI,SAAS,IAAI,MAAM;AAChF,cAAM,UAAU,QACX;AACL,YAAI,SAAS;AACT,kBAAQ,KAAK,iBAAqC;AAAA,YAC9C,SAAS;AAAA,YACT,OAAO,IAAI;AAAA,YACX,OAAO,IAAI,YAAY,IAAI,SAAS,MAAM;AAAA,YAC1C,KAAK,IAAI,YAAY,IAAI,SAAS,IAAI;AAAA,YACtC,SAAS,GAAG,eAAe;AAAA,UAC/B,CAAC;AAAA,QACL;AACA,gBAAQ,MAAM,YAAY,GAAG;AAAA,EAAY,cAAc,OAAO;AAAA,MAClE,OACK;AACD,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,IACA,YAAY,CAACE,YAAW,uBAAuB,QAAQ,KAAKA,OAAM;AAAA,EACtE;AACJ;AACA,SAAS,yBAAyB,SAAS,QAAQ,SAAS,SAAS;AACjE,QAAM,EAAE,WAAW,YAAY,IAAI;AACnC,QAAM,iBAAiB,CAAC,QAAQ;AAC5B,UAAM,MAAM,aAAa,SAAS,GAAG;AACrC,QAAI,SAAS,GAAG,GAAG;AACf,UAAI,WAAW;AACf,YAAM,gBAAgB,MAAM;AACxB,mBAAW;AAAA,MACf;AACA,YAAM,MAAM,qBAAqB,SAAS,KAAK,QAAQ,KAAK,KAAK,aAAa;AAC9E,aAAO,CAAC,WACF,MACA;AAAA,IACV,WACS,kBAAkB,GAAG,GAAG;AAC7B,aAAO;AAAA,IACX,OACK;AAED,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,aAAa;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACd;AACA,MAAI,QAAQ,WAAW;AACnB,eAAW,YAAY,QAAQ;AAAA,EACnC;AACA,MAAI,QAAQ,MAAM;AACd,eAAW,OAAO,QAAQ;AAAA,EAC9B;AACA,MAAI,QAAQ,OAAO;AACf,eAAW,QAAQ,QAAQ;AAAA,EAC/B;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC1B,eAAW,cAAc,QAAQ;AAAA,EACrC;AACA,SAAO;AACX;AASA,SAAS,SAAS,YAAY,MAAM;AAChC,QAAM,EAAE,iBAAiB,aAAa,gBAAgB,OAAO,IAAI;AACjE,QAAM,EAAE,qBAAqB,IAAI;AACjC,MAA+C,CAAC,eAAe,gBAAgB;AAC3E,WAAO,eAAe,CAA0B,CAAC;AACjD,WAAO;AAAA,EACX;AACA,QAAM,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,kBAAkB,GAAG,IAAI;AAClE,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,OAAO,CAAC,CAAC,QAAQ;AACvB,QAAM,SAAS,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,QAAQ;AACnE,QAAM,UAAU,eAAe,SAAS,gBAAgB,MAAM;AAC9D,MAAI,CAAC,SAAS,GAAG,KAAK,QAAQ,IAAI;AAC9B,WAAO,IAAI,KAAK,eAAe,MAAM,EAAE,OAAO,KAAK;AAAA,EACvD;AAEA,MAAI,iBAAiB,CAAC;AACtB,MAAI;AACJ,MAAIF,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,mBAAe,KAAK,QAAQ;AAC5B,QACI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,aAAO,eAAe,GAAiC;AAAA,QACnD;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAEA,QAA+C,WAAW,cAAc;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAA0B;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,QAAQ;AAAA,QACxB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,qBACI,gBAAgB,iBAAiB,CAAC;AACtC,IAAAA,UAAS,eAAe;AACxB,QAAI,cAAcA,OAAM;AACpB;AACJ,kBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC3D,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,cAAcA,OAAM,KAAK,CAAC,SAAS,YAAY,GAAG;AACnD,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAAI,KAAK,GAAG,iBAAiB;AAC7B,MAAI,CAAC,cAAc,SAAS,GAAG;AAC3B,SAAK,GAAG,OAAO,KAAK,UAAU,SAAS;AAAA,EAC3C;AACA,MAAI,YAAY,qBAAqB,IAAI,EAAE;AAC3C,MAAI,CAAC,WAAW;AACZ,gBAAY,IAAI,KAAK,eAAe,cAAc,OAAO,CAAC,GAAGA,SAAQ,SAAS,CAAC;AAC/E,yBAAqB,IAAI,IAAI,SAAS;AAAA,EAC1C;AACA,SAAO,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAC1E;AAEA,SAAS,qBAAqB,MAAM;AAChC,QAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,MAAI,UAAU,CAAC;AACf,MAAI,YAAY,CAAC;AACjB,MAAI;AACJ,MAAI,SAAS,IAAI,GAAG;AAGhB,QAAI,CAAC,0BAA0B,KAAK,IAAI,GAAG;AACvC,YAAM,gBAAgB,EAAkC;AAAA,IAC5D;AACA,YAAQ,IAAI,KAAK,IAAI;AACrB,QAAI;AAEA,YAAM,YAAY;AAAA,IACtB,SACO,GAAP;AACI,YAAM,gBAAgB,EAAkC;AAAA,IAC5D;AAAA,EACJ,WACS,OAAO,IAAI,GAAG;AACnB,QAAI,MAAM,KAAK,QAAQ,CAAC,GAAG;AACvB,YAAM,gBAAgB,EAA8B;AAAA,IACxD;AACA,YAAQ;AAAA,EACZ,WACS,SAAS,IAAI,GAAG;AACrB,YAAQ;AAAA,EACZ,OACK;AACD,UAAM,gBAAgB,EAAyB;AAAA,EACnD;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,MAAM;AAAA,EAClB,WACS,cAAc,IAAI,GAAG;AAC1B,cAAU;AAAA,EACd;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,cAAc,IAAI,GAAG;AAC1B,gBAAY;AAAA,EAChB;AACA,MAAI,cAAc,IAAI,GAAG;AACrB,gBAAY;AAAA,EAChB;AACA,SAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AACxD;AAEA,SAAS,oBAAoB,KAAK,QAAQA,SAAQ;AAC9C,QAAM,UAAU;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,KAAK,GAAG,WAAW;AACzB,QAAI,CAAC,QAAQ,qBAAqB,IAAI,EAAE,GAAG;AACvC;AAAA,IACJ;AACA,YAAQ,qBAAqB,OAAO,EAAE;AAAA,EAC1C;AACJ;AAGA,SAAS,OAAO,YAAY,MAAM;AAC9B,QAAM,EAAE,eAAe,aAAa,gBAAgB,OAAO,IAAI;AAC/D,QAAM,EAAE,mBAAmB,IAAI;AAC/B,MAA+C,CAAC,eAAe,cAAc;AACzE,WAAO,eAAe,CAA4B,CAAC;AACnD,WAAO;AAAA,EACX;AACA,QAAM,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,gBAAgB,GAAG,IAAI;AAChE,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,OAAO,CAAC,CAAC,QAAQ;AACvB,QAAM,SAAS,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS,QAAQ;AACnE,QAAM,UAAU,eAAe,SAAS,gBAAgB,MAAM;AAC9D,MAAI,CAAC,SAAS,GAAG,KAAK,QAAQ,IAAI;AAC9B,WAAO,IAAI,KAAK,aAAa,MAAM,EAAE,OAAO,KAAK;AAAA,EACrD;AAEA,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAIA,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,mBAAe,KAAK,QAAQ;AAC5B,QACI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,aAAO,eAAe,GAAmC;AAAA,QACrD;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAEA,QAA+C,WAAW,cAAc;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAA0B;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,QAAQ;AAAA,QACxB,CAAC;AAAA,MACL;AAAA,IACJ;AACA,mBACI,cAAc,iBAAiB,CAAC;AACpC,IAAAA,UAAS,aAAa;AACtB,QAAI,cAAcA,OAAM;AACpB;AACJ,kBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC3D,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,cAAcA,OAAM,KAAK,CAAC,SAAS,YAAY,GAAG;AACnD,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAAI,KAAK,GAAG,iBAAiB;AAC7B,MAAI,CAAC,cAAc,SAAS,GAAG;AAC3B,SAAK,GAAG,OAAO,KAAK,UAAU,SAAS;AAAA,EAC3C;AACA,MAAI,YAAY,mBAAmB,IAAI,EAAE;AACzC,MAAI,CAAC,WAAW;AACZ,gBAAY,IAAI,KAAK,aAAa,cAAc,OAAO,CAAC,GAAGA,SAAQ,SAAS,CAAC;AAC7E,uBAAmB,IAAI,IAAI,SAAS;AAAA,EACxC;AACA,SAAO,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAC1E;AAEA,SAAS,mBAAmB,MAAM;AAC9B,QAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,MAAI,UAAU,CAAC;AACf,MAAI,YAAY,CAAC;AACjB,MAAI,CAAC,SAAS,IAAI,GAAG;AACjB,UAAM,gBAAgB,EAAyB;AAAA,EACnD;AACA,QAAM,QAAQ;AACd,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,MAAM;AAAA,EAClB,WACS,cAAc,IAAI,GAAG;AAC1B,cAAU;AAAA,EACd;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,cAAc,IAAI,GAAG;AAC1B,gBAAY;AAAA,EAChB;AACA,MAAI,cAAc,IAAI,GAAG;AACrB,gBAAY;AAAA,EAChB;AACA,SAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AACxD;AAEA,SAAS,kBAAkB,KAAK,QAAQA,SAAQ;AAC5C,QAAM,UAAU;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,KAAK,GAAG,WAAW;AACzB,QAAI,CAAC,QAAQ,mBAAmB,IAAI,EAAE,GAAG;AACrC;AAAA,IACJ;AACA,YAAQ,mBAAmB,OAAO,EAAE;AAAA,EACxC;AACJ;AA57BA,IAcI,UAiBE,mBAMA,cAgBA,SACA,cACA,uBAWF,WAKA,iBACE,mBAGA,mBAEF,MA2ME,aACA,cASA,mBACF,cAmCED,gBAOA,uBACA,mBAkXA,aACA;AAjsBN;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAI,WAAW;AAiBf,IAAM,oBAAmC,mBAAmB,qBAAqB,iBAAiB;AAMlG,IAAM,eAAe;AAAA,MACjB,CAAC,IAAwB;AAAA,MACzB,CAAC,IAAgC;AAAA,MACjC,CAAC,IAA+B;AAAA,MAChC,CAAC,IAAoC;AAAA,MACrC,CAAC,IAA6B;AAAA,MAC9B,CAAC,IAAkC;AAAA,IACvC;AASA,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,wBAAwB;AAgB9B,IAAI,kBAAkB;AACtB,IAAM,oBAAmC,CAAC,SAAS;AAC/C,wBAAkB;AAAA,IACtB;AACA,IAAM,oBAAmC,MAAM;AAE/C,IAAI,OAAO;AA2MX,IAAM,cAAc;AACpB,IAAM,eAAe;AASrB,IAAM,oBAAoB,CAAC,WAAW;AACtC,IAAI,eAAe,uBAAO,OAAO,IAAI;AAmCrC,IAAMA,iBAAgB;AAAA,MAClB,CAAC,KAA4B;AAAA,MAC7B,CAAC,KAAiC;AAAA,MAElC,CAAC,KAAqC;AAAA,IAC1C;AAEA,IAAM,wBAAwB,MAAM;AACpC,IAAM,oBAAoB,CAAC,QAAQ,WAAW,GAAG;AAkXjD,IAAM,cAAc,OAAO,SAAS;AACpC,IAAM,iBAAiB;AAAA,MACnB,gBAAgB,eAAe,OAAO,KAAK,mBAAmB;AAAA,MAC9D,cAAc,eAAe,OAAO,KAAK,iBAAiB;AAAA,IAC9D;AA0PA;AACI,UAAI,OAAgD;AAChD,sBAAc,EAAE,4BAA4B;AAAA,MAChD;AAAA,IACJ;AAAA;AAAA;;;ACl8BA;AAAA;AAAA;AAOA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,MAAM;AAUV,QAAMI,WAAU;AAEhB,QAAMC,gBAAe;AAAA,MACjB,CAAC,IAA2B;AAAA,MAC5B,CAAC,IAAiC;AAAA,MAClC,CAAC,IAAkC;AAAA,MACnC,CAAC,IAA2C;AAAA,MAC5C,CAAC,KAA0C;AAAA,MAC3C,CAAC,KAA4C;AAAA,MAC7C,CAAC,KAAkC;AAAA,IACvC;AACA,aAASC,gBAAe,SAAS,MAAM;AACnC,aAAO,OAAO,OAAOD,cAAa,OAAO,GAAG,IAAI;AAAA,IACpD;AAEA,aAAS,gBAAgB,SAAS,MAAM;AACpC,aAAO,SAAS,mBAAmB,MAAM,MAAM,EAAE,UAAUE,gBAAe,KAAK,CAAE;AAAA,IACrF;AACA,QAAMA,iBAAgB;AAAA,MAClB,CAAC,KAAkC;AAAA,MACnC,CAAC,KAA4B;AAAA,MAC7B,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAyB;AAAA,MAC1B,CAAC,KAA4B;AAAA,MAC7B,CAAC,KAAwC;AAAA,MACzC,CAAC,KAA0B;AAAA,MAC3B,CAAC,KAAyB;AAAA,MAC1B,CAAC,KAA4C;AAAA,IACjD;AAEA,QAAM,gBAAgB;AACtB,QAAM,uBAAuB,OAAO,WAAW,kBAAkB;AACjE,QAAM,sBAAsB,OAAO,WAAW,iBAAiB;AAC/D,QAAM,oBAAoB,OAAO,WAAW,eAAe;AAC3D,QAAM,gBAAgB,OAAO,WAAW,iBAAiB;AACzD,QAAM,iBAAiB,OAAO,WAAW,kBAAkB;AAC3D,QAAM,uBAAuB,OAAO,WAAW,kBAAkB;AACjE,WAAO,WAAW,eAAe;AACjC,QAAM,mBAAmB,OAAO,WAAW,oBAAoB;AAC/D,QAAI,aAAa;AACjB,aAAS,yBAAyB,SAAS;AACvC,aAAQ,CAAC,KAAK,QAAQ,KAAK,SAAS;AAChC,eAAO,QAAQ,QAAQ,KAAK,IAAI,mBAAmB,KAAK,QAAW,IAAI;AAAA,MAC3E;AAAA,IACJ;AACA,aAAS,kBAAkB,QAAQ,SAAS;AACxC,YAAM,EAAE,UAAU,OAAO,IAAI;AAE7B,YAAM,MAAM,OAAO,cAAc,QAAQ,IACnC,WACA,OAAO,QAAQ,MAAM,IACjB,CAAC,IACD,EAAE,CAAC,SAAS,CAAC,EAAE;AAEzB,UAAI,OAAO,QAAQ,MAAM,GAAG;AACxB,eAAO,QAAQ,CAAC,EAAE,QAAAC,SAAQ,SAAS,MAAM;AACrC,cAAIA,SAAQ;AACR,gBAAIA,WAAU,IAAIA,YAAW,CAAC;AAC9B,qBAAS,UAAU,IAAIA,QAAO;AAAA,UAClC,OACK;AACD,qBAAS,UAAU,GAAG;AAAA,UAC1B;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,UAAI,QAAQ,UAAU;AAClB,mBAAW,OAAO,KAAK;AACnB,cAAI,OAAO,OAAO,KAAK,GAAG,GAAG;AACzB,qBAAS,eAAe,IAAI,IAAI;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,QAAM,uBAAuB,CAAC,QAAQ,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,QAAQ,GAAG;AAEjF,aAAS,SAAS,KAAK,KAAK;AAExB,UAAI,qBAAqB,GAAG,KAAK,qBAAqB,GAAG,GAAG;AACxD,cAAM,gBAAgB,EAAsB;AAAA,MAChD;AACA,iBAAW,OAAO,KAAK;AACnB,YAAI,OAAO,OAAO,KAAK,GAAG,GAAG;AACzB,cAAI,qBAAqB,IAAI,IAAI,KAAK,qBAAqB,IAAI,IAAI,GAAG;AAIlE,gBAAI,OAAO,IAAI;AAAA,UACnB,OACK;AAED,qBAAS,IAAI,MAAM,IAAI,IAAI;AAAA,UAC/B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAM,cAA6B,MAAM;AACrC,YAAM,WAAW,IAAI,mBAAmB;AACxC,aAAO,YAAY,SAAS,KAAK,iBAC3B,EAAE,CAAC,gBAAgB,SAAS,KAAK,eAAe,IAChD;AAAA,IACV;AAMA,aAAS,eAAe,UAAU,CAAC,GAAG;AAClC,YAAM,EAAE,OAAO,IAAI;AACnB,YAAM,YAAY,WAAW;AAC7B,UAAI,iBAAiB,OAAO,UAAU,QAAQ,aAAa,IACrD,QAAQ,gBACR;AACN,YAAM,UAAU,IAAI;AAAA,QAEpB,UAAU,iBACJ,OAAO,OAAO,QACd,OAAO,SAAS,QAAQ,MAAM,IAC1B,QAAQ,SACR;AAAA,MAAO;AACjB,YAAM,kBAAkB,IAAI;AAAA,QAE5B,UAAU,iBACJ,OAAO,eAAe,QACtB,OAAO,SAAS,QAAQ,cAAc,KACpC,OAAO,QAAQ,QAAQ,cAAc,KACrC,OAAO,cAAc,QAAQ,cAAc,KAC3C,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,MAAK;AACvB,YAAM,YAAY,IAAI,IAAI,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AACnE,YAAM,mBAAmB,IAAI,IAAI,OAAO,cAAc,QAAQ,eAAe,IACvE,QAAQ,kBACR,EAAE,CAAC,QAAQ,QAAQ,CAAC,EAAE,CAAC;AAC7B,YAAM,iBAAiB,IAAI,IAAI,OAAO,cAAc,QAAQ,aAAa,IACnE,QAAQ,gBACR,EAAE,CAAC,QAAQ,QAAQ,CAAC,EAAE,CAAC;AAG7B,UAAI,eAAe,SACb,OAAO,cACP,OAAO,UAAU,QAAQ,WAAW,KAAK,OAAO,SAAS,QAAQ,WAAW,IACxE,QAAQ,cACR;AAEV,UAAI,gBAAgB,SACd,OAAO,eACP,OAAO,UAAU,QAAQ,YAAY,KAAK,OAAO,SAAS,QAAQ,YAAY,IAC1E,QAAQ,eACR;AAEV,UAAI,gBAAgB,SACd,OAAO,eACP,OAAO,UAAU,QAAQ,YAAY,IACjC,QAAQ,eACR;AAEV,UAAI,kBAAkB,CAAC,CAAC,QAAQ;AAEhC,UAAI,WAAW,OAAO,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACtE,UAAI,kBAAkB,OAAO,WAAW,QAAQ,OAAO,IACjD,yBAAyB,QAAQ,OAAO,IACxC;AAEN,UAAI,mBAAmB,OAAO,WAAW,QAAQ,eAAe,IAC1D,QAAQ,kBACR;AACN,UAAI,mBAAmB,OAAO,UAAU,QAAQ,eAAe,IACzD,QAAQ,kBACR;AACN,UAAI,mBAAmB,CAAC,CAAC,QAAQ;AAGjC,YAAM,aAAa,SACb,OAAO,YACP,OAAO,cAAc,QAAQ,SAAS,IAClC,QAAQ,YACR,CAAC;AAEX,UAAI,eAAe,QAAQ,eAAgB,UAAU,OAAO;AAG5D,UAAI;AACJ,eAAS,iBAAiB;AACtB,eAAO,SAAS,kBAAkB;AAAA,UAC9B,SAASJ;AAAA,UACT,QAAQ,QAAQ;AAAA,UAChB,gBAAgB,gBAAgB;AAAA,UAChC,UAAU,UAAU;AAAA,UACpB,iBAAiB,iBAAiB;AAAA,UAClC,eAAe,eAAe;AAAA,UAC9B,WAAW;AAAA,UACX,aAAa;AAAA,UACb,SAAS,oBAAoB,OAAO,SAAY;AAAA,UAChD,aAAa;AAAA,UACb,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,iBAAiB,qBAAqB,OAAO,SAAY;AAAA,UACzD,iBAAiB;AAAA,UACjB,iBAAiB;AAAA,UACjB,sBAAsB,OAAO,cAAc,QAAQ,IAC7C,SAAS,uBACT;AAAA,UACN,oBAAoB,OAAO,cAAc,QAAQ,IAC3C,SAAS,qBACT;AAAA,UACN,aAAa,OAAO,cAAc,QAAQ,IACpC,SAAS,cACT;AAAA,UACN,QAAQ,EAAE,WAAW,MAAM;AAAA,QAC/B,CAAC;AAAA,MACL;AACA,iBAAW,eAAe;AAC1B,eAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAE5E,eAAS,wBAAwB;AAC7B,eAAO;AAAA,UACH,QAAQ;AAAA,UACR,gBAAgB;AAAA,UAChB,UAAU;AAAA,UACV,iBAAiB;AAAA,UACjB,eAAe;AAAA,QACnB;AAAA,MACJ;AAEA,YAAM,SAAS,IAAI,SAAS;AAAA,QACxB,KAAK,MAAM,QAAQ;AAAA,QACnB,KAAK,SAAO;AACR,kBAAQ,QAAQ;AAChB,mBAAS,SAAS,QAAQ;AAAA,QAC9B;AAAA,MACJ,CAAC;AAED,YAAM,iBAAiB,IAAI,SAAS;AAAA,QAChC,KAAK,MAAM,gBAAgB;AAAA,QAC3B,KAAK,SAAO;AACR,0BAAgB,QAAQ;AACxB,mBAAS,iBAAiB,gBAAgB;AAC1C,mBAAS,qBAAqB,UAAU,QAAQ,OAAO,GAAG;AAAA,QAC9D;AAAA,MACJ,CAAC;AAED,YAAM,WAAW,IAAI,SAAS,MAAM,UAAU,KAAK;AAEnD,YAAM,kBAAkB,IAAI,SAAS,MAAM,iBAAiB,KAAK;AAEjE,YAAM,gBAAgB,IAAI,SAAS,MAAM,eAAe,KAAK;AAE7D,eAAS,4BAA4B;AACjC,eAAO,OAAO,WAAW,gBAAgB,IAAI,mBAAmB;AAAA,MACpE;AAEA,eAAS,0BAA0B,SAAS;AACxC,2BAAmB;AACnB,iBAAS,kBAAkB;AAAA,MAC/B;AAEA,eAAS,oBAAoB;AACzB,eAAO;AAAA,MACX;AAEA,eAAS,kBAAkB,SAAS;AAChC,YAAI,YAAY,MAAM;AAClB,4BAAkB,yBAAyB,OAAO;AAAA,QACtD;AACA,mBAAW;AACX,iBAAS,UAAU;AAAA,MACvB;AACA,eAAS,2BAA2B,MAAM,KACxC;AACE,eAAO,SAAS,eAAe,CAAC,CAAC,IAAI,oBAAoB;AAAA,MAC7D;AACA,eAAS,aAAa,IAAI,gBAAgB,UAAU,iBAAiB,cAAc,kBAAkB;AACjG,8BAAsB;AAEtB,YAAI;AACJ;AACI,cAAI;AACA,qBAAS,kBAAkB,YAAY,CAAC;AACxC,kBAAM,GAAG,QAAQ;AAAA,UACrB,UACA;AACI,qBAAS,kBAAkB,IAAI;AAAA,UACnC;AAAA,QACJ;AACA,YAAI,OAAO,SAAS,GAAG,KAAK,QAAQ,SAAS,cAAc;AACvD,gBAAM,CAAC,KAAK,IAAI,IAAI,eAAe;AACnC,cAAI,UACA,OAAO,SAAS,GAAG,KACnB,2BAA2B,UAAU,IAAI,GAAG;AAC5C,gBAAI,kBACC,SAAS,wBAAwB,eAAe,GAAG,KAChD,SAAS,uBAAuB,cAAc,GAAG,IAAI;AACzD,qBAAO,KAAKE,gBAAe,GAA0B;AAAA,gBACjD;AAAA,gBACA,MAAM;AAAA,cACV,CAAC,CAAC;AAAA,YACN;AAEA;AACI,oBAAM,EAAE,aAAa,QAAQ,IAAI;AACjC,kBAAI,WAAW,eAAe;AAC1B,wBAAQ,KAAK,YAA0B;AAAA,kBACnC,MAAM;AAAA,kBACN;AAAA,kBACA,IAAI;AAAA,kBACJ,SAAS,GAAG,YAAY;AAAA,gBAC5B,CAAC;AAAA,cACL;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO,UAAU,gBACX,gBAAgB,MAAM,IACtB,aAAa,GAAG;AAAA,QAC1B,WACS,iBAAiB,GAAG,GAAG;AAC5B,iBAAO;AAAA,QACX,OACK;AAED,gBAAM,gBAAgB,EAA+B;AAAA,QACzD;AAAA,MACJ;AAEA,eAAS,KAAK,MAAM;AAChB,eAAO,aAAa,aAAW,SAAS,UAAU,SAAS,GAAG,IAAI,GAAG,MAAM,SAAS,mBAAmB,GAAG,IAAI,GAAG,aAAa,UAAQ,KAAK,EAAE,GAAG,IAAI,GAAG,SAAO,KAAK,SAAO,OAAO,SAAS,GAAG,CAAC;AAAA,MAClM;AAEA,eAAS,MAAM,MAAM;AACjB,cAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,YAAI,QAAQ,CAAC,OAAO,SAAS,IAAI,GAAG;AAChC,gBAAM,gBAAgB,EAAyB;AAAA,QACnD;AACA,eAAO,EAAE,GAAG,CAAC,MAAM,MAAM,OAAO,OAAO,EAAE,iBAAiB,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,MAClF;AAEA,eAAS,KAAK,MAAM;AAChB,eAAO,aAAa,aAAW,SAAS,SAAS,SAAS,GAAG,IAAI,GAAG,MAAM,SAAS,kBAAkB,GAAG,IAAI,GAAG,mBAAmB,UAAQ,KAAK,EAAE,GAAG,IAAI,GAAG,MAAM,SAAS,uBAAuB,SAAO,OAAO,SAAS,GAAG,CAAC;AAAA,MAChO;AAEA,eAAS,KAAK,MAAM;AAChB,eAAO,aAAa,aAAW,SAAS,OAAO,SAAS,GAAG,IAAI,GAAG,MAAM,SAAS,gBAAgB,GAAG,IAAI,GAAG,iBAAiB,UAAQ,KAAK,EAAE,GAAG,IAAI,GAAG,MAAM,SAAS,uBAAuB,SAAO,OAAO,SAAS,GAAG,CAAC;AAAA,MAC1N;AAEA,eAAS,UAAU,QAAQ;AACvB,eAAO,OAAO,IAAI,SAAO,OAAO,SAAS,GAAG,IAAI,IAAI,YAAY,IAAI,MAAM,MAAM,KAAK,CAAC,IAAI,GAAG;AAAA,MACjG;AACA,YAAM,cAAc,CAAC,QAAQ;AAC7B,YAAM,YAAY;AAAA,QACd;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACV;AAEA,eAAS,kBAAkB,MAAM;AAC7B,eAAO;AAAA,UAAa,aAAW;AAC3B,gBAAI;AACJ,kBAAMG,YAAW;AACjB,gBAAI;AACA,cAAAA,UAAS,YAAY;AACrB,oBAAM,SAAS,UAAUA,WAAU,GAAG,IAAI;AAAA,YAC9C,UACA;AACI,cAAAA,UAAS,YAAY;AAAA,YACzB;AACA,mBAAO;AAAA,UACX;AAAA,UAAG,MAAM,SAAS,mBAAmB,GAAG,IAAI;AAAA,UAAG;AAAA,UAE/C,UAAQ,KAAK,sBAAsB,GAAG,IAAI;AAAA,UAAG,SAAO,CAAC,IAAI,YAAY,IAAI,MAAM,MAAM,KAAK,CAAC,CAAC;AAAA,UAAG,SAAO,OAAO,QAAQ,GAAG;AAAA,QAAC;AAAA,MAC7H;AAEA,eAAS,eAAe,MAAM;AAC1B,eAAO;AAAA,UAAa,aAAW,SAAS,OAAO,SAAS,GAAG,IAAI;AAAA,UAAG,MAAM,SAAS,gBAAgB,GAAG,IAAI;AAAA,UAAG;AAAA,UAE3G,UAAQ,KAAK,mBAAmB,GAAG,IAAI;AAAA,UAAG,MAAM,CAAC;AAAA,UAAG,SAAO,OAAO,SAAS,GAAG,KAAK,OAAO,QAAQ,GAAG;AAAA,QAAC;AAAA,MAC1G;AAEA,eAAS,iBAAiB,MAAM;AAC5B,eAAO;AAAA,UAAa,aAAW,SAAS,SAAS,SAAS,GAAG,IAAI;AAAA,UAAG,MAAM,SAAS,kBAAkB,GAAG,IAAI;AAAA,UAAG;AAAA,UAE/G,UAAQ,KAAK,qBAAqB,GAAG,IAAI;AAAA,UAAG,MAAM,CAAC;AAAA,UAAG,SAAO,OAAO,SAAS,GAAG,KAAK,OAAO,QAAQ,GAAG;AAAA,QAAC;AAAA,MAC5G;AACA,eAAS,eAAe,OAAO;AAC3B,uBAAe;AACf,iBAAS,cAAc;AAAA,MAC3B;AAEA,eAAS,GAAG,KAAKD,SAAQ;AACrB,cAAM,eAAe,OAAO,SAASA,OAAM,IAAIA,UAAS,QAAQ;AAChE,cAAM,UAAU,iBAAiB,YAAY;AAC7C,eAAO,SAAS,aAAa,SAAS,GAAG,MAAM;AAAA,MACnD;AACA,eAAS,gBAAgB,KAAK;AAC1B,YAAIE,YAAW;AACf,cAAM,UAAU,SAAS,eAAe,UAAU,gBAAgB,OAAO,QAAQ,KAAK;AACtF,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,gBAAM,uBAAuB,UAAU,MAAM,QAAQ,OAAO,CAAC;AAC7D,gBAAM,eAAe,SAAS,aAAa,sBAAsB,GAAG;AACpE,cAAI,gBAAgB,MAAM;AACtB,YAAAA,YAAW;AACX;AAAA,UACJ;AAAA,QACJ;AACA,eAAOA;AAAA,MACX;AAEA,eAAS,GAAG,KAAK;AACb,cAAMA,YAAW,gBAAgB,GAAG;AAEpC,eAAOA,aAAY,OACbA,YACA,SACI,OAAO,GAAG,GAAG,KAAK,CAAC,IACnB,CAAC;AAAA,MACf;AAEA,eAAS,iBAAiBF,SAAQ;AAC9B,eAAQ,UAAU,MAAMA,YAAW,CAAC;AAAA,MACxC;AAEA,eAAS,iBAAiBA,SAAQ,SAAS;AACvC,kBAAU,MAAMA,WAAU;AAC1B,iBAAS,WAAW,UAAU;AAAA,MAClC;AAEA,eAAS,mBAAmBA,SAAQ,SAAS;AACzC,kBAAU,MAAMA,WAAU,UAAU,MAAMA,YAAW,CAAC;AACtD,iBAAS,SAAS,UAAU,MAAMA,QAAO;AACzC,iBAAS,WAAW,UAAU;AAAA,MAClC;AAEA,eAAS,kBAAkBA,SAAQ;AAC/B,eAAO,iBAAiB,MAAMA,YAAW,CAAC;AAAA,MAC9C;AAEA,eAAS,kBAAkBA,SAAQG,SAAQ;AACvC,yBAAiB,MAAMH,WAAUG;AACjC,iBAAS,kBAAkB,iBAAiB;AAC5C,iBAAS,oBAAoB,UAAUH,SAAQG,OAAM;AAAA,MACzD;AAEA,eAAS,oBAAoBH,SAAQG,SAAQ;AACzC,yBAAiB,MAAMH,WAAU,OAAO,OAAO,iBAAiB,MAAMA,YAAW,CAAC,GAAGG,OAAM;AAC3F,iBAAS,kBAAkB,iBAAiB;AAC5C,iBAAS,oBAAoB,UAAUH,SAAQG,OAAM;AAAA,MACzD;AAEA,eAAS,gBAAgBH,SAAQ;AAC7B,eAAO,eAAe,MAAMA,YAAW,CAAC;AAAA,MAC5C;AAEA,eAAS,gBAAgBA,SAAQG,SAAQ;AACrC,uBAAe,MAAMH,WAAUG;AAC/B,iBAAS,gBAAgB,eAAe;AACxC,iBAAS,kBAAkB,UAAUH,SAAQG,OAAM;AAAA,MACvD;AAEA,eAAS,kBAAkBH,SAAQG,SAAQ;AACvC,uBAAe,MAAMH,WAAU,OAAO,OAAO,eAAe,MAAMA,YAAW,CAAC,GAAGG,OAAM;AACvF,iBAAS,gBAAgB,eAAe;AACxC,iBAAS,kBAAkB,UAAUH,SAAQG,OAAM;AAAA,MACvD;AAEA;AAEA,UAAI,QAAQ;AACR,YAAI,MAAM,OAAO,QAAQ,CAAC,QAAQ;AAC9B,cAAI,gBAAgB;AAChB,oBAAQ,QAAQ;AAChB,qBAAS,SAAS;AAClB,qBAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,UAChF;AAAA,QACJ,CAAC;AACD,YAAI,MAAM,OAAO,gBAAgB,CAAC,QAAQ;AACtC,cAAI,gBAAgB;AAChB,4BAAgB,QAAQ;AACxB,qBAAS,iBAAiB;AAC1B,qBAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,UAChF;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,WAAW;AAAA,QACb,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,QACA,IAAI,gBAAgB;AAChB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,cAAc,KAAK;AACnB,2BAAiB;AACjB,cAAI,OAAO,QAAQ;AACf,oBAAQ,QAAQ,OAAO,OAAO;AAC9B,4BAAgB,QAAQ,OAAO,eAAe;AAC9C,qBAAS,qBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,UAChF;AAAA,QACJ;AAAA,QACA,IAAI,mBAAmB;AACnB,iBAAO,OAAO,KAAK,UAAU,KAAK,EAAE,KAAK;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI,YAAY;AACZ,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,cAAc;AACd,iBAAO,gBAAgB,CAAC;AAAA,QAC5B;AAAA,QACA,IAAI,WAAW;AACX,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,cAAc;AACd,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,YAAY,KAAK;AACjB,yBAAe;AACf,mBAAS,cAAc;AAAA,QAC3B;AAAA,QACA,IAAI,eAAe;AACf,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,aAAa,KAAK;AAClB,0BAAgB;AAChB,mBAAS,eAAe;AAAA,QAC5B;AAAA,QACA,IAAI,eAAe;AACf,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,aAAa,KAAK;AAClB,0BAAgB;AAAA,QACpB;AAAA,QACA,IAAI,iBAAiB;AACjB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,eAAe,KAAK;AACpB,4BAAkB;AAClB,mBAAS,iBAAiB;AAAA,QAC9B;AAAA,QACA,IAAI,kBAAkB;AAClB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,gBAAgB,KAAK;AACrB,6BAAmB;AACnB,mBAAS,kBAAkB;AAAA,QAC/B;AAAA,QACA,IAAI,kBAAkB;AAClB,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,gBAAgB,KAAK;AACrB,6BAAmB;AACnB,mBAAS,kBAAkB;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,CAAC,uBAAuB;AAAA,QACxB,CAAC,oBAAoB;AAAA,QACrB,CAAC,sBAAsB;AAAA,QACvB,CAAC,uBAAuB;AAAA,QACxB,CAAC,mBAAmB,QAAQ;AAAA,MAChC;AAEA;AACI,iBAAS,iBAAiB,CAAC,YAAY;AACnC,mBAAS,cAAc;AAAA,QAC3B;AACA,iBAAS,kBAAkB,MAAM;AAC7B,mBAAS,cAAc;AAAA,QAC3B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAOA,aAAS,uBAAuB,SAAS;AACrC,YAAM,SAAS,OAAO,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAClE,YAAM,iBAAiB,OAAO,SAAS,QAAQ,cAAc,KACzD,OAAO,QAAQ,QAAQ,cAAc,KACrC,OAAO,cAAc,QAAQ,cAAc,KAC3C,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,YAAM,UAAU,OAAO,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AACvE,YAAM,cAAc,OAAO,UAAU,QAAQ,qBAAqB,KAC9D,OAAO,SAAS,QAAQ,qBAAqB,IAC3C,CAAC,QAAQ,wBACT;AACN,YAAM,eAAe,OAAO,UAAU,QAAQ,kBAAkB,KAC5D,OAAO,SAAS,QAAQ,kBAAkB,IACxC,CAAC,QAAQ,qBACT;AACN,YAAM,eAAe,OAAO,UAAU,QAAQ,YAAY,IACpD,QAAQ,eACR;AACN,YAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,YAAM,YAAY,OAAO,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY,CAAC;AACjF,YAAM,qBAAqB,QAAQ;AACnC,YAAM,kBAAkB,OAAO,WAAW,QAAQ,eAAe,IAC3D,QAAQ,kBACR;AACN,YAAM,kBAAkB,OAAO,SAAS,QAAQ,iBAAiB,IAC3D,QAAQ,sBAAsB,QAC9B;AACN,YAAM,kBAAkB,CAAC,CAAC,QAAQ;AAClC,YAAM,gBAAgB,OAAO,UAAU,QAAQ,IAAI,IAAI,QAAQ,OAAO;AACtE,UAAI,QAAQ,WAAW;AACnB,eAAO,KAAKL,gBAAe,CAA+B,CAAC;AAAA,MAC/D;AACA,UAAI,QAAQ,0BAA0B;AAClC,eAAO,KAAKA,gBAAe,CAAwC,CAAC;AAAA,MACxE;AACA,UAAI,WAAW,QAAQ;AACvB,UAAI,OAAO,cAAc,QAAQ,cAAc,GAAG;AAC9C,cAAM,iBAAiB,QAAQ;AAC/B,cAAM,UAAU,OAAO,KAAK,cAAc;AAC1C,mBAAW,QAAQ,OAAO,CAACI,WAAUF,YAAW;AAC5C,gBAAM,UAAUE,UAASF,aAAYE,UAASF,WAAU,CAAC;AACzD,iBAAO,OAAO,SAAS,eAAeA,QAAO;AAC7C,iBAAOE;AAAA,QACX,GAAI,YAAY,CAAC,CAAE;AAAA,MACvB;AACA,YAAM,EAAE,QAAQ,QAAQ,mBAAmB,IAAI;AAC/C,YAAM,kBAAkB,QAAQ;AAChC,YAAM,gBAAgB,QAAQ;AAC9B,YAAM,WAAW,QAAQ;AACzB,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAMA,aAAS,cAAc,UAAU,CAAC,GAAG;AACjC,YAAM,WAAW,eAAe,uBAAuB,OAAO,CAAC;AAE/D,YAAM,UAAU;AAAA,QAEZ,IAAI,SAAS;AAAA,QAEb,IAAI,SAAS;AACT,iBAAO,SAAS,OAAO;AAAA,QAC3B;AAAA,QACA,IAAI,OAAO,KAAK;AACZ,mBAAS,OAAO,QAAQ;AAAA,QAC5B;AAAA,QAEA,IAAI,iBAAiB;AACjB,iBAAO,SAAS,eAAe;AAAA,QACnC;AAAA,QACA,IAAI,eAAe,KAAK;AACpB,mBAAS,eAAe,QAAQ;AAAA,QACpC;AAAA,QAEA,IAAI,WAAW;AACX,iBAAO,SAAS,SAAS;AAAA,QAC7B;AAAA,QAEA,IAAI,kBAAkB;AAClB,iBAAO,SAAS,gBAAgB;AAAA,QACpC;AAAA,QAEA,IAAI,gBAAgB;AAChB,iBAAO,SAAS,cAAc;AAAA,QAClC;AAAA,QAEA,IAAI,mBAAmB;AACnB,iBAAO,SAAS;AAAA,QACpB;AAAA,QAEA,IAAI,YAAY;AACZ,iBAAO,KAAKJ,gBAAe,CAA+B,CAAC;AAE3D,iBAAO;AAAA,YACH,cAAc;AACV,qBAAO,CAAC;AAAA,YACZ;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,IAAI,UAAU,KAAK;AACf,iBAAO,KAAKA,gBAAe,CAA+B,CAAC;AAAA,QAC/D;AAAA,QAEA,IAAI,UAAU;AACV,iBAAO,SAAS,kBAAkB;AAAA,QACtC;AAAA,QACA,IAAI,QAAQ,SAAS;AACjB,mBAAS,kBAAkB,OAAO;AAAA,QACtC;AAAA,QAEA,IAAI,wBAAwB;AACxB,iBAAO,OAAO,UAAU,SAAS,WAAW,IACtC,CAAC,SAAS,cACV,SAAS;AAAA,QACnB;AAAA,QACA,IAAI,sBAAsB,KAAK;AAC3B,mBAAS,cAAc,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,QAC1D;AAAA,QAEA,IAAI,qBAAqB;AACrB,iBAAO,OAAO,UAAU,SAAS,YAAY,IACvC,CAAC,SAAS,eACV,SAAS;AAAA,QACnB;AAAA,QACA,IAAI,mBAAmB,KAAK;AACxB,mBAAS,eAAe,OAAO,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,QAC3D;AAAA,QAEA,IAAI,YAAY;AACZ,iBAAO,SAAS;AAAA,QACpB;AAAA,QAEA,IAAI,yBAAyB;AACzB,iBAAO,SAAS;AAAA,QACpB;AAAA,QACA,IAAI,uBAAuB,KAAK;AAC5B,mBAAS,iBAAiB;AAAA,QAC9B;AAAA,QAEA,IAAI,kBAAkB;AAClB,iBAAO,SAAS,0BAA0B;AAAA,QAC9C;AAAA,QACA,IAAI,gBAAgB,SAAS;AACzB,mBAAS,0BAA0B,OAAO;AAAA,QAC9C;AAAA,QAEA,IAAI,OAAO;AACP,iBAAO,SAAS;AAAA,QACpB;AAAA,QACA,IAAI,KAAK,KAAK;AACV,mBAAS,gBAAgB;AAAA,QAC7B;AAAA,QAEA,IAAI,oBAAoB;AACpB,iBAAO,SAAS,kBAAkB,SAAS;AAAA,QAC/C;AAAA,QACA,IAAI,kBAAkB,KAAK;AACvB,mBAAS,kBAAkB,QAAQ;AAAA,QACvC;AAAA,QAEA,IAAI,sBAAsB;AACtB,iBAAO,SAAS;AAAA,QACpB;AAAA,QACA,IAAI,oBAAoB,KAAK;AACzB,mBAAS,kBAAkB;AAAA,QAC/B;AAAA,QAEA,IAAI,2BAA2B;AAC3B,iBAAO,KAAKA,gBAAe,CAAwC,CAAC;AACpE,iBAAO;AAAA,QACX;AAAA,QACA,IAAI,yBAAyB,KAAK;AAC9B,iBAAO,KAAKA,gBAAe,CAAwC,CAAC;AAAA,QACxE;AAAA,QAEA,IAAI,qBAAqB;AACrB,iBAAO,SAAS,eAAe,CAAC;AAAA,QACpC;AAAA,QAEA,YAAY;AAAA,QAEZ,KAAK,MAAM;AACP,gBAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,gBAAMM,WAAU,CAAC;AACjB,cAAI,OAAO;AACX,cAAI,QAAQ;AACZ,cAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AACxB,kBAAM,gBAAgB,EAAyB;AAAA,UACnD;AACA,gBAAM,MAAM;AACZ,cAAI,OAAO,SAAS,IAAI,GAAG;AACvB,YAAAA,SAAQ,SAAS;AAAA,UACrB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,mBAAO;AAAA,UACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,oBAAQ;AAAA,UACZ;AACA,cAAI,OAAO,QAAQ,IAAI,GAAG;AACtB,mBAAO;AAAA,UACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,oBAAQ;AAAA,UACZ;AACA,iBAAO,SAAS,EAAE,KAAK,QAAQ,SAAS,CAAC,GAAGA,QAAO;AAAA,QACvD;AAAA,QACA,MAAM,MAAM;AACR,iBAAO,SAAS,GAAG,GAAG,IAAI;AAAA,QAC9B;AAAA,QAEA,MAAM,MAAM;AACR,gBAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,gBAAMA,WAAU,EAAE,QAAQ,EAAE;AAC5B,cAAI,OAAO;AACX,cAAI,QAAQ;AACZ,cAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AACxB,kBAAM,gBAAgB,EAAyB;AAAA,UACnD;AACA,gBAAM,MAAM;AACZ,cAAI,OAAO,SAAS,IAAI,GAAG;AACvB,YAAAA,SAAQ,SAAS;AAAA,UACrB,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,YAAAA,SAAQ,SAAS;AAAA,UACrB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,mBAAO;AAAA,UACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,oBAAQ;AAAA,UACZ;AACA,cAAI,OAAO,SAAS,IAAI,GAAG;AACvB,YAAAA,SAAQ,SAAS;AAAA,UACrB,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,mBAAO;AAAA,UACX,WACS,OAAO,cAAc,IAAI,GAAG;AACjC,oBAAQ;AAAA,UACZ;AACA,iBAAO,SAAS,EAAE,KAAK,QAAQ,SAAS,CAAC,GAAGA,QAAO;AAAA,QACvD;AAAA,QAEA,GAAG,KAAK,QAAQ;AACZ,iBAAO,SAAS,GAAG,KAAK,MAAM;AAAA,QAClC;AAAA,QAEA,GAAG,KAAK;AACJ,iBAAO,SAAS,GAAG,GAAG;AAAA,QAC1B;AAAA,QAEA,iBAAiB,QAAQ;AACrB,iBAAO,SAAS,iBAAiB,MAAM;AAAA,QAC3C;AAAA,QAEA,iBAAiB,QAAQ,SAAS;AAC9B,mBAAS,iBAAiB,QAAQ,OAAO;AAAA,QAC7C;AAAA,QAEA,mBAAmB,QAAQ,SAAS;AAChC,mBAAS,mBAAmB,QAAQ,OAAO;AAAA,QAC/C;AAAA,QAEA,KAAK,MAAM;AACP,iBAAO,SAAS,EAAE,GAAG,IAAI;AAAA,QAC7B;AAAA,QAEA,kBAAkB,QAAQ;AACtB,iBAAO,SAAS,kBAAkB,MAAM;AAAA,QAC5C;AAAA,QAEA,kBAAkB,QAAQD,SAAQ;AAC9B,mBAAS,kBAAkB,QAAQA,OAAM;AAAA,QAC7C;AAAA,QAEA,oBAAoB,QAAQA,SAAQ;AAChC,mBAAS,oBAAoB,QAAQA,OAAM;AAAA,QAC/C;AAAA,QAEA,KAAK,MAAM;AACP,iBAAO,SAAS,EAAE,GAAG,IAAI;AAAA,QAC7B;AAAA,QAEA,gBAAgB,QAAQ;AACpB,iBAAO,SAAS,gBAAgB,MAAM;AAAA,QAC1C;AAAA,QAEA,gBAAgB,QAAQA,SAAQ;AAC5B,mBAAS,gBAAgB,QAAQA,OAAM;AAAA,QAC3C;AAAA,QAEA,kBAAkB,QAAQA,SAAQ;AAC9B,mBAAS,kBAAkB,QAAQA,OAAM;AAAA,QAC7C;AAAA,QAGA,eAAe,QAAQ,eAAe;AAClC,iBAAO,KAAKL,gBAAe,EAAuC,CAAC;AACnE,iBAAO;AAAA,QACX;AAAA,QAEA,6BAA6B,QAAQ;AACjC,gBAAM,EAAE,iCAAiC,IAAI;AAC7C,cAAI,kCAAkC;AAClC,6CAAiC,QAAQ,OAAO;AAAA,UACpD;AAAA,QACJ;AAAA,MACJ;AAEA;AACI,gBAAQ,kBAAkB,CAAC,YAAY;AAEnC,gBAAM,aAAa;AACnB,qBAAW,kBAAkB,WAAW,eAAe,OAAO;AAAA,QAClE;AACA,gBAAQ,mBAAmB,MAAM;AAE7B,gBAAM,aAAa;AACnB,qBAAW,mBAAmB,WAAW,gBAAgB;AAAA,QAC7D;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,QAAM,kBAAkB;AAAA,MACpB,KAAK;AAAA,QACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACzB;AAAA,MACA,QAAQ;AAAA,QACJ,MAAM;AAAA,MACV;AAAA,MACA,OAAO;AAAA,QACH,MAAM;AAAA,QACN,WAAW,CAAC,QAAQ,QAAQ,YAAY,QAAQ;AAAA,QAChD,SAAS;AAAA,MACb;AAAA,MACA,MAAM;AAAA,QACF,MAAM;AAAA,MACV;AAAA,IACJ;AAmDA,QAAM,cAAc;AAAA,MAEhB,MAAM;AAAA,MACN,OAAO,OAAO,OAAO;AAAA,QACjB,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,UAErB,WAAW,CAAC,QAAQ,OAAO,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG;AAAA,QAC1D;AAAA,MACJ,GAAG,eAAe;AAAA,MAElB,MAAM,OAAO,SAAS;AAClB,cAAM,EAAE,OAAO,MAAM,IAAI;AACzB,cAAM,OAAO,MAAM,QACf,QAAQ;AAAA,UACJ,UAAU,MAAM;AAAA,UAChB,gBAAgB;AAAA,QACpB,CAAC;AACL,cAAM,OAAO,OAAO,KAAK,KAAK,EAAE,OAAO,SAAO,QAAQ,GAAG;AACzD,eAAO,MAAM;AACT,gBAAM,UAAU,CAAC;AACjB,cAAI,MAAM,QAAQ;AACd,oBAAQ,SAAS,MAAM;AAAA,UAC3B;AACA,cAAI,MAAM,WAAW,QAAW;AAC5B,oBAAQ,SAAS,OAAO,SAAS,MAAM,MAAM,IAAI,CAAC,MAAM,SAAS,MAAM;AAAA,UAC3E;AACA,gBAAM,MAAM,kBAAkB,SAAS,IAAI;AAE3C,gBAAM,WAAW,KAAK,sBAAsB,MAAM,SAAS,KAAK,OAAO;AACvE,gBAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,KAAK;AAE7C,iBAAO,OAAO,SAAS,MAAM,GAAG,IAC1B,IAAI,EAAE,MAAM,KAAK,eAAe,QAAQ,IACxC,OAAO,SAAS,MAAM,GAAG,IACrB,IAAI,EAAE,MAAM,KAAK,eAAe,QAAQ,IACxC,IAAI,EAAE,IAAI,UAAU,eAAe,QAAQ;AAAA,QACzD;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,kBAAkB,EAAE,MAAM,GAAG,MAAM;AACxC,UAAI,KAAK,WAAW,KAAK,KAAK,OAAO,WAAW;AAE5C,eAAO,MAAM,UAAU,MAAM,QAAQ,IAAI,CAAC;AAAA,MAC9C,OACK;AAED,eAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AAC7B,gBAAM,OAAO,MAAM;AACnB,cAAI,MAAM;AACN,gBAAI,OAAO,KAAK;AAAA,UACpB;AACA,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AAAA,MACT;AAAA,IACJ;AAEA,aAAS,gBAAgB,OAAO,SAAS,UAAU,eAAe;AAC9D,YAAM,EAAE,OAAO,MAAM,IAAI;AACzB,aAAO,MAAM;AACT,cAAM,UAAU,EAAE,MAAM,KAAK;AAC7B,YAAI,YAAY,CAAC;AACjB,YAAI,MAAM,QAAQ;AACd,kBAAQ,SAAS,MAAM;AAAA,QAC3B;AACA,YAAI,OAAO,SAAS,MAAM,MAAM,GAAG;AAC/B,kBAAQ,MAAM,MAAM;AAAA,QACxB,WACS,OAAO,SAAS,MAAM,MAAM,GAAG;AAEpC,cAAI,OAAO,SAAS,MAAM,OAAO,GAAG,GAAG;AAEnC,oBAAQ,MAAM,MAAM,OAAO;AAAA,UAC/B;AAEA,sBAAY,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,CAACM,UAAS,SAAS;AAC5D,mBAAO,SAAS,SAAS,IAAI,IACvB,OAAO,OAAO,CAAC,GAAGA,UAAS,EAAE,CAAC,OAAO,MAAM,OAAO,MAAM,CAAC,IACzDA;AAAA,UACV,GAAG,CAAC,CAAC;AAAA,QACT;AACA,cAAM,QAAQ,cAAc,GAAG,CAAC,MAAM,OAAO,SAAS,SAAS,CAAC;AAChE,YAAI,WAAW,CAAC,QAAQ,GAAG;AAC3B,YAAI,OAAO,QAAQ,KAAK,GAAG;AACvB,qBAAW,MAAM,IAAI,CAAC,MAAM,UAAU;AAClC,kBAAM,OAAO,MAAM,KAAK;AACxB,mBAAO,OACD,KAAK,EAAE,CAAC,KAAK,OAAO,KAAK,OAAO,OAAO,MAAM,CAAC,IAC9C,CAAC,KAAK,KAAK;AAAA,UACrB,CAAC;AAAA,QACL,WACS,OAAO,SAAS,KAAK,GAAG;AAC7B,qBAAW,CAAC,KAAK;AAAA,QACrB;AACA,cAAM,gBAAgB,OAAO,OAAO,CAAC,GAAG,KAAK;AAE7C,eAAO,OAAO,SAAS,MAAM,GAAG,IAC1B,IAAI,EAAE,MAAM,KAAK,eAAe,QAAQ,IACxC,OAAO,SAAS,MAAM,GAAG,IACrB,IAAI,EAAE,MAAM,KAAK,eAAe,QAAQ,IACxC,IAAI,EAAE,IAAI,UAAU,eAAe,QAAQ;AAAA,MACzD;AAAA,IACJ;AAEA,QAAM,qBAAqB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAkBA,QAAM,eAAe;AAAA,MAEjB,MAAM;AAAA,MACN,OAAO,OAAO,OAAO;AAAA,QACjB,OAAO;AAAA,UACH,MAAM;AAAA,UACN,UAAU;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACzB;AAAA,MACJ,GAAG,eAAe;AAAA,MAElB,MAAM,OAAO,SAAS;AAClB,cAAM,OAAO,MAAM,QACf,QAAQ,EAAE,UAAU,UAAU,gBAAgB,KAAK,CAAC;AACxD,eAAO,gBAAgB,OAAO,SAAS,oBAAoB,IAAI,SAE/D,KAAK,mBAAmB,GAAG,IAAI,CAAC;AAAA,MACpC;AAAA,IACJ;AAEA,QAAM,uBAAuB;AAAA,MACzB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAkBA,QAAM,iBAAiB;AAAA,MAEnB,MAAM;AAAA,MACN,OAAO,OAAO,OAAO;AAAA,QACjB,OAAO;AAAA,UACH,MAAM,CAAC,QAAQ,IAAI;AAAA,UACnB,UAAU;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,QACzB;AAAA,MACJ,GAAG,eAAe;AAAA,MAElB,MAAM,OAAO,SAAS;AAClB,cAAM,OAAO,MAAM,QACf,QAAQ,EAAE,UAAU,UAAU,gBAAgB,KAAK,CAAC;AACxD,eAAO,gBAAgB,OAAO,SAAS,sBAAsB,IAAI,SAEjE,KAAK,qBAAqB,GAAG,IAAI,CAAC;AAAA,MACtC;AAAA,IACJ;AAEA,aAAS,cAAc,MAAM,UAAU;AACnC,YAAM,eAAe;AACrB,UAAI,KAAK,SAAS,eAAe;AAC7B,eAAQ,aAAa,cAAc,QAAQ,KAAK,KAAK;AAAA,MACzD,OACK;AACD,cAAM,UAAU,aAAa,cAAc,QAAQ;AACnD,eAAO,WAAW,OACZ,QAAQ,aACR,KAAK,OAAO;AAAA,MACtB;AAAA,IACJ;AACA,aAAS,YAAY,MAAM;AACvB,YAAM,OAAO,CAAC,IAAI,EAAE,UAAU,OAAO,UAAU,MAAM;AAEjD,YAAI,CAAC,YAAY,CAAC,SAAS,GAAG;AAC1B,gBAAM,gBAAgB,EAAyB;AAAA,QACnD;AACA,cAAM,WAAW,cAAc,MAAM,SAAS,CAAC;AAC/C,YAAI,UAAU,UAAU;AACpB,iBAAO,KAAKN,gBAAe,CAA8B,CAAC;AAAA,QAC9D;AACA,cAAM,cAAc,WAAW,KAAK;AACpC,WAAG,cAAc,SAAS,EAAE,GAAG,WAAW,WAAW,CAAC;AAAA,MAC1D;AACA,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ;AACA,aAAS,WAAW,OAAO;AACvB,UAAI,OAAO,SAAS,KAAK,GAAG;AACxB,eAAO,EAAE,MAAM,MAAM;AAAA,MACzB,WACS,OAAO,cAAc,KAAK,GAAG;AAClC,YAAI,EAAE,UAAU,QAAQ;AACpB,gBAAM,gBAAgB,IAAyB,MAAM;AAAA,QACzD;AACA,eAAO;AAAA,MACX,OACK;AACD,cAAM,gBAAgB,EAAsB;AAAA,MAChD;AAAA,IACJ;AACA,aAAS,WAAW,OAAO;AACvB,YAAM,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI;AAC/C,YAAM,UAAU,CAAC;AACjB,YAAM,QAAQ,QAAQ,CAAC;AACvB,UAAI,OAAO,SAAS,MAAM,GAAG;AACzB,gBAAQ,SAAS;AAAA,MACrB;AACA,UAAI,OAAO,SAAS,MAAM,GAAG;AACzB,gBAAQ,SAAS;AAAA,MACrB;AACA,UAAI,OAAO,SAAS,MAAM,GAAG;AACzB,gBAAQ,SAAS;AAAA,MACrB;AACA,aAAO,CAAC,MAAM,OAAO,OAAO;AAAA,IAChC;AAEA,aAAS,MAAM,KAAK,SAAS,SAAS;AAClC,YAAM,gBAAgB,OAAO,cAAc,QAAQ,EAAE,IAC/C,QAAQ,KACR,CAAC;AACP,YAAM,uBAAuB,CAAC,CAAC,cAAc;AAC7C,YAAM,gBAAgB,OAAO,UAAU,cAAc,aAAa,IAC5D,cAAc,gBACd;AACN,UAAI,iBAAiB,sBAAsB;AACvC,eAAO,KAAKA,gBAAe,IAA2C;AAAA,UAClE,MAAM,YAAY;AAAA,QACtB,CAAC,CAAC;AAAA,MACN;AACA,UAAI,eAAe;AAEf,YAAI,UAAU,CAAC,uBAAuB,YAAY,OAAO,QAAQ,WAAW;AAC5E,YAAI,UAAU,aAAa,MAAM,YAAY;AAC7C,YAAI,UAAU,eAAe,MAAM,cAAc;AAAA,MACrD;AAEA,UAAI,UAAU,KAAK,YAAY,IAAI,CAAC;AAAA,IACxC;AAGA,aAAS,YAAY,SAAS,UAAU,MAAM;AAC1C,aAAO;AAAA,QACH,eAAe;AACX,gBAAM,WAAW,IAAI,mBAAmB;AAExC,cAAI,CAAC,UAAU;AACX,kBAAM,gBAAgB,EAAyB;AAAA,UACnD;AACA,gBAAM,UAAU,KAAK;AACrB,cAAI,QAAQ,MAAM;AACd,kBAAM,cAAc,QAAQ;AAC5B,gBAAI,QAAQ,QAAQ;AAChB,0BAAY,SAAS,QAAQ;AAAA,YACjC;AACA,wBAAY,SAAS;AACrB,gBAAI,SAAS,KAAK,OAAO;AACrB,mBAAK,QAAQ,YAAY,SAAS,WAAW;AAAA,YACjD,OACK;AACD,0BAAY,qBAAqB;AACjC,mBAAK,QAAQ,cAAc,WAAW;AAAA,YAC1C;AAAA,UACJ,WACS,QAAQ,QAAQ;AACrB,gBAAI,SAAS,KAAK,OAAO;AACrB,mBAAK,QAAQ,YAAY,SAAS,OAAO;AAAA,YAC7C,OACK;AACD,mBAAK,QAAQ,cAAc;AAAA,gBACvB,QAAQ,QAAQ;AAAA,gBAChB,oBAAoB;AAAA,gBACpB,QAAQ;AAAA,cACZ,CAAC;AAAA,YACL;AAAA,UACJ,OACK;AAED,iBAAK,QAAQ;AAAA,UACjB;AACA,kBAAQ,6BAA6B,KAAK,KAAK;AAC/C,eAAK,cAAc,UAAU,KAAK,KAAK;AAEvC,eAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,eAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,eAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,eAAK,MAAM,CAAC,KAAK,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AACrD,eAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,eAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,eAAK,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;AAAA,QACzC;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA,gBAAgB;AACZ,gBAAM,WAAW,IAAI,mBAAmB;AAExC,cAAI,CAAC,UAAU;AACX,kBAAM,gBAAgB,EAAyB;AAAA,UACnD;AACA,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,iBAAO,KAAK;AACZ,eAAK,iBAAiB,QAAQ;AAC9B,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AACA,aAAS,YAAY,MAAM,SAAS;AAChC,WAAK,SAAS,QAAQ,UAAU,KAAK;AACrC,WAAK,iBAAiB,QAAQ,kBAAkB,KAAK;AACrD,WAAK,UAAU,QAAQ,WAAW,KAAK;AACvC,WAAK,wBACD,QAAQ,yBAAyB,KAAK;AAC1C,WAAK,qBACD,QAAQ,sBAAsB,KAAK;AACvC,WAAK,yBACD,QAAQ,0BAA0B,KAAK;AAC3C,WAAK,kBAAkB,QAAQ,mBAAmB,KAAK;AACvD,WAAK,oBAAoB,QAAQ,qBAAqB,KAAK;AAC3D,WAAK,sBACD,QAAQ,uBAAuB,KAAK;AACxC,WAAK,OAAO,QAAQ,QAAQ,KAAK;AACjC,WAAK,WAAW,sBAAsB,QAAQ,sBAAsB,KAAK,kBAAkB;AAC3F,YAAM,WAAW,kBAAkB,KAAK,QAAQ;AAAA,QAC5C,UAAU,QAAQ;AAAA,QAClB,QAAQ,QAAQ;AAAA,MACpB,CAAC;AACD,aAAO,KAAK,QAAQ,EAAE,QAAQ,YAAU,KAAK,mBAAmB,QAAQ,SAAS,OAAO,CAAC;AACzF,UAAI,QAAQ,iBAAiB;AACzB,eAAO,KAAK,QAAQ,eAAe,EAAE,QAAQ,YAAU,KAAK,oBAAoB,QAAQ,QAAQ,gBAAgB,OAAO,CAAC;AAAA,MAC5H;AACA,UAAI,QAAQ,eAAe;AACvB,eAAO,KAAK,QAAQ,aAAa,EAAE,QAAQ,YAAU,KAAK,kBAAkB,QAAQ,QAAQ,cAAc,OAAO,CAAC;AAAA,MACtH;AACA,aAAO;AAAA,IACX;AA4EA,aAAS,WAAW,UAAU,CAAC,GAAG;AAE9B,YAAM,eAAe,OAAO,UAAU,QAAQ,MAAM,IAC9C,QAAQ,SACR;AACN,YAAM,oBAAoB,CAAC,CAAC,QAAQ;AACpC,YAAM,cAAc,oBAAI,IAAI;AAE5B,YAAM,WAAW,eACX,cAAc,OAAO,IACrB,eAAe,OAAO;AAC5B,YAAM,SAAS,OAAO,WAAW,UAAW;AAC5C,YAAM,OAAO;AAAA,QAET,IAAI,OAAO;AAEP,iBAAO,eACG,WACA;AAAA,QAEd;AAAA,QAEA,MAAM,QAAQ,QAAQM,UAAS;AAE3B,cAAI,sBAAsB;AAC1B,cAAI,QAAQ,IAAI,qBAAqB,IAAI;AAEzC,cAAI,CAAC,gBAAgB,mBAAmB;AACpC,+BAAmB,KAAK,KAAK,MAAM;AAAA,UACvC;AAEA;AACI,kBAAM,KAAK,MAAM,GAAGA,QAAO;AAAA,UAC/B;AAEA,cAAI,cAAc;AACd,gBAAI,MAAM,YAAY,UAAU,SAAS,YAAY,IAAI,CAAC;AAAA,UAC9D;AAAA,QACJ;AAAA,QAEA,IAAI,SAAS;AACT,iBAAO;AAAA,QACX;AAAA,QAEA;AAAA,QAEA,cAAc,WAAW;AACrB,iBAAO,YAAY,IAAI,SAAS,KAAK;AAAA,QACzC;AAAA,QAEA,cAAc,WAAW,UAAU;AAC/B,sBAAY,IAAI,WAAW,QAAQ;AAAA,QACvC;AAAA,QAEA,iBAAiB,WAAW;AACxB,sBAAY,OAAO,SAAS;AAAA,QAChC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAmDA,aAAS,QAAQ,UAAU,CAAC,GAAG;AAC3B,YAAM,WAAW,IAAI,mBAAmB;AACxC,UAAI,YAAY,MAAM;AAClB,cAAM,gBAAgB,EAA+B;AAAA,MACzD;AACA,UAAI,CAAC,SAAS,WAAW,IAAI,qBAAqB;AAC9C,cAAM,gBAAgB,EAAsB;AAAA,MAChD;AACA,YAAM,OAAO,IAAI,OAAO,SAAS,WAAW,IAAI,mBAAmB;AAEnE,UAAI,CAAC,MAAM;AACP,cAAM,gBAAgB,EAAyB;AAAA,MACnD;AAEA,YAAMC,UAAS,KAAK,SAAS,gBACvB,KAAK,SACL,KAAK,OAAO;AAElB,YAAM,QAAQ,OAAO,cAAc,OAAO,IACnC,YAAY,SAAS,OAClB,UACA,WACJ,CAAC,QAAQ,WACL,UACA,QAAQ;AAClB,UAAI,UAAU,UAAU;AACpB,YAAI,WAAW,OAAO,SAAS,QAAQ,QAAQ,IAAI,QAAQ,WAAW,CAAC;AACvE,YAAI,kBAAkB,SAAS,MAAM;AACjC,qBAAW,kBAAkBA,QAAO,OAAO,OAAO;AAAA,YAC9C;AAAA,YACA,QAAQ,SAAS,KAAK;AAAA,UAC1B,CAAC;AAAA,QACL;AAEA,cAAM,UAAU,OAAO,KAAK,QAAQ;AACpC,YAAI,QAAQ,QAAQ;AAChB,kBAAQ,QAAQ,YAAU;AACtB,YAAAA,QAAO,mBAAmB,QAAQ,SAAS,OAAO;AAAA,UACtD,CAAC;AAAA,QACL;AAEA,YAAI,OAAO,SAAS,QAAQ,eAAe,GAAG;AAC1C,gBAAMC,WAAU,OAAO,KAAK,QAAQ,eAAe;AACnD,cAAIA,SAAQ,QAAQ;AAChB,YAAAA,SAAQ,QAAQ,YAAU;AACtB,cAAAD,QAAO,oBAAoB,QAAQ,QAAQ,gBAAgB,OAAO;AAAA,YACtE,CAAC;AAAA,UACL;AAAA,QACJ;AAEA,YAAI,OAAO,SAAS,QAAQ,aAAa,GAAG;AACxC,gBAAMC,WAAU,OAAO,KAAK,QAAQ,aAAa;AACjD,cAAIA,SAAQ,QAAQ;AAChB,YAAAA,SAAQ,QAAQ,YAAU;AACtB,cAAAD,QAAO,kBAAkB,QAAQ,QAAQ,cAAc,OAAO;AAAA,YAClE,CAAC;AAAA,UACL;AAAA,QACJ;AACA,eAAOA;AAAA,MACX;AACA,UAAI,UAAU,UAAU;AAEpB,YAAIE,YAAW,YAAY,MAAM,UAAU,QAAQ,cAAc;AACjE,YAAIA,aAAY,MAAM;AAClB;AACI,mBAAO,KAAKT,gBAAe,EAA+B,CAAC;AAAA,UAC/D;AACA,UAAAS,YAAWF;AAAA,QACf;AACA,eAAOE;AAAA,MACX;AAEA,UAAI,KAAK,SAAS,UAAU;AACxB,cAAM,gBAAgB,EAAqC;AAAA,MAC/D;AACA,YAAM,eAAe;AACrB,UAAI,WAAW,aAAa,cAAc,QAAQ;AAClD,UAAI,YAAY,MAAM;AAClB,cAAM,OAAO,SAAS;AACtB,cAAM,kBAAkB,OAAO,OAAO,CAAC,GAAG,OAAO;AACjD,YAAI,KAAK,QAAQ;AACb,0BAAgB,SAAS,KAAK;AAAA,QAClC;AACA,YAAIF,SAAQ;AACR,0BAAgB,SAASA;AAAA,QAC7B;AACA,mBAAW,eAAe,eAAe;AACzC,uBAAe,cAAc,QAAQ;AACrC,qBAAa,cAAc,UAAU,QAAQ;AAAA,MACjD;AACA,aAAO;AAAA,IACX;AACA,aAAS,YAAY,MAAM,QAAQ,eAAe,OAAO;AACrD,UAAI,WAAW;AACf,YAAM,OAAO,OAAO;AACpB,UAAI,UAAU,OAAO;AACrB,aAAO,WAAW,MAAM;AACpB,cAAM,eAAe;AACrB,YAAI,KAAK,SAAS,eAAe;AAC7B,qBAAW,aAAa,cAAc,OAAO;AAAA,QACjD,OACK;AACD,gBAAM,UAAU,aAAa,cAAc,OAAO;AAClD,cAAI,WAAW,MAAM;AACjB,uBAAW,QACN;AAAA,UACT;AAEA,cAAI,gBAAgB,YAAY,CAAC,SAAS,mBAAmB;AACzD,uBAAW;AAAA,UACf;AAAA,QACJ;AACA,YAAI,YAAY,MAAM;AAClB;AAAA,QACJ;AACA,YAAI,SAAS,SAAS;AAClB;AAAA,QACJ;AACA,kBAAU,QAAQ;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AACA,aAAS,eAAe,MAAM,QAAQ,UAAU;AAC5C,UAAI,UAAU,MAAM;AAAA,MACpB,GAAG,MAAM;AACT,UAAI,YAAY,MAAM;AAClB,aAAK,iBAAiB,MAAM;AAAA,MAChC,GAAG,MAAM;AAAA,IACb;AACA,QAAM,oBAAoB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,QAAM,sBAAsB,CAAC,KAAK,MAAM,KAAK,KAAK,IAAI;AACtD,aAAS,mBAAmB,KAAK,UAAU;AACvC,YAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,wBAAkB,QAAQ,UAAQ;AAC9B,cAAM,OAAO,OAAO,yBAAyB,UAAU,IAAI;AAC3D,YAAI,CAAC,MAAM;AACP,gBAAM,gBAAgB,EAAyB;AAAA,QACnD;AACA,cAAM,OAAO,IAAI,MAAM,KAAK,KAAK,IAC3B;AAAA,UACE,MAAM;AACF,mBAAO,KAAK,MAAM;AAAA,UACtB;AAAA,UAEA,IAAI,KAAK;AACL,iBAAK,MAAM,QAAQ;AAAA,UACvB;AAAA,QACJ,IACE;AAAA,UACE,MAAM;AACF,mBAAO,KAAK,OAAO,KAAK,IAAI;AAAA,UAChC;AAAA,QACJ;AACJ,eAAO,eAAe,MAAM,MAAM,IAAI;AAAA,MAC1C,CAAC;AACD,UAAI,OAAO,iBAAiB,QAAQ;AACpC,0BAAoB,QAAQ,YAAU;AAClC,cAAM,OAAO,OAAO,yBAAyB,UAAU,MAAM;AAC7D,YAAI,CAAC,QAAQ,CAAC,KAAK,OAAO;AACtB,gBAAM,gBAAgB,EAAyB;AAAA,QACnD;AACA,eAAO,eAAe,IAAI,OAAO,kBAAkB,IAAI,UAAU,IAAI;AAAA,MACzE,CAAC;AAAA,IACL;AAGA,aAAS,wBAAwB,SAAS,iBAAiB;AAE3D;AACI,YAAM,SAAS,OAAO,cAAc;AACpC,aAAO,cAAc;AACrB,eAAS,gBAAgB,OAAO,gCAAgC;AAAA,IACpE;AAEA,YAAQ,iBAAiB;AACzB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,UAAUT;AAClB,YAAQ,aAAa;AACrB,YAAQ,UAAU;AAClB,YAAQ,cAAc;AAAA;AAAA;;;ACrxDtB,IAAO,mBAAQ;", "names": ["hasOwn", "hasOwnProperty", "isObject", "index", "context", "ch", "parse", "errorMessages", "format", "msg", "source", "VERSION", "warnMessages", "getWarnMessage", "errorMessages", "locale", "_context", "messages", "format", "options", "global", "locales", "composer"]}