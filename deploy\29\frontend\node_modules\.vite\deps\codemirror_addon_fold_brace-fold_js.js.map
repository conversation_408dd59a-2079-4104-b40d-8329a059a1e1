{"version": 3, "sources": ["../../codemirror/addon/fold/brace-fold.js", "dep:codemirror_addon_fold_brace-fold_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n\"use strict\";\n\nfunction bracketFolding(pairs) {\n  return function(cm, start) {\n    var line = start.line, lineText = cm.getLine(line);\n\n    function findOpening(pair) {\n      var tokenType;\n      for (var at = start.ch, pass = 0;;) {\n        var found = at <= 0 ? -1 : lineText.lastIndexOf(pair[0], at - 1);\n        if (found == -1) {\n          if (pass == 1) break;\n          pass = 1;\n          at = lineText.length;\n          continue;\n        }\n        if (pass == 1 && found < start.ch) break;\n        tokenType = cm.getTokenTypeAt(CodeMirror.Pos(line, found + 1));\n        if (!/^(comment|string)/.test(tokenType)) return {ch: found + 1, tokenType: tokenType, pair: pair};\n        at = found - 1;\n      }\n    }\n\n    function findRange(found) {\n      var count = 1, lastLine = cm.lastLine(), end, startCh = found.ch, endCh\n      outer: for (var i = line; i <= lastLine; ++i) {\n        var text = cm.getLine(i), pos = i == line ? startCh : 0;\n        for (;;) {\n          var nextOpen = text.indexOf(found.pair[0], pos), nextClose = text.indexOf(found.pair[1], pos);\n          if (nextOpen < 0) nextOpen = text.length;\n          if (nextClose < 0) nextClose = text.length;\n          pos = Math.min(nextOpen, nextClose);\n          if (pos == text.length) break;\n          if (cm.getTokenTypeAt(CodeMirror.Pos(i, pos + 1)) == found.tokenType) {\n            if (pos == nextOpen) ++count;\n            else if (!--count) { end = i; endCh = pos; break outer; }\n          }\n          ++pos;\n        }\n      }\n\n      if (end == null || line == end) return null\n      return {from: CodeMirror.Pos(line, startCh),\n              to: CodeMirror.Pos(end, endCh)};\n    }\n\n    var found = []\n    for (var i = 0; i < pairs.length; i++) {\n      var open = findOpening(pairs[i])\n      if (open) found.push(open)\n    }\n    found.sort(function(a, b) { return a.ch - b.ch })\n    for (var i = 0; i < found.length; i++) {\n      var range = findRange(found[i])\n      if (range) return range\n    }\n    return null\n  }\n}\n\nCodeMirror.registerHelper(\"fold\", \"brace\", bracketFolding([[\"{\", \"}\"], [\"[\", \"]\"]]));\n\nCodeMirror.registerHelper(\"fold\", \"brace-paren\", bracketFolding([[\"{\", \"}\"], [\"[\", \"]\"], [\"(\", \")\"]]));\n\nCodeMirror.registerHelper(\"fold\", \"import\", function(cm, start) {\n  function hasImport(line) {\n    if (line < cm.firstLine() || line > cm.lastLine()) return null;\n    var start = cm.getTokenAt(CodeMirror.Pos(line, 1));\n    if (!/\\S/.test(start.string)) start = cm.getTokenAt(CodeMirror.Pos(line, start.end + 1));\n    if (start.type != \"keyword\" || start.string != \"import\") return null;\n    // Now find closing semicolon, return its position\n    for (var i = line, e = Math.min(cm.lastLine(), line + 10); i <= e; ++i) {\n      var text = cm.getLine(i), semi = text.indexOf(\";\");\n      if (semi != -1) return {startCh: start.end, end: CodeMirror.Pos(i, semi)};\n    }\n  }\n\n  var startLine = start.line, has = hasImport(startLine), prev;\n  if (!has || hasImport(startLine - 1) || ((prev = hasImport(startLine - 2)) && prev.end.line == startLine - 1))\n    return null;\n  for (var end = has.end;;) {\n    var next = hasImport(end.line + 1);\n    if (next == null) break;\n    end = next.end;\n  }\n  return {from: cm.clipPos(CodeMirror.Pos(startLine, has.startCh + 1)), to: end};\n});\n\nCodeMirror.registerHelper(\"fold\", \"include\", function(cm, start) {\n  function hasInclude(line) {\n    if (line < cm.firstLine() || line > cm.lastLine()) return null;\n    var start = cm.getTokenAt(CodeMirror.Pos(line, 1));\n    if (!/\\S/.test(start.string)) start = cm.getTokenAt(CodeMirror.Pos(line, start.end + 1));\n    if (start.type == \"meta\" && start.string.slice(0, 8) == \"#include\") return start.start + 8;\n  }\n\n  var startLine = start.line, has = hasInclude(startLine);\n  if (has == null || hasInclude(startLine - 1) != null) return null;\n  for (var end = startLine;;) {\n    var next = hasInclude(end + 1);\n    if (next == null) break;\n    ++end;\n  }\n  return {from: CodeMirror.Pos(startLine, has + 1),\n          to: cm.clipPos(CodeMirror.Pos(end))};\n});\n\n});\n", "export default require(\"./node_modules/codemirror/addon/fold/brace-fold.js\");"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACxB;AAEA,eAAS,eAAe,OAAO;AAC7B,eAAO,SAAS,IAAI,OAAO;AACzB,cAAI,OAAO,MAAM,MAAM,WAAW,GAAG,QAAQ,IAAI;AAEjD,mBAAS,YAAY,MAAM;AACzB,gBAAI;AACJ,qBAAS,KAAK,MAAM,IAAI,OAAO,OAAK;AAClC,kBAAIC,SAAQ,MAAM,IAAI,KAAK,SAAS,YAAY,KAAK,IAAI,KAAK,CAAC;AAC/D,kBAAIA,UAAS,IAAI;AACf,oBAAI,QAAQ;AAAG;AACf,uBAAO;AACP,qBAAK,SAAS;AACd;AAAA,cACF;AACA,kBAAI,QAAQ,KAAKA,SAAQ,MAAM;AAAI;AACnC,0BAAY,GAAG,eAAeD,YAAW,IAAI,MAAMC,SAAQ,CAAC,CAAC;AAC7D,kBAAI,CAAC,oBAAoB,KAAK,SAAS;AAAG,uBAAO,EAAC,IAAIA,SAAQ,GAAG,WAAsB,KAAU;AACjG,mBAAKA,SAAQ;AAAA,YACf;AAAA,UACF;AAEA,mBAAS,UAAUA,QAAO;AACxB,gBAAI,QAAQ,GAAG,WAAW,GAAG,SAAS,GAAG,KAAK,UAAUA,OAAM,IAAI;AAClE;AAAO,uBAASC,KAAI,MAAMA,MAAK,UAAU,EAAEA,IAAG;AAC5C,oBAAI,OAAO,GAAG,QAAQA,EAAC,GAAG,MAAMA,MAAK,OAAO,UAAU;AACtD,2BAAS;AACP,sBAAI,WAAW,KAAK,QAAQD,OAAM,KAAK,IAAI,GAAG,GAAG,YAAY,KAAK,QAAQA,OAAM,KAAK,IAAI,GAAG;AAC5F,sBAAI,WAAW;AAAG,+BAAW,KAAK;AAClC,sBAAI,YAAY;AAAG,gCAAY,KAAK;AACpC,wBAAM,KAAK,IAAI,UAAU,SAAS;AAClC,sBAAI,OAAO,KAAK;AAAQ;AACxB,sBAAI,GAAG,eAAeD,YAAW,IAAIE,IAAG,MAAM,CAAC,CAAC,KAAKD,OAAM,WAAW;AACpE,wBAAI,OAAO;AAAU,wBAAE;AAAA,6BACd,CAAC,EAAE,OAAO;AAAE,4BAAMC;AAAG,8BAAQ;AAAK,4BAAM;AAAA,oBAAO;AAAA,kBAC1D;AACA,oBAAE;AAAA,gBACJ;AAAA,cACF;AAEA,gBAAI,OAAO,QAAQ,QAAQ;AAAK,qBAAO;AACvC,mBAAO;AAAA,cAAC,MAAMF,YAAW,IAAI,MAAM,OAAO;AAAA,cAClC,IAAIA,YAAW,IAAI,KAAK,KAAK;AAAA,YAAC;AAAA,UACxC;AAEA,cAAI,QAAQ,CAAC;AACb,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,OAAO,YAAY,MAAM,EAAE;AAC/B,gBAAI;AAAM,oBAAM,KAAK,IAAI;AAAA,UAC3B;AACA,gBAAM,KAAK,SAAS,GAAG,GAAG;AAAE,mBAAO,EAAE,KAAK,EAAE;AAAA,UAAG,CAAC;AAChD,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,QAAQ,UAAU,MAAM,EAAE;AAC9B,gBAAI;AAAO,qBAAO;AAAA,UACpB;AACA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,MAAAA,YAAW,eAAe,QAAQ,SAAS,eAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAEnF,MAAAA,YAAW,eAAe,QAAQ,eAAe,eAAe,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AAErG,MAAAA,YAAW,eAAe,QAAQ,UAAU,SAAS,IAAI,OAAO;AAC9D,iBAAS,UAAU,MAAM;AACvB,cAAI,OAAO,GAAG,UAAU,KAAK,OAAO,GAAG,SAAS;AAAG,mBAAO;AAC1D,cAAIG,SAAQ,GAAG,WAAWH,YAAW,IAAI,MAAM,CAAC,CAAC;AACjD,cAAI,CAAC,KAAK,KAAKG,OAAM,MAAM;AAAG,YAAAA,SAAQ,GAAG,WAAWH,YAAW,IAAI,MAAMG,OAAM,MAAM,CAAC,CAAC;AACvF,cAAIA,OAAM,QAAQ,aAAaA,OAAM,UAAU;AAAU,mBAAO;AAEhE,mBAAS,IAAI,MAAM,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,OAAO,EAAE,GAAG,KAAK,GAAG,EAAE,GAAG;AACtE,gBAAI,OAAO,GAAG,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,GAAG;AACjD,gBAAI,QAAQ;AAAI,qBAAO,EAAC,SAASA,OAAM,KAAK,KAAKH,YAAW,IAAI,GAAG,IAAI,EAAC;AAAA,UAC1E;AAAA,QACF;AAEA,YAAI,YAAY,MAAM,MAAM,MAAM,UAAU,SAAS,GAAG;AACxD,YAAI,CAAC,OAAO,UAAU,YAAY,CAAC,MAAO,OAAO,UAAU,YAAY,CAAC,MAAM,KAAK,IAAI,QAAQ,YAAY;AACzG,iBAAO;AACT,iBAAS,MAAM,IAAI,SAAO;AACxB,cAAI,OAAO,UAAU,IAAI,OAAO,CAAC;AACjC,cAAI,QAAQ;AAAM;AAClB,gBAAM,KAAK;AAAA,QACb;AACA,eAAO,EAAC,MAAM,GAAG,QAAQA,YAAW,IAAI,WAAW,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,IAAG;AAAA,MAC/E,CAAC;AAED,MAAAA,YAAW,eAAe,QAAQ,WAAW,SAAS,IAAI,OAAO;AAC/D,iBAAS,WAAW,MAAM;AACxB,cAAI,OAAO,GAAG,UAAU,KAAK,OAAO,GAAG,SAAS;AAAG,mBAAO;AAC1D,cAAIG,SAAQ,GAAG,WAAWH,YAAW,IAAI,MAAM,CAAC,CAAC;AACjD,cAAI,CAAC,KAAK,KAAKG,OAAM,MAAM;AAAG,YAAAA,SAAQ,GAAG,WAAWH,YAAW,IAAI,MAAMG,OAAM,MAAM,CAAC,CAAC;AACvF,cAAIA,OAAM,QAAQ,UAAUA,OAAM,OAAO,MAAM,GAAG,CAAC,KAAK;AAAY,mBAAOA,OAAM,QAAQ;AAAA,QAC3F;AAEA,YAAI,YAAY,MAAM,MAAM,MAAM,WAAW,SAAS;AACtD,YAAI,OAAO,QAAQ,WAAW,YAAY,CAAC,KAAK;AAAM,iBAAO;AAC7D,iBAAS,MAAM,eAAa;AAC1B,cAAI,OAAO,WAAW,MAAM,CAAC;AAC7B,cAAI,QAAQ;AAAM;AAClB,YAAE;AAAA,QACJ;AACA,eAAO;AAAA,UAAC,MAAMH,YAAW,IAAI,WAAW,MAAM,CAAC;AAAA,UACvC,IAAI,GAAG,QAAQA,YAAW,IAAI,GAAG,CAAC;AAAA,QAAC;AAAA,MAC7C,CAAC;AAAA,IAED,CAAC;AAAA;AAAA;;;ACtHD,IAAO,8CAAQ;", "names": ["CodeMirror", "found", "i", "start"]}