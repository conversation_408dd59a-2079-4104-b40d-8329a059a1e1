{"version": 3, "sources": ["../../@vue/compiler-core/dist/compiler-core.esm-bundler.js", "../../@vue/compiler-dom/dist/compiler-dom.esm-bundler.js", "../../vue/dist/vue.cjs.js", "../../vue/index.js"], "sourcesContent": ["import { isString, hyphenate, NOOP, extend, isObject, NO, isArray, makeMap, isSymbol, EMPTY_OBJ, capitalize, camelize as camelize$1, PatchFlagNames, slotFlagsText, isOn, isBuiltInDirective, isReservedProp, toHandlerKey } from '@vue/shared';\nexport { generateCodeFrame } from '@vue/shared';\n\nfunction defaultOnError(error) {\r\n    throw error;\r\n}\r\nfunction defaultOnWarn(msg) {\r\n    (process.env.NODE_ENV !== 'production') && console.warn(`[Vue warn] ${msg.message}`);\r\n}\r\nfunction createCompilerError(code, loc, messages, additionalMessage) {\r\n    const msg = (process.env.NODE_ENV !== 'production') || !true\r\n        ? (messages || errorMessages)[code] + (additionalMessage || ``)\r\n        : code;\r\n    const error = new SyntaxError(String(msg));\r\n    error.code = code;\r\n    error.loc = loc;\r\n    return error;\r\n}\r\nconst errorMessages = {\r\n    // parse errors\r\n    [0 /* ABRUPT_CLOSING_OF_EMPTY_COMMENT */]: 'Illegal comment.',\r\n    [1 /* CDATA_IN_HTML_CONTENT */]: 'CDATA section is allowed only in XML context.',\r\n    [2 /* DUPLICATE_ATTRIBUTE */]: 'Duplicate attribute.',\r\n    [3 /* END_TAG_WITH_ATTRIBUTES */]: 'End tag cannot have attributes.',\r\n    [4 /* END_TAG_WITH_TRAILING_SOLIDUS */]: \"Illegal '/' in tags.\",\r\n    [5 /* EOF_BEFORE_TAG_NAME */]: 'Unexpected EOF in tag.',\r\n    [6 /* EOF_IN_CDATA */]: 'Unexpected EOF in CDATA section.',\r\n    [7 /* EOF_IN_COMMENT */]: 'Unexpected EOF in comment.',\r\n    [8 /* EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT */]: 'Unexpected EOF in script.',\r\n    [9 /* EOF_IN_TAG */]: 'Unexpected EOF in tag.',\r\n    [10 /* INCORRECTLY_CLOSED_COMMENT */]: 'Incorrectly closed comment.',\r\n    [11 /* INCORRECTLY_OPENED_COMMENT */]: 'Incorrectly opened comment.',\r\n    [12 /* INVALID_FIRST_CHARACTER_OF_TAG_NAME */]: \"Illegal tag name. Use '&lt;' to print '<'.\",\r\n    [13 /* MISSING_ATTRIBUTE_VALUE */]: 'Attribute value was expected.',\r\n    [14 /* MISSING_END_TAG_NAME */]: 'End tag name was expected.',\r\n    [15 /* MISSING_WHITESPACE_BETWEEN_ATTRIBUTES */]: 'Whitespace was expected.',\r\n    [16 /* NESTED_COMMENT */]: \"Unexpected '<!--' in comment.\",\r\n    [17 /* UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME */]: 'Attribute name cannot contain U+0022 (\"), U+0027 (\\'), and U+003C (<).',\r\n    [18 /* UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE */]: 'Unquoted attribute value cannot contain U+0022 (\"), U+0027 (\\'), U+003C (<), U+003D (=), and U+0060 (`).',\r\n    [19 /* UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME */]: \"Attribute name cannot start with '='.\",\r\n    [21 /* UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME */]: \"'<?' is allowed only in XML context.\",\r\n    [20 /* UNEXPECTED_NULL_CHARACTER */]: `Unexpected null character.`,\r\n    [22 /* UNEXPECTED_SOLIDUS_IN_TAG */]: \"Illegal '/' in tags.\",\r\n    // Vue-specific parse errors\r\n    [23 /* X_INVALID_END_TAG */]: 'Invalid end tag.',\r\n    [24 /* X_MISSING_END_TAG */]: 'Element is missing end tag.',\r\n    [25 /* X_MISSING_INTERPOLATION_END */]: 'Interpolation end sign was not found.',\r\n    [27 /* X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END */]: 'End bracket for dynamic directive argument was not found. ' +\r\n        'Note that dynamic directive argument cannot contain spaces.',\r\n    [26 /* X_MISSING_DIRECTIVE_NAME */]: 'Legal directive name was expected.',\r\n    // transform errors\r\n    [28 /* X_V_IF_NO_EXPRESSION */]: `v-if/v-else-if is missing expression.`,\r\n    [29 /* X_V_IF_SAME_KEY */]: `v-if/else branches must use unique keys.`,\r\n    [30 /* X_V_ELSE_NO_ADJACENT_IF */]: `v-else/v-else-if has no adjacent v-if or v-else-if.`,\r\n    [31 /* X_V_FOR_NO_EXPRESSION */]: `v-for is missing expression.`,\r\n    [32 /* X_V_FOR_MALFORMED_EXPRESSION */]: `v-for has invalid expression.`,\r\n    [33 /* X_V_FOR_TEMPLATE_KEY_PLACEMENT */]: `<template v-for> key should be placed on the <template> tag.`,\r\n    [34 /* X_V_BIND_NO_EXPRESSION */]: `v-bind is missing expression.`,\r\n    [35 /* X_V_ON_NO_EXPRESSION */]: `v-on is missing expression.`,\r\n    [36 /* X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET */]: `Unexpected custom directive on <slot> outlet.`,\r\n    [37 /* X_V_SLOT_MIXED_SLOT_USAGE */]: `Mixed v-slot usage on both the component and nested <template>.` +\r\n        `When there are multiple named slots, all slots should use <template> ` +\r\n        `syntax to avoid scope ambiguity.`,\r\n    [38 /* X_V_SLOT_DUPLICATE_SLOT_NAMES */]: `Duplicate slot names found. `,\r\n    [39 /* X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN */]: `Extraneous children found when component already has explicitly named ` +\r\n        `default slot. These children will be ignored.`,\r\n    [40 /* X_V_SLOT_MISPLACED */]: `v-slot can only be used on components or <template> tags.`,\r\n    [41 /* X_V_MODEL_NO_EXPRESSION */]: `v-model is missing expression.`,\r\n    [42 /* X_V_MODEL_MALFORMED_EXPRESSION */]: `v-model value must be a valid JavaScript member expression.`,\r\n    [43 /* X_V_MODEL_ON_SCOPE_VARIABLE */]: `v-model cannot be used on v-for or v-slot scope variables because they are not writable.`,\r\n    [44 /* X_INVALID_EXPRESSION */]: `Error parsing JavaScript expression: `,\r\n    [45 /* X_KEEP_ALIVE_INVALID_CHILDREN */]: `<KeepAlive> expects exactly one child component.`,\r\n    // generic errors\r\n    [46 /* X_PREFIX_ID_NOT_SUPPORTED */]: `\"prefixIdentifiers\" option is not supported in this build of compiler.`,\r\n    [47 /* X_MODULE_MODE_NOT_SUPPORTED */]: `ES module mode is not supported in this build of compiler.`,\r\n    [48 /* X_CACHE_HANDLER_NOT_SUPPORTED */]: `\"cacheHandlers\" option is only supported when the \"prefixIdentifiers\" option is enabled.`,\r\n    [49 /* X_SCOPE_ID_NOT_SUPPORTED */]: `\"scopeId\" option is only supported in module mode.`,\r\n    // just to fulfill types\r\n    [50 /* __EXTEND_POINT__ */]: ``\r\n};\n\nconst FRAGMENT = Symbol((process.env.NODE_ENV !== 'production') ? `Fragment` : ``);\r\nconst TELEPORT = Symbol((process.env.NODE_ENV !== 'production') ? `Teleport` : ``);\r\nconst SUSPENSE = Symbol((process.env.NODE_ENV !== 'production') ? `Suspense` : ``);\r\nconst KEEP_ALIVE = Symbol((process.env.NODE_ENV !== 'production') ? `KeepAlive` : ``);\r\nconst BASE_TRANSITION = Symbol((process.env.NODE_ENV !== 'production') ? `BaseTransition` : ``);\r\nconst OPEN_BLOCK = Symbol((process.env.NODE_ENV !== 'production') ? `openBlock` : ``);\r\nconst CREATE_BLOCK = Symbol((process.env.NODE_ENV !== 'production') ? `createBlock` : ``);\r\nconst CREATE_ELEMENT_BLOCK = Symbol((process.env.NODE_ENV !== 'production') ? `createElementBlock` : ``);\r\nconst CREATE_VNODE = Symbol((process.env.NODE_ENV !== 'production') ? `createVNode` : ``);\r\nconst CREATE_ELEMENT_VNODE = Symbol((process.env.NODE_ENV !== 'production') ? `createElementVNode` : ``);\r\nconst CREATE_COMMENT = Symbol((process.env.NODE_ENV !== 'production') ? `createCommentVNode` : ``);\r\nconst CREATE_TEXT = Symbol((process.env.NODE_ENV !== 'production') ? `createTextVNode` : ``);\r\nconst CREATE_STATIC = Symbol((process.env.NODE_ENV !== 'production') ? `createStaticVNode` : ``);\r\nconst RESOLVE_COMPONENT = Symbol((process.env.NODE_ENV !== 'production') ? `resolveComponent` : ``);\r\nconst RESOLVE_DYNAMIC_COMPONENT = Symbol((process.env.NODE_ENV !== 'production') ? `resolveDynamicComponent` : ``);\r\nconst RESOLVE_DIRECTIVE = Symbol((process.env.NODE_ENV !== 'production') ? `resolveDirective` : ``);\r\nconst RESOLVE_FILTER = Symbol((process.env.NODE_ENV !== 'production') ? `resolveFilter` : ``);\r\nconst WITH_DIRECTIVES = Symbol((process.env.NODE_ENV !== 'production') ? `withDirectives` : ``);\r\nconst RENDER_LIST = Symbol((process.env.NODE_ENV !== 'production') ? `renderList` : ``);\r\nconst RENDER_SLOT = Symbol((process.env.NODE_ENV !== 'production') ? `renderSlot` : ``);\r\nconst CREATE_SLOTS = Symbol((process.env.NODE_ENV !== 'production') ? `createSlots` : ``);\r\nconst TO_DISPLAY_STRING = Symbol((process.env.NODE_ENV !== 'production') ? `toDisplayString` : ``);\r\nconst MERGE_PROPS = Symbol((process.env.NODE_ENV !== 'production') ? `mergeProps` : ``);\r\nconst NORMALIZE_CLASS = Symbol((process.env.NODE_ENV !== 'production') ? `normalizeClass` : ``);\r\nconst NORMALIZE_STYLE = Symbol((process.env.NODE_ENV !== 'production') ? `normalizeStyle` : ``);\r\nconst NORMALIZE_PROPS = Symbol((process.env.NODE_ENV !== 'production') ? `normalizeProps` : ``);\r\nconst GUARD_REACTIVE_PROPS = Symbol((process.env.NODE_ENV !== 'production') ? `guardReactiveProps` : ``);\r\nconst TO_HANDLERS = Symbol((process.env.NODE_ENV !== 'production') ? `toHandlers` : ``);\r\nconst CAMELIZE = Symbol((process.env.NODE_ENV !== 'production') ? `camelize` : ``);\r\nconst CAPITALIZE = Symbol((process.env.NODE_ENV !== 'production') ? `capitalize` : ``);\r\nconst TO_HANDLER_KEY = Symbol((process.env.NODE_ENV !== 'production') ? `toHandlerKey` : ``);\r\nconst SET_BLOCK_TRACKING = Symbol((process.env.NODE_ENV !== 'production') ? `setBlockTracking` : ``);\r\nconst PUSH_SCOPE_ID = Symbol((process.env.NODE_ENV !== 'production') ? `pushScopeId` : ``);\r\nconst POP_SCOPE_ID = Symbol((process.env.NODE_ENV !== 'production') ? `popScopeId` : ``);\r\nconst WITH_CTX = Symbol((process.env.NODE_ENV !== 'production') ? `withCtx` : ``);\r\nconst UNREF = Symbol((process.env.NODE_ENV !== 'production') ? `unref` : ``);\r\nconst IS_REF = Symbol((process.env.NODE_ENV !== 'production') ? `isRef` : ``);\r\nconst WITH_MEMO = Symbol((process.env.NODE_ENV !== 'production') ? `withMemo` : ``);\r\nconst IS_MEMO_SAME = Symbol((process.env.NODE_ENV !== 'production') ? `isMemoSame` : ``);\r\n// Name mapping for runtime helpers that need to be imported from 'vue' in\r\n// generated code. Make sure these are correctly exported in the runtime!\r\n// Using `any` here because TS doesn't allow symbols as index type.\r\nconst helperNameMap = {\r\n    [FRAGMENT]: `Fragment`,\r\n    [TELEPORT]: `Teleport`,\r\n    [SUSPENSE]: `Suspense`,\r\n    [KEEP_ALIVE]: `KeepAlive`,\r\n    [BASE_TRANSITION]: `BaseTransition`,\r\n    [OPEN_BLOCK]: `openBlock`,\r\n    [CREATE_BLOCK]: `createBlock`,\r\n    [CREATE_ELEMENT_BLOCK]: `createElementBlock`,\r\n    [CREATE_VNODE]: `createVNode`,\r\n    [CREATE_ELEMENT_VNODE]: `createElementVNode`,\r\n    [CREATE_COMMENT]: `createCommentVNode`,\r\n    [CREATE_TEXT]: `createTextVNode`,\r\n    [CREATE_STATIC]: `createStaticVNode`,\r\n    [RESOLVE_COMPONENT]: `resolveComponent`,\r\n    [RESOLVE_DYNAMIC_COMPONENT]: `resolveDynamicComponent`,\r\n    [RESOLVE_DIRECTIVE]: `resolveDirective`,\r\n    [RESOLVE_FILTER]: `resolveFilter`,\r\n    [WITH_DIRECTIVES]: `withDirectives`,\r\n    [RENDER_LIST]: `renderList`,\r\n    [RENDER_SLOT]: `renderSlot`,\r\n    [CREATE_SLOTS]: `createSlots`,\r\n    [TO_DISPLAY_STRING]: `toDisplayString`,\r\n    [MERGE_PROPS]: `mergeProps`,\r\n    [NORMALIZE_CLASS]: `normalizeClass`,\r\n    [NORMALIZE_STYLE]: `normalizeStyle`,\r\n    [NORMALIZE_PROPS]: `normalizeProps`,\r\n    [GUARD_REACTIVE_PROPS]: `guardReactiveProps`,\r\n    [TO_HANDLERS]: `toHandlers`,\r\n    [CAMELIZE]: `camelize`,\r\n    [CAPITALIZE]: `capitalize`,\r\n    [TO_HANDLER_KEY]: `toHandlerKey`,\r\n    [SET_BLOCK_TRACKING]: `setBlockTracking`,\r\n    [PUSH_SCOPE_ID]: `pushScopeId`,\r\n    [POP_SCOPE_ID]: `popScopeId`,\r\n    [WITH_CTX]: `withCtx`,\r\n    [UNREF]: `unref`,\r\n    [IS_REF]: `isRef`,\r\n    [WITH_MEMO]: `withMemo`,\r\n    [IS_MEMO_SAME]: `isMemoSame`\r\n};\r\nfunction registerRuntimeHelpers(helpers) {\r\n    Object.getOwnPropertySymbols(helpers).forEach(s => {\r\n        helperNameMap[s] = helpers[s];\r\n    });\r\n}\n\n// AST Utilities ---------------------------------------------------------------\r\n// Some expressions, e.g. sequence and conditional expressions, are never\r\n// associated with template nodes, so their source locations are just a stub.\r\n// Container types like CompoundExpression also don't need a real location.\r\nconst locStub = {\r\n    source: '',\r\n    start: { line: 1, column: 1, offset: 0 },\r\n    end: { line: 1, column: 1, offset: 0 }\r\n};\r\nfunction createRoot(children, loc = locStub) {\r\n    return {\r\n        type: 0 /* ROOT */,\r\n        children,\r\n        helpers: [],\r\n        components: [],\r\n        directives: [],\r\n        hoists: [],\r\n        imports: [],\r\n        cached: 0,\r\n        temps: 0,\r\n        codegenNode: undefined,\r\n        loc\r\n    };\r\n}\r\nfunction createVNodeCall(context, tag, props, children, patchFlag, dynamicProps, directives, isBlock = false, disableTracking = false, isComponent = false, loc = locStub) {\r\n    if (context) {\r\n        if (isBlock) {\r\n            context.helper(OPEN_BLOCK);\r\n            context.helper(getVNodeBlockHelper(context.inSSR, isComponent));\r\n        }\r\n        else {\r\n            context.helper(getVNodeHelper(context.inSSR, isComponent));\r\n        }\r\n        if (directives) {\r\n            context.helper(WITH_DIRECTIVES);\r\n        }\r\n    }\r\n    return {\r\n        type: 13 /* VNODE_CALL */,\r\n        tag,\r\n        props,\r\n        children,\r\n        patchFlag,\r\n        dynamicProps,\r\n        directives,\r\n        isBlock,\r\n        disableTracking,\r\n        isComponent,\r\n        loc\r\n    };\r\n}\r\nfunction createArrayExpression(elements, loc = locStub) {\r\n    return {\r\n        type: 17 /* JS_ARRAY_EXPRESSION */,\r\n        loc,\r\n        elements\r\n    };\r\n}\r\nfunction createObjectExpression(properties, loc = locStub) {\r\n    return {\r\n        type: 15 /* JS_OBJECT_EXPRESSION */,\r\n        loc,\r\n        properties\r\n    };\r\n}\r\nfunction createObjectProperty(key, value) {\r\n    return {\r\n        type: 16 /* JS_PROPERTY */,\r\n        loc: locStub,\r\n        key: isString(key) ? createSimpleExpression(key, true) : key,\r\n        value\r\n    };\r\n}\r\nfunction createSimpleExpression(content, isStatic = false, loc = locStub, constType = 0 /* NOT_CONSTANT */) {\r\n    return {\r\n        type: 4 /* SIMPLE_EXPRESSION */,\r\n        loc,\r\n        content,\r\n        isStatic,\r\n        constType: isStatic ? 3 /* CAN_STRINGIFY */ : constType\r\n    };\r\n}\r\nfunction createInterpolation(content, loc) {\r\n    return {\r\n        type: 5 /* INTERPOLATION */,\r\n        loc,\r\n        content: isString(content)\r\n            ? createSimpleExpression(content, false, loc)\r\n            : content\r\n    };\r\n}\r\nfunction createCompoundExpression(children, loc = locStub) {\r\n    return {\r\n        type: 8 /* COMPOUND_EXPRESSION */,\r\n        loc,\r\n        children\r\n    };\r\n}\r\nfunction createCallExpression(callee, args = [], loc = locStub) {\r\n    return {\r\n        type: 14 /* JS_CALL_EXPRESSION */,\r\n        loc,\r\n        callee,\r\n        arguments: args\r\n    };\r\n}\r\nfunction createFunctionExpression(params, returns = undefined, newline = false, isSlot = false, loc = locStub) {\r\n    return {\r\n        type: 18 /* JS_FUNCTION_EXPRESSION */,\r\n        params,\r\n        returns,\r\n        newline,\r\n        isSlot,\r\n        loc\r\n    };\r\n}\r\nfunction createConditionalExpression(test, consequent, alternate, newline = true) {\r\n    return {\r\n        type: 19 /* JS_CONDITIONAL_EXPRESSION */,\r\n        test,\r\n        consequent,\r\n        alternate,\r\n        newline,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createCacheExpression(index, value, isVNode = false) {\r\n    return {\r\n        type: 20 /* JS_CACHE_EXPRESSION */,\r\n        index,\r\n        value,\r\n        isVNode,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createBlockStatement(body) {\r\n    return {\r\n        type: 21 /* JS_BLOCK_STATEMENT */,\r\n        body,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createTemplateLiteral(elements) {\r\n    return {\r\n        type: 22 /* JS_TEMPLATE_LITERAL */,\r\n        elements,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createIfStatement(test, consequent, alternate) {\r\n    return {\r\n        type: 23 /* JS_IF_STATEMENT */,\r\n        test,\r\n        consequent,\r\n        alternate,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createAssignmentExpression(left, right) {\r\n    return {\r\n        type: 24 /* JS_ASSIGNMENT_EXPRESSION */,\r\n        left,\r\n        right,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createSequenceExpression(expressions) {\r\n    return {\r\n        type: 25 /* JS_SEQUENCE_EXPRESSION */,\r\n        expressions,\r\n        loc: locStub\r\n    };\r\n}\r\nfunction createReturnStatement(returns) {\r\n    return {\r\n        type: 26 /* JS_RETURN_STATEMENT */,\r\n        returns,\r\n        loc: locStub\r\n    };\r\n}\n\nconst isStaticExp = (p) => p.type === 4 /* SIMPLE_EXPRESSION */ && p.isStatic;\r\nconst isBuiltInType = (tag, expected) => tag === expected || tag === hyphenate(expected);\r\nfunction isCoreComponent(tag) {\r\n    if (isBuiltInType(tag, 'Teleport')) {\r\n        return TELEPORT;\r\n    }\r\n    else if (isBuiltInType(tag, 'Suspense')) {\r\n        return SUSPENSE;\r\n    }\r\n    else if (isBuiltInType(tag, 'KeepAlive')) {\r\n        return KEEP_ALIVE;\r\n    }\r\n    else if (isBuiltInType(tag, 'BaseTransition')) {\r\n        return BASE_TRANSITION;\r\n    }\r\n}\r\nconst nonIdentifierRE = /^\\d|[^\\$\\w]/;\r\nconst isSimpleIdentifier = (name) => !nonIdentifierRE.test(name);\r\nconst validFirstIdentCharRE = /[A-Za-z_$\\xA0-\\uFFFF]/;\r\nconst validIdentCharRE = /[\\.\\?\\w$\\xA0-\\uFFFF]/;\r\nconst whitespaceRE = /\\s+[.[]\\s*|\\s*[.[]\\s+/g;\r\n/**\r\n * Simple lexer to check if an expression is a member expression. This is\r\n * lax and only checks validity at the root level (i.e. does not validate exps\r\n * inside square brackets), but it's ok since these are only used on template\r\n * expressions and false positives are invalid expressions in the first place.\r\n */\r\nconst isMemberExpressionBrowser = (path) => {\r\n    // remove whitespaces around . or [ first\r\n    path = path.trim().replace(whitespaceRE, s => s.trim());\r\n    let state = 0 /* inMemberExp */;\r\n    let stateStack = [];\r\n    let currentOpenBracketCount = 0;\r\n    let currentOpenParensCount = 0;\r\n    let currentStringType = null;\r\n    for (let i = 0; i < path.length; i++) {\r\n        const char = path.charAt(i);\r\n        switch (state) {\r\n            case 0 /* inMemberExp */:\r\n                if (char === '[') {\r\n                    stateStack.push(state);\r\n                    state = 1 /* inBrackets */;\r\n                    currentOpenBracketCount++;\r\n                }\r\n                else if (char === '(') {\r\n                    stateStack.push(state);\r\n                    state = 2 /* inParens */;\r\n                    currentOpenParensCount++;\r\n                }\r\n                else if (!(i === 0 ? validFirstIdentCharRE : validIdentCharRE).test(char)) {\r\n                    return false;\r\n                }\r\n                break;\r\n            case 1 /* inBrackets */:\r\n                if (char === `'` || char === `\"` || char === '`') {\r\n                    stateStack.push(state);\r\n                    state = 3 /* inString */;\r\n                    currentStringType = char;\r\n                }\r\n                else if (char === `[`) {\r\n                    currentOpenBracketCount++;\r\n                }\r\n                else if (char === `]`) {\r\n                    if (!--currentOpenBracketCount) {\r\n                        state = stateStack.pop();\r\n                    }\r\n                }\r\n                break;\r\n            case 2 /* inParens */:\r\n                if (char === `'` || char === `\"` || char === '`') {\r\n                    stateStack.push(state);\r\n                    state = 3 /* inString */;\r\n                    currentStringType = char;\r\n                }\r\n                else if (char === `(`) {\r\n                    currentOpenParensCount++;\r\n                }\r\n                else if (char === `)`) {\r\n                    // if the exp ends as a call then it should not be considered valid\r\n                    if (i === path.length - 1) {\r\n                        return false;\r\n                    }\r\n                    if (!--currentOpenParensCount) {\r\n                        state = stateStack.pop();\r\n                    }\r\n                }\r\n                break;\r\n            case 3 /* inString */:\r\n                if (char === currentStringType) {\r\n                    state = stateStack.pop();\r\n                    currentStringType = null;\r\n                }\r\n                break;\r\n        }\r\n    }\r\n    return !currentOpenBracketCount && !currentOpenParensCount;\r\n};\r\nconst isMemberExpressionNode = NOOP\r\n    ;\r\nconst isMemberExpression = isMemberExpressionBrowser\r\n    ;\r\nfunction getInnerRange(loc, offset, length) {\r\n    const source = loc.source.slice(offset, offset + length);\r\n    const newLoc = {\r\n        source,\r\n        start: advancePositionWithClone(loc.start, loc.source, offset),\r\n        end: loc.end\r\n    };\r\n    if (length != null) {\r\n        newLoc.end = advancePositionWithClone(loc.start, loc.source, offset + length);\r\n    }\r\n    return newLoc;\r\n}\r\nfunction advancePositionWithClone(pos, source, numberOfCharacters = source.length) {\r\n    return advancePositionWithMutation(extend({}, pos), source, numberOfCharacters);\r\n}\r\n// advance by mutation without cloning (for performance reasons), since this\r\n// gets called a lot in the parser\r\nfunction advancePositionWithMutation(pos, source, numberOfCharacters = source.length) {\r\n    let linesCount = 0;\r\n    let lastNewLinePos = -1;\r\n    for (let i = 0; i < numberOfCharacters; i++) {\r\n        if (source.charCodeAt(i) === 10 /* newline char code */) {\r\n            linesCount++;\r\n            lastNewLinePos = i;\r\n        }\r\n    }\r\n    pos.offset += numberOfCharacters;\r\n    pos.line += linesCount;\r\n    pos.column =\r\n        lastNewLinePos === -1\r\n            ? pos.column + numberOfCharacters\r\n            : numberOfCharacters - lastNewLinePos;\r\n    return pos;\r\n}\r\nfunction assert(condition, msg) {\r\n    /* istanbul ignore if */\r\n    if (!condition) {\r\n        throw new Error(msg || `unexpected compiler condition`);\r\n    }\r\n}\r\nfunction findDir(node, name, allowEmpty = false) {\r\n    for (let i = 0; i < node.props.length; i++) {\r\n        const p = node.props[i];\r\n        if (p.type === 7 /* DIRECTIVE */ &&\r\n            (allowEmpty || p.exp) &&\r\n            (isString(name) ? p.name === name : name.test(p.name))) {\r\n            return p;\r\n        }\r\n    }\r\n}\r\nfunction findProp(node, name, dynamicOnly = false, allowEmpty = false) {\r\n    for (let i = 0; i < node.props.length; i++) {\r\n        const p = node.props[i];\r\n        if (p.type === 6 /* ATTRIBUTE */) {\r\n            if (dynamicOnly)\r\n                continue;\r\n            if (p.name === name && (p.value || allowEmpty)) {\r\n                return p;\r\n            }\r\n        }\r\n        else if (p.name === 'bind' &&\r\n            (p.exp || allowEmpty) &&\r\n            isStaticArgOf(p.arg, name)) {\r\n            return p;\r\n        }\r\n    }\r\n}\r\nfunction isStaticArgOf(arg, name) {\r\n    return !!(arg && isStaticExp(arg) && arg.content === name);\r\n}\r\nfunction hasDynamicKeyVBind(node) {\r\n    return node.props.some(p => p.type === 7 /* DIRECTIVE */ &&\r\n        p.name === 'bind' &&\r\n        (!p.arg || // v-bind=\"obj\"\r\n            p.arg.type !== 4 /* SIMPLE_EXPRESSION */ || // v-bind:[_ctx.foo]\r\n            !p.arg.isStatic) // v-bind:[foo]\r\n    );\r\n}\r\nfunction isText(node) {\r\n    return node.type === 5 /* INTERPOLATION */ || node.type === 2 /* TEXT */;\r\n}\r\nfunction isVSlot(p) {\r\n    return p.type === 7 /* DIRECTIVE */ && p.name === 'slot';\r\n}\r\nfunction isTemplateNode(node) {\r\n    return (node.type === 1 /* ELEMENT */ && node.tagType === 3 /* TEMPLATE */);\r\n}\r\nfunction isSlotOutlet(node) {\r\n    return node.type === 1 /* ELEMENT */ && node.tagType === 2 /* SLOT */;\r\n}\r\nfunction getVNodeHelper(ssr, isComponent) {\r\n    return ssr || isComponent ? CREATE_VNODE : CREATE_ELEMENT_VNODE;\r\n}\r\nfunction getVNodeBlockHelper(ssr, isComponent) {\r\n    return ssr || isComponent ? CREATE_BLOCK : CREATE_ELEMENT_BLOCK;\r\n}\r\nconst propsHelperSet = new Set([NORMALIZE_PROPS, GUARD_REACTIVE_PROPS]);\r\nfunction getUnnormalizedProps(props, callPath = []) {\r\n    if (props &&\r\n        !isString(props) &&\r\n        props.type === 14 /* JS_CALL_EXPRESSION */) {\r\n        const callee = props.callee;\r\n        if (!isString(callee) && propsHelperSet.has(callee)) {\r\n            return getUnnormalizedProps(props.arguments[0], callPath.concat(props));\r\n        }\r\n    }\r\n    return [props, callPath];\r\n}\r\nfunction injectProp(node, prop, context) {\r\n    let propsWithInjection;\r\n    /**\r\n     * 1. mergeProps(...)\r\n     * 2. toHandlers(...)\r\n     * 3. normalizeProps(...)\r\n     * 4. normalizeProps(guardReactiveProps(...))\r\n     *\r\n     * we need to get the real props before normalization\r\n     */\r\n    let props = node.type === 13 /* VNODE_CALL */ ? node.props : node.arguments[2];\r\n    let callPath = [];\r\n    let parentCall;\r\n    if (props &&\r\n        !isString(props) &&\r\n        props.type === 14 /* JS_CALL_EXPRESSION */) {\r\n        const ret = getUnnormalizedProps(props);\r\n        props = ret[0];\r\n        callPath = ret[1];\r\n        parentCall = callPath[callPath.length - 1];\r\n    }\r\n    if (props == null || isString(props)) {\r\n        propsWithInjection = createObjectExpression([prop]);\r\n    }\r\n    else if (props.type === 14 /* JS_CALL_EXPRESSION */) {\r\n        // merged props... add ours\r\n        // only inject key to object literal if it's the first argument so that\r\n        // if doesn't override user provided keys\r\n        const first = props.arguments[0];\r\n        if (!isString(first) && first.type === 15 /* JS_OBJECT_EXPRESSION */) {\r\n            first.properties.unshift(prop);\r\n        }\r\n        else {\r\n            if (props.callee === TO_HANDLERS) {\r\n                // #2366\r\n                propsWithInjection = createCallExpression(context.helper(MERGE_PROPS), [\r\n                    createObjectExpression([prop]),\r\n                    props\r\n                ]);\r\n            }\r\n            else {\r\n                props.arguments.unshift(createObjectExpression([prop]));\r\n            }\r\n        }\r\n        !propsWithInjection && (propsWithInjection = props);\r\n    }\r\n    else if (props.type === 15 /* JS_OBJECT_EXPRESSION */) {\r\n        let alreadyExists = false;\r\n        // check existing key to avoid overriding user provided keys\r\n        if (prop.key.type === 4 /* SIMPLE_EXPRESSION */) {\r\n            const propKeyName = prop.key.content;\r\n            alreadyExists = props.properties.some(p => p.key.type === 4 /* SIMPLE_EXPRESSION */ &&\r\n                p.key.content === propKeyName);\r\n        }\r\n        if (!alreadyExists) {\r\n            props.properties.unshift(prop);\r\n        }\r\n        propsWithInjection = props;\r\n    }\r\n    else {\r\n        // single v-bind with expression, return a merged replacement\r\n        propsWithInjection = createCallExpression(context.helper(MERGE_PROPS), [\r\n            createObjectExpression([prop]),\r\n            props\r\n        ]);\r\n        // in the case of nested helper call, e.g. `normalizeProps(guardReactiveProps(props))`,\r\n        // it will be rewritten as `normalizeProps(mergeProps({ key: 0 }, props))`,\r\n        // the `guardReactiveProps` will no longer be needed\r\n        if (parentCall && parentCall.callee === GUARD_REACTIVE_PROPS) {\r\n            parentCall = callPath[callPath.length - 2];\r\n        }\r\n    }\r\n    if (node.type === 13 /* VNODE_CALL */) {\r\n        if (parentCall) {\r\n            parentCall.arguments[0] = propsWithInjection;\r\n        }\r\n        else {\r\n            node.props = propsWithInjection;\r\n        }\r\n    }\r\n    else {\r\n        if (parentCall) {\r\n            parentCall.arguments[0] = propsWithInjection;\r\n        }\r\n        else {\r\n            node.arguments[2] = propsWithInjection;\r\n        }\r\n    }\r\n}\r\nfunction toValidAssetId(name, type) {\r\n    // see issue#4422, we need adding identifier on validAssetId if variable `name` has specific character\r\n    return `_${type}_${name.replace(/[^\\w]/g, (searchValue, replaceValue) => {\r\n        return searchValue === '-' ? '_' : name.charCodeAt(replaceValue).toString();\r\n    })}`;\r\n}\r\n// Check if a node contains expressions that reference current context scope ids\r\nfunction hasScopeRef(node, ids) {\r\n    if (!node || Object.keys(ids).length === 0) {\r\n        return false;\r\n    }\r\n    switch (node.type) {\r\n        case 1 /* ELEMENT */:\r\n            for (let i = 0; i < node.props.length; i++) {\r\n                const p = node.props[i];\r\n                if (p.type === 7 /* DIRECTIVE */ &&\r\n                    (hasScopeRef(p.arg, ids) || hasScopeRef(p.exp, ids))) {\r\n                    return true;\r\n                }\r\n            }\r\n            return node.children.some(c => hasScopeRef(c, ids));\r\n        case 11 /* FOR */:\r\n            if (hasScopeRef(node.source, ids)) {\r\n                return true;\r\n            }\r\n            return node.children.some(c => hasScopeRef(c, ids));\r\n        case 9 /* IF */:\r\n            return node.branches.some(b => hasScopeRef(b, ids));\r\n        case 10 /* IF_BRANCH */:\r\n            if (hasScopeRef(node.condition, ids)) {\r\n                return true;\r\n            }\r\n            return node.children.some(c => hasScopeRef(c, ids));\r\n        case 4 /* SIMPLE_EXPRESSION */:\r\n            return (!node.isStatic &&\r\n                isSimpleIdentifier(node.content) &&\r\n                !!ids[node.content]);\r\n        case 8 /* COMPOUND_EXPRESSION */:\r\n            return node.children.some(c => isObject(c) && hasScopeRef(c, ids));\r\n        case 5 /* INTERPOLATION */:\r\n        case 12 /* TEXT_CALL */:\r\n            return hasScopeRef(node.content, ids);\r\n        case 2 /* TEXT */:\r\n        case 3 /* COMMENT */:\r\n            return false;\r\n        default:\r\n            if ((process.env.NODE_ENV !== 'production')) ;\r\n            return false;\r\n    }\r\n}\r\nfunction getMemoedVNodeCall(node) {\r\n    if (node.type === 14 /* JS_CALL_EXPRESSION */ && node.callee === WITH_MEMO) {\r\n        return node.arguments[1].returns;\r\n    }\r\n    else {\r\n        return node;\r\n    }\r\n}\r\nfunction makeBlock(node, { helper, removeHelper, inSSR }) {\r\n    if (!node.isBlock) {\r\n        node.isBlock = true;\r\n        removeHelper(getVNodeHelper(inSSR, node.isComponent));\r\n        helper(OPEN_BLOCK);\r\n        helper(getVNodeBlockHelper(inSSR, node.isComponent));\r\n    }\r\n}\n\nconst deprecationData = {\r\n    [\"COMPILER_IS_ON_ELEMENT\" /* COMPILER_IS_ON_ELEMENT */]: {\r\n        message: `Platform-native elements with \"is\" prop will no longer be ` +\r\n            `treated as components in Vue 3 unless the \"is\" value is explicitly ` +\r\n            `prefixed with \"vue:\".`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/custom-elements-interop.html`\r\n    },\r\n    [\"COMPILER_V_BIND_SYNC\" /* COMPILER_V_BIND_SYNC */]: {\r\n        message: key => `.sync modifier for v-bind has been removed. Use v-model with ` +\r\n            `argument instead. \\`v-bind:${key}.sync\\` should be changed to ` +\r\n            `\\`v-model:${key}\\`.`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/v-model.html`\r\n    },\r\n    [\"COMPILER_V_BIND_PROP\" /* COMPILER_V_BIND_PROP */]: {\r\n        message: `.prop modifier for v-bind has been removed and no longer necessary. ` +\r\n            `Vue 3 will automatically set a binding as DOM property when appropriate.`\r\n    },\r\n    [\"COMPILER_V_BIND_OBJECT_ORDER\" /* COMPILER_V_BIND_OBJECT_ORDER */]: {\r\n        message: `v-bind=\"obj\" usage is now order sensitive and behaves like JavaScript ` +\r\n            `object spread: it will now overwrite an existing non-mergeable attribute ` +\r\n            `that appears before v-bind in the case of conflict. ` +\r\n            `To retain 2.x behavior, move v-bind to make it the first attribute. ` +\r\n            `You can also suppress this warning if the usage is intended.`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/v-bind.html`\r\n    },\r\n    [\"COMPILER_V_ON_NATIVE\" /* COMPILER_V_ON_NATIVE */]: {\r\n        message: `.native modifier for v-on has been removed as is no longer necessary.`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/v-on-native-modifier-removed.html`\r\n    },\r\n    [\"COMPILER_V_IF_V_FOR_PRECEDENCE\" /* COMPILER_V_IF_V_FOR_PRECEDENCE */]: {\r\n        message: `v-if / v-for precedence when used on the same element has changed ` +\r\n            `in Vue 3: v-if now takes higher precedence and will no longer have ` +\r\n            `access to v-for scope variables. It is best to avoid the ambiguity ` +\r\n            `with <template> tags or use a computed property that filters v-for ` +\r\n            `data source.`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/v-if-v-for.html`\r\n    },\r\n    [\"COMPILER_NATIVE_TEMPLATE\" /* COMPILER_NATIVE_TEMPLATE */]: {\r\n        message: `<template> with no special directives will render as a native template ` +\r\n            `element instead of its inner content in Vue 3.`\r\n    },\r\n    [\"COMPILER_INLINE_TEMPLATE\" /* COMPILER_INLINE_TEMPLATE */]: {\r\n        message: `\"inline-template\" has been removed in Vue 3.`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/inline-template-attribute.html`\r\n    },\r\n    [\"COMPILER_FILTER\" /* COMPILER_FILTERS */]: {\r\n        message: `filters have been removed in Vue 3. ` +\r\n            `The \"|\" symbol will be treated as native JavaScript bitwise OR operator. ` +\r\n            `Use method calls or computed properties instead.`,\r\n        link: `https://v3-migration.vuejs.org/breaking-changes/filters.html`\r\n    }\r\n};\r\nfunction getCompatValue(key, context) {\r\n    const config = context.options\r\n        ? context.options.compatConfig\r\n        : context.compatConfig;\r\n    const value = config && config[key];\r\n    if (key === 'MODE') {\r\n        return value || 3; // compiler defaults to v3 behavior\r\n    }\r\n    else {\r\n        return value;\r\n    }\r\n}\r\nfunction isCompatEnabled(key, context) {\r\n    const mode = getCompatValue('MODE', context);\r\n    const value = getCompatValue(key, context);\r\n    // in v3 mode, only enable if explicitly set to true\r\n    // otherwise enable for any non-false value\r\n    return mode === 3 ? value === true : value !== false;\r\n}\r\nfunction checkCompatEnabled(key, context, loc, ...args) {\r\n    const enabled = isCompatEnabled(key, context);\r\n    if ((process.env.NODE_ENV !== 'production') && enabled) {\r\n        warnDeprecation(key, context, loc, ...args);\r\n    }\r\n    return enabled;\r\n}\r\nfunction warnDeprecation(key, context, loc, ...args) {\r\n    const val = getCompatValue(key, context);\r\n    if (val === 'suppress-warning') {\r\n        return;\r\n    }\r\n    const { message, link } = deprecationData[key];\r\n    const msg = `(deprecation ${key}) ${typeof message === 'function' ? message(...args) : message}${link ? `\\n  Details: ${link}` : ``}`;\r\n    const err = new SyntaxError(msg);\r\n    err.code = key;\r\n    if (loc)\r\n        err.loc = loc;\r\n    context.onWarn(err);\r\n}\n\n// The default decoder only provides escapes for characters reserved as part of\r\n// the template syntax, and is only used if the custom renderer did not provide\r\n// a platform-specific decoder.\r\nconst decodeRE = /&(gt|lt|amp|apos|quot);/g;\r\nconst decodeMap = {\r\n    gt: '>',\r\n    lt: '<',\r\n    amp: '&',\r\n    apos: \"'\",\r\n    quot: '\"'\r\n};\r\nconst defaultParserOptions = {\r\n    delimiters: [`{{`, `}}`],\r\n    getNamespace: () => 0 /* HTML */,\r\n    getTextMode: () => 0 /* DATA */,\r\n    isVoidTag: NO,\r\n    isPreTag: NO,\r\n    isCustomElement: NO,\r\n    decodeEntities: (rawText) => rawText.replace(decodeRE, (_, p1) => decodeMap[p1]),\r\n    onError: defaultOnError,\r\n    onWarn: defaultOnWarn,\r\n    comments: (process.env.NODE_ENV !== 'production')\r\n};\r\nfunction baseParse(content, options = {}) {\r\n    const context = createParserContext(content, options);\r\n    const start = getCursor(context);\r\n    return createRoot(parseChildren(context, 0 /* DATA */, []), getSelection(context, start));\r\n}\r\nfunction createParserContext(content, rawOptions) {\r\n    const options = extend({}, defaultParserOptions);\r\n    let key;\r\n    for (key in rawOptions) {\r\n        // @ts-ignore\r\n        options[key] =\r\n            rawOptions[key] === undefined\r\n                ? defaultParserOptions[key]\r\n                : rawOptions[key];\r\n    }\r\n    return {\r\n        options,\r\n        column: 1,\r\n        line: 1,\r\n        offset: 0,\r\n        originalSource: content,\r\n        source: content,\r\n        inPre: false,\r\n        inVPre: false,\r\n        onWarn: options.onWarn\r\n    };\r\n}\r\nfunction parseChildren(context, mode, ancestors) {\r\n    const parent = last(ancestors);\r\n    const ns = parent ? parent.ns : 0 /* HTML */;\r\n    const nodes = [];\r\n    while (!isEnd(context, mode, ancestors)) {\r\n        const s = context.source;\r\n        let node = undefined;\r\n        if (mode === 0 /* DATA */ || mode === 1 /* RCDATA */) {\r\n            if (!context.inVPre && startsWith(s, context.options.delimiters[0])) {\r\n                // '{{'\r\n                node = parseInterpolation(context, mode);\r\n            }\r\n            else if (mode === 0 /* DATA */ && s[0] === '<') {\r\n                // https://html.spec.whatwg.org/multipage/parsing.html#tag-open-state\r\n                if (s.length === 1) {\r\n                    emitError(context, 5 /* EOF_BEFORE_TAG_NAME */, 1);\r\n                }\r\n                else if (s[1] === '!') {\r\n                    // https://html.spec.whatwg.org/multipage/parsing.html#markup-declaration-open-state\r\n                    if (startsWith(s, '<!--')) {\r\n                        node = parseComment(context);\r\n                    }\r\n                    else if (startsWith(s, '<!DOCTYPE')) {\r\n                        // Ignore DOCTYPE by a limitation.\r\n                        node = parseBogusComment(context);\r\n                    }\r\n                    else if (startsWith(s, '<![CDATA[')) {\r\n                        if (ns !== 0 /* HTML */) {\r\n                            node = parseCDATA(context, ancestors);\r\n                        }\r\n                        else {\r\n                            emitError(context, 1 /* CDATA_IN_HTML_CONTENT */);\r\n                            node = parseBogusComment(context);\r\n                        }\r\n                    }\r\n                    else {\r\n                        emitError(context, 11 /* INCORRECTLY_OPENED_COMMENT */);\r\n                        node = parseBogusComment(context);\r\n                    }\r\n                }\r\n                else if (s[1] === '/') {\r\n                    // https://html.spec.whatwg.org/multipage/parsing.html#end-tag-open-state\r\n                    if (s.length === 2) {\r\n                        emitError(context, 5 /* EOF_BEFORE_TAG_NAME */, 2);\r\n                    }\r\n                    else if (s[2] === '>') {\r\n                        emitError(context, 14 /* MISSING_END_TAG_NAME */, 2);\r\n                        advanceBy(context, 3);\r\n                        continue;\r\n                    }\r\n                    else if (/[a-z]/i.test(s[2])) {\r\n                        emitError(context, 23 /* X_INVALID_END_TAG */);\r\n                        parseTag(context, 1 /* End */, parent);\r\n                        continue;\r\n                    }\r\n                    else {\r\n                        emitError(context, 12 /* INVALID_FIRST_CHARACTER_OF_TAG_NAME */, 2);\r\n                        node = parseBogusComment(context);\r\n                    }\r\n                }\r\n                else if (/[a-z]/i.test(s[1])) {\r\n                    node = parseElement(context, ancestors);\r\n                    // 2.x <template> with no directive compat\r\n                    if (isCompatEnabled(\"COMPILER_NATIVE_TEMPLATE\" /* COMPILER_NATIVE_TEMPLATE */, context) &&\r\n                        node &&\r\n                        node.tag === 'template' &&\r\n                        !node.props.some(p => p.type === 7 /* DIRECTIVE */ &&\r\n                            isSpecialTemplateDirective(p.name))) {\r\n                        (process.env.NODE_ENV !== 'production') &&\r\n                            warnDeprecation(\"COMPILER_NATIVE_TEMPLATE\" /* COMPILER_NATIVE_TEMPLATE */, context, node.loc);\r\n                        node = node.children;\r\n                    }\r\n                }\r\n                else if (s[1] === '?') {\r\n                    emitError(context, 21 /* UNEXPECTED_QUESTION_MARK_INSTEAD_OF_TAG_NAME */, 1);\r\n                    node = parseBogusComment(context);\r\n                }\r\n                else {\r\n                    emitError(context, 12 /* INVALID_FIRST_CHARACTER_OF_TAG_NAME */, 1);\r\n                }\r\n            }\r\n        }\r\n        if (!node) {\r\n            node = parseText(context, mode);\r\n        }\r\n        if (isArray(node)) {\r\n            for (let i = 0; i < node.length; i++) {\r\n                pushNode(nodes, node[i]);\r\n            }\r\n        }\r\n        else {\r\n            pushNode(nodes, node);\r\n        }\r\n    }\r\n    // Whitespace handling strategy like v2\r\n    let removedWhitespace = false;\r\n    if (mode !== 2 /* RAWTEXT */ && mode !== 1 /* RCDATA */) {\r\n        const shouldCondense = context.options.whitespace !== 'preserve';\r\n        for (let i = 0; i < nodes.length; i++) {\r\n            const node = nodes[i];\r\n            if (!context.inPre && node.type === 2 /* TEXT */) {\r\n                if (!/[^\\t\\r\\n\\f ]/.test(node.content)) {\r\n                    const prev = nodes[i - 1];\r\n                    const next = nodes[i + 1];\r\n                    // Remove if:\r\n                    // - the whitespace is the first or last node, or:\r\n                    // - (condense mode) the whitespace is adjacent to a comment, or:\r\n                    // - (condense mode) the whitespace is between two elements AND contains newline\r\n                    if (!prev ||\r\n                        !next ||\r\n                        (shouldCondense &&\r\n                            (prev.type === 3 /* COMMENT */ ||\r\n                                next.type === 3 /* COMMENT */ ||\r\n                                (prev.type === 1 /* ELEMENT */ &&\r\n                                    next.type === 1 /* ELEMENT */ &&\r\n                                    /[\\r\\n]/.test(node.content))))) {\r\n                        removedWhitespace = true;\r\n                        nodes[i] = null;\r\n                    }\r\n                    else {\r\n                        // Otherwise, the whitespace is condensed into a single space\r\n                        node.content = ' ';\r\n                    }\r\n                }\r\n                else if (shouldCondense) {\r\n                    // in condense mode, consecutive whitespaces in text are condensed\r\n                    // down to a single space.\r\n                    node.content = node.content.replace(/[\\t\\r\\n\\f ]+/g, ' ');\r\n                }\r\n            }\r\n            // Remove comment nodes if desired by configuration.\r\n            else if (node.type === 3 /* COMMENT */ && !context.options.comments) {\r\n                removedWhitespace = true;\r\n                nodes[i] = null;\r\n            }\r\n        }\r\n        if (context.inPre && parent && context.options.isPreTag(parent.tag)) {\r\n            // remove leading newline per html spec\r\n            // https://html.spec.whatwg.org/multipage/grouping-content.html#the-pre-element\r\n            const first = nodes[0];\r\n            if (first && first.type === 2 /* TEXT */) {\r\n                first.content = first.content.replace(/^\\r?\\n/, '');\r\n            }\r\n        }\r\n    }\r\n    return removedWhitespace ? nodes.filter(Boolean) : nodes;\r\n}\r\nfunction pushNode(nodes, node) {\r\n    if (node.type === 2 /* TEXT */) {\r\n        const prev = last(nodes);\r\n        // Merge if both this and the previous node are text and those are\r\n        // consecutive. This happens for cases like \"a < b\".\r\n        if (prev &&\r\n            prev.type === 2 /* TEXT */ &&\r\n            prev.loc.end.offset === node.loc.start.offset) {\r\n            prev.content += node.content;\r\n            prev.loc.end = node.loc.end;\r\n            prev.loc.source += node.loc.source;\r\n            return;\r\n        }\r\n    }\r\n    nodes.push(node);\r\n}\r\nfunction parseCDATA(context, ancestors) {\r\n    advanceBy(context, 9);\r\n    const nodes = parseChildren(context, 3 /* CDATA */, ancestors);\r\n    if (context.source.length === 0) {\r\n        emitError(context, 6 /* EOF_IN_CDATA */);\r\n    }\r\n    else {\r\n        advanceBy(context, 3);\r\n    }\r\n    return nodes;\r\n}\r\nfunction parseComment(context) {\r\n    const start = getCursor(context);\r\n    let content;\r\n    // Regular comment.\r\n    const match = /--(\\!)?>/.exec(context.source);\r\n    if (!match) {\r\n        content = context.source.slice(4);\r\n        advanceBy(context, context.source.length);\r\n        emitError(context, 7 /* EOF_IN_COMMENT */);\r\n    }\r\n    else {\r\n        if (match.index <= 3) {\r\n            emitError(context, 0 /* ABRUPT_CLOSING_OF_EMPTY_COMMENT */);\r\n        }\r\n        if (match[1]) {\r\n            emitError(context, 10 /* INCORRECTLY_CLOSED_COMMENT */);\r\n        }\r\n        content = context.source.slice(4, match.index);\r\n        // Advancing with reporting nested comments.\r\n        const s = context.source.slice(0, match.index);\r\n        let prevIndex = 1, nestedIndex = 0;\r\n        while ((nestedIndex = s.indexOf('<!--', prevIndex)) !== -1) {\r\n            advanceBy(context, nestedIndex - prevIndex + 1);\r\n            if (nestedIndex + 4 < s.length) {\r\n                emitError(context, 16 /* NESTED_COMMENT */);\r\n            }\r\n            prevIndex = nestedIndex + 1;\r\n        }\r\n        advanceBy(context, match.index + match[0].length - prevIndex + 1);\r\n    }\r\n    return {\r\n        type: 3 /* COMMENT */,\r\n        content,\r\n        loc: getSelection(context, start)\r\n    };\r\n}\r\nfunction parseBogusComment(context) {\r\n    const start = getCursor(context);\r\n    const contentStart = context.source[1] === '?' ? 1 : 2;\r\n    let content;\r\n    const closeIndex = context.source.indexOf('>');\r\n    if (closeIndex === -1) {\r\n        content = context.source.slice(contentStart);\r\n        advanceBy(context, context.source.length);\r\n    }\r\n    else {\r\n        content = context.source.slice(contentStart, closeIndex);\r\n        advanceBy(context, closeIndex + 1);\r\n    }\r\n    return {\r\n        type: 3 /* COMMENT */,\r\n        content,\r\n        loc: getSelection(context, start)\r\n    };\r\n}\r\nfunction parseElement(context, ancestors) {\r\n    // Start tag.\r\n    const wasInPre = context.inPre;\r\n    const wasInVPre = context.inVPre;\r\n    const parent = last(ancestors);\r\n    const element = parseTag(context, 0 /* Start */, parent);\r\n    const isPreBoundary = context.inPre && !wasInPre;\r\n    const isVPreBoundary = context.inVPre && !wasInVPre;\r\n    if (element.isSelfClosing || context.options.isVoidTag(element.tag)) {\r\n        // #4030 self-closing <pre> tag\r\n        if (isPreBoundary) {\r\n            context.inPre = false;\r\n        }\r\n        if (isVPreBoundary) {\r\n            context.inVPre = false;\r\n        }\r\n        return element;\r\n    }\r\n    // Children.\r\n    ancestors.push(element);\r\n    const mode = context.options.getTextMode(element, parent);\r\n    const children = parseChildren(context, mode, ancestors);\r\n    ancestors.pop();\r\n    // 2.x inline-template compat\r\n    {\r\n        const inlineTemplateProp = element.props.find(p => p.type === 6 /* ATTRIBUTE */ && p.name === 'inline-template');\r\n        if (inlineTemplateProp &&\r\n            checkCompatEnabled(\"COMPILER_INLINE_TEMPLATE\" /* COMPILER_INLINE_TEMPLATE */, context, inlineTemplateProp.loc)) {\r\n            const loc = getSelection(context, element.loc.end);\r\n            inlineTemplateProp.value = {\r\n                type: 2 /* TEXT */,\r\n                content: loc.source,\r\n                loc\r\n            };\r\n        }\r\n    }\r\n    element.children = children;\r\n    // End tag.\r\n    if (startsWithEndTagOpen(context.source, element.tag)) {\r\n        parseTag(context, 1 /* End */, parent);\r\n    }\r\n    else {\r\n        emitError(context, 24 /* X_MISSING_END_TAG */, 0, element.loc.start);\r\n        if (context.source.length === 0 && element.tag.toLowerCase() === 'script') {\r\n            const first = children[0];\r\n            if (first && startsWith(first.loc.source, '<!--')) {\r\n                emitError(context, 8 /* EOF_IN_SCRIPT_HTML_COMMENT_LIKE_TEXT */);\r\n            }\r\n        }\r\n    }\r\n    element.loc = getSelection(context, element.loc.start);\r\n    if (isPreBoundary) {\r\n        context.inPre = false;\r\n    }\r\n    if (isVPreBoundary) {\r\n        context.inVPre = false;\r\n    }\r\n    return element;\r\n}\r\nconst isSpecialTemplateDirective = /*#__PURE__*/ makeMap(`if,else,else-if,for,slot`);\r\nfunction parseTag(context, type, parent) {\r\n    // Tag open.\r\n    const start = getCursor(context);\r\n    const match = /^<\\/?([a-z][^\\t\\r\\n\\f />]*)/i.exec(context.source);\r\n    const tag = match[1];\r\n    const ns = context.options.getNamespace(tag, parent);\r\n    advanceBy(context, match[0].length);\r\n    advanceSpaces(context);\r\n    // save current state in case we need to re-parse attributes with v-pre\r\n    const cursor = getCursor(context);\r\n    const currentSource = context.source;\r\n    // check <pre> tag\r\n    if (context.options.isPreTag(tag)) {\r\n        context.inPre = true;\r\n    }\r\n    // Attributes.\r\n    let props = parseAttributes(context, type);\r\n    // check v-pre\r\n    if (type === 0 /* Start */ &&\r\n        !context.inVPre &&\r\n        props.some(p => p.type === 7 /* DIRECTIVE */ && p.name === 'pre')) {\r\n        context.inVPre = true;\r\n        // reset context\r\n        extend(context, cursor);\r\n        context.source = currentSource;\r\n        // re-parse attrs and filter out v-pre itself\r\n        props = parseAttributes(context, type).filter(p => p.name !== 'v-pre');\r\n    }\r\n    // Tag close.\r\n    let isSelfClosing = false;\r\n    if (context.source.length === 0) {\r\n        emitError(context, 9 /* EOF_IN_TAG */);\r\n    }\r\n    else {\r\n        isSelfClosing = startsWith(context.source, '/>');\r\n        if (type === 1 /* End */ && isSelfClosing) {\r\n            emitError(context, 4 /* END_TAG_WITH_TRAILING_SOLIDUS */);\r\n        }\r\n        advanceBy(context, isSelfClosing ? 2 : 1);\r\n    }\r\n    if (type === 1 /* End */) {\r\n        return;\r\n    }\r\n    // 2.x deprecation checks\r\n    if ((process.env.NODE_ENV !== 'production') &&\r\n        isCompatEnabled(\"COMPILER_V_IF_V_FOR_PRECEDENCE\" /* COMPILER_V_IF_V_FOR_PRECEDENCE */, context)) {\r\n        let hasIf = false;\r\n        let hasFor = false;\r\n        for (let i = 0; i < props.length; i++) {\r\n            const p = props[i];\r\n            if (p.type === 7 /* DIRECTIVE */) {\r\n                if (p.name === 'if') {\r\n                    hasIf = true;\r\n                }\r\n                else if (p.name === 'for') {\r\n                    hasFor = true;\r\n                }\r\n            }\r\n            if (hasIf && hasFor) {\r\n                warnDeprecation(\"COMPILER_V_IF_V_FOR_PRECEDENCE\" /* COMPILER_V_IF_V_FOR_PRECEDENCE */, context, getSelection(context, start));\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    let tagType = 0 /* ELEMENT */;\r\n    if (!context.inVPre) {\r\n        if (tag === 'slot') {\r\n            tagType = 2 /* SLOT */;\r\n        }\r\n        else if (tag === 'template') {\r\n            if (props.some(p => p.type === 7 /* DIRECTIVE */ && isSpecialTemplateDirective(p.name))) {\r\n                tagType = 3 /* TEMPLATE */;\r\n            }\r\n        }\r\n        else if (isComponent(tag, props, context)) {\r\n            tagType = 1 /* COMPONENT */;\r\n        }\r\n    }\r\n    return {\r\n        type: 1 /* ELEMENT */,\r\n        ns,\r\n        tag,\r\n        tagType,\r\n        props,\r\n        isSelfClosing,\r\n        children: [],\r\n        loc: getSelection(context, start),\r\n        codegenNode: undefined // to be created during transform phase\r\n    };\r\n}\r\nfunction isComponent(tag, props, context) {\r\n    const options = context.options;\r\n    if (options.isCustomElement(tag)) {\r\n        return false;\r\n    }\r\n    if (tag === 'component' ||\r\n        /^[A-Z]/.test(tag) ||\r\n        isCoreComponent(tag) ||\r\n        (options.isBuiltInComponent && options.isBuiltInComponent(tag)) ||\r\n        (options.isNativeTag && !options.isNativeTag(tag))) {\r\n        return true;\r\n    }\r\n    // at this point the tag should be a native tag, but check for potential \"is\"\r\n    // casting\r\n    for (let i = 0; i < props.length; i++) {\r\n        const p = props[i];\r\n        if (p.type === 6 /* ATTRIBUTE */) {\r\n            if (p.name === 'is' && p.value) {\r\n                if (p.value.content.startsWith('vue:')) {\r\n                    return true;\r\n                }\r\n                else if (checkCompatEnabled(\"COMPILER_IS_ON_ELEMENT\" /* COMPILER_IS_ON_ELEMENT */, context, p.loc)) {\r\n                    return true;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // directive\r\n            // v-is (TODO Deprecate)\r\n            if (p.name === 'is') {\r\n                return true;\r\n            }\r\n            else if (\r\n            // :is on plain element - only treat as component in compat mode\r\n            p.name === 'bind' &&\r\n                isStaticArgOf(p.arg, 'is') &&\r\n                true &&\r\n                checkCompatEnabled(\"COMPILER_IS_ON_ELEMENT\" /* COMPILER_IS_ON_ELEMENT */, context, p.loc)) {\r\n                return true;\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction parseAttributes(context, type) {\r\n    const props = [];\r\n    const attributeNames = new Set();\r\n    while (context.source.length > 0 &&\r\n        !startsWith(context.source, '>') &&\r\n        !startsWith(context.source, '/>')) {\r\n        if (startsWith(context.source, '/')) {\r\n            emitError(context, 22 /* UNEXPECTED_SOLIDUS_IN_TAG */);\r\n            advanceBy(context, 1);\r\n            advanceSpaces(context);\r\n            continue;\r\n        }\r\n        if (type === 1 /* End */) {\r\n            emitError(context, 3 /* END_TAG_WITH_ATTRIBUTES */);\r\n        }\r\n        const attr = parseAttribute(context, attributeNames);\r\n        // Trim whitespace between class\r\n        // https://github.com/vuejs/core/issues/4251\r\n        if (attr.type === 6 /* ATTRIBUTE */ &&\r\n            attr.value &&\r\n            attr.name === 'class') {\r\n            attr.value.content = attr.value.content.replace(/\\s+/g, ' ').trim();\r\n        }\r\n        if (type === 0 /* Start */) {\r\n            props.push(attr);\r\n        }\r\n        if (/^[^\\t\\r\\n\\f />]/.test(context.source)) {\r\n            emitError(context, 15 /* MISSING_WHITESPACE_BETWEEN_ATTRIBUTES */);\r\n        }\r\n        advanceSpaces(context);\r\n    }\r\n    return props;\r\n}\r\nfunction parseAttribute(context, nameSet) {\r\n    // Name.\r\n    const start = getCursor(context);\r\n    const match = /^[^\\t\\r\\n\\f />][^\\t\\r\\n\\f />=]*/.exec(context.source);\r\n    const name = match[0];\r\n    if (nameSet.has(name)) {\r\n        emitError(context, 2 /* DUPLICATE_ATTRIBUTE */);\r\n    }\r\n    nameSet.add(name);\r\n    if (name[0] === '=') {\r\n        emitError(context, 19 /* UNEXPECTED_EQUALS_SIGN_BEFORE_ATTRIBUTE_NAME */);\r\n    }\r\n    {\r\n        const pattern = /[\"'<]/g;\r\n        let m;\r\n        while ((m = pattern.exec(name))) {\r\n            emitError(context, 17 /* UNEXPECTED_CHARACTER_IN_ATTRIBUTE_NAME */, m.index);\r\n        }\r\n    }\r\n    advanceBy(context, name.length);\r\n    // Value\r\n    let value = undefined;\r\n    if (/^[\\t\\r\\n\\f ]*=/.test(context.source)) {\r\n        advanceSpaces(context);\r\n        advanceBy(context, 1);\r\n        advanceSpaces(context);\r\n        value = parseAttributeValue(context);\r\n        if (!value) {\r\n            emitError(context, 13 /* MISSING_ATTRIBUTE_VALUE */);\r\n        }\r\n    }\r\n    const loc = getSelection(context, start);\r\n    if (!context.inVPre && /^(v-[A-Za-z0-9-]|:|\\.|@|#)/.test(name)) {\r\n        const match = /(?:^v-([a-z0-9-]+))?(?:(?::|^\\.|^@|^#)(\\[[^\\]]+\\]|[^\\.]+))?(.+)?$/i.exec(name);\r\n        let isPropShorthand = startsWith(name, '.');\r\n        let dirName = match[1] ||\r\n            (isPropShorthand || startsWith(name, ':')\r\n                ? 'bind'\r\n                : startsWith(name, '@')\r\n                    ? 'on'\r\n                    : 'slot');\r\n        let arg;\r\n        if (match[2]) {\r\n            const isSlot = dirName === 'slot';\r\n            const startOffset = name.lastIndexOf(match[2]);\r\n            const loc = getSelection(context, getNewPosition(context, start, startOffset), getNewPosition(context, start, startOffset + match[2].length + ((isSlot && match[3]) || '').length));\r\n            let content = match[2];\r\n            let isStatic = true;\r\n            if (content.startsWith('[')) {\r\n                isStatic = false;\r\n                if (!content.endsWith(']')) {\r\n                    emitError(context, 27 /* X_MISSING_DYNAMIC_DIRECTIVE_ARGUMENT_END */);\r\n                    content = content.slice(1);\r\n                }\r\n                else {\r\n                    content = content.slice(1, content.length - 1);\r\n                }\r\n            }\r\n            else if (isSlot) {\r\n                // #1241 special case for v-slot: vuetify relies extensively on slot\r\n                // names containing dots. v-slot doesn't have any modifiers and Vue 2.x\r\n                // supports such usage so we are keeping it consistent with 2.x.\r\n                content += match[3] || '';\r\n            }\r\n            arg = {\r\n                type: 4 /* SIMPLE_EXPRESSION */,\r\n                content,\r\n                isStatic,\r\n                constType: isStatic\r\n                    ? 3 /* CAN_STRINGIFY */\r\n                    : 0 /* NOT_CONSTANT */,\r\n                loc\r\n            };\r\n        }\r\n        if (value && value.isQuoted) {\r\n            const valueLoc = value.loc;\r\n            valueLoc.start.offset++;\r\n            valueLoc.start.column++;\r\n            valueLoc.end = advancePositionWithClone(valueLoc.start, value.content);\r\n            valueLoc.source = valueLoc.source.slice(1, -1);\r\n        }\r\n        const modifiers = match[3] ? match[3].slice(1).split('.') : [];\r\n        if (isPropShorthand)\r\n            modifiers.push('prop');\r\n        // 2.x compat v-bind:foo.sync -> v-model:foo\r\n        if (dirName === 'bind' && arg) {\r\n            if (modifiers.includes('sync') &&\r\n                checkCompatEnabled(\"COMPILER_V_BIND_SYNC\" /* COMPILER_V_BIND_SYNC */, context, loc, arg.loc.source)) {\r\n                dirName = 'model';\r\n                modifiers.splice(modifiers.indexOf('sync'), 1);\r\n            }\r\n            if ((process.env.NODE_ENV !== 'production') && modifiers.includes('prop')) {\r\n                checkCompatEnabled(\"COMPILER_V_BIND_PROP\" /* COMPILER_V_BIND_PROP */, context, loc);\r\n            }\r\n        }\r\n        return {\r\n            type: 7 /* DIRECTIVE */,\r\n            name: dirName,\r\n            exp: value && {\r\n                type: 4 /* SIMPLE_EXPRESSION */,\r\n                content: value.content,\r\n                isStatic: false,\r\n                // Treat as non-constant by default. This can be potentially set to\r\n                // other values by `transformExpression` to make it eligible for hoisting.\r\n                constType: 0 /* NOT_CONSTANT */,\r\n                loc: value.loc\r\n            },\r\n            arg,\r\n            modifiers,\r\n            loc\r\n        };\r\n    }\r\n    // missing directive name or illegal directive name\r\n    if (!context.inVPre && startsWith(name, 'v-')) {\r\n        emitError(context, 26 /* X_MISSING_DIRECTIVE_NAME */);\r\n    }\r\n    return {\r\n        type: 6 /* ATTRIBUTE */,\r\n        name,\r\n        value: value && {\r\n            type: 2 /* TEXT */,\r\n            content: value.content,\r\n            loc: value.loc\r\n        },\r\n        loc\r\n    };\r\n}\r\nfunction parseAttributeValue(context) {\r\n    const start = getCursor(context);\r\n    let content;\r\n    const quote = context.source[0];\r\n    const isQuoted = quote === `\"` || quote === `'`;\r\n    if (isQuoted) {\r\n        // Quoted value.\r\n        advanceBy(context, 1);\r\n        const endIndex = context.source.indexOf(quote);\r\n        if (endIndex === -1) {\r\n            content = parseTextData(context, context.source.length, 4 /* ATTRIBUTE_VALUE */);\r\n        }\r\n        else {\r\n            content = parseTextData(context, endIndex, 4 /* ATTRIBUTE_VALUE */);\r\n            advanceBy(context, 1);\r\n        }\r\n    }\r\n    else {\r\n        // Unquoted\r\n        const match = /^[^\\t\\r\\n\\f >]+/.exec(context.source);\r\n        if (!match) {\r\n            return undefined;\r\n        }\r\n        const unexpectedChars = /[\"'<=`]/g;\r\n        let m;\r\n        while ((m = unexpectedChars.exec(match[0]))) {\r\n            emitError(context, 18 /* UNEXPECTED_CHARACTER_IN_UNQUOTED_ATTRIBUTE_VALUE */, m.index);\r\n        }\r\n        content = parseTextData(context, match[0].length, 4 /* ATTRIBUTE_VALUE */);\r\n    }\r\n    return { content, isQuoted, loc: getSelection(context, start) };\r\n}\r\nfunction parseInterpolation(context, mode) {\r\n    const [open, close] = context.options.delimiters;\r\n    const closeIndex = context.source.indexOf(close, open.length);\r\n    if (closeIndex === -1) {\r\n        emitError(context, 25 /* X_MISSING_INTERPOLATION_END */);\r\n        return undefined;\r\n    }\r\n    const start = getCursor(context);\r\n    advanceBy(context, open.length);\r\n    const innerStart = getCursor(context);\r\n    const innerEnd = getCursor(context);\r\n    const rawContentLength = closeIndex - open.length;\r\n    const rawContent = context.source.slice(0, rawContentLength);\r\n    const preTrimContent = parseTextData(context, rawContentLength, mode);\r\n    const content = preTrimContent.trim();\r\n    const startOffset = preTrimContent.indexOf(content);\r\n    if (startOffset > 0) {\r\n        advancePositionWithMutation(innerStart, rawContent, startOffset);\r\n    }\r\n    const endOffset = rawContentLength - (preTrimContent.length - content.length - startOffset);\r\n    advancePositionWithMutation(innerEnd, rawContent, endOffset);\r\n    advanceBy(context, close.length);\r\n    return {\r\n        type: 5 /* INTERPOLATION */,\r\n        content: {\r\n            type: 4 /* SIMPLE_EXPRESSION */,\r\n            isStatic: false,\r\n            // Set `isConstant` to false by default and will decide in transformExpression\r\n            constType: 0 /* NOT_CONSTANT */,\r\n            content,\r\n            loc: getSelection(context, innerStart, innerEnd)\r\n        },\r\n        loc: getSelection(context, start)\r\n    };\r\n}\r\nfunction parseText(context, mode) {\r\n    const endTokens = mode === 3 /* CDATA */ ? [']]>'] : ['<', context.options.delimiters[0]];\r\n    let endIndex = context.source.length;\r\n    for (let i = 0; i < endTokens.length; i++) {\r\n        const index = context.source.indexOf(endTokens[i], 1);\r\n        if (index !== -1 && endIndex > index) {\r\n            endIndex = index;\r\n        }\r\n    }\r\n    const start = getCursor(context);\r\n    const content = parseTextData(context, endIndex, mode);\r\n    return {\r\n        type: 2 /* TEXT */,\r\n        content,\r\n        loc: getSelection(context, start)\r\n    };\r\n}\r\n/**\r\n * Get text data with a given length from the current location.\r\n * This translates HTML entities in the text data.\r\n */\r\nfunction parseTextData(context, length, mode) {\r\n    const rawText = context.source.slice(0, length);\r\n    advanceBy(context, length);\r\n    if (mode === 2 /* RAWTEXT */ ||\r\n        mode === 3 /* CDATA */ ||\r\n        !rawText.includes('&')) {\r\n        return rawText;\r\n    }\r\n    else {\r\n        // DATA or RCDATA containing \"&\"\". Entity decoding required.\r\n        return context.options.decodeEntities(rawText, mode === 4 /* ATTRIBUTE_VALUE */);\r\n    }\r\n}\r\nfunction getCursor(context) {\r\n    const { column, line, offset } = context;\r\n    return { column, line, offset };\r\n}\r\nfunction getSelection(context, start, end) {\r\n    end = end || getCursor(context);\r\n    return {\r\n        start,\r\n        end,\r\n        source: context.originalSource.slice(start.offset, end.offset)\r\n    };\r\n}\r\nfunction last(xs) {\r\n    return xs[xs.length - 1];\r\n}\r\nfunction startsWith(source, searchString) {\r\n    return source.startsWith(searchString);\r\n}\r\nfunction advanceBy(context, numberOfCharacters) {\r\n    const { source } = context;\r\n    advancePositionWithMutation(context, source, numberOfCharacters);\r\n    context.source = source.slice(numberOfCharacters);\r\n}\r\nfunction advanceSpaces(context) {\r\n    const match = /^[\\t\\r\\n\\f ]+/.exec(context.source);\r\n    if (match) {\r\n        advanceBy(context, match[0].length);\r\n    }\r\n}\r\nfunction getNewPosition(context, start, numberOfCharacters) {\r\n    return advancePositionWithClone(start, context.originalSource.slice(start.offset, numberOfCharacters), numberOfCharacters);\r\n}\r\nfunction emitError(context, code, offset, loc = getCursor(context)) {\r\n    if (offset) {\r\n        loc.offset += offset;\r\n        loc.column += offset;\r\n    }\r\n    context.options.onError(createCompilerError(code, {\r\n        start: loc,\r\n        end: loc,\r\n        source: ''\r\n    }));\r\n}\r\nfunction isEnd(context, mode, ancestors) {\r\n    const s = context.source;\r\n    switch (mode) {\r\n        case 0 /* DATA */:\r\n            if (startsWith(s, '</')) {\r\n                // TODO: probably bad performance\r\n                for (let i = ancestors.length - 1; i >= 0; --i) {\r\n                    if (startsWithEndTagOpen(s, ancestors[i].tag)) {\r\n                        return true;\r\n                    }\r\n                }\r\n            }\r\n            break;\r\n        case 1 /* RCDATA */:\r\n        case 2 /* RAWTEXT */: {\r\n            const parent = last(ancestors);\r\n            if (parent && startsWithEndTagOpen(s, parent.tag)) {\r\n                return true;\r\n            }\r\n            break;\r\n        }\r\n        case 3 /* CDATA */:\r\n            if (startsWith(s, ']]>')) {\r\n                return true;\r\n            }\r\n            break;\r\n    }\r\n    return !s;\r\n}\r\nfunction startsWithEndTagOpen(source, tag) {\r\n    return (startsWith(source, '</') &&\r\n        source.slice(2, 2 + tag.length).toLowerCase() === tag.toLowerCase() &&\r\n        /[\\t\\r\\n\\f />]/.test(source[2 + tag.length] || '>'));\r\n}\n\nfunction hoistStatic(root, context) {\r\n    walk(root, context, \r\n    // Root node is unfortunately non-hoistable due to potential parent\r\n    // fallthrough attributes.\r\n    isSingleElementRoot(root, root.children[0]));\r\n}\r\nfunction isSingleElementRoot(root, child) {\r\n    const { children } = root;\r\n    return (children.length === 1 &&\r\n        child.type === 1 /* ELEMENT */ &&\r\n        !isSlotOutlet(child));\r\n}\r\nfunction walk(node, context, doNotHoistNode = false) {\r\n    const { children } = node;\r\n    const originalCount = children.length;\r\n    let hoistedCount = 0;\r\n    for (let i = 0; i < children.length; i++) {\r\n        const child = children[i];\r\n        // only plain elements & text calls are eligible for hoisting.\r\n        if (child.type === 1 /* ELEMENT */ &&\r\n            child.tagType === 0 /* ELEMENT */) {\r\n            const constantType = doNotHoistNode\r\n                ? 0 /* NOT_CONSTANT */\r\n                : getConstantType(child, context);\r\n            if (constantType > 0 /* NOT_CONSTANT */) {\r\n                if (constantType >= 2 /* CAN_HOIST */) {\r\n                    child.codegenNode.patchFlag =\r\n                        -1 /* HOISTED */ + ((process.env.NODE_ENV !== 'production') ? ` /* HOISTED */` : ``);\r\n                    child.codegenNode = context.hoist(child.codegenNode);\r\n                    hoistedCount++;\r\n                    continue;\r\n                }\r\n            }\r\n            else {\r\n                // node may contain dynamic children, but its props may be eligible for\r\n                // hoisting.\r\n                const codegenNode = child.codegenNode;\r\n                if (codegenNode.type === 13 /* VNODE_CALL */) {\r\n                    const flag = getPatchFlag(codegenNode);\r\n                    if ((!flag ||\r\n                        flag === 512 /* NEED_PATCH */ ||\r\n                        flag === 1 /* TEXT */) &&\r\n                        getGeneratedPropsConstantType(child, context) >=\r\n                            2 /* CAN_HOIST */) {\r\n                        const props = getNodeProps(child);\r\n                        if (props) {\r\n                            codegenNode.props = context.hoist(props);\r\n                        }\r\n                    }\r\n                    if (codegenNode.dynamicProps) {\r\n                        codegenNode.dynamicProps = context.hoist(codegenNode.dynamicProps);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        else if (child.type === 12 /* TEXT_CALL */ &&\r\n            getConstantType(child.content, context) >= 2 /* CAN_HOIST */) {\r\n            child.codegenNode = context.hoist(child.codegenNode);\r\n            hoistedCount++;\r\n        }\r\n        // walk further\r\n        if (child.type === 1 /* ELEMENT */) {\r\n            const isComponent = child.tagType === 1 /* COMPONENT */;\r\n            if (isComponent) {\r\n                context.scopes.vSlot++;\r\n            }\r\n            walk(child, context);\r\n            if (isComponent) {\r\n                context.scopes.vSlot--;\r\n            }\r\n        }\r\n        else if (child.type === 11 /* FOR */) {\r\n            // Do not hoist v-for single child because it has to be a block\r\n            walk(child, context, child.children.length === 1);\r\n        }\r\n        else if (child.type === 9 /* IF */) {\r\n            for (let i = 0; i < child.branches.length; i++) {\r\n                // Do not hoist v-if single child because it has to be a block\r\n                walk(child.branches[i], context, child.branches[i].children.length === 1);\r\n            }\r\n        }\r\n    }\r\n    if (hoistedCount && context.transformHoist) {\r\n        context.transformHoist(children, context, node);\r\n    }\r\n    // all children were hoisted - the entire children array is hoistable.\r\n    if (hoistedCount &&\r\n        hoistedCount === originalCount &&\r\n        node.type === 1 /* ELEMENT */ &&\r\n        node.tagType === 0 /* ELEMENT */ &&\r\n        node.codegenNode &&\r\n        node.codegenNode.type === 13 /* VNODE_CALL */ &&\r\n        isArray(node.codegenNode.children)) {\r\n        node.codegenNode.children = context.hoist(createArrayExpression(node.codegenNode.children));\r\n    }\r\n}\r\nfunction getConstantType(node, context) {\r\n    const { constantCache } = context;\r\n    switch (node.type) {\r\n        case 1 /* ELEMENT */:\r\n            if (node.tagType !== 0 /* ELEMENT */) {\r\n                return 0 /* NOT_CONSTANT */;\r\n            }\r\n            const cached = constantCache.get(node);\r\n            if (cached !== undefined) {\r\n                return cached;\r\n            }\r\n            const codegenNode = node.codegenNode;\r\n            if (codegenNode.type !== 13 /* VNODE_CALL */) {\r\n                return 0 /* NOT_CONSTANT */;\r\n            }\r\n            if (codegenNode.isBlock &&\r\n                node.tag !== 'svg' &&\r\n                node.tag !== 'foreignObject') {\r\n                return 0 /* NOT_CONSTANT */;\r\n            }\r\n            const flag = getPatchFlag(codegenNode);\r\n            if (!flag) {\r\n                let returnType = 3 /* CAN_STRINGIFY */;\r\n                // Element itself has no patch flag. However we still need to check:\r\n                // 1. Even for a node with no patch flag, it is possible for it to contain\r\n                // non-hoistable expressions that refers to scope variables, e.g. compiler\r\n                // injected keys or cached event handlers. Therefore we need to always\r\n                // check the codegenNode's props to be sure.\r\n                const generatedPropsType = getGeneratedPropsConstantType(node, context);\r\n                if (generatedPropsType === 0 /* NOT_CONSTANT */) {\r\n                    constantCache.set(node, 0 /* NOT_CONSTANT */);\r\n                    return 0 /* NOT_CONSTANT */;\r\n                }\r\n                if (generatedPropsType < returnType) {\r\n                    returnType = generatedPropsType;\r\n                }\r\n                // 2. its children.\r\n                for (let i = 0; i < node.children.length; i++) {\r\n                    const childType = getConstantType(node.children[i], context);\r\n                    if (childType === 0 /* NOT_CONSTANT */) {\r\n                        constantCache.set(node, 0 /* NOT_CONSTANT */);\r\n                        return 0 /* NOT_CONSTANT */;\r\n                    }\r\n                    if (childType < returnType) {\r\n                        returnType = childType;\r\n                    }\r\n                }\r\n                // 3. if the type is not already CAN_SKIP_PATCH which is the lowest non-0\r\n                // type, check if any of the props can cause the type to be lowered\r\n                // we can skip can_patch because it's guaranteed by the absence of a\r\n                // patchFlag.\r\n                if (returnType > 1 /* CAN_SKIP_PATCH */) {\r\n                    for (let i = 0; i < node.props.length; i++) {\r\n                        const p = node.props[i];\r\n                        if (p.type === 7 /* DIRECTIVE */ && p.name === 'bind' && p.exp) {\r\n                            const expType = getConstantType(p.exp, context);\r\n                            if (expType === 0 /* NOT_CONSTANT */) {\r\n                                constantCache.set(node, 0 /* NOT_CONSTANT */);\r\n                                return 0 /* NOT_CONSTANT */;\r\n                            }\r\n                            if (expType < returnType) {\r\n                                returnType = expType;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n                // only svg/foreignObject could be block here, however if they are\r\n                // static then they don't need to be blocks since there will be no\r\n                // nested updates.\r\n                if (codegenNode.isBlock) {\r\n                    // except set custom directives.\r\n                    for (let i = 0; i < node.props.length; i++) {\r\n                        const p = node.props[i];\r\n                        if (p.type === 7 /* DIRECTIVE */) {\r\n                            constantCache.set(node, 0 /* NOT_CONSTANT */);\r\n                            return 0 /* NOT_CONSTANT */;\r\n                        }\r\n                    }\r\n                    context.removeHelper(OPEN_BLOCK);\r\n                    context.removeHelper(getVNodeBlockHelper(context.inSSR, codegenNode.isComponent));\r\n                    codegenNode.isBlock = false;\r\n                    context.helper(getVNodeHelper(context.inSSR, codegenNode.isComponent));\r\n                }\r\n                constantCache.set(node, returnType);\r\n                return returnType;\r\n            }\r\n            else {\r\n                constantCache.set(node, 0 /* NOT_CONSTANT */);\r\n                return 0 /* NOT_CONSTANT */;\r\n            }\r\n        case 2 /* TEXT */:\r\n        case 3 /* COMMENT */:\r\n            return 3 /* CAN_STRINGIFY */;\r\n        case 9 /* IF */:\r\n        case 11 /* FOR */:\r\n        case 10 /* IF_BRANCH */:\r\n            return 0 /* NOT_CONSTANT */;\r\n        case 5 /* INTERPOLATION */:\r\n        case 12 /* TEXT_CALL */:\r\n            return getConstantType(node.content, context);\r\n        case 4 /* SIMPLE_EXPRESSION */:\r\n            return node.constType;\r\n        case 8 /* COMPOUND_EXPRESSION */:\r\n            let returnType = 3 /* CAN_STRINGIFY */;\r\n            for (let i = 0; i < node.children.length; i++) {\r\n                const child = node.children[i];\r\n                if (isString(child) || isSymbol(child)) {\r\n                    continue;\r\n                }\r\n                const childType = getConstantType(child, context);\r\n                if (childType === 0 /* NOT_CONSTANT */) {\r\n                    return 0 /* NOT_CONSTANT */;\r\n                }\r\n                else if (childType < returnType) {\r\n                    returnType = childType;\r\n                }\r\n            }\r\n            return returnType;\r\n        default:\r\n            if ((process.env.NODE_ENV !== 'production')) ;\r\n            return 0 /* NOT_CONSTANT */;\r\n    }\r\n}\r\nconst allowHoistedHelperSet = new Set([\r\n    NORMALIZE_CLASS,\r\n    NORMALIZE_STYLE,\r\n    NORMALIZE_PROPS,\r\n    GUARD_REACTIVE_PROPS\r\n]);\r\nfunction getConstantTypeOfHelperCall(value, context) {\r\n    if (value.type === 14 /* JS_CALL_EXPRESSION */ &&\r\n        !isString(value.callee) &&\r\n        allowHoistedHelperSet.has(value.callee)) {\r\n        const arg = value.arguments[0];\r\n        if (arg.type === 4 /* SIMPLE_EXPRESSION */) {\r\n            return getConstantType(arg, context);\r\n        }\r\n        else if (arg.type === 14 /* JS_CALL_EXPRESSION */) {\r\n            // in the case of nested helper call, e.g. `normalizeProps(guardReactiveProps(exp))`\r\n            return getConstantTypeOfHelperCall(arg, context);\r\n        }\r\n    }\r\n    return 0 /* NOT_CONSTANT */;\r\n}\r\nfunction getGeneratedPropsConstantType(node, context) {\r\n    let returnType = 3 /* CAN_STRINGIFY */;\r\n    const props = getNodeProps(node);\r\n    if (props && props.type === 15 /* JS_OBJECT_EXPRESSION */) {\r\n        const { properties } = props;\r\n        for (let i = 0; i < properties.length; i++) {\r\n            const { key, value } = properties[i];\r\n            const keyType = getConstantType(key, context);\r\n            if (keyType === 0 /* NOT_CONSTANT */) {\r\n                return keyType;\r\n            }\r\n            if (keyType < returnType) {\r\n                returnType = keyType;\r\n            }\r\n            let valueType;\r\n            if (value.type === 4 /* SIMPLE_EXPRESSION */) {\r\n                valueType = getConstantType(value, context);\r\n            }\r\n            else if (value.type === 14 /* JS_CALL_EXPRESSION */) {\r\n                // some helper calls can be hoisted,\r\n                // such as the `normalizeProps` generated by the compiler for pre-normalize class,\r\n                // in this case we need to respect the ConstantType of the helper's arguments\r\n                valueType = getConstantTypeOfHelperCall(value, context);\r\n            }\r\n            else {\r\n                valueType = 0 /* NOT_CONSTANT */;\r\n            }\r\n            if (valueType === 0 /* NOT_CONSTANT */) {\r\n                return valueType;\r\n            }\r\n            if (valueType < returnType) {\r\n                returnType = valueType;\r\n            }\r\n        }\r\n    }\r\n    return returnType;\r\n}\r\nfunction getNodeProps(node) {\r\n    const codegenNode = node.codegenNode;\r\n    if (codegenNode.type === 13 /* VNODE_CALL */) {\r\n        return codegenNode.props;\r\n    }\r\n}\r\nfunction getPatchFlag(node) {\r\n    const flag = node.patchFlag;\r\n    return flag ? parseInt(flag, 10) : undefined;\r\n}\n\nfunction createTransformContext(root, { filename = '', prefixIdentifiers = false, hoistStatic = false, cacheHandlers = false, nodeTransforms = [], directiveTransforms = {}, transformHoist = null, isBuiltInComponent = NOOP, isCustomElement = NOOP, expressionPlugins = [], scopeId = null, slotted = true, ssr = false, inSSR = false, ssrCssVars = ``, bindingMetadata = EMPTY_OBJ, inline = false, isTS = false, onError = defaultOnError, onWarn = defaultOnWarn, compatConfig }) {\r\n    const nameMatch = filename.replace(/\\?.*$/, '').match(/([^/\\\\]+)\\.\\w+$/);\r\n    const context = {\r\n        // options\r\n        selfName: nameMatch && capitalize(camelize$1(nameMatch[1])),\r\n        prefixIdentifiers,\r\n        hoistStatic,\r\n        cacheHandlers,\r\n        nodeTransforms,\r\n        directiveTransforms,\r\n        transformHoist,\r\n        isBuiltInComponent,\r\n        isCustomElement,\r\n        expressionPlugins,\r\n        scopeId,\r\n        slotted,\r\n        ssr,\r\n        inSSR,\r\n        ssrCssVars,\r\n        bindingMetadata,\r\n        inline,\r\n        isTS,\r\n        onError,\r\n        onWarn,\r\n        compatConfig,\r\n        // state\r\n        root,\r\n        helpers: new Map(),\r\n        components: new Set(),\r\n        directives: new Set(),\r\n        hoists: [],\r\n        imports: [],\r\n        constantCache: new Map(),\r\n        temps: 0,\r\n        cached: 0,\r\n        identifiers: Object.create(null),\r\n        scopes: {\r\n            vFor: 0,\r\n            vSlot: 0,\r\n            vPre: 0,\r\n            vOnce: 0\r\n        },\r\n        parent: null,\r\n        currentNode: root,\r\n        childIndex: 0,\r\n        inVOnce: false,\r\n        // methods\r\n        helper(name) {\r\n            const count = context.helpers.get(name) || 0;\r\n            context.helpers.set(name, count + 1);\r\n            return name;\r\n        },\r\n        removeHelper(name) {\r\n            const count = context.helpers.get(name);\r\n            if (count) {\r\n                const currentCount = count - 1;\r\n                if (!currentCount) {\r\n                    context.helpers.delete(name);\r\n                }\r\n                else {\r\n                    context.helpers.set(name, currentCount);\r\n                }\r\n            }\r\n        },\r\n        helperString(name) {\r\n            return `_${helperNameMap[context.helper(name)]}`;\r\n        },\r\n        replaceNode(node) {\r\n            /* istanbul ignore if */\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                if (!context.currentNode) {\r\n                    throw new Error(`Node being replaced is already removed.`);\r\n                }\r\n                if (!context.parent) {\r\n                    throw new Error(`Cannot replace root node.`);\r\n                }\r\n            }\r\n            context.parent.children[context.childIndex] = context.currentNode = node;\r\n        },\r\n        removeNode(node) {\r\n            if ((process.env.NODE_ENV !== 'production') && !context.parent) {\r\n                throw new Error(`Cannot remove root node.`);\r\n            }\r\n            const list = context.parent.children;\r\n            const removalIndex = node\r\n                ? list.indexOf(node)\r\n                : context.currentNode\r\n                    ? context.childIndex\r\n                    : -1;\r\n            /* istanbul ignore if */\r\n            if ((process.env.NODE_ENV !== 'production') && removalIndex < 0) {\r\n                throw new Error(`node being removed is not a child of current parent`);\r\n            }\r\n            if (!node || node === context.currentNode) {\r\n                // current node removed\r\n                context.currentNode = null;\r\n                context.onNodeRemoved();\r\n            }\r\n            else {\r\n                // sibling node removed\r\n                if (context.childIndex > removalIndex) {\r\n                    context.childIndex--;\r\n                    context.onNodeRemoved();\r\n                }\r\n            }\r\n            context.parent.children.splice(removalIndex, 1);\r\n        },\r\n        onNodeRemoved: () => { },\r\n        addIdentifiers(exp) {\r\n        },\r\n        removeIdentifiers(exp) {\r\n        },\r\n        hoist(exp) {\r\n            if (isString(exp))\r\n                exp = createSimpleExpression(exp);\r\n            context.hoists.push(exp);\r\n            const identifier = createSimpleExpression(`_hoisted_${context.hoists.length}`, false, exp.loc, 2 /* CAN_HOIST */);\r\n            identifier.hoisted = exp;\r\n            return identifier;\r\n        },\r\n        cache(exp, isVNode = false) {\r\n            return createCacheExpression(context.cached++, exp, isVNode);\r\n        }\r\n    };\r\n    {\r\n        context.filters = new Set();\r\n    }\r\n    return context;\r\n}\r\nfunction transform(root, options) {\r\n    const context = createTransformContext(root, options);\r\n    traverseNode(root, context);\r\n    if (options.hoistStatic) {\r\n        hoistStatic(root, context);\r\n    }\r\n    if (!options.ssr) {\r\n        createRootCodegen(root, context);\r\n    }\r\n    // finalize meta information\r\n    root.helpers = [...context.helpers.keys()];\r\n    root.components = [...context.components];\r\n    root.directives = [...context.directives];\r\n    root.imports = context.imports;\r\n    root.hoists = context.hoists;\r\n    root.temps = context.temps;\r\n    root.cached = context.cached;\r\n    {\r\n        root.filters = [...context.filters];\r\n    }\r\n}\r\nfunction createRootCodegen(root, context) {\r\n    const { helper } = context;\r\n    const { children } = root;\r\n    if (children.length === 1) {\r\n        const child = children[0];\r\n        // if the single child is an element, turn it into a block.\r\n        if (isSingleElementRoot(root, child) && child.codegenNode) {\r\n            // single element root is never hoisted so codegenNode will never be\r\n            // SimpleExpressionNode\r\n            const codegenNode = child.codegenNode;\r\n            if (codegenNode.type === 13 /* VNODE_CALL */) {\r\n                makeBlock(codegenNode, context);\r\n            }\r\n            root.codegenNode = codegenNode;\r\n        }\r\n        else {\r\n            // - single <slot/>, IfNode, ForNode: already blocks.\r\n            // - single text node: always patched.\r\n            // root codegen falls through via genNode()\r\n            root.codegenNode = child;\r\n        }\r\n    }\r\n    else if (children.length > 1) {\r\n        // root has multiple nodes - return a fragment block.\r\n        let patchFlag = 64 /* STABLE_FRAGMENT */;\r\n        let patchFlagText = PatchFlagNames[64 /* STABLE_FRAGMENT */];\r\n        // check if the fragment actually contains a single valid child with\r\n        // the rest being comments\r\n        if ((process.env.NODE_ENV !== 'production') &&\r\n            children.filter(c => c.type !== 3 /* COMMENT */).length === 1) {\r\n            patchFlag |= 2048 /* DEV_ROOT_FRAGMENT */;\r\n            patchFlagText += `, ${PatchFlagNames[2048 /* DEV_ROOT_FRAGMENT */]}`;\r\n        }\r\n        root.codegenNode = createVNodeCall(context, helper(FRAGMENT), undefined, root.children, patchFlag + ((process.env.NODE_ENV !== 'production') ? ` /* ${patchFlagText} */` : ``), undefined, undefined, true, undefined, false /* isComponent */);\r\n    }\r\n    else ;\r\n}\r\nfunction traverseChildren(parent, context) {\r\n    let i = 0;\r\n    const nodeRemoved = () => {\r\n        i--;\r\n    };\r\n    for (; i < parent.children.length; i++) {\r\n        const child = parent.children[i];\r\n        if (isString(child))\r\n            continue;\r\n        context.parent = parent;\r\n        context.childIndex = i;\r\n        context.onNodeRemoved = nodeRemoved;\r\n        traverseNode(child, context);\r\n    }\r\n}\r\nfunction traverseNode(node, context) {\r\n    context.currentNode = node;\r\n    // apply transform plugins\r\n    const { nodeTransforms } = context;\r\n    const exitFns = [];\r\n    for (let i = 0; i < nodeTransforms.length; i++) {\r\n        const onExit = nodeTransforms[i](node, context);\r\n        if (onExit) {\r\n            if (isArray(onExit)) {\r\n                exitFns.push(...onExit);\r\n            }\r\n            else {\r\n                exitFns.push(onExit);\r\n            }\r\n        }\r\n        if (!context.currentNode) {\r\n            // node was removed\r\n            return;\r\n        }\r\n        else {\r\n            // node may have been replaced\r\n            node = context.currentNode;\r\n        }\r\n    }\r\n    switch (node.type) {\r\n        case 3 /* COMMENT */:\r\n            if (!context.ssr) {\r\n                // inject import for the Comment symbol, which is needed for creating\r\n                // comment nodes with `createVNode`\r\n                context.helper(CREATE_COMMENT);\r\n            }\r\n            break;\r\n        case 5 /* INTERPOLATION */:\r\n            // no need to traverse, but we need to inject toString helper\r\n            if (!context.ssr) {\r\n                context.helper(TO_DISPLAY_STRING);\r\n            }\r\n            break;\r\n        // for container types, further traverse downwards\r\n        case 9 /* IF */:\r\n            for (let i = 0; i < node.branches.length; i++) {\r\n                traverseNode(node.branches[i], context);\r\n            }\r\n            break;\r\n        case 10 /* IF_BRANCH */:\r\n        case 11 /* FOR */:\r\n        case 1 /* ELEMENT */:\r\n        case 0 /* ROOT */:\r\n            traverseChildren(node, context);\r\n            break;\r\n    }\r\n    // exit transforms\r\n    context.currentNode = node;\r\n    let i = exitFns.length;\r\n    while (i--) {\r\n        exitFns[i]();\r\n    }\r\n}\r\nfunction createStructuralDirectiveTransform(name, fn) {\r\n    const matches = isString(name)\r\n        ? (n) => n === name\r\n        : (n) => name.test(n);\r\n    return (node, context) => {\r\n        if (node.type === 1 /* ELEMENT */) {\r\n            const { props } = node;\r\n            // structural directive transforms are not concerned with slots\r\n            // as they are handled separately in vSlot.ts\r\n            if (node.tagType === 3 /* TEMPLATE */ && props.some(isVSlot)) {\r\n                return;\r\n            }\r\n            const exitFns = [];\r\n            for (let i = 0; i < props.length; i++) {\r\n                const prop = props[i];\r\n                if (prop.type === 7 /* DIRECTIVE */ && matches(prop.name)) {\r\n                    // structural directives are removed to avoid infinite recursion\r\n                    // also we remove them *before* applying so that it can further\r\n                    // traverse itself in case it moves the node around\r\n                    props.splice(i, 1);\r\n                    i--;\r\n                    const onExit = fn(node, prop, context);\r\n                    if (onExit)\r\n                        exitFns.push(onExit);\r\n                }\r\n            }\r\n            return exitFns;\r\n        }\r\n    };\r\n}\n\nconst PURE_ANNOTATION = `/*#__PURE__*/`;\r\nconst aliasHelper = (s) => `${helperNameMap[s]}: _${helperNameMap[s]}`;\r\nfunction createCodegenContext(ast, { mode = 'function', prefixIdentifiers = mode === 'module', sourceMap = false, filename = `template.vue.html`, scopeId = null, optimizeImports = false, runtimeGlobalName = `Vue`, runtimeModuleName = `vue`, ssrRuntimeModuleName = 'vue/server-renderer', ssr = false, isTS = false, inSSR = false }) {\r\n    const context = {\r\n        mode,\r\n        prefixIdentifiers,\r\n        sourceMap,\r\n        filename,\r\n        scopeId,\r\n        optimizeImports,\r\n        runtimeGlobalName,\r\n        runtimeModuleName,\r\n        ssrRuntimeModuleName,\r\n        ssr,\r\n        isTS,\r\n        inSSR,\r\n        source: ast.loc.source,\r\n        code: ``,\r\n        column: 1,\r\n        line: 1,\r\n        offset: 0,\r\n        indentLevel: 0,\r\n        pure: false,\r\n        map: undefined,\r\n        helper(key) {\r\n            return `_${helperNameMap[key]}`;\r\n        },\r\n        push(code, node) {\r\n            context.code += code;\r\n        },\r\n        indent() {\r\n            newline(++context.indentLevel);\r\n        },\r\n        deindent(withoutNewLine = false) {\r\n            if (withoutNewLine) {\r\n                --context.indentLevel;\r\n            }\r\n            else {\r\n                newline(--context.indentLevel);\r\n            }\r\n        },\r\n        newline() {\r\n            newline(context.indentLevel);\r\n        }\r\n    };\r\n    function newline(n) {\r\n        context.push('\\n' + `  `.repeat(n));\r\n    }\r\n    return context;\r\n}\r\nfunction generate(ast, options = {}) {\r\n    const context = createCodegenContext(ast, options);\r\n    if (options.onContextCreated)\r\n        options.onContextCreated(context);\r\n    const { mode, push, prefixIdentifiers, indent, deindent, newline, scopeId, ssr } = context;\r\n    const hasHelpers = ast.helpers.length > 0;\r\n    const useWithBlock = !prefixIdentifiers && mode !== 'module';\r\n    // preambles\r\n    // in setup() inline mode, the preamble is generated in a sub context\r\n    // and returned separately.\r\n    const preambleContext = context;\r\n    {\r\n        genFunctionPreamble(ast, preambleContext);\r\n    }\r\n    // enter render function\r\n    const functionName = ssr ? `ssrRender` : `render`;\r\n    const args = ssr ? ['_ctx', '_push', '_parent', '_attrs'] : ['_ctx', '_cache'];\r\n    const signature = args.join(', ');\r\n    {\r\n        push(`function ${functionName}(${signature}) {`);\r\n    }\r\n    indent();\r\n    if (useWithBlock) {\r\n        push(`with (_ctx) {`);\r\n        indent();\r\n        // function mode const declarations should be inside with block\r\n        // also they should be renamed to avoid collision with user properties\r\n        if (hasHelpers) {\r\n            push(`const { ${ast.helpers.map(aliasHelper).join(', ')} } = _Vue`);\r\n            push(`\\n`);\r\n            newline();\r\n        }\r\n    }\r\n    // generate asset resolution statements\r\n    if (ast.components.length) {\r\n        genAssets(ast.components, 'component', context);\r\n        if (ast.directives.length || ast.temps > 0) {\r\n            newline();\r\n        }\r\n    }\r\n    if (ast.directives.length) {\r\n        genAssets(ast.directives, 'directive', context);\r\n        if (ast.temps > 0) {\r\n            newline();\r\n        }\r\n    }\r\n    if (ast.filters && ast.filters.length) {\r\n        newline();\r\n        genAssets(ast.filters, 'filter', context);\r\n        newline();\r\n    }\r\n    if (ast.temps > 0) {\r\n        push(`let `);\r\n        for (let i = 0; i < ast.temps; i++) {\r\n            push(`${i > 0 ? `, ` : ``}_temp${i}`);\r\n        }\r\n    }\r\n    if (ast.components.length || ast.directives.length || ast.temps) {\r\n        push(`\\n`);\r\n        newline();\r\n    }\r\n    // generate the VNode tree expression\r\n    if (!ssr) {\r\n        push(`return `);\r\n    }\r\n    if (ast.codegenNode) {\r\n        genNode(ast.codegenNode, context);\r\n    }\r\n    else {\r\n        push(`null`);\r\n    }\r\n    if (useWithBlock) {\r\n        deindent();\r\n        push(`}`);\r\n    }\r\n    deindent();\r\n    push(`}`);\r\n    return {\r\n        ast,\r\n        code: context.code,\r\n        preamble: ``,\r\n        // SourceMapGenerator does have toJSON() method but it's not in the types\r\n        map: context.map ? context.map.toJSON() : undefined\r\n    };\r\n}\r\nfunction genFunctionPreamble(ast, context) {\r\n    const { ssr, prefixIdentifiers, push, newline, runtimeModuleName, runtimeGlobalName, ssrRuntimeModuleName } = context;\r\n    const VueBinding = runtimeGlobalName;\r\n    // Generate const declaration for helpers\r\n    // In prefix mode, we place the const declaration at top so it's done\r\n    // only once; But if we not prefixing, we place the declaration inside the\r\n    // with block so it doesn't incur the `in` check cost for every helper access.\r\n    if (ast.helpers.length > 0) {\r\n        {\r\n            // \"with\" mode.\r\n            // save Vue in a separate variable to avoid collision\r\n            push(`const _Vue = ${VueBinding}\\n`);\r\n            // in \"with\" mode, helpers are declared inside the with block to avoid\r\n            // has check cost, but hoists are lifted out of the function - we need\r\n            // to provide the helper here.\r\n            if (ast.hoists.length) {\r\n                const staticHelpers = [\r\n                    CREATE_VNODE,\r\n                    CREATE_ELEMENT_VNODE,\r\n                    CREATE_COMMENT,\r\n                    CREATE_TEXT,\r\n                    CREATE_STATIC\r\n                ]\r\n                    .filter(helper => ast.helpers.includes(helper))\r\n                    .map(aliasHelper)\r\n                    .join(', ');\r\n                push(`const { ${staticHelpers} } = _Vue\\n`);\r\n            }\r\n        }\r\n    }\r\n    genHoists(ast.hoists, context);\r\n    newline();\r\n    push(`return `);\r\n}\r\nfunction genAssets(assets, type, { helper, push, newline, isTS }) {\r\n    const resolver = helper(type === 'filter'\r\n        ? RESOLVE_FILTER\r\n        : type === 'component'\r\n            ? RESOLVE_COMPONENT\r\n            : RESOLVE_DIRECTIVE);\r\n    for (let i = 0; i < assets.length; i++) {\r\n        let id = assets[i];\r\n        // potential component implicit self-reference inferred from SFC filename\r\n        const maybeSelfReference = id.endsWith('__self');\r\n        if (maybeSelfReference) {\r\n            id = id.slice(0, -6);\r\n        }\r\n        push(`const ${toValidAssetId(id, type)} = ${resolver}(${JSON.stringify(id)}${maybeSelfReference ? `, true` : ``})${isTS ? `!` : ``}`);\r\n        if (i < assets.length - 1) {\r\n            newline();\r\n        }\r\n    }\r\n}\r\nfunction genHoists(hoists, context) {\r\n    if (!hoists.length) {\r\n        return;\r\n    }\r\n    context.pure = true;\r\n    const { push, newline, helper, scopeId, mode } = context;\r\n    newline();\r\n    for (let i = 0; i < hoists.length; i++) {\r\n        const exp = hoists[i];\r\n        if (exp) {\r\n            push(`const _hoisted_${i + 1} = ${``}`);\r\n            genNode(exp, context);\r\n            newline();\r\n        }\r\n    }\r\n    context.pure = false;\r\n}\r\nfunction isText$1(n) {\r\n    return (isString(n) ||\r\n        n.type === 4 /* SIMPLE_EXPRESSION */ ||\r\n        n.type === 2 /* TEXT */ ||\r\n        n.type === 5 /* INTERPOLATION */ ||\r\n        n.type === 8 /* COMPOUND_EXPRESSION */);\r\n}\r\nfunction genNodeListAsArray(nodes, context) {\r\n    const multilines = nodes.length > 3 ||\r\n        (((process.env.NODE_ENV !== 'production')) && nodes.some(n => isArray(n) || !isText$1(n)));\r\n    context.push(`[`);\r\n    multilines && context.indent();\r\n    genNodeList(nodes, context, multilines);\r\n    multilines && context.deindent();\r\n    context.push(`]`);\r\n}\r\nfunction genNodeList(nodes, context, multilines = false, comma = true) {\r\n    const { push, newline } = context;\r\n    for (let i = 0; i < nodes.length; i++) {\r\n        const node = nodes[i];\r\n        if (isString(node)) {\r\n            push(node);\r\n        }\r\n        else if (isArray(node)) {\r\n            genNodeListAsArray(node, context);\r\n        }\r\n        else {\r\n            genNode(node, context);\r\n        }\r\n        if (i < nodes.length - 1) {\r\n            if (multilines) {\r\n                comma && push(',');\r\n                newline();\r\n            }\r\n            else {\r\n                comma && push(', ');\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction genNode(node, context) {\r\n    if (isString(node)) {\r\n        context.push(node);\r\n        return;\r\n    }\r\n    if (isSymbol(node)) {\r\n        context.push(context.helper(node));\r\n        return;\r\n    }\r\n    switch (node.type) {\r\n        case 1 /* ELEMENT */:\r\n        case 9 /* IF */:\r\n        case 11 /* FOR */:\r\n            (process.env.NODE_ENV !== 'production') &&\r\n                assert(node.codegenNode != null, `Codegen node is missing for element/if/for node. ` +\r\n                    `Apply appropriate transforms first.`);\r\n            genNode(node.codegenNode, context);\r\n            break;\r\n        case 2 /* TEXT */:\r\n            genText(node, context);\r\n            break;\r\n        case 4 /* SIMPLE_EXPRESSION */:\r\n            genExpression(node, context);\r\n            break;\r\n        case 5 /* INTERPOLATION */:\r\n            genInterpolation(node, context);\r\n            break;\r\n        case 12 /* TEXT_CALL */:\r\n            genNode(node.codegenNode, context);\r\n            break;\r\n        case 8 /* COMPOUND_EXPRESSION */:\r\n            genCompoundExpression(node, context);\r\n            break;\r\n        case 3 /* COMMENT */:\r\n            genComment(node, context);\r\n            break;\r\n        case 13 /* VNODE_CALL */:\r\n            genVNodeCall(node, context);\r\n            break;\r\n        case 14 /* JS_CALL_EXPRESSION */:\r\n            genCallExpression(node, context);\r\n            break;\r\n        case 15 /* JS_OBJECT_EXPRESSION */:\r\n            genObjectExpression(node, context);\r\n            break;\r\n        case 17 /* JS_ARRAY_EXPRESSION */:\r\n            genArrayExpression(node, context);\r\n            break;\r\n        case 18 /* JS_FUNCTION_EXPRESSION */:\r\n            genFunctionExpression(node, context);\r\n            break;\r\n        case 19 /* JS_CONDITIONAL_EXPRESSION */:\r\n            genConditionalExpression(node, context);\r\n            break;\r\n        case 20 /* JS_CACHE_EXPRESSION */:\r\n            genCacheExpression(node, context);\r\n            break;\r\n        case 21 /* JS_BLOCK_STATEMENT */:\r\n            genNodeList(node.body, context, true, false);\r\n            break;\r\n        // SSR only types\r\n        case 22 /* JS_TEMPLATE_LITERAL */:\r\n            break;\r\n        case 23 /* JS_IF_STATEMENT */:\r\n            break;\r\n        case 24 /* JS_ASSIGNMENT_EXPRESSION */:\r\n            break;\r\n        case 25 /* JS_SEQUENCE_EXPRESSION */:\r\n            break;\r\n        case 26 /* JS_RETURN_STATEMENT */:\r\n            break;\r\n        /* istanbul ignore next */\r\n        case 10 /* IF_BRANCH */:\r\n            // noop\r\n            break;\r\n        default:\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                assert(false, `unhandled codegen node type: ${node.type}`);\r\n                // make sure we exhaust all possible types\r\n                const exhaustiveCheck = node;\r\n                return exhaustiveCheck;\r\n            }\r\n    }\r\n}\r\nfunction genText(node, context) {\r\n    context.push(JSON.stringify(node.content), node);\r\n}\r\nfunction genExpression(node, context) {\r\n    const { content, isStatic } = node;\r\n    context.push(isStatic ? JSON.stringify(content) : content, node);\r\n}\r\nfunction genInterpolation(node, context) {\r\n    const { push, helper, pure } = context;\r\n    if (pure)\r\n        push(PURE_ANNOTATION);\r\n    push(`${helper(TO_DISPLAY_STRING)}(`);\r\n    genNode(node.content, context);\r\n    push(`)`);\r\n}\r\nfunction genCompoundExpression(node, context) {\r\n    for (let i = 0; i < node.children.length; i++) {\r\n        const child = node.children[i];\r\n        if (isString(child)) {\r\n            context.push(child);\r\n        }\r\n        else {\r\n            genNode(child, context);\r\n        }\r\n    }\r\n}\r\nfunction genExpressionAsPropertyKey(node, context) {\r\n    const { push } = context;\r\n    if (node.type === 8 /* COMPOUND_EXPRESSION */) {\r\n        push(`[`);\r\n        genCompoundExpression(node, context);\r\n        push(`]`);\r\n    }\r\n    else if (node.isStatic) {\r\n        // only quote keys if necessary\r\n        const text = isSimpleIdentifier(node.content)\r\n            ? node.content\r\n            : JSON.stringify(node.content);\r\n        push(text, node);\r\n    }\r\n    else {\r\n        push(`[${node.content}]`, node);\r\n    }\r\n}\r\nfunction genComment(node, context) {\r\n    const { push, helper, pure } = context;\r\n    if (pure) {\r\n        push(PURE_ANNOTATION);\r\n    }\r\n    push(`${helper(CREATE_COMMENT)}(${JSON.stringify(node.content)})`, node);\r\n}\r\nfunction genVNodeCall(node, context) {\r\n    const { push, helper, pure } = context;\r\n    const { tag, props, children, patchFlag, dynamicProps, directives, isBlock, disableTracking, isComponent } = node;\r\n    if (directives) {\r\n        push(helper(WITH_DIRECTIVES) + `(`);\r\n    }\r\n    if (isBlock) {\r\n        push(`(${helper(OPEN_BLOCK)}(${disableTracking ? `true` : ``}), `);\r\n    }\r\n    if (pure) {\r\n        push(PURE_ANNOTATION);\r\n    }\r\n    const callHelper = isBlock\r\n        ? getVNodeBlockHelper(context.inSSR, isComponent)\r\n        : getVNodeHelper(context.inSSR, isComponent);\r\n    push(helper(callHelper) + `(`, node);\r\n    genNodeList(genNullableArgs([tag, props, children, patchFlag, dynamicProps]), context);\r\n    push(`)`);\r\n    if (isBlock) {\r\n        push(`)`);\r\n    }\r\n    if (directives) {\r\n        push(`, `);\r\n        genNode(directives, context);\r\n        push(`)`);\r\n    }\r\n}\r\nfunction genNullableArgs(args) {\r\n    let i = args.length;\r\n    while (i--) {\r\n        if (args[i] != null)\r\n            break;\r\n    }\r\n    return args.slice(0, i + 1).map(arg => arg || `null`);\r\n}\r\n// JavaScript\r\nfunction genCallExpression(node, context) {\r\n    const { push, helper, pure } = context;\r\n    const callee = isString(node.callee) ? node.callee : helper(node.callee);\r\n    if (pure) {\r\n        push(PURE_ANNOTATION);\r\n    }\r\n    push(callee + `(`, node);\r\n    genNodeList(node.arguments, context);\r\n    push(`)`);\r\n}\r\nfunction genObjectExpression(node, context) {\r\n    const { push, indent, deindent, newline } = context;\r\n    const { properties } = node;\r\n    if (!properties.length) {\r\n        push(`{}`, node);\r\n        return;\r\n    }\r\n    const multilines = properties.length > 1 ||\r\n        (((process.env.NODE_ENV !== 'production')) &&\r\n            properties.some(p => p.value.type !== 4 /* SIMPLE_EXPRESSION */));\r\n    push(multilines ? `{` : `{ `);\r\n    multilines && indent();\r\n    for (let i = 0; i < properties.length; i++) {\r\n        const { key, value } = properties[i];\r\n        // key\r\n        genExpressionAsPropertyKey(key, context);\r\n        push(`: `);\r\n        // value\r\n        genNode(value, context);\r\n        if (i < properties.length - 1) {\r\n            // will only reach this if it's multilines\r\n            push(`,`);\r\n            newline();\r\n        }\r\n    }\r\n    multilines && deindent();\r\n    push(multilines ? `}` : ` }`);\r\n}\r\nfunction genArrayExpression(node, context) {\r\n    genNodeListAsArray(node.elements, context);\r\n}\r\nfunction genFunctionExpression(node, context) {\r\n    const { push, indent, deindent } = context;\r\n    const { params, returns, body, newline, isSlot } = node;\r\n    if (isSlot) {\r\n        // wrap slot functions with owner context\r\n        push(`_${helperNameMap[WITH_CTX]}(`);\r\n    }\r\n    push(`(`, node);\r\n    if (isArray(params)) {\r\n        genNodeList(params, context);\r\n    }\r\n    else if (params) {\r\n        genNode(params, context);\r\n    }\r\n    push(`) => `);\r\n    if (newline || body) {\r\n        push(`{`);\r\n        indent();\r\n    }\r\n    if (returns) {\r\n        if (newline) {\r\n            push(`return `);\r\n        }\r\n        if (isArray(returns)) {\r\n            genNodeListAsArray(returns, context);\r\n        }\r\n        else {\r\n            genNode(returns, context);\r\n        }\r\n    }\r\n    else if (body) {\r\n        genNode(body, context);\r\n    }\r\n    if (newline || body) {\r\n        deindent();\r\n        push(`}`);\r\n    }\r\n    if (isSlot) {\r\n        if (node.isNonScopedSlot) {\r\n            push(`, undefined, true`);\r\n        }\r\n        push(`)`);\r\n    }\r\n}\r\nfunction genConditionalExpression(node, context) {\r\n    const { test, consequent, alternate, newline: needNewline } = node;\r\n    const { push, indent, deindent, newline } = context;\r\n    if (test.type === 4 /* SIMPLE_EXPRESSION */) {\r\n        const needsParens = !isSimpleIdentifier(test.content);\r\n        needsParens && push(`(`);\r\n        genExpression(test, context);\r\n        needsParens && push(`)`);\r\n    }\r\n    else {\r\n        push(`(`);\r\n        genNode(test, context);\r\n        push(`)`);\r\n    }\r\n    needNewline && indent();\r\n    context.indentLevel++;\r\n    needNewline || push(` `);\r\n    push(`? `);\r\n    genNode(consequent, context);\r\n    context.indentLevel--;\r\n    needNewline && newline();\r\n    needNewline || push(` `);\r\n    push(`: `);\r\n    const isNested = alternate.type === 19 /* JS_CONDITIONAL_EXPRESSION */;\r\n    if (!isNested) {\r\n        context.indentLevel++;\r\n    }\r\n    genNode(alternate, context);\r\n    if (!isNested) {\r\n        context.indentLevel--;\r\n    }\r\n    needNewline && deindent(true /* without newline */);\r\n}\r\nfunction genCacheExpression(node, context) {\r\n    const { push, helper, indent, deindent, newline } = context;\r\n    push(`_cache[${node.index}] || (`);\r\n    if (node.isVNode) {\r\n        indent();\r\n        push(`${helper(SET_BLOCK_TRACKING)}(-1),`);\r\n        newline();\r\n    }\r\n    push(`_cache[${node.index}] = `);\r\n    genNode(node.value, context);\r\n    if (node.isVNode) {\r\n        push(`,`);\r\n        newline();\r\n        push(`${helper(SET_BLOCK_TRACKING)}(1),`);\r\n        newline();\r\n        push(`_cache[${node.index}]`);\r\n        deindent();\r\n    }\r\n    push(`)`);\r\n}\n\nfunction walkIdentifiers(root, onIdentifier, includeAll = false, parentStack = [], knownIds = Object.create(null)) {\r\n    {\r\n        return;\r\n    }\r\n}\r\nfunction isReferencedIdentifier(id, parent, parentStack) {\r\n    {\r\n        return false;\r\n    }\r\n}\r\nfunction isInDestructureAssignment(parent, parentStack) {\r\n    if (parent &&\r\n        (parent.type === 'ObjectProperty' || parent.type === 'ArrayPattern')) {\r\n        let i = parentStack.length;\r\n        while (i--) {\r\n            const p = parentStack[i];\r\n            if (p.type === 'AssignmentExpression') {\r\n                return true;\r\n            }\r\n            else if (p.type !== 'ObjectProperty' && !p.type.endsWith('Pattern')) {\r\n                break;\r\n            }\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction walkFunctionParams(node, onIdent) {\r\n    for (const p of node.params) {\r\n        for (const id of extractIdentifiers(p)) {\r\n            onIdent(id);\r\n        }\r\n    }\r\n}\r\nfunction walkBlockDeclarations(block, onIdent) {\r\n    for (const stmt of block.body) {\r\n        if (stmt.type === 'VariableDeclaration') {\r\n            if (stmt.declare)\r\n                continue;\r\n            for (const decl of stmt.declarations) {\r\n                for (const id of extractIdentifiers(decl.id)) {\r\n                    onIdent(id);\r\n                }\r\n            }\r\n        }\r\n        else if (stmt.type === 'FunctionDeclaration' ||\r\n            stmt.type === 'ClassDeclaration') {\r\n            if (stmt.declare || !stmt.id)\r\n                continue;\r\n            onIdent(stmt.id);\r\n        }\r\n    }\r\n}\r\nfunction extractIdentifiers(param, nodes = []) {\r\n    switch (param.type) {\r\n        case 'Identifier':\r\n            nodes.push(param);\r\n            break;\r\n        case 'MemberExpression':\r\n            let object = param;\r\n            while (object.type === 'MemberExpression') {\r\n                object = object.object;\r\n            }\r\n            nodes.push(object);\r\n            break;\r\n        case 'ObjectPattern':\r\n            for (const prop of param.properties) {\r\n                if (prop.type === 'RestElement') {\r\n                    extractIdentifiers(prop.argument, nodes);\r\n                }\r\n                else {\r\n                    extractIdentifiers(prop.value, nodes);\r\n                }\r\n            }\r\n            break;\r\n        case 'ArrayPattern':\r\n            param.elements.forEach(element => {\r\n                if (element)\r\n                    extractIdentifiers(element, nodes);\r\n            });\r\n            break;\r\n        case 'RestElement':\r\n            extractIdentifiers(param.argument, nodes);\r\n            break;\r\n        case 'AssignmentPattern':\r\n            extractIdentifiers(param.left, nodes);\r\n            break;\r\n    }\r\n    return nodes;\r\n}\r\nconst isFunctionType = (node) => {\r\n    return /Function(?:Expression|Declaration)$|Method$/.test(node.type);\r\n};\r\nconst isStaticProperty = (node) => node &&\r\n    (node.type === 'ObjectProperty' || node.type === 'ObjectMethod') &&\r\n    !node.computed;\r\nconst isStaticPropertyKey = (node, parent) => isStaticProperty(parent) && parent.key === node;\n\n// these keywords should not appear inside expressions, but operators like\r\n// typeof, instanceof and in are allowed\r\nconst prohibitedKeywordRE = new RegExp('\\\\b' +\r\n    ('do,if,for,let,new,try,var,case,else,with,await,break,catch,class,const,' +\r\n        'super,throw,while,yield,delete,export,import,return,switch,default,' +\r\n        'extends,finally,continue,debugger,function,arguments,typeof,void')\r\n        .split(',')\r\n        .join('\\\\b|\\\\b') +\r\n    '\\\\b');\r\n// strip strings in expressions\r\nconst stripStringRE = /'(?:[^'\\\\]|\\\\.)*'|\"(?:[^\"\\\\]|\\\\.)*\"|`(?:[^`\\\\]|\\\\.)*\\$\\{|\\}(?:[^`\\\\]|\\\\.)*`|`(?:[^`\\\\]|\\\\.)*`/g;\r\n/**\r\n * Validate a non-prefixed expression.\r\n * This is only called when using the in-browser runtime compiler since it\r\n * doesn't prefix expressions.\r\n */\r\nfunction validateBrowserExpression(node, context, asParams = false, asRawStatements = false) {\r\n    const exp = node.content;\r\n    // empty expressions are validated per-directive since some directives\r\n    // do allow empty expressions.\r\n    if (!exp.trim()) {\r\n        return;\r\n    }\r\n    try {\r\n        new Function(asRawStatements\r\n            ? ` ${exp} `\r\n            : `return ${asParams ? `(${exp}) => {}` : `(${exp})`}`);\r\n    }\r\n    catch (e) {\r\n        let message = e.message;\r\n        const keywordMatch = exp\r\n            .replace(stripStringRE, '')\r\n            .match(prohibitedKeywordRE);\r\n        if (keywordMatch) {\r\n            message = `avoid using JavaScript keyword as property name: \"${keywordMatch[0]}\"`;\r\n        }\r\n        context.onError(createCompilerError(44 /* X_INVALID_EXPRESSION */, node.loc, undefined, message));\r\n    }\r\n}\n\nconst transformExpression = (node, context) => {\r\n    if (node.type === 5 /* INTERPOLATION */) {\r\n        node.content = processExpression(node.content, context);\r\n    }\r\n    else if (node.type === 1 /* ELEMENT */) {\r\n        // handle directives on element\r\n        for (let i = 0; i < node.props.length; i++) {\r\n            const dir = node.props[i];\r\n            // do not process for v-on & v-for since they are special handled\r\n            if (dir.type === 7 /* DIRECTIVE */ && dir.name !== 'for') {\r\n                const exp = dir.exp;\r\n                const arg = dir.arg;\r\n                // do not process exp if this is v-on:arg - we need special handling\r\n                // for wrapping inline statements.\r\n                if (exp &&\r\n                    exp.type === 4 /* SIMPLE_EXPRESSION */ &&\r\n                    !(dir.name === 'on' && arg)) {\r\n                    dir.exp = processExpression(exp, context, \r\n                    // slot args must be processed as function params\r\n                    dir.name === 'slot');\r\n                }\r\n                if (arg && arg.type === 4 /* SIMPLE_EXPRESSION */ && !arg.isStatic) {\r\n                    dir.arg = processExpression(arg, context);\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n// Important: since this function uses Node.js only dependencies, it should\r\n// always be used with a leading !true check so that it can be\r\n// tree-shaken from the browser build.\r\nfunction processExpression(node, context, \r\n// some expressions like v-slot props & v-for aliases should be parsed as\r\n// function params\r\nasParams = false, \r\n// v-on handler values may contain multiple statements\r\nasRawStatements = false, localVars = Object.create(context.identifiers)) {\r\n    {\r\n        if ((process.env.NODE_ENV !== 'production')) {\r\n            // simple in-browser validation (same logic in 2.x)\r\n            validateBrowserExpression(node, context, asParams, asRawStatements);\r\n        }\r\n        return node;\r\n    }\r\n}\n\nconst transformIf = createStructuralDirectiveTransform(/^(if|else|else-if)$/, (node, dir, context) => {\r\n    return processIf(node, dir, context, (ifNode, branch, isRoot) => {\r\n        // #1587: We need to dynamically increment the key based on the current\r\n        // node's sibling nodes, since chained v-if/else branches are\r\n        // rendered at the same depth\r\n        const siblings = context.parent.children;\r\n        let i = siblings.indexOf(ifNode);\r\n        let key = 0;\r\n        while (i-- >= 0) {\r\n            const sibling = siblings[i];\r\n            if (sibling && sibling.type === 9 /* IF */) {\r\n                key += sibling.branches.length;\r\n            }\r\n        }\r\n        // Exit callback. Complete the codegenNode when all children have been\r\n        // transformed.\r\n        return () => {\r\n            if (isRoot) {\r\n                ifNode.codegenNode = createCodegenNodeForBranch(branch, key, context);\r\n            }\r\n            else {\r\n                // attach this branch's codegen node to the v-if root.\r\n                const parentCondition = getParentCondition(ifNode.codegenNode);\r\n                parentCondition.alternate = createCodegenNodeForBranch(branch, key + ifNode.branches.length - 1, context);\r\n            }\r\n        };\r\n    });\r\n});\r\n// target-agnostic transform used for both Client and SSR\r\nfunction processIf(node, dir, context, processCodegen) {\r\n    if (dir.name !== 'else' &&\r\n        (!dir.exp || !dir.exp.content.trim())) {\r\n        const loc = dir.exp ? dir.exp.loc : node.loc;\r\n        context.onError(createCompilerError(28 /* X_V_IF_NO_EXPRESSION */, dir.loc));\r\n        dir.exp = createSimpleExpression(`true`, false, loc);\r\n    }\r\n    if ((process.env.NODE_ENV !== 'production') && true && dir.exp) {\r\n        validateBrowserExpression(dir.exp, context);\r\n    }\r\n    if (dir.name === 'if') {\r\n        const branch = createIfBranch(node, dir);\r\n        const ifNode = {\r\n            type: 9 /* IF */,\r\n            loc: node.loc,\r\n            branches: [branch]\r\n        };\r\n        context.replaceNode(ifNode);\r\n        if (processCodegen) {\r\n            return processCodegen(ifNode, branch, true);\r\n        }\r\n    }\r\n    else {\r\n        // locate the adjacent v-if\r\n        const siblings = context.parent.children;\r\n        const comments = [];\r\n        let i = siblings.indexOf(node);\r\n        while (i-- >= -1) {\r\n            const sibling = siblings[i];\r\n            if ((process.env.NODE_ENV !== 'production') && sibling && sibling.type === 3 /* COMMENT */) {\r\n                context.removeNode(sibling);\r\n                comments.unshift(sibling);\r\n                continue;\r\n            }\r\n            if (sibling &&\r\n                sibling.type === 2 /* TEXT */ &&\r\n                !sibling.content.trim().length) {\r\n                context.removeNode(sibling);\r\n                continue;\r\n            }\r\n            if (sibling && sibling.type === 9 /* IF */) {\r\n                // Check if v-else was followed by v-else-if\r\n                if (dir.name === 'else-if' &&\r\n                    sibling.branches[sibling.branches.length - 1].condition === undefined) {\r\n                    context.onError(createCompilerError(30 /* X_V_ELSE_NO_ADJACENT_IF */, node.loc));\r\n                }\r\n                // move the node to the if node's branches\r\n                context.removeNode();\r\n                const branch = createIfBranch(node, dir);\r\n                if ((process.env.NODE_ENV !== 'production') &&\r\n                    comments.length &&\r\n                    // #3619 ignore comments if the v-if is direct child of <transition>\r\n                    !(context.parent &&\r\n                        context.parent.type === 1 /* ELEMENT */ &&\r\n                        isBuiltInType(context.parent.tag, 'transition'))) {\r\n                    branch.children = [...comments, ...branch.children];\r\n                }\r\n                // check if user is forcing same key on different branches\r\n                if ((process.env.NODE_ENV !== 'production') || !true) {\r\n                    const key = branch.userKey;\r\n                    if (key) {\r\n                        sibling.branches.forEach(({ userKey }) => {\r\n                            if (isSameKey(userKey, key)) {\r\n                                context.onError(createCompilerError(29 /* X_V_IF_SAME_KEY */, branch.userKey.loc));\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n                sibling.branches.push(branch);\r\n                const onExit = processCodegen && processCodegen(sibling, branch, false);\r\n                // since the branch was removed, it will not be traversed.\r\n                // make sure to traverse here.\r\n                traverseNode(branch, context);\r\n                // call on exit\r\n                if (onExit)\r\n                    onExit();\r\n                // make sure to reset currentNode after traversal to indicate this\r\n                // node has been removed.\r\n                context.currentNode = null;\r\n            }\r\n            else {\r\n                context.onError(createCompilerError(30 /* X_V_ELSE_NO_ADJACENT_IF */, node.loc));\r\n            }\r\n            break;\r\n        }\r\n    }\r\n}\r\nfunction createIfBranch(node, dir) {\r\n    const isTemplateIf = node.tagType === 3 /* TEMPLATE */;\r\n    return {\r\n        type: 10 /* IF_BRANCH */,\r\n        loc: node.loc,\r\n        condition: dir.name === 'else' ? undefined : dir.exp,\r\n        children: isTemplateIf && !findDir(node, 'for') ? node.children : [node],\r\n        userKey: findProp(node, `key`),\r\n        isTemplateIf\r\n    };\r\n}\r\nfunction createCodegenNodeForBranch(branch, keyIndex, context) {\r\n    if (branch.condition) {\r\n        return createConditionalExpression(branch.condition, createChildrenCodegenNode(branch, keyIndex, context), \r\n        // make sure to pass in asBlock: true so that the comment node call\r\n        // closes the current block.\r\n        createCallExpression(context.helper(CREATE_COMMENT), [\r\n            (process.env.NODE_ENV !== 'production') ? '\"v-if\"' : '\"\"',\r\n            'true'\r\n        ]));\r\n    }\r\n    else {\r\n        return createChildrenCodegenNode(branch, keyIndex, context);\r\n    }\r\n}\r\nfunction createChildrenCodegenNode(branch, keyIndex, context) {\r\n    const { helper } = context;\r\n    const keyProperty = createObjectProperty(`key`, createSimpleExpression(`${keyIndex}`, false, locStub, 2 /* CAN_HOIST */));\r\n    const { children } = branch;\r\n    const firstChild = children[0];\r\n    const needFragmentWrapper = children.length !== 1 || firstChild.type !== 1 /* ELEMENT */;\r\n    if (needFragmentWrapper) {\r\n        if (children.length === 1 && firstChild.type === 11 /* FOR */) {\r\n            // optimize away nested fragments when child is a ForNode\r\n            const vnodeCall = firstChild.codegenNode;\r\n            injectProp(vnodeCall, keyProperty, context);\r\n            return vnodeCall;\r\n        }\r\n        else {\r\n            let patchFlag = 64 /* STABLE_FRAGMENT */;\r\n            let patchFlagText = PatchFlagNames[64 /* STABLE_FRAGMENT */];\r\n            // check if the fragment actually contains a single valid child with\r\n            // the rest being comments\r\n            if ((process.env.NODE_ENV !== 'production') &&\r\n                !branch.isTemplateIf &&\r\n                children.filter(c => c.type !== 3 /* COMMENT */).length === 1) {\r\n                patchFlag |= 2048 /* DEV_ROOT_FRAGMENT */;\r\n                patchFlagText += `, ${PatchFlagNames[2048 /* DEV_ROOT_FRAGMENT */]}`;\r\n            }\r\n            return createVNodeCall(context, helper(FRAGMENT), createObjectExpression([keyProperty]), children, patchFlag + ((process.env.NODE_ENV !== 'production') ? ` /* ${patchFlagText} */` : ``), undefined, undefined, true, false, false /* isComponent */, branch.loc);\r\n        }\r\n    }\r\n    else {\r\n        const ret = firstChild.codegenNode;\r\n        const vnodeCall = getMemoedVNodeCall(ret);\r\n        // Change createVNode to createBlock.\r\n        if (vnodeCall.type === 13 /* VNODE_CALL */) {\r\n            makeBlock(vnodeCall, context);\r\n        }\r\n        // inject branch key\r\n        injectProp(vnodeCall, keyProperty, context);\r\n        return ret;\r\n    }\r\n}\r\nfunction isSameKey(a, b) {\r\n    if (!a || a.type !== b.type) {\r\n        return false;\r\n    }\r\n    if (a.type === 6 /* ATTRIBUTE */) {\r\n        if (a.value.content !== b.value.content) {\r\n            return false;\r\n        }\r\n    }\r\n    else {\r\n        // directive\r\n        const exp = a.exp;\r\n        const branchExp = b.exp;\r\n        if (exp.type !== branchExp.type) {\r\n            return false;\r\n        }\r\n        if (exp.type !== 4 /* SIMPLE_EXPRESSION */ ||\r\n            exp.isStatic !== branchExp.isStatic ||\r\n            exp.content !== branchExp.content) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\nfunction getParentCondition(node) {\r\n    while (true) {\r\n        if (node.type === 19 /* JS_CONDITIONAL_EXPRESSION */) {\r\n            if (node.alternate.type === 19 /* JS_CONDITIONAL_EXPRESSION */) {\r\n                node = node.alternate;\r\n            }\r\n            else {\r\n                return node;\r\n            }\r\n        }\r\n        else if (node.type === 20 /* JS_CACHE_EXPRESSION */) {\r\n            node = node.value;\r\n        }\r\n    }\r\n}\n\nconst transformFor = createStructuralDirectiveTransform('for', (node, dir, context) => {\r\n    const { helper, removeHelper } = context;\r\n    return processFor(node, dir, context, forNode => {\r\n        // create the loop render function expression now, and add the\r\n        // iterator on exit after all children have been traversed\r\n        const renderExp = createCallExpression(helper(RENDER_LIST), [\r\n            forNode.source\r\n        ]);\r\n        const isTemplate = isTemplateNode(node);\r\n        const memo = findDir(node, 'memo');\r\n        const keyProp = findProp(node, `key`);\r\n        const keyExp = keyProp &&\r\n            (keyProp.type === 6 /* ATTRIBUTE */\r\n                ? createSimpleExpression(keyProp.value.content, true)\r\n                : keyProp.exp);\r\n        const keyProperty = keyProp ? createObjectProperty(`key`, keyExp) : null;\r\n        const isStableFragment = forNode.source.type === 4 /* SIMPLE_EXPRESSION */ &&\r\n            forNode.source.constType > 0 /* NOT_CONSTANT */;\r\n        const fragmentFlag = isStableFragment\r\n            ? 64 /* STABLE_FRAGMENT */\r\n            : keyProp\r\n                ? 128 /* KEYED_FRAGMENT */\r\n                : 256 /* UNKEYED_FRAGMENT */;\r\n        forNode.codegenNode = createVNodeCall(context, helper(FRAGMENT), undefined, renderExp, fragmentFlag +\r\n            ((process.env.NODE_ENV !== 'production') ? ` /* ${PatchFlagNames[fragmentFlag]} */` : ``), undefined, undefined, true /* isBlock */, !isStableFragment /* disableTracking */, false /* isComponent */, node.loc);\r\n        return () => {\r\n            // finish the codegen now that all children have been traversed\r\n            let childBlock;\r\n            const { children } = forNode;\r\n            // check <template v-for> key placement\r\n            if (((process.env.NODE_ENV !== 'production') || !true) && isTemplate) {\r\n                node.children.some(c => {\r\n                    if (c.type === 1 /* ELEMENT */) {\r\n                        const key = findProp(c, 'key');\r\n                        if (key) {\r\n                            context.onError(createCompilerError(33 /* X_V_FOR_TEMPLATE_KEY_PLACEMENT */, key.loc));\r\n                            return true;\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n            const needFragmentWrapper = children.length !== 1 || children[0].type !== 1 /* ELEMENT */;\r\n            const slotOutlet = isSlotOutlet(node)\r\n                ? node\r\n                : isTemplate &&\r\n                    node.children.length === 1 &&\r\n                    isSlotOutlet(node.children[0])\r\n                    ? node.children[0] // api-extractor somehow fails to infer this\r\n                    : null;\r\n            if (slotOutlet) {\r\n                // <slot v-for=\"...\"> or <template v-for=\"...\"><slot/></template>\r\n                childBlock = slotOutlet.codegenNode;\r\n                if (isTemplate && keyProperty) {\r\n                    // <template v-for=\"...\" :key=\"...\"><slot/></template>\r\n                    // we need to inject the key to the renderSlot() call.\r\n                    // the props for renderSlot is passed as the 3rd argument.\r\n                    injectProp(childBlock, keyProperty, context);\r\n                }\r\n            }\r\n            else if (needFragmentWrapper) {\r\n                // <template v-for=\"...\"> with text or multi-elements\r\n                // should generate a fragment block for each loop\r\n                childBlock = createVNodeCall(context, helper(FRAGMENT), keyProperty ? createObjectExpression([keyProperty]) : undefined, node.children, 64 /* STABLE_FRAGMENT */ +\r\n                    ((process.env.NODE_ENV !== 'production')\r\n                        ? ` /* ${PatchFlagNames[64 /* STABLE_FRAGMENT */]} */`\r\n                        : ``), undefined, undefined, true, undefined, false /* isComponent */);\r\n            }\r\n            else {\r\n                // Normal element v-for. Directly use the child's codegenNode\r\n                // but mark it as a block.\r\n                childBlock = children[0]\r\n                    .codegenNode;\r\n                if (isTemplate && keyProperty) {\r\n                    injectProp(childBlock, keyProperty, context);\r\n                }\r\n                if (childBlock.isBlock !== !isStableFragment) {\r\n                    if (childBlock.isBlock) {\r\n                        // switch from block to vnode\r\n                        removeHelper(OPEN_BLOCK);\r\n                        removeHelper(getVNodeBlockHelper(context.inSSR, childBlock.isComponent));\r\n                    }\r\n                    else {\r\n                        // switch from vnode to block\r\n                        removeHelper(getVNodeHelper(context.inSSR, childBlock.isComponent));\r\n                    }\r\n                }\r\n                childBlock.isBlock = !isStableFragment;\r\n                if (childBlock.isBlock) {\r\n                    helper(OPEN_BLOCK);\r\n                    helper(getVNodeBlockHelper(context.inSSR, childBlock.isComponent));\r\n                }\r\n                else {\r\n                    helper(getVNodeHelper(context.inSSR, childBlock.isComponent));\r\n                }\r\n            }\r\n            if (memo) {\r\n                const loop = createFunctionExpression(createForLoopParams(forNode.parseResult, [\r\n                    createSimpleExpression(`_cached`)\r\n                ]));\r\n                loop.body = createBlockStatement([\r\n                    createCompoundExpression([`const _memo = (`, memo.exp, `)`]),\r\n                    createCompoundExpression([\r\n                        `if (_cached`,\r\n                        ...(keyExp ? [` && _cached.key === `, keyExp] : []),\r\n                        ` && ${context.helperString(IS_MEMO_SAME)}(_cached, _memo)) return _cached`\r\n                    ]),\r\n                    createCompoundExpression([`const _item = `, childBlock]),\r\n                    createSimpleExpression(`_item.memo = _memo`),\r\n                    createSimpleExpression(`return _item`)\r\n                ]);\r\n                renderExp.arguments.push(loop, createSimpleExpression(`_cache`), createSimpleExpression(String(context.cached++)));\r\n            }\r\n            else {\r\n                renderExp.arguments.push(createFunctionExpression(createForLoopParams(forNode.parseResult), childBlock, true /* force newline */));\r\n            }\r\n        };\r\n    });\r\n});\r\n// target-agnostic transform used for both Client and SSR\r\nfunction processFor(node, dir, context, processCodegen) {\r\n    if (!dir.exp) {\r\n        context.onError(createCompilerError(31 /* X_V_FOR_NO_EXPRESSION */, dir.loc));\r\n        return;\r\n    }\r\n    const parseResult = parseForExpression(\r\n    // can only be simple expression because vFor transform is applied\r\n    // before expression transform.\r\n    dir.exp, context);\r\n    if (!parseResult) {\r\n        context.onError(createCompilerError(32 /* X_V_FOR_MALFORMED_EXPRESSION */, dir.loc));\r\n        return;\r\n    }\r\n    const { addIdentifiers, removeIdentifiers, scopes } = context;\r\n    const { source, value, key, index } = parseResult;\r\n    const forNode = {\r\n        type: 11 /* FOR */,\r\n        loc: dir.loc,\r\n        source,\r\n        valueAlias: value,\r\n        keyAlias: key,\r\n        objectIndexAlias: index,\r\n        parseResult,\r\n        children: isTemplateNode(node) ? node.children : [node]\r\n    };\r\n    context.replaceNode(forNode);\r\n    // bookkeeping\r\n    scopes.vFor++;\r\n    const onExit = processCodegen && processCodegen(forNode);\r\n    return () => {\r\n        scopes.vFor--;\r\n        if (onExit)\r\n            onExit();\r\n    };\r\n}\r\nconst forAliasRE = /([\\s\\S]*?)\\s+(?:in|of)\\s+([\\s\\S]*)/;\r\n// This regex doesn't cover the case if key or index aliases have destructuring,\r\n// but those do not make sense in the first place, so this works in practice.\r\nconst forIteratorRE = /,([^,\\}\\]]*)(?:,([^,\\}\\]]*))?$/;\r\nconst stripParensRE = /^\\(|\\)$/g;\r\nfunction parseForExpression(input, context) {\r\n    const loc = input.loc;\r\n    const exp = input.content;\r\n    const inMatch = exp.match(forAliasRE);\r\n    if (!inMatch)\r\n        return;\r\n    const [, LHS, RHS] = inMatch;\r\n    const result = {\r\n        source: createAliasExpression(loc, RHS.trim(), exp.indexOf(RHS, LHS.length)),\r\n        value: undefined,\r\n        key: undefined,\r\n        index: undefined\r\n    };\r\n    if ((process.env.NODE_ENV !== 'production') && true) {\r\n        validateBrowserExpression(result.source, context);\r\n    }\r\n    let valueContent = LHS.trim().replace(stripParensRE, '').trim();\r\n    const trimmedOffset = LHS.indexOf(valueContent);\r\n    const iteratorMatch = valueContent.match(forIteratorRE);\r\n    if (iteratorMatch) {\r\n        valueContent = valueContent.replace(forIteratorRE, '').trim();\r\n        const keyContent = iteratorMatch[1].trim();\r\n        let keyOffset;\r\n        if (keyContent) {\r\n            keyOffset = exp.indexOf(keyContent, trimmedOffset + valueContent.length);\r\n            result.key = createAliasExpression(loc, keyContent, keyOffset);\r\n            if ((process.env.NODE_ENV !== 'production') && true) {\r\n                validateBrowserExpression(result.key, context, true);\r\n            }\r\n        }\r\n        if (iteratorMatch[2]) {\r\n            const indexContent = iteratorMatch[2].trim();\r\n            if (indexContent) {\r\n                result.index = createAliasExpression(loc, indexContent, exp.indexOf(indexContent, result.key\r\n                    ? keyOffset + keyContent.length\r\n                    : trimmedOffset + valueContent.length));\r\n                if ((process.env.NODE_ENV !== 'production') && true) {\r\n                    validateBrowserExpression(result.index, context, true);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (valueContent) {\r\n        result.value = createAliasExpression(loc, valueContent, trimmedOffset);\r\n        if ((process.env.NODE_ENV !== 'production') && true) {\r\n            validateBrowserExpression(result.value, context, true);\r\n        }\r\n    }\r\n    return result;\r\n}\r\nfunction createAliasExpression(range, content, offset) {\r\n    return createSimpleExpression(content, false, getInnerRange(range, offset, content.length));\r\n}\r\nfunction createForLoopParams({ value, key, index }, memoArgs = []) {\r\n    return createParamsList([value, key, index, ...memoArgs]);\r\n}\r\nfunction createParamsList(args) {\r\n    let i = args.length;\r\n    while (i--) {\r\n        if (args[i])\r\n            break;\r\n    }\r\n    return args\r\n        .slice(0, i + 1)\r\n        .map((arg, i) => arg || createSimpleExpression(`_`.repeat(i + 1), false));\r\n}\n\nconst defaultFallback = createSimpleExpression(`undefined`, false);\r\n// A NodeTransform that:\r\n// 1. Tracks scope identifiers for scoped slots so that they don't get prefixed\r\n//    by transformExpression. This is only applied in non-browser builds with\r\n//    { prefixIdentifiers: true }.\r\n// 2. Track v-slot depths so that we know a slot is inside another slot.\r\n//    Note the exit callback is executed before buildSlots() on the same node,\r\n//    so only nested slots see positive numbers.\r\nconst trackSlotScopes = (node, context) => {\r\n    if (node.type === 1 /* ELEMENT */ &&\r\n        (node.tagType === 1 /* COMPONENT */ ||\r\n            node.tagType === 3 /* TEMPLATE */)) {\r\n        // We are only checking non-empty v-slot here\r\n        // since we only care about slots that introduce scope variables.\r\n        const vSlot = findDir(node, 'slot');\r\n        if (vSlot) {\r\n            vSlot.exp;\r\n            context.scopes.vSlot++;\r\n            return () => {\r\n                context.scopes.vSlot--;\r\n            };\r\n        }\r\n    }\r\n};\r\n// A NodeTransform that tracks scope identifiers for scoped slots with v-for.\r\n// This transform is only applied in non-browser builds with { prefixIdentifiers: true }\r\nconst trackVForSlotScopes = (node, context) => {\r\n    let vFor;\r\n    if (isTemplateNode(node) &&\r\n        node.props.some(isVSlot) &&\r\n        (vFor = findDir(node, 'for'))) {\r\n        const result = (vFor.parseResult = parseForExpression(vFor.exp, context));\r\n        if (result) {\r\n            const { value, key, index } = result;\r\n            const { addIdentifiers, removeIdentifiers } = context;\r\n            value && addIdentifiers(value);\r\n            key && addIdentifiers(key);\r\n            index && addIdentifiers(index);\r\n            return () => {\r\n                value && removeIdentifiers(value);\r\n                key && removeIdentifiers(key);\r\n                index && removeIdentifiers(index);\r\n            };\r\n        }\r\n    }\r\n};\r\nconst buildClientSlotFn = (props, children, loc) => createFunctionExpression(props, children, false /* newline */, true /* isSlot */, children.length ? children[0].loc : loc);\r\n// Instead of being a DirectiveTransform, v-slot processing is called during\r\n// transformElement to build the slots object for a component.\r\nfunction buildSlots(node, context, buildSlotFn = buildClientSlotFn) {\r\n    context.helper(WITH_CTX);\r\n    const { children, loc } = node;\r\n    const slotsProperties = [];\r\n    const dynamicSlots = [];\r\n    // If the slot is inside a v-for or another v-slot, force it to be dynamic\r\n    // since it likely uses a scope variable.\r\n    let hasDynamicSlots = context.scopes.vSlot > 0 || context.scopes.vFor > 0;\r\n    // 1. Check for slot with slotProps on component itself.\r\n    //    <Comp v-slot=\"{ prop }\"/>\r\n    const onComponentSlot = findDir(node, 'slot', true);\r\n    if (onComponentSlot) {\r\n        const { arg, exp } = onComponentSlot;\r\n        if (arg && !isStaticExp(arg)) {\r\n            hasDynamicSlots = true;\r\n        }\r\n        slotsProperties.push(createObjectProperty(arg || createSimpleExpression('default', true), buildSlotFn(exp, children, loc)));\r\n    }\r\n    // 2. Iterate through children and check for template slots\r\n    //    <template v-slot:foo=\"{ prop }\">\r\n    let hasTemplateSlots = false;\r\n    let hasNamedDefaultSlot = false;\r\n    const implicitDefaultChildren = [];\r\n    const seenSlotNames = new Set();\r\n    for (let i = 0; i < children.length; i++) {\r\n        const slotElement = children[i];\r\n        let slotDir;\r\n        if (!isTemplateNode(slotElement) ||\r\n            !(slotDir = findDir(slotElement, 'slot', true))) {\r\n            // not a <template v-slot>, skip.\r\n            if (slotElement.type !== 3 /* COMMENT */) {\r\n                implicitDefaultChildren.push(slotElement);\r\n            }\r\n            continue;\r\n        }\r\n        if (onComponentSlot) {\r\n            // already has on-component slot - this is incorrect usage.\r\n            context.onError(createCompilerError(37 /* X_V_SLOT_MIXED_SLOT_USAGE */, slotDir.loc));\r\n            break;\r\n        }\r\n        hasTemplateSlots = true;\r\n        const { children: slotChildren, loc: slotLoc } = slotElement;\r\n        const { arg: slotName = createSimpleExpression(`default`, true), exp: slotProps, loc: dirLoc } = slotDir;\r\n        // check if name is dynamic.\r\n        let staticSlotName;\r\n        if (isStaticExp(slotName)) {\r\n            staticSlotName = slotName ? slotName.content : `default`;\r\n        }\r\n        else {\r\n            hasDynamicSlots = true;\r\n        }\r\n        const slotFunction = buildSlotFn(slotProps, slotChildren, slotLoc);\r\n        // check if this slot is conditional (v-if/v-for)\r\n        let vIf;\r\n        let vElse;\r\n        let vFor;\r\n        if ((vIf = findDir(slotElement, 'if'))) {\r\n            hasDynamicSlots = true;\r\n            dynamicSlots.push(createConditionalExpression(vIf.exp, buildDynamicSlot(slotName, slotFunction), defaultFallback));\r\n        }\r\n        else if ((vElse = findDir(slotElement, /^else(-if)?$/, true /* allowEmpty */))) {\r\n            // find adjacent v-if\r\n            let j = i;\r\n            let prev;\r\n            while (j--) {\r\n                prev = children[j];\r\n                if (prev.type !== 3 /* COMMENT */) {\r\n                    break;\r\n                }\r\n            }\r\n            if (prev && isTemplateNode(prev) && findDir(prev, 'if')) {\r\n                // remove node\r\n                children.splice(i, 1);\r\n                i--;\r\n                // attach this slot to previous conditional\r\n                let conditional = dynamicSlots[dynamicSlots.length - 1];\r\n                while (conditional.alternate.type === 19 /* JS_CONDITIONAL_EXPRESSION */) {\r\n                    conditional = conditional.alternate;\r\n                }\r\n                conditional.alternate = vElse.exp\r\n                    ? createConditionalExpression(vElse.exp, buildDynamicSlot(slotName, slotFunction), defaultFallback)\r\n                    : buildDynamicSlot(slotName, slotFunction);\r\n            }\r\n            else {\r\n                context.onError(createCompilerError(30 /* X_V_ELSE_NO_ADJACENT_IF */, vElse.loc));\r\n            }\r\n        }\r\n        else if ((vFor = findDir(slotElement, 'for'))) {\r\n            hasDynamicSlots = true;\r\n            const parseResult = vFor.parseResult ||\r\n                parseForExpression(vFor.exp, context);\r\n            if (parseResult) {\r\n                // Render the dynamic slots as an array and add it to the createSlot()\r\n                // args. The runtime knows how to handle it appropriately.\r\n                dynamicSlots.push(createCallExpression(context.helper(RENDER_LIST), [\r\n                    parseResult.source,\r\n                    createFunctionExpression(createForLoopParams(parseResult), buildDynamicSlot(slotName, slotFunction), true /* force newline */)\r\n                ]));\r\n            }\r\n            else {\r\n                context.onError(createCompilerError(32 /* X_V_FOR_MALFORMED_EXPRESSION */, vFor.loc));\r\n            }\r\n        }\r\n        else {\r\n            // check duplicate static names\r\n            if (staticSlotName) {\r\n                if (seenSlotNames.has(staticSlotName)) {\r\n                    context.onError(createCompilerError(38 /* X_V_SLOT_DUPLICATE_SLOT_NAMES */, dirLoc));\r\n                    continue;\r\n                }\r\n                seenSlotNames.add(staticSlotName);\r\n                if (staticSlotName === 'default') {\r\n                    hasNamedDefaultSlot = true;\r\n                }\r\n            }\r\n            slotsProperties.push(createObjectProperty(slotName, slotFunction));\r\n        }\r\n    }\r\n    if (!onComponentSlot) {\r\n        const buildDefaultSlotProperty = (props, children) => {\r\n            const fn = buildSlotFn(props, children, loc);\r\n            if (context.compatConfig) {\r\n                fn.isNonScopedSlot = true;\r\n            }\r\n            return createObjectProperty(`default`, fn);\r\n        };\r\n        if (!hasTemplateSlots) {\r\n            // implicit default slot (on component)\r\n            slotsProperties.push(buildDefaultSlotProperty(undefined, children));\r\n        }\r\n        else if (implicitDefaultChildren.length &&\r\n            // #3766\r\n            // with whitespace: 'preserve', whitespaces between slots will end up in\r\n            // implicitDefaultChildren. Ignore if all implicit children are whitespaces.\r\n            implicitDefaultChildren.some(node => isNonWhitespaceContent(node))) {\r\n            // implicit default slot (mixed with named slots)\r\n            if (hasNamedDefaultSlot) {\r\n                context.onError(createCompilerError(39 /* X_V_SLOT_EXTRANEOUS_DEFAULT_SLOT_CHILDREN */, implicitDefaultChildren[0].loc));\r\n            }\r\n            else {\r\n                slotsProperties.push(buildDefaultSlotProperty(undefined, implicitDefaultChildren));\r\n            }\r\n        }\r\n    }\r\n    const slotFlag = hasDynamicSlots\r\n        ? 2 /* DYNAMIC */\r\n        : hasForwardedSlots(node.children)\r\n            ? 3 /* FORWARDED */\r\n            : 1 /* STABLE */;\r\n    let slots = createObjectExpression(slotsProperties.concat(createObjectProperty(`_`, \r\n    // 2 = compiled but dynamic = can skip normalization, but must run diff\r\n    // 1 = compiled and static = can skip normalization AND diff as optimized\r\n    createSimpleExpression(slotFlag + ((process.env.NODE_ENV !== 'production') ? ` /* ${slotFlagsText[slotFlag]} */` : ``), false))), loc);\r\n    if (dynamicSlots.length) {\r\n        slots = createCallExpression(context.helper(CREATE_SLOTS), [\r\n            slots,\r\n            createArrayExpression(dynamicSlots)\r\n        ]);\r\n    }\r\n    return {\r\n        slots,\r\n        hasDynamicSlots\r\n    };\r\n}\r\nfunction buildDynamicSlot(name, fn) {\r\n    return createObjectExpression([\r\n        createObjectProperty(`name`, name),\r\n        createObjectProperty(`fn`, fn)\r\n    ]);\r\n}\r\nfunction hasForwardedSlots(children) {\r\n    for (let i = 0; i < children.length; i++) {\r\n        const child = children[i];\r\n        switch (child.type) {\r\n            case 1 /* ELEMENT */:\r\n                if (child.tagType === 2 /* SLOT */ ||\r\n                    hasForwardedSlots(child.children)) {\r\n                    return true;\r\n                }\r\n                break;\r\n            case 9 /* IF */:\r\n                if (hasForwardedSlots(child.branches))\r\n                    return true;\r\n                break;\r\n            case 10 /* IF_BRANCH */:\r\n            case 11 /* FOR */:\r\n                if (hasForwardedSlots(child.children))\r\n                    return true;\r\n                break;\r\n        }\r\n    }\r\n    return false;\r\n}\r\nfunction isNonWhitespaceContent(node) {\r\n    if (node.type !== 2 /* TEXT */ && node.type !== 12 /* TEXT_CALL */)\r\n        return true;\r\n    return node.type === 2 /* TEXT */\r\n        ? !!node.content.trim()\r\n        : isNonWhitespaceContent(node.content);\r\n}\n\n// some directive transforms (e.g. v-model) may return a symbol for runtime\r\n// import, which should be used instead of a resolveDirective call.\r\nconst directiveImportMap = new WeakMap();\r\n// generate a JavaScript AST for this element's codegen\r\nconst transformElement = (node, context) => {\r\n    // perform the work on exit, after all child expressions have been\r\n    // processed and merged.\r\n    return function postTransformElement() {\r\n        node = context.currentNode;\r\n        if (!(node.type === 1 /* ELEMENT */ &&\r\n            (node.tagType === 0 /* ELEMENT */ ||\r\n                node.tagType === 1 /* COMPONENT */))) {\r\n            return;\r\n        }\r\n        const { tag, props } = node;\r\n        const isComponent = node.tagType === 1 /* COMPONENT */;\r\n        // The goal of the transform is to create a codegenNode implementing the\r\n        // VNodeCall interface.\r\n        let vnodeTag = isComponent\r\n            ? resolveComponentType(node, context)\r\n            : `\"${tag}\"`;\r\n        const isDynamicComponent = isObject(vnodeTag) && vnodeTag.callee === RESOLVE_DYNAMIC_COMPONENT;\r\n        let vnodeProps;\r\n        let vnodeChildren;\r\n        let vnodePatchFlag;\r\n        let patchFlag = 0;\r\n        let vnodeDynamicProps;\r\n        let dynamicPropNames;\r\n        let vnodeDirectives;\r\n        let shouldUseBlock = \r\n        // dynamic component may resolve to plain elements\r\n        isDynamicComponent ||\r\n            vnodeTag === TELEPORT ||\r\n            vnodeTag === SUSPENSE ||\r\n            (!isComponent &&\r\n                // <svg> and <foreignObject> must be forced into blocks so that block\r\n                // updates inside get proper isSVG flag at runtime. (#639, #643)\r\n                // This is technically web-specific, but splitting the logic out of core\r\n                // leads to too much unnecessary complexity.\r\n                (tag === 'svg' || tag === 'foreignObject'));\r\n        // props\r\n        if (props.length > 0) {\r\n            const propsBuildResult = buildProps(node, context, undefined, isComponent, isDynamicComponent);\r\n            vnodeProps = propsBuildResult.props;\r\n            patchFlag = propsBuildResult.patchFlag;\r\n            dynamicPropNames = propsBuildResult.dynamicPropNames;\r\n            const directives = propsBuildResult.directives;\r\n            vnodeDirectives =\r\n                directives && directives.length\r\n                    ? createArrayExpression(directives.map(dir => buildDirectiveArgs(dir, context)))\r\n                    : undefined;\r\n            if (propsBuildResult.shouldUseBlock) {\r\n                shouldUseBlock = true;\r\n            }\r\n        }\r\n        // children\r\n        if (node.children.length > 0) {\r\n            if (vnodeTag === KEEP_ALIVE) {\r\n                // Although a built-in component, we compile KeepAlive with raw children\r\n                // instead of slot functions so that it can be used inside Transition\r\n                // or other Transition-wrapping HOCs.\r\n                // To ensure correct updates with block optimizations, we need to:\r\n                // 1. Force keep-alive into a block. This avoids its children being\r\n                //    collected by a parent block.\r\n                shouldUseBlock = true;\r\n                // 2. Force keep-alive to always be updated, since it uses raw children.\r\n                patchFlag |= 1024 /* DYNAMIC_SLOTS */;\r\n                if ((process.env.NODE_ENV !== 'production') && node.children.length > 1) {\r\n                    context.onError(createCompilerError(45 /* X_KEEP_ALIVE_INVALID_CHILDREN */, {\r\n                        start: node.children[0].loc.start,\r\n                        end: node.children[node.children.length - 1].loc.end,\r\n                        source: ''\r\n                    }));\r\n                }\r\n            }\r\n            const shouldBuildAsSlots = isComponent &&\r\n                // Teleport is not a real component and has dedicated runtime handling\r\n                vnodeTag !== TELEPORT &&\r\n                // explained above.\r\n                vnodeTag !== KEEP_ALIVE;\r\n            if (shouldBuildAsSlots) {\r\n                const { slots, hasDynamicSlots } = buildSlots(node, context);\r\n                vnodeChildren = slots;\r\n                if (hasDynamicSlots) {\r\n                    patchFlag |= 1024 /* DYNAMIC_SLOTS */;\r\n                }\r\n            }\r\n            else if (node.children.length === 1 && vnodeTag !== TELEPORT) {\r\n                const child = node.children[0];\r\n                const type = child.type;\r\n                // check for dynamic text children\r\n                const hasDynamicTextChild = type === 5 /* INTERPOLATION */ ||\r\n                    type === 8 /* COMPOUND_EXPRESSION */;\r\n                if (hasDynamicTextChild &&\r\n                    getConstantType(child, context) === 0 /* NOT_CONSTANT */) {\r\n                    patchFlag |= 1 /* TEXT */;\r\n                }\r\n                // pass directly if the only child is a text node\r\n                // (plain / interpolation / expression)\r\n                if (hasDynamicTextChild || type === 2 /* TEXT */) {\r\n                    vnodeChildren = child;\r\n                }\r\n                else {\r\n                    vnodeChildren = node.children;\r\n                }\r\n            }\r\n            else {\r\n                vnodeChildren = node.children;\r\n            }\r\n        }\r\n        // patchFlag & dynamicPropNames\r\n        if (patchFlag !== 0) {\r\n            if ((process.env.NODE_ENV !== 'production')) {\r\n                if (patchFlag < 0) {\r\n                    // special flags (negative and mutually exclusive)\r\n                    vnodePatchFlag = patchFlag + ` /* ${PatchFlagNames[patchFlag]} */`;\r\n                }\r\n                else {\r\n                    // bitwise flags\r\n                    const flagNames = Object.keys(PatchFlagNames)\r\n                        .map(Number)\r\n                        .filter(n => n > 0 && patchFlag & n)\r\n                        .map(n => PatchFlagNames[n])\r\n                        .join(`, `);\r\n                    vnodePatchFlag = patchFlag + ` /* ${flagNames} */`;\r\n                }\r\n            }\r\n            else {\r\n                vnodePatchFlag = String(patchFlag);\r\n            }\r\n            if (dynamicPropNames && dynamicPropNames.length) {\r\n                vnodeDynamicProps = stringifyDynamicPropNames(dynamicPropNames);\r\n            }\r\n        }\r\n        node.codegenNode = createVNodeCall(context, vnodeTag, vnodeProps, vnodeChildren, vnodePatchFlag, vnodeDynamicProps, vnodeDirectives, !!shouldUseBlock, false /* disableTracking */, isComponent, node.loc);\r\n    };\r\n};\r\nfunction resolveComponentType(node, context, ssr = false) {\r\n    let { tag } = node;\r\n    // 1. dynamic component\r\n    const isExplicitDynamic = isComponentTag(tag);\r\n    const isProp = findProp(node, 'is');\r\n    if (isProp) {\r\n        if (isExplicitDynamic ||\r\n            (isCompatEnabled(\"COMPILER_IS_ON_ELEMENT\" /* COMPILER_IS_ON_ELEMENT */, context))) {\r\n            const exp = isProp.type === 6 /* ATTRIBUTE */\r\n                ? isProp.value && createSimpleExpression(isProp.value.content, true)\r\n                : isProp.exp;\r\n            if (exp) {\r\n                return createCallExpression(context.helper(RESOLVE_DYNAMIC_COMPONENT), [\r\n                    exp\r\n                ]);\r\n            }\r\n        }\r\n        else if (isProp.type === 6 /* ATTRIBUTE */ &&\r\n            isProp.value.content.startsWith('vue:')) {\r\n            // <button is=\"vue:xxx\">\r\n            // if not <component>, only is value that starts with \"vue:\" will be\r\n            // treated as component by the parse phase and reach here, unless it's\r\n            // compat mode where all is values are considered components\r\n            tag = isProp.value.content.slice(4);\r\n        }\r\n    }\r\n    // 1.5 v-is (TODO: Deprecate)\r\n    const isDir = !isExplicitDynamic && findDir(node, 'is');\r\n    if (isDir && isDir.exp) {\r\n        return createCallExpression(context.helper(RESOLVE_DYNAMIC_COMPONENT), [\r\n            isDir.exp\r\n        ]);\r\n    }\r\n    // 2. built-in components (Teleport, Transition, KeepAlive, Suspense...)\r\n    const builtIn = isCoreComponent(tag) || context.isBuiltInComponent(tag);\r\n    if (builtIn) {\r\n        // built-ins are simply fallthroughs / have special handling during ssr\r\n        // so we don't need to import their runtime equivalents\r\n        if (!ssr)\r\n            context.helper(builtIn);\r\n        return builtIn;\r\n    }\r\n    // 5. user component (resolve)\r\n    context.helper(RESOLVE_COMPONENT);\r\n    context.components.add(tag);\r\n    return toValidAssetId(tag, `component`);\r\n}\r\nfunction buildProps(node, context, props = node.props, isComponent, isDynamicComponent, ssr = false) {\r\n    const { tag, loc: elementLoc, children } = node;\r\n    let properties = [];\r\n    const mergeArgs = [];\r\n    const runtimeDirectives = [];\r\n    const hasChildren = children.length > 0;\r\n    let shouldUseBlock = false;\r\n    // patchFlag analysis\r\n    let patchFlag = 0;\r\n    let hasRef = false;\r\n    let hasClassBinding = false;\r\n    let hasStyleBinding = false;\r\n    let hasHydrationEventBinding = false;\r\n    let hasDynamicKeys = false;\r\n    let hasVnodeHook = false;\r\n    const dynamicPropNames = [];\r\n    const analyzePatchFlag = ({ key, value }) => {\r\n        if (isStaticExp(key)) {\r\n            const name = key.content;\r\n            const isEventHandler = isOn(name);\r\n            if (isEventHandler &&\r\n                (!isComponent || isDynamicComponent) &&\r\n                // omit the flag for click handlers because hydration gives click\r\n                // dedicated fast path.\r\n                name.toLowerCase() !== 'onclick' &&\r\n                // omit v-model handlers\r\n                name !== 'onUpdate:modelValue' &&\r\n                // omit onVnodeXXX hooks\r\n                !isReservedProp(name)) {\r\n                hasHydrationEventBinding = true;\r\n            }\r\n            if (isEventHandler && isReservedProp(name)) {\r\n                hasVnodeHook = true;\r\n            }\r\n            if (value.type === 20 /* JS_CACHE_EXPRESSION */ ||\r\n                ((value.type === 4 /* SIMPLE_EXPRESSION */ ||\r\n                    value.type === 8 /* COMPOUND_EXPRESSION */) &&\r\n                    getConstantType(value, context) > 0)) {\r\n                // skip if the prop is a cached handler or has constant value\r\n                return;\r\n            }\r\n            if (name === 'ref') {\r\n                hasRef = true;\r\n            }\r\n            else if (name === 'class') {\r\n                hasClassBinding = true;\r\n            }\r\n            else if (name === 'style') {\r\n                hasStyleBinding = true;\r\n            }\r\n            else if (name !== 'key' && !dynamicPropNames.includes(name)) {\r\n                dynamicPropNames.push(name);\r\n            }\r\n            // treat the dynamic class and style binding of the component as dynamic props\r\n            if (isComponent &&\r\n                (name === 'class' || name === 'style') &&\r\n                !dynamicPropNames.includes(name)) {\r\n                dynamicPropNames.push(name);\r\n            }\r\n        }\r\n        else {\r\n            hasDynamicKeys = true;\r\n        }\r\n    };\r\n    for (let i = 0; i < props.length; i++) {\r\n        // static attribute\r\n        const prop = props[i];\r\n        if (prop.type === 6 /* ATTRIBUTE */) {\r\n            const { loc, name, value } = prop;\r\n            let isStatic = true;\r\n            if (name === 'ref') {\r\n                hasRef = true;\r\n                if (context.scopes.vFor > 0) {\r\n                    properties.push(createObjectProperty(createSimpleExpression('ref_for', true), createSimpleExpression('true')));\r\n                }\r\n            }\r\n            // skip is on <component>, or is=\"vue:xxx\"\r\n            if (name === 'is' &&\r\n                (isComponentTag(tag) ||\r\n                    (value && value.content.startsWith('vue:')) ||\r\n                    (isCompatEnabled(\"COMPILER_IS_ON_ELEMENT\" /* COMPILER_IS_ON_ELEMENT */, context)))) {\r\n                continue;\r\n            }\r\n            properties.push(createObjectProperty(createSimpleExpression(name, true, getInnerRange(loc, 0, name.length)), createSimpleExpression(value ? value.content : '', isStatic, value ? value.loc : loc)));\r\n        }\r\n        else {\r\n            // directives\r\n            const { name, arg, exp, loc } = prop;\r\n            const isVBind = name === 'bind';\r\n            const isVOn = name === 'on';\r\n            // skip v-slot - it is handled by its dedicated transform.\r\n            if (name === 'slot') {\r\n                if (!isComponent) {\r\n                    context.onError(createCompilerError(40 /* X_V_SLOT_MISPLACED */, loc));\r\n                }\r\n                continue;\r\n            }\r\n            // skip v-once/v-memo - they are handled by dedicated transforms.\r\n            if (name === 'once' || name === 'memo') {\r\n                continue;\r\n            }\r\n            // skip v-is and :is on <component>\r\n            if (name === 'is' ||\r\n                (isVBind &&\r\n                    isStaticArgOf(arg, 'is') &&\r\n                    (isComponentTag(tag) ||\r\n                        (isCompatEnabled(\"COMPILER_IS_ON_ELEMENT\" /* COMPILER_IS_ON_ELEMENT */, context))))) {\r\n                continue;\r\n            }\r\n            // skip v-on in SSR compilation\r\n            if (isVOn && ssr) {\r\n                continue;\r\n            }\r\n            if (\r\n            // #938: elements with dynamic keys should be forced into blocks\r\n            (isVBind && isStaticArgOf(arg, 'key')) ||\r\n                // inline before-update hooks need to force block so that it is invoked\r\n                // before children\r\n                (isVOn && hasChildren && isStaticArgOf(arg, 'vue:before-update'))) {\r\n                shouldUseBlock = true;\r\n            }\r\n            if (isVBind && isStaticArgOf(arg, 'ref') && context.scopes.vFor > 0) {\r\n                properties.push(createObjectProperty(createSimpleExpression('ref_for', true), createSimpleExpression('true')));\r\n            }\r\n            // special case for v-bind and v-on with no argument\r\n            if (!arg && (isVBind || isVOn)) {\r\n                hasDynamicKeys = true;\r\n                if (exp) {\r\n                    if (properties.length) {\r\n                        mergeArgs.push(createObjectExpression(dedupeProperties(properties), elementLoc));\r\n                        properties = [];\r\n                    }\r\n                    if (isVBind) {\r\n                        {\r\n                            // 2.x v-bind object order compat\r\n                            if ((process.env.NODE_ENV !== 'production')) {\r\n                                const hasOverridableKeys = mergeArgs.some(arg => {\r\n                                    if (arg.type === 15 /* JS_OBJECT_EXPRESSION */) {\r\n                                        return arg.properties.some(({ key }) => {\r\n                                            if (key.type !== 4 /* SIMPLE_EXPRESSION */ ||\r\n                                                !key.isStatic) {\r\n                                                return true;\r\n                                            }\r\n                                            return (key.content !== 'class' &&\r\n                                                key.content !== 'style' &&\r\n                                                !isOn(key.content));\r\n                                        });\r\n                                    }\r\n                                    else {\r\n                                        // dynamic expression\r\n                                        return true;\r\n                                    }\r\n                                });\r\n                                if (hasOverridableKeys) {\r\n                                    checkCompatEnabled(\"COMPILER_V_BIND_OBJECT_ORDER\" /* COMPILER_V_BIND_OBJECT_ORDER */, context, loc);\r\n                                }\r\n                            }\r\n                            if (isCompatEnabled(\"COMPILER_V_BIND_OBJECT_ORDER\" /* COMPILER_V_BIND_OBJECT_ORDER */, context)) {\r\n                                mergeArgs.unshift(exp);\r\n                                continue;\r\n                            }\r\n                        }\r\n                        mergeArgs.push(exp);\r\n                    }\r\n                    else {\r\n                        // v-on=\"obj\" -> toHandlers(obj)\r\n                        mergeArgs.push({\r\n                            type: 14 /* JS_CALL_EXPRESSION */,\r\n                            loc,\r\n                            callee: context.helper(TO_HANDLERS),\r\n                            arguments: [exp]\r\n                        });\r\n                    }\r\n                }\r\n                else {\r\n                    context.onError(createCompilerError(isVBind\r\n                        ? 34 /* X_V_BIND_NO_EXPRESSION */\r\n                        : 35 /* X_V_ON_NO_EXPRESSION */, loc));\r\n                }\r\n                continue;\r\n            }\r\n            const directiveTransform = context.directiveTransforms[name];\r\n            if (directiveTransform) {\r\n                // has built-in directive transform.\r\n                const { props, needRuntime } = directiveTransform(prop, node, context);\r\n                !ssr && props.forEach(analyzePatchFlag);\r\n                properties.push(...props);\r\n                if (needRuntime) {\r\n                    runtimeDirectives.push(prop);\r\n                    if (isSymbol(needRuntime)) {\r\n                        directiveImportMap.set(prop, needRuntime);\r\n                    }\r\n                }\r\n            }\r\n            else if (!isBuiltInDirective(name)) {\r\n                // no built-in transform, this is a user custom directive.\r\n                runtimeDirectives.push(prop);\r\n                // custom dirs may use beforeUpdate so they need to force blocks\r\n                // to ensure before-update gets called before children update\r\n                if (hasChildren) {\r\n                    shouldUseBlock = true;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    let propsExpression = undefined;\r\n    // has v-bind=\"object\" or v-on=\"object\", wrap with mergeProps\r\n    if (mergeArgs.length) {\r\n        if (properties.length) {\r\n            mergeArgs.push(createObjectExpression(dedupeProperties(properties), elementLoc));\r\n        }\r\n        if (mergeArgs.length > 1) {\r\n            propsExpression = createCallExpression(context.helper(MERGE_PROPS), mergeArgs, elementLoc);\r\n        }\r\n        else {\r\n            // single v-bind with nothing else - no need for a mergeProps call\r\n            propsExpression = mergeArgs[0];\r\n        }\r\n    }\r\n    else if (properties.length) {\r\n        propsExpression = createObjectExpression(dedupeProperties(properties), elementLoc);\r\n    }\r\n    // patchFlag analysis\r\n    if (hasDynamicKeys) {\r\n        patchFlag |= 16 /* FULL_PROPS */;\r\n    }\r\n    else {\r\n        if (hasClassBinding && !isComponent) {\r\n            patchFlag |= 2 /* CLASS */;\r\n        }\r\n        if (hasStyleBinding && !isComponent) {\r\n            patchFlag |= 4 /* STYLE */;\r\n        }\r\n        if (dynamicPropNames.length) {\r\n            patchFlag |= 8 /* PROPS */;\r\n        }\r\n        if (hasHydrationEventBinding) {\r\n            patchFlag |= 32 /* HYDRATE_EVENTS */;\r\n        }\r\n    }\r\n    if (!shouldUseBlock &&\r\n        (patchFlag === 0 || patchFlag === 32 /* HYDRATE_EVENTS */) &&\r\n        (hasRef || hasVnodeHook || runtimeDirectives.length > 0)) {\r\n        patchFlag |= 512 /* NEED_PATCH */;\r\n    }\r\n    // pre-normalize props, SSR is skipped for now\r\n    if (!context.inSSR && propsExpression) {\r\n        switch (propsExpression.type) {\r\n            case 15 /* JS_OBJECT_EXPRESSION */:\r\n                // means that there is no v-bind,\r\n                // but still need to deal with dynamic key binding\r\n                let classKeyIndex = -1;\r\n                let styleKeyIndex = -1;\r\n                let hasDynamicKey = false;\r\n                for (let i = 0; i < propsExpression.properties.length; i++) {\r\n                    const key = propsExpression.properties[i].key;\r\n                    if (isStaticExp(key)) {\r\n                        if (key.content === 'class') {\r\n                            classKeyIndex = i;\r\n                        }\r\n                        else if (key.content === 'style') {\r\n                            styleKeyIndex = i;\r\n                        }\r\n                    }\r\n                    else if (!key.isHandlerKey) {\r\n                        hasDynamicKey = true;\r\n                    }\r\n                }\r\n                const classProp = propsExpression.properties[classKeyIndex];\r\n                const styleProp = propsExpression.properties[styleKeyIndex];\r\n                // no dynamic key\r\n                if (!hasDynamicKey) {\r\n                    if (classProp && !isStaticExp(classProp.value)) {\r\n                        classProp.value = createCallExpression(context.helper(NORMALIZE_CLASS), [classProp.value]);\r\n                    }\r\n                    if (styleProp &&\r\n                        // the static style is compiled into an object,\r\n                        // so use `hasStyleBinding` to ensure that it is a dynamic style binding\r\n                        (hasStyleBinding ||\r\n                            (styleProp.value.type === 4 /* SIMPLE_EXPRESSION */ &&\r\n                                styleProp.value.content.trim()[0] === `[`) ||\r\n                            // v-bind:style and style both exist,\r\n                            // v-bind:style with static literal object\r\n                            styleProp.value.type === 17 /* JS_ARRAY_EXPRESSION */)) {\r\n                        styleProp.value = createCallExpression(context.helper(NORMALIZE_STYLE), [styleProp.value]);\r\n                    }\r\n                }\r\n                else {\r\n                    // dynamic key binding, wrap with `normalizeProps`\r\n                    propsExpression = createCallExpression(context.helper(NORMALIZE_PROPS), [propsExpression]);\r\n                }\r\n                break;\r\n            case 14 /* JS_CALL_EXPRESSION */:\r\n                // mergeProps call, do nothing\r\n                break;\r\n            default:\r\n                // single v-bind\r\n                propsExpression = createCallExpression(context.helper(NORMALIZE_PROPS), [\r\n                    createCallExpression(context.helper(GUARD_REACTIVE_PROPS), [\r\n                        propsExpression\r\n                    ])\r\n                ]);\r\n                break;\r\n        }\r\n    }\r\n    return {\r\n        props: propsExpression,\r\n        directives: runtimeDirectives,\r\n        patchFlag,\r\n        dynamicPropNames,\r\n        shouldUseBlock\r\n    };\r\n}\r\n// Dedupe props in an object literal.\r\n// Literal duplicated attributes would have been warned during the parse phase,\r\n// however, it's possible to encounter duplicated `onXXX` handlers with different\r\n// modifiers. We also need to merge static and dynamic class / style attributes.\r\n// - onXXX handlers / style: merge into array\r\n// - class: merge into single expression with concatenation\r\nfunction dedupeProperties(properties) {\r\n    const knownProps = new Map();\r\n    const deduped = [];\r\n    for (let i = 0; i < properties.length; i++) {\r\n        const prop = properties[i];\r\n        // dynamic keys are always allowed\r\n        if (prop.key.type === 8 /* COMPOUND_EXPRESSION */ || !prop.key.isStatic) {\r\n            deduped.push(prop);\r\n            continue;\r\n        }\r\n        const name = prop.key.content;\r\n        const existing = knownProps.get(name);\r\n        if (existing) {\r\n            if (name === 'style' || name === 'class' || isOn(name)) {\r\n                mergeAsArray(existing, prop);\r\n            }\r\n            // unexpected duplicate, should have emitted error during parse\r\n        }\r\n        else {\r\n            knownProps.set(name, prop);\r\n            deduped.push(prop);\r\n        }\r\n    }\r\n    return deduped;\r\n}\r\nfunction mergeAsArray(existing, incoming) {\r\n    if (existing.value.type === 17 /* JS_ARRAY_EXPRESSION */) {\r\n        existing.value.elements.push(incoming.value);\r\n    }\r\n    else {\r\n        existing.value = createArrayExpression([existing.value, incoming.value], existing.loc);\r\n    }\r\n}\r\nfunction buildDirectiveArgs(dir, context) {\r\n    const dirArgs = [];\r\n    const runtime = directiveImportMap.get(dir);\r\n    if (runtime) {\r\n        // built-in directive with runtime\r\n        dirArgs.push(context.helperString(runtime));\r\n    }\r\n    else {\r\n        {\r\n            // inject statement for resolving directive\r\n            context.helper(RESOLVE_DIRECTIVE);\r\n            context.directives.add(dir.name);\r\n            dirArgs.push(toValidAssetId(dir.name, `directive`));\r\n        }\r\n    }\r\n    const { loc } = dir;\r\n    if (dir.exp)\r\n        dirArgs.push(dir.exp);\r\n    if (dir.arg) {\r\n        if (!dir.exp) {\r\n            dirArgs.push(`void 0`);\r\n        }\r\n        dirArgs.push(dir.arg);\r\n    }\r\n    if (Object.keys(dir.modifiers).length) {\r\n        if (!dir.arg) {\r\n            if (!dir.exp) {\r\n                dirArgs.push(`void 0`);\r\n            }\r\n            dirArgs.push(`void 0`);\r\n        }\r\n        const trueExpression = createSimpleExpression(`true`, false, loc);\r\n        dirArgs.push(createObjectExpression(dir.modifiers.map(modifier => createObjectProperty(modifier, trueExpression)), loc));\r\n    }\r\n    return createArrayExpression(dirArgs, dir.loc);\r\n}\r\nfunction stringifyDynamicPropNames(props) {\r\n    let propsNamesString = `[`;\r\n    for (let i = 0, l = props.length; i < l; i++) {\r\n        propsNamesString += JSON.stringify(props[i]);\r\n        if (i < l - 1)\r\n            propsNamesString += ', ';\r\n    }\r\n    return propsNamesString + `]`;\r\n}\r\nfunction isComponentTag(tag) {\r\n    return tag === 'component' || tag === 'Component';\r\n}\n\n(process.env.NODE_ENV !== 'production')\r\n    ? Object.freeze({})\r\n    : {};\r\n(process.env.NODE_ENV !== 'production') ? Object.freeze([]) : [];\r\nconst cacheStringFunction = (fn) => {\r\n    const cache = Object.create(null);\r\n    return ((str) => {\r\n        const hit = cache[str];\r\n        return hit || (cache[str] = fn(str));\r\n    });\r\n};\r\nconst camelizeRE = /-(\\w)/g;\r\n/**\r\n * @private\r\n */\r\nconst camelize = cacheStringFunction((str) => {\r\n    return str.replace(camelizeRE, (_, c) => (c ? c.toUpperCase() : ''));\r\n});\n\nconst transformSlotOutlet = (node, context) => {\r\n    if (isSlotOutlet(node)) {\r\n        const { children, loc } = node;\r\n        const { slotName, slotProps } = processSlotOutlet(node, context);\r\n        const slotArgs = [\r\n            context.prefixIdentifiers ? `_ctx.$slots` : `$slots`,\r\n            slotName,\r\n            '{}',\r\n            'undefined',\r\n            'true'\r\n        ];\r\n        let expectedLen = 2;\r\n        if (slotProps) {\r\n            slotArgs[2] = slotProps;\r\n            expectedLen = 3;\r\n        }\r\n        if (children.length) {\r\n            slotArgs[3] = createFunctionExpression([], children, false, false, loc);\r\n            expectedLen = 4;\r\n        }\r\n        if (context.scopeId && !context.slotted) {\r\n            expectedLen = 5;\r\n        }\r\n        slotArgs.splice(expectedLen); // remove unused arguments\r\n        node.codegenNode = createCallExpression(context.helper(RENDER_SLOT), slotArgs, loc);\r\n    }\r\n};\r\nfunction processSlotOutlet(node, context) {\r\n    let slotName = `\"default\"`;\r\n    let slotProps = undefined;\r\n    const nonNameProps = [];\r\n    for (let i = 0; i < node.props.length; i++) {\r\n        const p = node.props[i];\r\n        if (p.type === 6 /* ATTRIBUTE */) {\r\n            if (p.value) {\r\n                if (p.name === 'name') {\r\n                    slotName = JSON.stringify(p.value.content);\r\n                }\r\n                else {\r\n                    p.name = camelize(p.name);\r\n                    nonNameProps.push(p);\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            if (p.name === 'bind' && isStaticArgOf(p.arg, 'name')) {\r\n                if (p.exp)\r\n                    slotName = p.exp;\r\n            }\r\n            else {\r\n                if (p.name === 'bind' && p.arg && isStaticExp(p.arg)) {\r\n                    p.arg.content = camelize(p.arg.content);\r\n                }\r\n                nonNameProps.push(p);\r\n            }\r\n        }\r\n    }\r\n    if (nonNameProps.length > 0) {\r\n        const { props, directives } = buildProps(node, context, nonNameProps, false, false);\r\n        slotProps = props;\r\n        if (directives.length) {\r\n            context.onError(createCompilerError(36 /* X_V_SLOT_UNEXPECTED_DIRECTIVE_ON_SLOT_OUTLET */, directives[0].loc));\r\n        }\r\n    }\r\n    return {\r\n        slotName,\r\n        slotProps\r\n    };\r\n}\n\nconst fnExpRE = /^\\s*([\\w$_]+|(async\\s*)?\\([^)]*?\\))\\s*=>|^\\s*(async\\s+)?function(?:\\s+[\\w$]+)?\\s*\\(/;\r\nconst transformOn = (dir, node, context, augmentor) => {\r\n    const { loc, modifiers, arg } = dir;\r\n    if (!dir.exp && !modifiers.length) {\r\n        context.onError(createCompilerError(35 /* X_V_ON_NO_EXPRESSION */, loc));\r\n    }\r\n    let eventName;\r\n    if (arg.type === 4 /* SIMPLE_EXPRESSION */) {\r\n        if (arg.isStatic) {\r\n            let rawName = arg.content;\r\n            // TODO deprecate @vnodeXXX usage\r\n            if (rawName.startsWith('vue:')) {\r\n                rawName = `vnode-${rawName.slice(4)}`;\r\n            }\r\n            // for all event listeners, auto convert it to camelCase. See issue #2249\r\n            eventName = createSimpleExpression(toHandlerKey(camelize$1(rawName)), true, arg.loc);\r\n        }\r\n        else {\r\n            // #2388\r\n            eventName = createCompoundExpression([\r\n                `${context.helperString(TO_HANDLER_KEY)}(`,\r\n                arg,\r\n                `)`\r\n            ]);\r\n        }\r\n    }\r\n    else {\r\n        // already a compound expression.\r\n        eventName = arg;\r\n        eventName.children.unshift(`${context.helperString(TO_HANDLER_KEY)}(`);\r\n        eventName.children.push(`)`);\r\n    }\r\n    // handler processing\r\n    let exp = dir.exp;\r\n    if (exp && !exp.content.trim()) {\r\n        exp = undefined;\r\n    }\r\n    let shouldCache = context.cacheHandlers && !exp && !context.inVOnce;\r\n    if (exp) {\r\n        const isMemberExp = isMemberExpression(exp.content);\r\n        const isInlineStatement = !(isMemberExp || fnExpRE.test(exp.content));\r\n        const hasMultipleStatements = exp.content.includes(`;`);\r\n        if ((process.env.NODE_ENV !== 'production') && true) {\r\n            validateBrowserExpression(exp, context, false, hasMultipleStatements);\r\n        }\r\n        if (isInlineStatement || (shouldCache && isMemberExp)) {\r\n            // wrap inline statement in a function expression\r\n            exp = createCompoundExpression([\r\n                `${isInlineStatement\r\n                    ? `$event`\r\n                    : `${``}(...args)`} => ${hasMultipleStatements ? `{` : `(`}`,\r\n                exp,\r\n                hasMultipleStatements ? `}` : `)`\r\n            ]);\r\n        }\r\n    }\r\n    let ret = {\r\n        props: [\r\n            createObjectProperty(eventName, exp || createSimpleExpression(`() => {}`, false, loc))\r\n        ]\r\n    };\r\n    // apply extended compiler augmentor\r\n    if (augmentor) {\r\n        ret = augmentor(ret);\r\n    }\r\n    if (shouldCache) {\r\n        // cache handlers so that it's always the same handler being passed down.\r\n        // this avoids unnecessary re-renders when users use inline handlers on\r\n        // components.\r\n        ret.props[0].value = context.cache(ret.props[0].value);\r\n    }\r\n    // mark the key as handler for props normalization check\r\n    ret.props.forEach(p => (p.key.isHandlerKey = true));\r\n    return ret;\r\n};\n\n// v-bind without arg is handled directly in ./transformElements.ts due to it affecting\r\n// codegen for the entire props object. This transform here is only for v-bind\r\n// *with* args.\r\nconst transformBind = (dir, _node, context) => {\r\n    const { exp, modifiers, loc } = dir;\r\n    const arg = dir.arg;\r\n    if (arg.type !== 4 /* SIMPLE_EXPRESSION */) {\r\n        arg.children.unshift(`(`);\r\n        arg.children.push(`) || \"\"`);\r\n    }\r\n    else if (!arg.isStatic) {\r\n        arg.content = `${arg.content} || \"\"`;\r\n    }\r\n    // .sync is replaced by v-model:arg\r\n    if (modifiers.includes('camel')) {\r\n        if (arg.type === 4 /* SIMPLE_EXPRESSION */) {\r\n            if (arg.isStatic) {\r\n                arg.content = camelize$1(arg.content);\r\n            }\r\n            else {\r\n                arg.content = `${context.helperString(CAMELIZE)}(${arg.content})`;\r\n            }\r\n        }\r\n        else {\r\n            arg.children.unshift(`${context.helperString(CAMELIZE)}(`);\r\n            arg.children.push(`)`);\r\n        }\r\n    }\r\n    if (!context.inSSR) {\r\n        if (modifiers.includes('prop')) {\r\n            injectPrefix(arg, '.');\r\n        }\r\n        if (modifiers.includes('attr')) {\r\n            injectPrefix(arg, '^');\r\n        }\r\n    }\r\n    if (!exp ||\r\n        (exp.type === 4 /* SIMPLE_EXPRESSION */ && !exp.content.trim())) {\r\n        context.onError(createCompilerError(34 /* X_V_BIND_NO_EXPRESSION */, loc));\r\n        return {\r\n            props: [createObjectProperty(arg, createSimpleExpression('', true, loc))]\r\n        };\r\n    }\r\n    return {\r\n        props: [createObjectProperty(arg, exp)]\r\n    };\r\n};\r\nconst injectPrefix = (arg, prefix) => {\r\n    if (arg.type === 4 /* SIMPLE_EXPRESSION */) {\r\n        if (arg.isStatic) {\r\n            arg.content = prefix + arg.content;\r\n        }\r\n        else {\r\n            arg.content = `\\`${prefix}\\${${arg.content}}\\``;\r\n        }\r\n    }\r\n    else {\r\n        arg.children.unshift(`'${prefix}' + (`);\r\n        arg.children.push(`)`);\r\n    }\r\n};\n\n// Merge adjacent text nodes and expressions into a single expression\r\n// e.g. <div>abc {{ d }} {{ e }}</div> should have a single expression node as child.\r\nconst transformText = (node, context) => {\r\n    if (node.type === 0 /* ROOT */ ||\r\n        node.type === 1 /* ELEMENT */ ||\r\n        node.type === 11 /* FOR */ ||\r\n        node.type === 10 /* IF_BRANCH */) {\r\n        // perform the transform on node exit so that all expressions have already\r\n        // been processed.\r\n        return () => {\r\n            const children = node.children;\r\n            let currentContainer = undefined;\r\n            let hasText = false;\r\n            for (let i = 0; i < children.length; i++) {\r\n                const child = children[i];\r\n                if (isText(child)) {\r\n                    hasText = true;\r\n                    for (let j = i + 1; j < children.length; j++) {\r\n                        const next = children[j];\r\n                        if (isText(next)) {\r\n                            if (!currentContainer) {\r\n                                currentContainer = children[i] = createCompoundExpression([child], child.loc);\r\n                            }\r\n                            // merge adjacent text node into current\r\n                            currentContainer.children.push(` + `, next);\r\n                            children.splice(j, 1);\r\n                            j--;\r\n                        }\r\n                        else {\r\n                            currentContainer = undefined;\r\n                            break;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            if (!hasText ||\r\n                // if this is a plain element with a single text child, leave it\r\n                // as-is since the runtime has dedicated fast path for this by directly\r\n                // setting textContent of the element.\r\n                // for component root it's always normalized anyway.\r\n                (children.length === 1 &&\r\n                    (node.type === 0 /* ROOT */ ||\r\n                        (node.type === 1 /* ELEMENT */ &&\r\n                            node.tagType === 0 /* ELEMENT */ &&\r\n                            // #3756\r\n                            // custom directives can potentially add DOM elements arbitrarily,\r\n                            // we need to avoid setting textContent of the element at runtime\r\n                            // to avoid accidentally overwriting the DOM elements added\r\n                            // by the user through custom directives.\r\n                            !node.props.find(p => p.type === 7 /* DIRECTIVE */ &&\r\n                                !context.directiveTransforms[p.name]) &&\r\n                            // in compat mode, <template> tags with no special directives\r\n                            // will be rendered as a fragment so its children must be\r\n                            // converted into vnodes.\r\n                            !(node.tag === 'template'))))) {\r\n                return;\r\n            }\r\n            // pre-convert text nodes into createTextVNode(text) calls to avoid\r\n            // runtime normalization.\r\n            for (let i = 0; i < children.length; i++) {\r\n                const child = children[i];\r\n                if (isText(child) || child.type === 8 /* COMPOUND_EXPRESSION */) {\r\n                    const callArgs = [];\r\n                    // createTextVNode defaults to single whitespace, so if it is a\r\n                    // single space the code could be an empty call to save bytes.\r\n                    if (child.type !== 2 /* TEXT */ || child.content !== ' ') {\r\n                        callArgs.push(child);\r\n                    }\r\n                    // mark dynamic text with flag so it gets patched inside a block\r\n                    if (!context.ssr &&\r\n                        getConstantType(child, context) === 0 /* NOT_CONSTANT */) {\r\n                        callArgs.push(1 /* TEXT */ +\r\n                            ((process.env.NODE_ENV !== 'production') ? ` /* ${PatchFlagNames[1 /* TEXT */]} */` : ``));\r\n                    }\r\n                    children[i] = {\r\n                        type: 12 /* TEXT_CALL */,\r\n                        content: child,\r\n                        loc: child.loc,\r\n                        codegenNode: createCallExpression(context.helper(CREATE_TEXT), callArgs)\r\n                    };\r\n                }\r\n            }\r\n        };\r\n    }\r\n};\n\nconst seen = new WeakSet();\r\nconst transformOnce = (node, context) => {\r\n    if (node.type === 1 /* ELEMENT */ && findDir(node, 'once', true)) {\r\n        if (seen.has(node) || context.inVOnce) {\r\n            return;\r\n        }\r\n        seen.add(node);\r\n        context.inVOnce = true;\r\n        context.helper(SET_BLOCK_TRACKING);\r\n        return () => {\r\n            context.inVOnce = false;\r\n            const cur = context.currentNode;\r\n            if (cur.codegenNode) {\r\n                cur.codegenNode = context.cache(cur.codegenNode, true /* isVNode */);\r\n            }\r\n        };\r\n    }\r\n};\n\nconst transformModel = (dir, node, context) => {\r\n    const { exp, arg } = dir;\r\n    if (!exp) {\r\n        context.onError(createCompilerError(41 /* X_V_MODEL_NO_EXPRESSION */, dir.loc));\r\n        return createTransformProps();\r\n    }\r\n    const rawExp = exp.loc.source;\r\n    const expString = exp.type === 4 /* SIMPLE_EXPRESSION */ ? exp.content : rawExp;\r\n    // im SFC <script setup> inline mode, the exp may have been transformed into\r\n    // _unref(exp)\r\n    context.bindingMetadata[rawExp];\r\n    const maybeRef = !true    /* SETUP_CONST */;\r\n    if (!expString.trim() ||\r\n        (!isMemberExpression(expString) && !maybeRef)) {\r\n        context.onError(createCompilerError(42 /* X_V_MODEL_MALFORMED_EXPRESSION */, exp.loc));\r\n        return createTransformProps();\r\n    }\r\n    const propName = arg ? arg : createSimpleExpression('modelValue', true);\r\n    const eventName = arg\r\n        ? isStaticExp(arg)\r\n            ? `onUpdate:${arg.content}`\r\n            : createCompoundExpression(['\"onUpdate:\" + ', arg])\r\n        : `onUpdate:modelValue`;\r\n    let assignmentExp;\r\n    const eventArg = context.isTS ? `($event: any)` : `$event`;\r\n    {\r\n        assignmentExp = createCompoundExpression([\r\n            `${eventArg} => ((`,\r\n            exp,\r\n            `) = $event)`\r\n        ]);\r\n    }\r\n    const props = [\r\n        // modelValue: foo\r\n        createObjectProperty(propName, dir.exp),\r\n        // \"onUpdate:modelValue\": $event => (foo = $event)\r\n        createObjectProperty(eventName, assignmentExp)\r\n    ];\r\n    // modelModifiers: { foo: true, \"bar-baz\": true }\r\n    if (dir.modifiers.length && node.tagType === 1 /* COMPONENT */) {\r\n        const modifiers = dir.modifiers\r\n            .map(m => (isSimpleIdentifier(m) ? m : JSON.stringify(m)) + `: true`)\r\n            .join(`, `);\r\n        const modifiersKey = arg\r\n            ? isStaticExp(arg)\r\n                ? `${arg.content}Modifiers`\r\n                : createCompoundExpression([arg, ' + \"Modifiers\"'])\r\n            : `modelModifiers`;\r\n        props.push(createObjectProperty(modifiersKey, createSimpleExpression(`{ ${modifiers} }`, false, dir.loc, 2 /* CAN_HOIST */)));\r\n    }\r\n    return createTransformProps(props);\r\n};\r\nfunction createTransformProps(props = []) {\r\n    return { props };\r\n}\n\nconst validDivisionCharRE = /[\\w).+\\-_$\\]]/;\r\nconst transformFilter = (node, context) => {\r\n    if (!isCompatEnabled(\"COMPILER_FILTER\" /* COMPILER_FILTERS */, context)) {\r\n        return;\r\n    }\r\n    if (node.type === 5 /* INTERPOLATION */) {\r\n        // filter rewrite is applied before expression transform so only\r\n        // simple expressions are possible at this stage\r\n        rewriteFilter(node.content, context);\r\n    }\r\n    if (node.type === 1 /* ELEMENT */) {\r\n        node.props.forEach((prop) => {\r\n            if (prop.type === 7 /* DIRECTIVE */ &&\r\n                prop.name !== 'for' &&\r\n                prop.exp) {\r\n                rewriteFilter(prop.exp, context);\r\n            }\r\n        });\r\n    }\r\n};\r\nfunction rewriteFilter(node, context) {\r\n    if (node.type === 4 /* SIMPLE_EXPRESSION */) {\r\n        parseFilter(node, context);\r\n    }\r\n    else {\r\n        for (let i = 0; i < node.children.length; i++) {\r\n            const child = node.children[i];\r\n            if (typeof child !== 'object')\r\n                continue;\r\n            if (child.type === 4 /* SIMPLE_EXPRESSION */) {\r\n                parseFilter(child, context);\r\n            }\r\n            else if (child.type === 8 /* COMPOUND_EXPRESSION */) {\r\n                rewriteFilter(node, context);\r\n            }\r\n            else if (child.type === 5 /* INTERPOLATION */) {\r\n                rewriteFilter(child.content, context);\r\n            }\r\n        }\r\n    }\r\n}\r\nfunction parseFilter(node, context) {\r\n    const exp = node.content;\r\n    let inSingle = false;\r\n    let inDouble = false;\r\n    let inTemplateString = false;\r\n    let inRegex = false;\r\n    let curly = 0;\r\n    let square = 0;\r\n    let paren = 0;\r\n    let lastFilterIndex = 0;\r\n    let c, prev, i, expression, filters = [];\r\n    for (i = 0; i < exp.length; i++) {\r\n        prev = c;\r\n        c = exp.charCodeAt(i);\r\n        if (inSingle) {\r\n            if (c === 0x27 && prev !== 0x5c)\r\n                inSingle = false;\r\n        }\r\n        else if (inDouble) {\r\n            if (c === 0x22 && prev !== 0x5c)\r\n                inDouble = false;\r\n        }\r\n        else if (inTemplateString) {\r\n            if (c === 0x60 && prev !== 0x5c)\r\n                inTemplateString = false;\r\n        }\r\n        else if (inRegex) {\r\n            if (c === 0x2f && prev !== 0x5c)\r\n                inRegex = false;\r\n        }\r\n        else if (c === 0x7c && // pipe\r\n            exp.charCodeAt(i + 1) !== 0x7c &&\r\n            exp.charCodeAt(i - 1) !== 0x7c &&\r\n            !curly &&\r\n            !square &&\r\n            !paren) {\r\n            if (expression === undefined) {\r\n                // first filter, end of expression\r\n                lastFilterIndex = i + 1;\r\n                expression = exp.slice(0, i).trim();\r\n            }\r\n            else {\r\n                pushFilter();\r\n            }\r\n        }\r\n        else {\r\n            switch (c) {\r\n                case 0x22:\r\n                    inDouble = true;\r\n                    break; // \"\r\n                case 0x27:\r\n                    inSingle = true;\r\n                    break; // '\r\n                case 0x60:\r\n                    inTemplateString = true;\r\n                    break; // `\r\n                case 0x28:\r\n                    paren++;\r\n                    break; // (\r\n                case 0x29:\r\n                    paren--;\r\n                    break; // )\r\n                case 0x5b:\r\n                    square++;\r\n                    break; // [\r\n                case 0x5d:\r\n                    square--;\r\n                    break; // ]\r\n                case 0x7b:\r\n                    curly++;\r\n                    break; // {\r\n                case 0x7d:\r\n                    curly--;\r\n                    break; // }\r\n            }\r\n            if (c === 0x2f) {\r\n                // /\r\n                let j = i - 1;\r\n                let p;\r\n                // find first non-whitespace prev char\r\n                for (; j >= 0; j--) {\r\n                    p = exp.charAt(j);\r\n                    if (p !== ' ')\r\n                        break;\r\n                }\r\n                if (!p || !validDivisionCharRE.test(p)) {\r\n                    inRegex = true;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (expression === undefined) {\r\n        expression = exp.slice(0, i).trim();\r\n    }\r\n    else if (lastFilterIndex !== 0) {\r\n        pushFilter();\r\n    }\r\n    function pushFilter() {\r\n        filters.push(exp.slice(lastFilterIndex, i).trim());\r\n        lastFilterIndex = i + 1;\r\n    }\r\n    if (filters.length) {\r\n        (process.env.NODE_ENV !== 'production') &&\r\n            warnDeprecation(\"COMPILER_FILTER\" /* COMPILER_FILTERS */, context, node.loc);\r\n        for (i = 0; i < filters.length; i++) {\r\n            expression = wrapFilter(expression, filters[i], context);\r\n        }\r\n        node.content = expression;\r\n    }\r\n}\r\nfunction wrapFilter(exp, filter, context) {\r\n    context.helper(RESOLVE_FILTER);\r\n    const i = filter.indexOf('(');\r\n    if (i < 0) {\r\n        context.filters.add(filter);\r\n        return `${toValidAssetId(filter, 'filter')}(${exp})`;\r\n    }\r\n    else {\r\n        const name = filter.slice(0, i);\r\n        const args = filter.slice(i + 1);\r\n        context.filters.add(name);\r\n        return `${toValidAssetId(name, 'filter')}(${exp}${args !== ')' ? ',' + args : args}`;\r\n    }\r\n}\n\nconst seen$1 = new WeakSet();\r\nconst transformMemo = (node, context) => {\r\n    if (node.type === 1 /* ELEMENT */) {\r\n        const dir = findDir(node, 'memo');\r\n        if (!dir || seen$1.has(node)) {\r\n            return;\r\n        }\r\n        seen$1.add(node);\r\n        return () => {\r\n            const codegenNode = node.codegenNode ||\r\n                context.currentNode.codegenNode;\r\n            if (codegenNode && codegenNode.type === 13 /* VNODE_CALL */) {\r\n                // non-component sub tree should be turned into a block\r\n                if (node.tagType !== 1 /* COMPONENT */) {\r\n                    makeBlock(codegenNode, context);\r\n                }\r\n                node.codegenNode = createCallExpression(context.helper(WITH_MEMO), [\r\n                    dir.exp,\r\n                    createFunctionExpression(undefined, codegenNode),\r\n                    `_cache`,\r\n                    String(context.cached++)\r\n                ]);\r\n            }\r\n        };\r\n    }\r\n};\n\nfunction getBaseTransformPreset(prefixIdentifiers) {\r\n    return [\r\n        [\r\n            transformOnce,\r\n            transformIf,\r\n            transformMemo,\r\n            transformFor,\r\n            ...([transformFilter] ),\r\n            ...((process.env.NODE_ENV !== 'production')\r\n                    ? [transformExpression]\r\n                    : []),\r\n            transformSlotOutlet,\r\n            transformElement,\r\n            trackSlotScopes,\r\n            transformText\r\n        ],\r\n        {\r\n            on: transformOn,\r\n            bind: transformBind,\r\n            model: transformModel\r\n        }\r\n    ];\r\n}\r\n// we name it `baseCompile` so that higher order compilers like\r\n// @vue/compiler-dom can export `compile` while re-exporting everything else.\r\nfunction baseCompile(template, options = {}) {\r\n    const onError = options.onError || defaultOnError;\r\n    const isModuleMode = options.mode === 'module';\r\n    /* istanbul ignore if */\r\n    {\r\n        if (options.prefixIdentifiers === true) {\r\n            onError(createCompilerError(46 /* X_PREFIX_ID_NOT_SUPPORTED */));\r\n        }\r\n        else if (isModuleMode) {\r\n            onError(createCompilerError(47 /* X_MODULE_MODE_NOT_SUPPORTED */));\r\n        }\r\n    }\r\n    const prefixIdentifiers = !true ;\r\n    if (options.cacheHandlers) {\r\n        onError(createCompilerError(48 /* X_CACHE_HANDLER_NOT_SUPPORTED */));\r\n    }\r\n    if (options.scopeId && !isModuleMode) {\r\n        onError(createCompilerError(49 /* X_SCOPE_ID_NOT_SUPPORTED */));\r\n    }\r\n    const ast = isString(template) ? baseParse(template, options) : template;\r\n    const [nodeTransforms, directiveTransforms] = getBaseTransformPreset();\r\n    transform(ast, extend({}, options, {\r\n        prefixIdentifiers,\r\n        nodeTransforms: [\r\n            ...nodeTransforms,\r\n            ...(options.nodeTransforms || []) // user transforms\r\n        ],\r\n        directiveTransforms: extend({}, directiveTransforms, options.directiveTransforms || {} // user transforms\r\n        )\r\n    }));\r\n    return generate(ast, extend({}, options, {\r\n        prefixIdentifiers\r\n    }));\r\n}\n\nconst noopDirectiveTransform = () => ({ props: [] });\n\nexport { BASE_TRANSITION, CAMELIZE, CAPITALIZE, CREATE_BLOCK, CREATE_COMMENT, CREATE_ELEMENT_BLOCK, CREATE_ELEMENT_VNODE, CREATE_SLOTS, CREATE_STATIC, CREATE_TEXT, CREATE_VNODE, FRAGMENT, GUARD_REACTIVE_PROPS, IS_MEMO_SAME, IS_REF, KEEP_ALIVE, MERGE_PROPS, NORMALIZE_CLASS, NORMALIZE_PROPS, NORMALIZE_STYLE, OPEN_BLOCK, POP_SCOPE_ID, PUSH_SCOPE_ID, RENDER_LIST, RENDER_SLOT, RESOLVE_COMPONENT, RESOLVE_DIRECTIVE, RESOLVE_DYNAMIC_COMPONENT, RESOLVE_FILTER, SET_BLOCK_TRACKING, SUSPENSE, TELEPORT, TO_DISPLAY_STRING, TO_HANDLERS, TO_HANDLER_KEY, UNREF, WITH_CTX, WITH_DIRECTIVES, WITH_MEMO, advancePositionWithClone, advancePositionWithMutation, assert, baseCompile, baseParse, buildDirectiveArgs, buildProps, buildSlots, checkCompatEnabled, createArrayExpression, createAssignmentExpression, createBlockStatement, createCacheExpression, createCallExpression, createCompilerError, createCompoundExpression, createConditionalExpression, createForLoopParams, createFunctionExpression, createIfStatement, createInterpolation, createObjectExpression, createObjectProperty, createReturnStatement, createRoot, createSequenceExpression, createSimpleExpression, createStructuralDirectiveTransform, createTemplateLiteral, createTransformContext, createVNodeCall, extractIdentifiers, findDir, findProp, generate, getBaseTransformPreset, getConstantType, getInnerRange, getMemoedVNodeCall, getVNodeBlockHelper, getVNodeHelper, hasDynamicKeyVBind, hasScopeRef, helperNameMap, injectProp, isBuiltInType, isCoreComponent, isFunctionType, isInDestructureAssignment, isMemberExpression, isMemberExpressionBrowser, isMemberExpressionNode, isReferencedIdentifier, isSimpleIdentifier, isSlotOutlet, isStaticArgOf, isStaticExp, isStaticProperty, isStaticPropertyKey, isTemplateNode, isText, isVSlot, locStub, makeBlock, noopDirectiveTransform, processExpression, processFor, processIf, processSlotOutlet, registerRuntimeHelpers, resolveComponentType, toValidAssetId, trackSlotScopes, trackVForSlotScopes, transform, transformBind, transformElement, transformExpression, transformModel, transformOn, traverseNode, walkBlockDeclarations, walkFunctionParams, walkIdentifiers, warnDeprecation };\n", "import { registerRuntimeHelpers, isBuiltInType, createSimpleExpression, createCompilerError, createObjectProperty, getConstantType, createCallExpression, TO_DISPLAY_STRING, transformModel as transformModel$1, findProp, hasDynamicKeyVBind, transformOn as transformOn$1, createCompoundExpression, isStaticExp, checkCompatEnabled, noopDirectiveTransform, baseCompile, baseParse } from '@vue/compiler-core';\nexport * from '@vue/compiler-core';\nimport { isVoidTag, isHTMLTag, isSVGTag, makeMap, parseStringStyle, capitalize, extend } from '@vue/shared';\n\nconst V_MODEL_RADIO = Symbol((process.env.NODE_ENV !== 'production') ? `vModelRadio` : ``);\r\nconst V_MODEL_CHECKBOX = Symbol((process.env.NODE_ENV !== 'production') ? `vModelCheckbox` : ``);\r\nconst V_MODEL_TEXT = Symbol((process.env.NODE_ENV !== 'production') ? `vModelText` : ``);\r\nconst V_MODEL_SELECT = Symbol((process.env.NODE_ENV !== 'production') ? `vModelSelect` : ``);\r\nconst V_MODEL_DYNAMIC = Symbol((process.env.NODE_ENV !== 'production') ? `vModelDynamic` : ``);\r\nconst V_ON_WITH_MODIFIERS = Symbol((process.env.NODE_ENV !== 'production') ? `vOnModifiersGuard` : ``);\r\nconst V_ON_WITH_KEYS = Symbol((process.env.NODE_ENV !== 'production') ? `vOnKeysGuard` : ``);\r\nconst V_SHOW = Symbol((process.env.NODE_ENV !== 'production') ? `vShow` : ``);\r\nconst TRANSITION = Symbol((process.env.NODE_ENV !== 'production') ? `Transition` : ``);\r\nconst TRANSITION_GROUP = Symbol((process.env.NODE_ENV !== 'production') ? `TransitionGroup` : ``);\r\nregisterRuntimeHelpers({\r\n    [V_MODEL_RADIO]: `vModelRadio`,\r\n    [V_MODEL_CHECKBOX]: `vModelCheckbox`,\r\n    [V_MODEL_TEXT]: `vModelText`,\r\n    [V_MODEL_SELECT]: `vModelSelect`,\r\n    [V_MODEL_DYNAMIC]: `vModelDynamic`,\r\n    [V_ON_WITH_MODIFIERS]: `withModifiers`,\r\n    [V_ON_WITH_KEYS]: `withKeys`,\r\n    [V_SHOW]: `vShow`,\r\n    [TRANSITION]: `Transition`,\r\n    [TRANSITION_GROUP]: `TransitionGroup`\r\n});\n\n/* eslint-disable no-restricted-globals */\r\nlet decoder;\r\nfunction decodeHtmlBrowser(raw, asAttr = false) {\r\n    if (!decoder) {\r\n        decoder = document.createElement('div');\r\n    }\r\n    if (asAttr) {\r\n        decoder.innerHTML = `<div foo=\"${raw.replace(/\"/g, '&quot;')}\">`;\r\n        return decoder.children[0].getAttribute('foo');\r\n    }\r\n    else {\r\n        decoder.innerHTML = raw;\r\n        return decoder.textContent;\r\n    }\r\n}\n\nconst isRawTextContainer = /*#__PURE__*/ makeMap('style,iframe,script,noscript', true);\r\nconst parserOptions = {\r\n    isVoidTag,\r\n    isNativeTag: tag => isHTMLTag(tag) || isSVGTag(tag),\r\n    isPreTag: tag => tag === 'pre',\r\n    decodeEntities: decodeHtmlBrowser ,\r\n    isBuiltInComponent: (tag) => {\r\n        if (isBuiltInType(tag, `Transition`)) {\r\n            return TRANSITION;\r\n        }\r\n        else if (isBuiltInType(tag, `TransitionGroup`)) {\r\n            return TRANSITION_GROUP;\r\n        }\r\n    },\r\n    // https://html.spec.whatwg.org/multipage/parsing.html#tree-construction-dispatcher\r\n    getNamespace(tag, parent) {\r\n        let ns = parent ? parent.ns : 0 /* HTML */;\r\n        if (parent && ns === 2 /* MATH_ML */) {\r\n            if (parent.tag === 'annotation-xml') {\r\n                if (tag === 'svg') {\r\n                    return 1 /* SVG */;\r\n                }\r\n                if (parent.props.some(a => a.type === 6 /* ATTRIBUTE */ &&\r\n                    a.name === 'encoding' &&\r\n                    a.value != null &&\r\n                    (a.value.content === 'text/html' ||\r\n                        a.value.content === 'application/xhtml+xml'))) {\r\n                    ns = 0 /* HTML */;\r\n                }\r\n            }\r\n            else if (/^m(?:[ions]|text)$/.test(parent.tag) &&\r\n                tag !== 'mglyph' &&\r\n                tag !== 'malignmark') {\r\n                ns = 0 /* HTML */;\r\n            }\r\n        }\r\n        else if (parent && ns === 1 /* SVG */) {\r\n            if (parent.tag === 'foreignObject' ||\r\n                parent.tag === 'desc' ||\r\n                parent.tag === 'title') {\r\n                ns = 0 /* HTML */;\r\n            }\r\n        }\r\n        if (ns === 0 /* HTML */) {\r\n            if (tag === 'svg') {\r\n                return 1 /* SVG */;\r\n            }\r\n            if (tag === 'math') {\r\n                return 2 /* MATH_ML */;\r\n            }\r\n        }\r\n        return ns;\r\n    },\r\n    // https://html.spec.whatwg.org/multipage/parsing.html#parsing-html-fragments\r\n    getTextMode({ tag, ns }) {\r\n        if (ns === 0 /* HTML */) {\r\n            if (tag === 'textarea' || tag === 'title') {\r\n                return 1 /* RCDATA */;\r\n            }\r\n            if (isRawTextContainer(tag)) {\r\n                return 2 /* RAWTEXT */;\r\n            }\r\n        }\r\n        return 0 /* DATA */;\r\n    }\r\n};\n\n// Parse inline CSS strings for static style attributes into an object.\r\n// This is a NodeTransform since it works on the static `style` attribute and\r\n// converts it into a dynamic equivalent:\r\n// style=\"color: red\" -> :style='{ \"color\": \"red\" }'\r\n// It is then processed by `transformElement` and included in the generated\r\n// props.\r\nconst transformStyle = node => {\r\n    if (node.type === 1 /* ELEMENT */) {\r\n        node.props.forEach((p, i) => {\r\n            if (p.type === 6 /* ATTRIBUTE */ && p.name === 'style' && p.value) {\r\n                // replace p with an expression node\r\n                node.props[i] = {\r\n                    type: 7 /* DIRECTIVE */,\r\n                    name: `bind`,\r\n                    arg: createSimpleExpression(`style`, true, p.loc),\r\n                    exp: parseInlineCSS(p.value.content, p.loc),\r\n                    modifiers: [],\r\n                    loc: p.loc\r\n                };\r\n            }\r\n        });\r\n    }\r\n};\r\nconst parseInlineCSS = (cssText, loc) => {\r\n    const normalized = parseStringStyle(cssText);\r\n    return createSimpleExpression(JSON.stringify(normalized), false, loc, 3 /* CAN_STRINGIFY */);\r\n};\n\nfunction createDOMCompilerError(code, loc) {\r\n    return createCompilerError(code, loc, (process.env.NODE_ENV !== 'production') || !true ? DOMErrorMessages : undefined);\r\n}\r\nconst DOMErrorMessages = {\r\n    [50 /* X_V_HTML_NO_EXPRESSION */]: `v-html is missing expression.`,\r\n    [51 /* X_V_HTML_WITH_CHILDREN */]: `v-html will override element children.`,\r\n    [52 /* X_V_TEXT_NO_EXPRESSION */]: `v-text is missing expression.`,\r\n    [53 /* X_V_TEXT_WITH_CHILDREN */]: `v-text will override element children.`,\r\n    [54 /* X_V_MODEL_ON_INVALID_ELEMENT */]: `v-model can only be used on <input>, <textarea> and <select> elements.`,\r\n    [55 /* X_V_MODEL_ARG_ON_ELEMENT */]: `v-model argument is not supported on plain elements.`,\r\n    [56 /* X_V_MODEL_ON_FILE_INPUT_ELEMENT */]: `v-model cannot be used on file inputs since they are read-only. Use a v-on:change listener instead.`,\r\n    [57 /* X_V_MODEL_UNNECESSARY_VALUE */]: `Unnecessary value binding used alongside v-model. It will interfere with v-model's behavior.`,\r\n    [58 /* X_V_SHOW_NO_EXPRESSION */]: `v-show is missing expression.`,\r\n    [59 /* X_TRANSITION_INVALID_CHILDREN */]: `<Transition> expects exactly one child element or component.`,\r\n    [60 /* X_IGNORED_SIDE_EFFECT_TAG */]: `Tags with side effect (<script> and <style>) are ignored in client component templates.`\r\n};\n\nconst transformVHtml = (dir, node, context) => {\r\n    const { exp, loc } = dir;\r\n    if (!exp) {\r\n        context.onError(createDOMCompilerError(50 /* X_V_HTML_NO_EXPRESSION */, loc));\r\n    }\r\n    if (node.children.length) {\r\n        context.onError(createDOMCompilerError(51 /* X_V_HTML_WITH_CHILDREN */, loc));\r\n        node.children.length = 0;\r\n    }\r\n    return {\r\n        props: [\r\n            createObjectProperty(createSimpleExpression(`innerHTML`, true, loc), exp || createSimpleExpression('', true))\r\n        ]\r\n    };\r\n};\n\nconst transformVText = (dir, node, context) => {\r\n    const { exp, loc } = dir;\r\n    if (!exp) {\r\n        context.onError(createDOMCompilerError(52 /* X_V_TEXT_NO_EXPRESSION */, loc));\r\n    }\r\n    if (node.children.length) {\r\n        context.onError(createDOMCompilerError(53 /* X_V_TEXT_WITH_CHILDREN */, loc));\r\n        node.children.length = 0;\r\n    }\r\n    return {\r\n        props: [\r\n            createObjectProperty(createSimpleExpression(`textContent`, true), exp\r\n                ? getConstantType(exp, context) > 0\r\n                    ? exp\r\n                    : createCallExpression(context.helperString(TO_DISPLAY_STRING), [exp], loc)\r\n                : createSimpleExpression('', true))\r\n        ]\r\n    };\r\n};\n\nconst transformModel = (dir, node, context) => {\r\n    const baseResult = transformModel$1(dir, node, context);\r\n    // base transform has errors OR component v-model (only need props)\r\n    if (!baseResult.props.length || node.tagType === 1 /* COMPONENT */) {\r\n        return baseResult;\r\n    }\r\n    if (dir.arg) {\r\n        context.onError(createDOMCompilerError(55 /* X_V_MODEL_ARG_ON_ELEMENT */, dir.arg.loc));\r\n    }\r\n    function checkDuplicatedValue() {\r\n        const value = findProp(node, 'value');\r\n        if (value) {\r\n            context.onError(createDOMCompilerError(57 /* X_V_MODEL_UNNECESSARY_VALUE */, value.loc));\r\n        }\r\n    }\r\n    const { tag } = node;\r\n    const isCustomElement = context.isCustomElement(tag);\r\n    if (tag === 'input' ||\r\n        tag === 'textarea' ||\r\n        tag === 'select' ||\r\n        isCustomElement) {\r\n        let directiveToUse = V_MODEL_TEXT;\r\n        let isInvalidType = false;\r\n        if (tag === 'input' || isCustomElement) {\r\n            const type = findProp(node, `type`);\r\n            if (type) {\r\n                if (type.type === 7 /* DIRECTIVE */) {\r\n                    // :type=\"foo\"\r\n                    directiveToUse = V_MODEL_DYNAMIC;\r\n                }\r\n                else if (type.value) {\r\n                    switch (type.value.content) {\r\n                        case 'radio':\r\n                            directiveToUse = V_MODEL_RADIO;\r\n                            break;\r\n                        case 'checkbox':\r\n                            directiveToUse = V_MODEL_CHECKBOX;\r\n                            break;\r\n                        case 'file':\r\n                            isInvalidType = true;\r\n                            context.onError(createDOMCompilerError(56 /* X_V_MODEL_ON_FILE_INPUT_ELEMENT */, dir.loc));\r\n                            break;\r\n                        default:\r\n                            // text type\r\n                            (process.env.NODE_ENV !== 'production') && checkDuplicatedValue();\r\n                            break;\r\n                    }\r\n                }\r\n            }\r\n            else if (hasDynamicKeyVBind(node)) {\r\n                // element has bindings with dynamic keys, which can possibly contain\r\n                // \"type\".\r\n                directiveToUse = V_MODEL_DYNAMIC;\r\n            }\r\n            else {\r\n                // text type\r\n                (process.env.NODE_ENV !== 'production') && checkDuplicatedValue();\r\n            }\r\n        }\r\n        else if (tag === 'select') {\r\n            directiveToUse = V_MODEL_SELECT;\r\n        }\r\n        else {\r\n            // textarea\r\n            (process.env.NODE_ENV !== 'production') && checkDuplicatedValue();\r\n        }\r\n        // inject runtime directive\r\n        // by returning the helper symbol via needRuntime\r\n        // the import will replaced a resolveDirective call.\r\n        if (!isInvalidType) {\r\n            baseResult.needRuntime = context.helper(directiveToUse);\r\n        }\r\n    }\r\n    else {\r\n        context.onError(createDOMCompilerError(54 /* X_V_MODEL_ON_INVALID_ELEMENT */, dir.loc));\r\n    }\r\n    // native vmodel doesn't need the `modelValue` props since they are also\r\n    // passed to the runtime as `binding.value`. removing it reduces code size.\r\n    baseResult.props = baseResult.props.filter(p => !(p.key.type === 4 /* SIMPLE_EXPRESSION */ &&\r\n        p.key.content === 'modelValue'));\r\n    return baseResult;\r\n};\n\nconst isEventOptionModifier = /*#__PURE__*/ makeMap(`passive,once,capture`);\r\nconst isNonKeyModifier = /*#__PURE__*/ makeMap(\r\n// event propagation management\r\n`stop,prevent,self,` +\r\n    // system modifiers + exact\r\n    `ctrl,shift,alt,meta,exact,` +\r\n    // mouse\r\n    `middle`);\r\n// left & right could be mouse or key modifiers based on event type\r\nconst maybeKeyModifier = /*#__PURE__*/ makeMap('left,right');\r\nconst isKeyboardEvent = /*#__PURE__*/ makeMap(`onkeyup,onkeydown,onkeypress`, true);\r\nconst resolveModifiers = (key, modifiers, context, loc) => {\r\n    const keyModifiers = [];\r\n    const nonKeyModifiers = [];\r\n    const eventOptionModifiers = [];\r\n    for (let i = 0; i < modifiers.length; i++) {\r\n        const modifier = modifiers[i];\r\n        if (modifier === 'native' &&\r\n            checkCompatEnabled(\"COMPILER_V_ON_NATIVE\" /* COMPILER_V_ON_NATIVE */, context, loc)) {\r\n            eventOptionModifiers.push(modifier);\r\n        }\r\n        else if (isEventOptionModifier(modifier)) {\r\n            // eventOptionModifiers: modifiers for addEventListener() options,\r\n            // e.g. .passive & .capture\r\n            eventOptionModifiers.push(modifier);\r\n        }\r\n        else {\r\n            // runtimeModifiers: modifiers that needs runtime guards\r\n            if (maybeKeyModifier(modifier)) {\r\n                if (isStaticExp(key)) {\r\n                    if (isKeyboardEvent(key.content)) {\r\n                        keyModifiers.push(modifier);\r\n                    }\r\n                    else {\r\n                        nonKeyModifiers.push(modifier);\r\n                    }\r\n                }\r\n                else {\r\n                    keyModifiers.push(modifier);\r\n                    nonKeyModifiers.push(modifier);\r\n                }\r\n            }\r\n            else {\r\n                if (isNonKeyModifier(modifier)) {\r\n                    nonKeyModifiers.push(modifier);\r\n                }\r\n                else {\r\n                    keyModifiers.push(modifier);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return {\r\n        keyModifiers,\r\n        nonKeyModifiers,\r\n        eventOptionModifiers\r\n    };\r\n};\r\nconst transformClick = (key, event) => {\r\n    const isStaticClick = isStaticExp(key) && key.content.toLowerCase() === 'onclick';\r\n    return isStaticClick\r\n        ? createSimpleExpression(event, true)\r\n        : key.type !== 4 /* SIMPLE_EXPRESSION */\r\n            ? createCompoundExpression([\r\n                `(`,\r\n                key,\r\n                `) === \"onClick\" ? \"${event}\" : (`,\r\n                key,\r\n                `)`\r\n            ])\r\n            : key;\r\n};\r\nconst transformOn = (dir, node, context) => {\r\n    return transformOn$1(dir, node, context, baseResult => {\r\n        const { modifiers } = dir;\r\n        if (!modifiers.length)\r\n            return baseResult;\r\n        let { key, value: handlerExp } = baseResult.props[0];\r\n        const { keyModifiers, nonKeyModifiers, eventOptionModifiers } = resolveModifiers(key, modifiers, context, dir.loc);\r\n        // normalize click.right and click.middle since they don't actually fire\r\n        if (nonKeyModifiers.includes('right')) {\r\n            key = transformClick(key, `onContextmenu`);\r\n        }\r\n        if (nonKeyModifiers.includes('middle')) {\r\n            key = transformClick(key, `onMouseup`);\r\n        }\r\n        if (nonKeyModifiers.length) {\r\n            handlerExp = createCallExpression(context.helper(V_ON_WITH_MODIFIERS), [\r\n                handlerExp,\r\n                JSON.stringify(nonKeyModifiers)\r\n            ]);\r\n        }\r\n        if (keyModifiers.length &&\r\n            // if event name is dynamic, always wrap with keys guard\r\n            (!isStaticExp(key) || isKeyboardEvent(key.content))) {\r\n            handlerExp = createCallExpression(context.helper(V_ON_WITH_KEYS), [\r\n                handlerExp,\r\n                JSON.stringify(keyModifiers)\r\n            ]);\r\n        }\r\n        if (eventOptionModifiers.length) {\r\n            const modifierPostfix = eventOptionModifiers.map(capitalize).join('');\r\n            key = isStaticExp(key)\r\n                ? createSimpleExpression(`${key.content}${modifierPostfix}`, true)\r\n                : createCompoundExpression([`(`, key, `) + \"${modifierPostfix}\"`]);\r\n        }\r\n        return {\r\n            props: [createObjectProperty(key, handlerExp)]\r\n        };\r\n    });\r\n};\n\nconst transformShow = (dir, node, context) => {\r\n    const { exp, loc } = dir;\r\n    if (!exp) {\r\n        context.onError(createDOMCompilerError(58 /* X_V_SHOW_NO_EXPRESSION */, loc));\r\n    }\r\n    return {\r\n        props: [],\r\n        needRuntime: context.helper(V_SHOW)\r\n    };\r\n};\n\nconst transformTransition = (node, context) => {\r\n    if (node.type === 1 /* ELEMENT */ &&\r\n        node.tagType === 1 /* COMPONENT */) {\r\n        const component = context.isBuiltInComponent(node.tag);\r\n        if (component === TRANSITION) {\r\n            return () => {\r\n                if (!node.children.length) {\r\n                    return;\r\n                }\r\n                // warn multiple transition children\r\n                if (hasMultipleChildren(node)) {\r\n                    context.onError(createDOMCompilerError(59 /* X_TRANSITION_INVALID_CHILDREN */, {\r\n                        start: node.children[0].loc.start,\r\n                        end: node.children[node.children.length - 1].loc.end,\r\n                        source: ''\r\n                    }));\r\n                }\r\n                // check if it's s single child w/ v-show\r\n                // if yes, inject \"persisted: true\" to the transition props\r\n                const child = node.children[0];\r\n                if (child.type === 1 /* ELEMENT */) {\r\n                    for (const p of child.props) {\r\n                        if (p.type === 7 /* DIRECTIVE */ && p.name === 'show') {\r\n                            node.props.push({\r\n                                type: 6 /* ATTRIBUTE */,\r\n                                name: 'persisted',\r\n                                value: undefined,\r\n                                loc: node.loc\r\n                            });\r\n                        }\r\n                    }\r\n                }\r\n            };\r\n        }\r\n    }\r\n};\r\nfunction hasMultipleChildren(node) {\r\n    // #1352 filter out potential comment nodes.\r\n    const children = (node.children = node.children.filter(c => c.type !== 3 /* COMMENT */ &&\r\n        !(c.type === 2 /* TEXT */ && !c.content.trim())));\r\n    const child = children[0];\r\n    return (children.length !== 1 ||\r\n        child.type === 11 /* FOR */ ||\r\n        (child.type === 9 /* IF */ && child.branches.some(hasMultipleChildren)));\r\n}\n\nconst ignoreSideEffectTags = (node, context) => {\r\n    if (node.type === 1 /* ELEMENT */ &&\r\n        node.tagType === 0 /* ELEMENT */ &&\r\n        (node.tag === 'script' || node.tag === 'style')) {\r\n        context.onError(createDOMCompilerError(60 /* X_IGNORED_SIDE_EFFECT_TAG */, node.loc));\r\n        context.removeNode();\r\n    }\r\n};\n\nconst DOMNodeTransforms = [\r\n    transformStyle,\r\n    ...((process.env.NODE_ENV !== 'production') ? [transformTransition] : [])\r\n];\r\nconst DOMDirectiveTransforms = {\r\n    cloak: noopDirectiveTransform,\r\n    html: transformVHtml,\r\n    text: transformVText,\r\n    model: transformModel,\r\n    on: transformOn,\r\n    show: transformShow\r\n};\r\nfunction compile(template, options = {}) {\r\n    return baseCompile(template, extend({}, parserOptions, options, {\r\n        nodeTransforms: [\r\n            // ignore <script> and <tag>\r\n            // this is not put inside DOMNodeTransforms because that list is used\r\n            // by compiler-ssr to generate vnode fallback branches\r\n            ignoreSideEffectTags,\r\n            ...DOMNodeTransforms,\r\n            ...(options.nodeTransforms || [])\r\n        ],\r\n        directiveTransforms: extend({}, DOMDirectiveTransforms, options.directiveTransforms || {}),\r\n        transformHoist: null \r\n    }));\r\n}\r\nfunction parse(template, options = {}) {\r\n    return baseParse(template, extend({}, parserOptions, options));\r\n}\n\nexport { DOMDirectiveTransforms, DOMNodeTransforms, TRANSITION, TRANSITION_GROUP, V_MODEL_CHECKBOX, V_MODEL_DYNAMIC, V_MODEL_RADIO, V_MODEL_SELECT, V_MODEL_TEXT, V_ON_WITH_KEYS, V_ON_WITH_MODIFIERS, V_SHOW, compile, createDOMCompilerError, parse, parserOptions, transformStyle };\n", "'use strict';\n\nObject.defineProperty(exports, '__esModule', { value: true });\n\nvar compilerDom = require('@vue/compiler-dom');\nvar runtimeDom = require('@vue/runtime-dom');\nvar shared = require('@vue/shared');\n\nfunction _interopNamespace(e) {\n  if (e && e.__esModule) return e;\n  var n = Object.create(null);\n  if (e) {\n    Object.keys(e).forEach(function (k) {\n      n[k] = e[k];\n    });\n  }\n  n['default'] = e;\n  return Object.freeze(n);\n}\n\nvar runtimeDom__namespace = /*#__PURE__*/_interopNamespace(runtimeDom);\n\n// This entry is the \"full-build\" that includes both the runtime\r\nconst compileCache = Object.create(null);\r\nfunction compileToFunction(template, options) {\r\n    if (!shared.isString(template)) {\r\n        if (template.nodeType) {\r\n            template = template.innerHTML;\r\n        }\r\n        else {\r\n            runtimeDom.warn(`invalid template option: `, template);\r\n            return shared.NOOP;\r\n        }\r\n    }\r\n    const key = template;\r\n    const cached = compileCache[key];\r\n    if (cached) {\r\n        return cached;\r\n    }\r\n    if (template[0] === '#') {\r\n        const el = document.querySelector(template);\r\n        if (!el) {\r\n            runtimeDom.warn(`Template element not found or is empty: ${template}`);\r\n        }\r\n        // __UNSAFE__\r\n        // Reason: potential execution of JS expressions in in-DOM template.\r\n        // The user must make sure the in-DOM template is trusted. If it's rendered\r\n        // by the server, the template should not contain any user data.\r\n        template = el ? el.innerHTML : ``;\r\n    }\r\n    const { code } = compilerDom.compile(template, shared.extend({\r\n        hoistStatic: true,\r\n        onError: onError ,\r\n        onWarn: e => onError(e, true) \r\n    }, options));\r\n    function onError(err, asWarning = false) {\r\n        const message = asWarning\r\n            ? err.message\r\n            : `Template compilation error: ${err.message}`;\r\n        const codeFrame = err.loc &&\r\n            shared.generateCodeFrame(template, err.loc.start.offset, err.loc.end.offset);\r\n        runtimeDom.warn(codeFrame ? `${message}\\n${codeFrame}` : message);\r\n    }\r\n    // The wildcard import results in a huge object with every export\r\n    // with keys that cannot be mangled, and can be quite heavy size-wise.\r\n    // In the global build we know `Vue` is available globally so we can avoid\r\n    // the wildcard object.\r\n    const render = (new Function('Vue', code)(runtimeDom__namespace));\r\n    render._rc = true;\r\n    return (compileCache[key] = render);\r\n}\r\nruntimeDom.registerRuntimeCompiler(compileToFunction);\n\nObject.keys(runtimeDom).forEach(function (k) {\n  if (k !== 'default') exports[k] = runtimeDom[k];\n});\nexports.compile = compileToFunction;\n", "'use strict'\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./dist/vue.cjs.prod.js')\n} else {\n  module.exports = require('./dist/vue.cjs.js')\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,SAAS,eAAe,OAAO;AAC3B,QAAM;AACV;AACA,SAAS,cAAc,KAAK;AACxB,EAA2C,QAAQ,KAAK,cAAc,IAAI,SAAS;AACvF;AACA,SAAS,oBAAoB,MAAM,KAAK,UAAU,mBAAmB;AACjE,QAAM,MAAO,QACN,YAAY,eAAe,SAAS,qBAAqB,MAC1D;AACN,QAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,QAAM,OAAO;AACb,QAAM,MAAM;AACZ,SAAO;AACX;AAmJA,SAAS,uBAAuB,SAAS;AACrC,SAAO,sBAAsB,OAAO,EAAE,QAAQ,OAAK;AAC/C,kBAAc,KAAK,QAAQ;AAAA,EAC/B,CAAC;AACL;AAWA,SAAS,WAAW,UAAU,MAAM,SAAS;AACzC,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,SAAS,CAAC;AAAA,IACV,YAAY,CAAC;AAAA,IACb,YAAY,CAAC;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,aAAa;AAAA,IACb;AAAA,EACJ;AACJ;AACA,SAAS,gBAAgB,SAAS,KAAK,OAAO,UAAU,WAAW,cAAc,YAAY,UAAU,OAAO,kBAAkB,OAAOA,eAAc,OAAO,MAAM,SAAS;AACvK,MAAI,SAAS;AACT,QAAI,SAAS;AACT,cAAQ,OAAO,UAAU;AACzB,cAAQ,OAAO,oBAAoB,QAAQ,OAAOA,YAAW,CAAC;AAAA,IAClE,OACK;AACD,cAAQ,OAAO,eAAe,QAAQ,OAAOA,YAAW,CAAC;AAAA,IAC7D;AACA,QAAI,YAAY;AACZ,cAAQ,OAAO,eAAe;AAAA,IAClC;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,UAAU,MAAM,SAAS;AACpD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,uBAAuB,YAAY,MAAM,SAAS;AACvD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,qBAAqB,KAAK,OAAO;AACtC,SAAO;AAAA,IACH,MAAM;AAAA,IACN,KAAK;AAAA,IACL,KAAK,SAAS,GAAG,IAAI,uBAAuB,KAAK,IAAI,IAAI;AAAA,IACzD;AAAA,EACJ;AACJ;AACA,SAAS,uBAAuB,SAAS,WAAW,OAAO,MAAM,SAAS,YAAY,GAAsB;AACxG,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,WAAW,IAAwB;AAAA,EAClD;AACJ;AACA,SAAS,oBAAoB,SAAS,KAAK;AACvC,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,SAAS,SAAS,OAAO,IACnB,uBAAuB,SAAS,OAAO,GAAG,IAC1C;AAAA,EACV;AACJ;AACA,SAAS,yBAAyB,UAAU,MAAM,SAAS;AACvD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,qBAAqB,QAAQ,OAAO,CAAC,GAAG,MAAM,SAAS;AAC5D,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACf;AACJ;AACA,SAAS,yBAAyB,QAAQ,UAAU,QAAW,UAAU,OAAO,SAAS,OAAO,MAAM,SAAS;AAC3G,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,4BAA4B,MAAM,YAAY,WAAW,UAAU,MAAM;AAC9E,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,sBAAsB,OAAO,OAAO,UAAU,OAAO;AAC1D,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,qBAAqB,MAAM;AAChC,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,sBAAsB,UAAU;AACrC,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,kBAAkB,MAAM,YAAY,WAAW;AACpD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,2BAA2B,MAAM,OAAO;AAC7C,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,yBAAyB,aAAa;AAC3C,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AACA,SAAS,sBAAsB,SAAS;AACpC,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK;AAAA,EACT;AACJ;AAIA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,cAAc,KAAK,UAAU,GAAG;AAChC,WAAO;AAAA,EACX,WACS,cAAc,KAAK,UAAU,GAAG;AACrC,WAAO;AAAA,EACX,WACS,cAAc,KAAK,WAAW,GAAG;AACtC,WAAO;AAAA,EACX,WACS,cAAc,KAAK,gBAAgB,GAAG;AAC3C,WAAO;AAAA,EACX;AACJ;AAsFA,SAAS,cAAc,KAAK,QAAQ,QAAQ;AACxC,QAAM,SAAS,IAAI,OAAO,MAAM,QAAQ,SAAS,MAAM;AACvD,QAAM,SAAS;AAAA,IACX;AAAA,IACA,OAAO,yBAAyB,IAAI,OAAO,IAAI,QAAQ,MAAM;AAAA,IAC7D,KAAK,IAAI;AAAA,EACb;AACA,MAAI,UAAU,MAAM;AAChB,WAAO,MAAM,yBAAyB,IAAI,OAAO,IAAI,QAAQ,SAAS,MAAM;AAAA,EAChF;AACA,SAAO;AACX;AACA,SAAS,yBAAyB,KAAK,QAAQ,qBAAqB,OAAO,QAAQ;AAC/E,SAAO,4BAA4B,OAAO,CAAC,GAAG,GAAG,GAAG,QAAQ,kBAAkB;AAClF;AAGA,SAAS,4BAA4B,KAAK,QAAQ,qBAAqB,OAAO,QAAQ;AAClF,MAAI,aAAa;AACjB,MAAI,iBAAiB;AACrB,WAAS,IAAI,GAAG,IAAI,oBAAoB,KAAK;AACzC,QAAI,OAAO,WAAW,CAAC,MAAM,IAA4B;AACrD;AACA,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI,SACA,mBAAmB,KACb,IAAI,SAAS,qBACb,qBAAqB;AAC/B,SAAO;AACX;AACA,SAAS,OAAO,WAAW,KAAK;AAE5B,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,MAAM,OAAO,+BAA+B;AAAA,EAC1D;AACJ;AACA,SAAS,QAAQ,MAAM,MAAM,aAAa,OAAO;AAC7C,WAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAM,IAAI,KAAK,MAAM;AACrB,QAAI,EAAE,SAAS,MACV,cAAc,EAAE,SAChB,SAAS,IAAI,IAAI,EAAE,SAAS,OAAO,KAAK,KAAK,EAAE,IAAI,IAAI;AACxD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,SAAS,MAAM,MAAM,cAAc,OAAO,aAAa,OAAO;AACnE,WAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAM,IAAI,KAAK,MAAM;AACrB,QAAI,EAAE,SAAS,GAAmB;AAC9B,UAAI;AACA;AACJ,UAAI,EAAE,SAAS,SAAS,EAAE,SAAS,aAAa;AAC5C,eAAO;AAAA,MACX;AAAA,IACJ,WACS,EAAE,SAAS,WACf,EAAE,OAAO,eACV,cAAc,EAAE,KAAK,IAAI,GAAG;AAC5B,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,KAAK,MAAM;AAC9B,SAAO,CAAC,EAAE,OAAO,YAAY,GAAG,KAAK,IAAI,YAAY;AACzD;AACA,SAAS,mBAAmB,MAAM;AAC9B,SAAO,KAAK,MAAM;AAAA,IAAK,OAAK,EAAE,SAAS,KACnC,EAAE,SAAS,WACV,CAAC,EAAE,OACA,EAAE,IAAI,SAAS,KACf,CAAC,EAAE,IAAI;AAAA,EACf;AACJ;AACA,SAAS,OAAO,MAAM;AAClB,SAAO,KAAK,SAAS,KAAyB,KAAK,SAAS;AAChE;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,EAAE,SAAS,KAAqB,EAAE,SAAS;AACtD;AACA,SAAS,eAAe,MAAM;AAC1B,SAAQ,KAAK,SAAS,KAAmB,KAAK,YAAY;AAC9D;AACA,SAAS,aAAa,MAAM;AACxB,SAAO,KAAK,SAAS,KAAmB,KAAK,YAAY;AAC7D;AACA,SAAS,eAAe,KAAKA,cAAa;AACtC,SAAO,OAAOA,eAAc,eAAe;AAC/C;AACA,SAAS,oBAAoB,KAAKA,cAAa;AAC3C,SAAO,OAAOA,eAAc,eAAe;AAC/C;AAEA,SAAS,qBAAqB,OAAO,WAAW,CAAC,GAAG;AAChD,MAAI,SACA,CAAC,SAAS,KAAK,KACf,MAAM,SAAS,IAA6B;AAC5C,UAAM,SAAS,MAAM;AACrB,QAAI,CAAC,SAAS,MAAM,KAAK,eAAe,IAAI,MAAM,GAAG;AACjD,aAAO,qBAAqB,MAAM,UAAU,IAAI,SAAS,OAAO,KAAK,CAAC;AAAA,IAC1E;AAAA,EACJ;AACA,SAAO,CAAC,OAAO,QAAQ;AAC3B;AACA,SAAS,WAAW,MAAM,MAAM,SAAS;AACrC,MAAI;AASJ,MAAI,QAAQ,KAAK,SAAS,KAAsB,KAAK,QAAQ,KAAK,UAAU;AAC5E,MAAI,WAAW,CAAC;AAChB,MAAI;AACJ,MAAI,SACA,CAAC,SAAS,KAAK,KACf,MAAM,SAAS,IAA6B;AAC5C,UAAM,MAAM,qBAAqB,KAAK;AACtC,YAAQ,IAAI;AACZ,eAAW,IAAI;AACf,iBAAa,SAAS,SAAS,SAAS;AAAA,EAC5C;AACA,MAAI,SAAS,QAAQ,SAAS,KAAK,GAAG;AAClC,yBAAqB,uBAAuB,CAAC,IAAI,CAAC;AAAA,EACtD,WACS,MAAM,SAAS,IAA6B;AAIjD,UAAM,QAAQ,MAAM,UAAU;AAC9B,QAAI,CAAC,SAAS,KAAK,KAAK,MAAM,SAAS,IAA+B;AAClE,YAAM,WAAW,QAAQ,IAAI;AAAA,IACjC,OACK;AACD,UAAI,MAAM,WAAW,aAAa;AAE9B,6BAAqB,qBAAqB,QAAQ,OAAO,WAAW,GAAG;AAAA,UACnE,uBAAuB,CAAC,IAAI,CAAC;AAAA,UAC7B;AAAA,QACJ,CAAC;AAAA,MACL,OACK;AACD,cAAM,UAAU,QAAQ,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAAA,MAC1D;AAAA,IACJ;AACA,KAAC,uBAAuB,qBAAqB;AAAA,EACjD,WACS,MAAM,SAAS,IAA+B;AACnD,QAAI,gBAAgB;AAEpB,QAAI,KAAK,IAAI,SAAS,GAA2B;AAC7C,YAAM,cAAc,KAAK,IAAI;AAC7B,sBAAgB,MAAM,WAAW,KAAK,OAAK,EAAE,IAAI,SAAS,KACtD,EAAE,IAAI,YAAY,WAAW;AAAA,IACrC;AACA,QAAI,CAAC,eAAe;AAChB,YAAM,WAAW,QAAQ,IAAI;AAAA,IACjC;AACA,yBAAqB;AAAA,EACzB,OACK;AAED,yBAAqB,qBAAqB,QAAQ,OAAO,WAAW,GAAG;AAAA,MACnE,uBAAuB,CAAC,IAAI,CAAC;AAAA,MAC7B;AAAA,IACJ,CAAC;AAID,QAAI,cAAc,WAAW,WAAW,sBAAsB;AAC1D,mBAAa,SAAS,SAAS,SAAS;AAAA,IAC5C;AAAA,EACJ;AACA,MAAI,KAAK,SAAS,IAAqB;AACnC,QAAI,YAAY;AACZ,iBAAW,UAAU,KAAK;AAAA,IAC9B,OACK;AACD,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ,OACK;AACD,QAAI,YAAY;AACZ,iBAAW,UAAU,KAAK;AAAA,IAC9B,OACK;AACD,WAAK,UAAU,KAAK;AAAA,IACxB;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,MAAM,MAAM;AAEhC,SAAO,IAAI,QAAQ,KAAK,QAAQ,UAAU,CAAC,aAAa,iBAAiB;AACrE,WAAO,gBAAgB,MAAM,MAAM,KAAK,WAAW,YAAY,EAAE,SAAS;AAAA,EAC9E,CAAC;AACL;AAEA,SAAS,YAAY,MAAM,KAAK;AAC5B,MAAI,CAAC,QAAQ,OAAO,KAAK,GAAG,EAAE,WAAW,GAAG;AACxC,WAAO;AAAA,EACX;AACA,UAAQ,KAAK;AAAA,SACJ;AACD,eAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,cAAM,IAAI,KAAK,MAAM;AACrB,YAAI,EAAE,SAAS,MACV,YAAY,EAAE,KAAK,GAAG,KAAK,YAAY,EAAE,KAAK,GAAG,IAAI;AACtD,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO,KAAK,SAAS,KAAK,OAAK,YAAY,GAAG,GAAG,CAAC;AAAA,SACjD;AACD,UAAI,YAAY,KAAK,QAAQ,GAAG,GAAG;AAC/B,eAAO;AAAA,MACX;AACA,aAAO,KAAK,SAAS,KAAK,OAAK,YAAY,GAAG,GAAG,CAAC;AAAA,SACjD;AACD,aAAO,KAAK,SAAS,KAAK,OAAK,YAAY,GAAG,GAAG,CAAC;AAAA,SACjD;AACD,UAAI,YAAY,KAAK,WAAW,GAAG,GAAG;AAClC,eAAO;AAAA,MACX;AACA,aAAO,KAAK,SAAS,KAAK,OAAK,YAAY,GAAG,GAAG,CAAC;AAAA,SACjD;AACD,aAAQ,CAAC,KAAK,YACV,mBAAmB,KAAK,OAAO,KAC/B,CAAC,CAAC,IAAI,KAAK;AAAA,SACd;AACD,aAAO,KAAK,SAAS,KAAK,OAAK,SAAS,CAAC,KAAK,YAAY,GAAG,GAAG,CAAC;AAAA,SAChE;AAAA,SACA;AACD,aAAO,YAAY,KAAK,SAAS,GAAG;AAAA,SACnC;AAAA,SACA;AACD,aAAO;AAAA;AAEP,UAAK;AAAwC;AAC7C,aAAO;AAAA;AAEnB;AACA,SAAS,mBAAmB,MAAM;AAC9B,MAAI,KAAK,SAAS,MAA+B,KAAK,WAAW,WAAW;AACxE,WAAO,KAAK,UAAU,GAAG;AAAA,EAC7B,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,MAAM,EAAE,QAAQ,cAAc,MAAM,GAAG;AACtD,MAAI,CAAC,KAAK,SAAS;AACf,SAAK,UAAU;AACf,iBAAa,eAAe,OAAO,KAAK,WAAW,CAAC;AACpD,WAAO,UAAU;AACjB,WAAO,oBAAoB,OAAO,KAAK,WAAW,CAAC;AAAA,EACvD;AACJ;AAsDA,SAAS,eAAe,KAAK,SAAS;AAClC,QAAM,SAAS,QAAQ,UACjB,QAAQ,QAAQ,eAChB,QAAQ;AACd,QAAM,QAAQ,UAAU,OAAO;AAC/B,MAAI,QAAQ,QAAQ;AAChB,WAAO,SAAS;AAAA,EACpB,OACK;AACD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,gBAAgB,KAAK,SAAS;AACnC,QAAM,OAAO,eAAe,QAAQ,OAAO;AAC3C,QAAM,QAAQ,eAAe,KAAK,OAAO;AAGzC,SAAO,SAAS,IAAI,UAAU,OAAO,UAAU;AACnD;AACA,SAAS,mBAAmB,KAAK,SAAS,QAAQ,MAAM;AACpD,QAAM,UAAU,gBAAgB,KAAK,OAAO;AAC5C,MAA+C,SAAS;AACpD,oBAAgB,KAAK,SAAS,KAAK,GAAG,IAAI;AAAA,EAC9C;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,KAAK,SAAS,QAAQ,MAAM;AACjD,QAAM,MAAM,eAAe,KAAK,OAAO;AACvC,MAAI,QAAQ,oBAAoB;AAC5B;AAAA,EACJ;AACA,QAAM,EAAE,SAAS,KAAK,IAAI,gBAAgB;AAC1C,QAAM,MAAM,gBAAgB,QAAQ,OAAO,YAAY,aAAa,QAAQ,GAAG,IAAI,IAAI,UAAU,OAAO;AAAA,aAAgB,SAAS;AACjI,QAAM,MAAM,IAAI,YAAY,GAAG;AAC/B,MAAI,OAAO;AACX,MAAI;AACA,QAAI,MAAM;AACd,UAAQ,OAAO,GAAG;AACtB;AAyBA,SAAS,UAAU,SAAS,UAAU,CAAC,GAAG;AACtC,QAAM,UAAU,oBAAoB,SAAS,OAAO;AACpD,QAAM,QAAQ,UAAU,OAAO;AAC/B,SAAO,WAAW,cAAc,SAAS,GAAc,CAAC,CAAC,GAAG,aAAa,SAAS,KAAK,CAAC;AAC5F;AACA,SAAS,oBAAoB,SAAS,YAAY;AAC9C,QAAM,UAAU,OAAO,CAAC,GAAG,oBAAoB;AAC/C,MAAI;AACJ,OAAK,OAAO,YAAY;AAEpB,YAAQ,OACJ,WAAW,SAAS,SACd,qBAAqB,OACrB,WAAW;AAAA,EACzB;AACA,SAAO;AAAA,IACH;AAAA,IACA,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,QAAQ,QAAQ;AAAA,EACpB;AACJ;AACA,SAAS,cAAc,SAAS,MAAM,WAAW;AAC7C,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAM,KAAK,SAAS,OAAO,KAAK;AAChC,QAAM,QAAQ,CAAC;AACf,SAAO,CAAC,MAAM,SAAS,MAAM,SAAS,GAAG;AACrC,UAAM,IAAI,QAAQ;AAClB,QAAI,OAAO;AACX,QAAI,SAAS,KAAgB,SAAS,GAAgB;AAClD,UAAI,CAAC,QAAQ,UAAU,WAAW,GAAG,QAAQ,QAAQ,WAAW,EAAE,GAAG;AAEjE,eAAO,mBAAmB,SAAS,IAAI;AAAA,MAC3C,WACS,SAAS,KAAgB,EAAE,OAAO,KAAK;AAE5C,YAAI,EAAE,WAAW,GAAG;AAChB,oBAAU,SAAS,GAA6B,CAAC;AAAA,QACrD,WACS,EAAE,OAAO,KAAK;AAEnB,cAAI,WAAW,GAAG,MAAM,GAAG;AACvB,mBAAO,aAAa,OAAO;AAAA,UAC/B,WACS,WAAW,GAAG,WAAW,GAAG;AAEjC,mBAAO,kBAAkB,OAAO;AAAA,UACpC,WACS,WAAW,GAAG,WAAW,GAAG;AACjC,gBAAI,OAAO,GAAc;AACrB,qBAAO,WAAW,SAAS,SAAS;AAAA,YACxC,OACK;AACD,wBAAU,SAAS,CAA6B;AAChD,qBAAO,kBAAkB,OAAO;AAAA,YACpC;AAAA,UACJ,OACK;AACD,sBAAU,SAAS,EAAmC;AACtD,mBAAO,kBAAkB,OAAO;AAAA,UACpC;AAAA,QACJ,WACS,EAAE,OAAO,KAAK;AAEnB,cAAI,EAAE,WAAW,GAAG;AAChB,sBAAU,SAAS,GAA6B,CAAC;AAAA,UACrD,WACS,EAAE,OAAO,KAAK;AACnB,sBAAU,SAAS,IAA+B,CAAC;AACnD,sBAAU,SAAS,CAAC;AACpB;AAAA,UACJ,WACS,SAAS,KAAK,EAAE,EAAE,GAAG;AAC1B,sBAAU,SAAS,EAA0B;AAC7C,qBAAS,SAAS,GAAa,MAAM;AACrC;AAAA,UACJ,OACK;AACD,sBAAU,SAAS,IAA8C,CAAC;AAClE,mBAAO,kBAAkB,OAAO;AAAA,UACpC;AAAA,QACJ,WACS,SAAS,KAAK,EAAE,EAAE,GAAG;AAC1B,iBAAO,aAAa,SAAS,SAAS;AAEtC,cAAI,gBAAgB,4BAA2D,OAAO,KAClF,QACA,KAAK,QAAQ,cACb,CAAC,KAAK,MAAM,KAAK,OAAK,EAAE,SAAS,KAC7B,2BAA2B,EAAE,IAAI,CAAC,GAAG;AACzC,YACI,gBAAgB,4BAA2D,SAAS,KAAK,GAAG;AAChG,mBAAO,KAAK;AAAA,UAChB;AAAA,QACJ,WACS,EAAE,OAAO,KAAK;AACnB,oBAAU,SAAS,IAAuD,CAAC;AAC3E,iBAAO,kBAAkB,OAAO;AAAA,QACpC,OACK;AACD,oBAAU,SAAS,IAA8C,CAAC;AAAA,QACtE;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,MAAM;AACP,aAAO,UAAU,SAAS,IAAI;AAAA,IAClC;AACA,QAAI,QAAQ,IAAI,GAAG;AACf,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,iBAAS,OAAO,KAAK,EAAE;AAAA,MAC3B;AAAA,IACJ,OACK;AACD,eAAS,OAAO,IAAI;AAAA,IACxB;AAAA,EACJ;AAEA,MAAI,oBAAoB;AACxB,MAAI,SAAS,KAAmB,SAAS,GAAgB;AACrD,UAAM,iBAAiB,QAAQ,QAAQ,eAAe;AACtD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,OAAO,MAAM;AACnB,UAAI,CAAC,QAAQ,SAAS,KAAK,SAAS,GAAc;AAC9C,YAAI,CAAC,eAAe,KAAK,KAAK,OAAO,GAAG;AACpC,gBAAM,OAAO,MAAM,IAAI;AACvB,gBAAM,OAAO,MAAM,IAAI;AAKvB,cAAI,CAAC,QACD,CAAC,QACA,mBACI,KAAK,SAAS,KACX,KAAK,SAAS,KACb,KAAK,SAAS,KACX,KAAK,SAAS,KACd,SAAS,KAAK,KAAK,OAAO,IAAM;AAC5C,gCAAoB;AACpB,kBAAM,KAAK;AAAA,UACf,OACK;AAED,iBAAK,UAAU;AAAA,UACnB;AAAA,QACJ,WACS,gBAAgB;AAGrB,eAAK,UAAU,KAAK,QAAQ,QAAQ,iBAAiB,GAAG;AAAA,QAC5D;AAAA,MACJ,WAES,KAAK,SAAS,KAAmB,CAAC,QAAQ,QAAQ,UAAU;AACjE,4BAAoB;AACpB,cAAM,KAAK;AAAA,MACf;AAAA,IACJ;AACA,QAAI,QAAQ,SAAS,UAAU,QAAQ,QAAQ,SAAS,OAAO,GAAG,GAAG;AAGjE,YAAM,QAAQ,MAAM;AACpB,UAAI,SAAS,MAAM,SAAS,GAAc;AACtC,cAAM,UAAU,MAAM,QAAQ,QAAQ,UAAU,EAAE;AAAA,MACtD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,oBAAoB,MAAM,OAAO,OAAO,IAAI;AACvD;AACA,SAAS,SAAS,OAAO,MAAM;AAC3B,MAAI,KAAK,SAAS,GAAc;AAC5B,UAAM,OAAO,KAAK,KAAK;AAGvB,QAAI,QACA,KAAK,SAAS,KACd,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI,MAAM,QAAQ;AAC/C,WAAK,WAAW,KAAK;AACrB,WAAK,IAAI,MAAM,KAAK,IAAI;AACxB,WAAK,IAAI,UAAU,KAAK,IAAI;AAC5B;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,KAAK,IAAI;AACnB;AACA,SAAS,WAAW,SAAS,WAAW;AACpC,YAAU,SAAS,CAAC;AACpB,QAAM,QAAQ,cAAc,SAAS,GAAe,SAAS;AAC7D,MAAI,QAAQ,OAAO,WAAW,GAAG;AAC7B,cAAU,SAAS,CAAoB;AAAA,EAC3C,OACK;AACD,cAAU,SAAS,CAAC;AAAA,EACxB;AACA,SAAO;AACX;AACA,SAAS,aAAa,SAAS;AAC3B,QAAM,QAAQ,UAAU,OAAO;AAC/B,MAAI;AAEJ,QAAM,QAAQ,WAAW,KAAK,QAAQ,MAAM;AAC5C,MAAI,CAAC,OAAO;AACR,cAAU,QAAQ,OAAO,MAAM,CAAC;AAChC,cAAU,SAAS,QAAQ,OAAO,MAAM;AACxC,cAAU,SAAS,CAAsB;AAAA,EAC7C,OACK;AACD,QAAI,MAAM,SAAS,GAAG;AAClB,gBAAU,SAAS,CAAuC;AAAA,IAC9D;AACA,QAAI,MAAM,IAAI;AACV,gBAAU,SAAS,EAAmC;AAAA,IAC1D;AACA,cAAU,QAAQ,OAAO,MAAM,GAAG,MAAM,KAAK;AAE7C,UAAM,IAAI,QAAQ,OAAO,MAAM,GAAG,MAAM,KAAK;AAC7C,QAAI,YAAY,GAAG,cAAc;AACjC,YAAQ,cAAc,EAAE,QAAQ,QAAQ,SAAS,OAAO,IAAI;AACxD,gBAAU,SAAS,cAAc,YAAY,CAAC;AAC9C,UAAI,cAAc,IAAI,EAAE,QAAQ;AAC5B,kBAAU,SAAS,EAAuB;AAAA,MAC9C;AACA,kBAAY,cAAc;AAAA,IAC9B;AACA,cAAU,SAAS,MAAM,QAAQ,MAAM,GAAG,SAAS,YAAY,CAAC;AAAA,EACpE;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK,aAAa,SAAS,KAAK;AAAA,EACpC;AACJ;AACA,SAAS,kBAAkB,SAAS;AAChC,QAAM,QAAQ,UAAU,OAAO;AAC/B,QAAM,eAAe,QAAQ,OAAO,OAAO,MAAM,IAAI;AACrD,MAAI;AACJ,QAAM,aAAa,QAAQ,OAAO,QAAQ,GAAG;AAC7C,MAAI,eAAe,IAAI;AACnB,cAAU,QAAQ,OAAO,MAAM,YAAY;AAC3C,cAAU,SAAS,QAAQ,OAAO,MAAM;AAAA,EAC5C,OACK;AACD,cAAU,QAAQ,OAAO,MAAM,cAAc,UAAU;AACvD,cAAU,SAAS,aAAa,CAAC;AAAA,EACrC;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK,aAAa,SAAS,KAAK;AAAA,EACpC;AACJ;AACA,SAAS,aAAa,SAAS,WAAW;AAEtC,QAAM,WAAW,QAAQ;AACzB,QAAM,YAAY,QAAQ;AAC1B,QAAM,SAAS,KAAK,SAAS;AAC7B,QAAM,UAAU,SAAS,SAAS,GAAe,MAAM;AACvD,QAAM,gBAAgB,QAAQ,SAAS,CAAC;AACxC,QAAM,iBAAiB,QAAQ,UAAU,CAAC;AAC1C,MAAI,QAAQ,iBAAiB,QAAQ,QAAQ,UAAU,QAAQ,GAAG,GAAG;AAEjE,QAAI,eAAe;AACf,cAAQ,QAAQ;AAAA,IACpB;AACA,QAAI,gBAAgB;AAChB,cAAQ,SAAS;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AAEA,YAAU,KAAK,OAAO;AACtB,QAAM,OAAO,QAAQ,QAAQ,YAAY,SAAS,MAAM;AACxD,QAAM,WAAW,cAAc,SAAS,MAAM,SAAS;AACvD,YAAU,IAAI;AAEd;AACI,UAAM,qBAAqB,QAAQ,MAAM,KAAK,OAAK,EAAE,SAAS,KAAqB,EAAE,SAAS,iBAAiB;AAC/G,QAAI,sBACA,mBAAmB,4BAA2D,SAAS,mBAAmB,GAAG,GAAG;AAChH,YAAM,MAAM,aAAa,SAAS,QAAQ,IAAI,GAAG;AACjD,yBAAmB,QAAQ;AAAA,QACvB,MAAM;AAAA,QACN,SAAS,IAAI;AAAA,QACb;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,UAAQ,WAAW;AAEnB,MAAI,qBAAqB,QAAQ,QAAQ,QAAQ,GAAG,GAAG;AACnD,aAAS,SAAS,GAAa,MAAM;AAAA,EACzC,OACK;AACD,cAAU,SAAS,IAA4B,GAAG,QAAQ,IAAI,KAAK;AACnE,QAAI,QAAQ,OAAO,WAAW,KAAK,QAAQ,IAAI,YAAY,MAAM,UAAU;AACvE,YAAM,QAAQ,SAAS;AACvB,UAAI,SAAS,WAAW,MAAM,IAAI,QAAQ,MAAM,GAAG;AAC/C,kBAAU,SAAS,CAA4C;AAAA,MACnE;AAAA,IACJ;AAAA,EACJ;AACA,UAAQ,MAAM,aAAa,SAAS,QAAQ,IAAI,KAAK;AACrD,MAAI,eAAe;AACf,YAAQ,QAAQ;AAAA,EACpB;AACA,MAAI,gBAAgB;AAChB,YAAQ,SAAS;AAAA,EACrB;AACA,SAAO;AACX;AAEA,SAAS,SAAS,SAAS,MAAM,QAAQ;AAErC,QAAM,QAAQ,UAAU,OAAO;AAC/B,QAAM,QAAQ,+BAA+B,KAAK,QAAQ,MAAM;AAChE,QAAM,MAAM,MAAM;AAClB,QAAM,KAAK,QAAQ,QAAQ,aAAa,KAAK,MAAM;AACnD,YAAU,SAAS,MAAM,GAAG,MAAM;AAClC,gBAAc,OAAO;AAErB,QAAM,SAAS,UAAU,OAAO;AAChC,QAAM,gBAAgB,QAAQ;AAE9B,MAAI,QAAQ,QAAQ,SAAS,GAAG,GAAG;AAC/B,YAAQ,QAAQ;AAAA,EACpB;AAEA,MAAI,QAAQ,gBAAgB,SAAS,IAAI;AAEzC,MAAI,SAAS,KACT,CAAC,QAAQ,UACT,MAAM,KAAK,OAAK,EAAE,SAAS,KAAqB,EAAE,SAAS,KAAK,GAAG;AACnE,YAAQ,SAAS;AAEjB,WAAO,SAAS,MAAM;AACtB,YAAQ,SAAS;AAEjB,YAAQ,gBAAgB,SAAS,IAAI,EAAE,OAAO,OAAK,EAAE,SAAS,OAAO;AAAA,EACzE;AAEA,MAAI,gBAAgB;AACpB,MAAI,QAAQ,OAAO,WAAW,GAAG;AAC7B,cAAU,SAAS,CAAkB;AAAA,EACzC,OACK;AACD,oBAAgB,WAAW,QAAQ,QAAQ,IAAI;AAC/C,QAAI,SAAS,KAAe,eAAe;AACvC,gBAAU,SAAS,CAAqC;AAAA,IAC5D;AACA,cAAU,SAAS,gBAAgB,IAAI,CAAC;AAAA,EAC5C;AACA,MAAI,SAAS,GAAa;AACtB;AAAA,EACJ;AAEA,MACI,gBAAgB,kCAAuE,OAAO,GAAG;AACjG,QAAI,QAAQ;AACZ,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,IAAI,MAAM;AAChB,UAAI,EAAE,SAAS,GAAmB;AAC9B,YAAI,EAAE,SAAS,MAAM;AACjB,kBAAQ;AAAA,QACZ,WACS,EAAE,SAAS,OAAO;AACvB,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,UAAI,SAAS,QAAQ;AACjB,wBAAgB,kCAAuE,SAAS,aAAa,SAAS,KAAK,CAAC;AAC5H;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,UAAU;AACd,MAAI,CAAC,QAAQ,QAAQ;AACjB,QAAI,QAAQ,QAAQ;AAChB,gBAAU;AAAA,IACd,WACS,QAAQ,YAAY;AACzB,UAAI,MAAM,KAAK,OAAK,EAAE,SAAS,KAAqB,2BAA2B,EAAE,IAAI,CAAC,GAAG;AACrF,kBAAU;AAAA,MACd;AAAA,IACJ,WACS,YAAY,KAAK,OAAO,OAAO,GAAG;AACvC,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,CAAC;AAAA,IACX,KAAK,aAAa,SAAS,KAAK;AAAA,IAChC,aAAa;AAAA,EACjB;AACJ;AACA,SAAS,YAAY,KAAK,OAAO,SAAS;AACtC,QAAM,UAAU,QAAQ;AACxB,MAAI,QAAQ,gBAAgB,GAAG,GAAG;AAC9B,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,eACR,SAAS,KAAK,GAAG,KACjB,gBAAgB,GAAG,KAClB,QAAQ,sBAAsB,QAAQ,mBAAmB,GAAG,KAC5D,QAAQ,eAAe,CAAC,QAAQ,YAAY,GAAG,GAAI;AACpD,WAAO;AAAA,EACX;AAGA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,IAAI,MAAM;AAChB,QAAI,EAAE,SAAS,GAAmB;AAC9B,UAAI,EAAE,SAAS,QAAQ,EAAE,OAAO;AAC5B,YAAI,EAAE,MAAM,QAAQ,WAAW,MAAM,GAAG;AACpC,iBAAO;AAAA,QACX,WACS,mBAAmB,0BAAuD,SAAS,EAAE,GAAG,GAAG;AAChG,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ,OACK;AAGD,UAAI,EAAE,SAAS,MAAM;AACjB,eAAO;AAAA,MACX,WAGA,EAAE,SAAS,UACP,cAAc,EAAE,KAAK,IAAI,KACzB,QACA,mBAAmB,0BAAuD,SAAS,EAAE,GAAG,GAAG;AAC3F,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,gBAAgB,SAAS,MAAM;AACpC,QAAM,QAAQ,CAAC;AACf,QAAM,iBAAiB,oBAAI,IAAI;AAC/B,SAAO,QAAQ,OAAO,SAAS,KAC3B,CAAC,WAAW,QAAQ,QAAQ,GAAG,KAC/B,CAAC,WAAW,QAAQ,QAAQ,IAAI,GAAG;AACnC,QAAI,WAAW,QAAQ,QAAQ,GAAG,GAAG;AACjC,gBAAU,SAAS,EAAkC;AACrD,gBAAU,SAAS,CAAC;AACpB,oBAAc,OAAO;AACrB;AAAA,IACJ;AACA,QAAI,SAAS,GAAa;AACtB,gBAAU,SAAS,CAA+B;AAAA,IACtD;AACA,UAAM,OAAO,eAAe,SAAS,cAAc;AAGnD,QAAI,KAAK,SAAS,KACd,KAAK,SACL,KAAK,SAAS,SAAS;AACvB,WAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,QAAQ,QAAQ,GAAG,EAAE,KAAK;AAAA,IACtE;AACA,QAAI,SAAS,GAAe;AACxB,YAAM,KAAK,IAAI;AAAA,IACnB;AACA,QAAI,kBAAkB,KAAK,QAAQ,MAAM,GAAG;AACxC,gBAAU,SAAS,EAA8C;AAAA,IACrE;AACA,kBAAc,OAAO;AAAA,EACzB;AACA,SAAO;AACX;AACA,SAAS,eAAe,SAAS,SAAS;AAEtC,QAAM,QAAQ,UAAU,OAAO;AAC/B,QAAM,QAAQ,kCAAkC,KAAK,QAAQ,MAAM;AACnE,QAAM,OAAO,MAAM;AACnB,MAAI,QAAQ,IAAI,IAAI,GAAG;AACnB,cAAU,SAAS,CAA2B;AAAA,EAClD;AACA,UAAQ,IAAI,IAAI;AAChB,MAAI,KAAK,OAAO,KAAK;AACjB,cAAU,SAAS,EAAqD;AAAA,EAC5E;AACA;AACI,UAAM,UAAU;AAChB,QAAI;AACJ,WAAQ,IAAI,QAAQ,KAAK,IAAI,GAAI;AAC7B,gBAAU,SAAS,IAAiD,EAAE,KAAK;AAAA,IAC/E;AAAA,EACJ;AACA,YAAU,SAAS,KAAK,MAAM;AAE9B,MAAI,QAAQ;AACZ,MAAI,iBAAiB,KAAK,QAAQ,MAAM,GAAG;AACvC,kBAAc,OAAO;AACrB,cAAU,SAAS,CAAC;AACpB,kBAAc,OAAO;AACrB,YAAQ,oBAAoB,OAAO;AACnC,QAAI,CAAC,OAAO;AACR,gBAAU,SAAS,EAAgC;AAAA,IACvD;AAAA,EACJ;AACA,QAAM,MAAM,aAAa,SAAS,KAAK;AACvC,MAAI,CAAC,QAAQ,UAAU,6BAA6B,KAAK,IAAI,GAAG;AAC5D,UAAMC,SAAQ,qEAAqE,KAAK,IAAI;AAC5F,QAAI,kBAAkB,WAAW,MAAM,GAAG;AAC1C,QAAI,UAAUA,OAAM,OACf,mBAAmB,WAAW,MAAM,GAAG,IAClC,SACA,WAAW,MAAM,GAAG,IAChB,OACA;AACd,QAAI;AACJ,QAAIA,OAAM,IAAI;AACV,YAAM,SAAS,YAAY;AAC3B,YAAM,cAAc,KAAK,YAAYA,OAAM,EAAE;AAC7C,YAAMC,OAAM,aAAa,SAAS,eAAe,SAAS,OAAO,WAAW,GAAG,eAAe,SAAS,OAAO,cAAcD,OAAM,GAAG,UAAW,UAAUA,OAAM,MAAO,IAAI,MAAM,CAAC;AAClL,UAAI,UAAUA,OAAM;AACpB,UAAI,WAAW;AACf,UAAI,QAAQ,WAAW,GAAG,GAAG;AACzB,mBAAW;AACX,YAAI,CAAC,QAAQ,SAAS,GAAG,GAAG;AACxB,oBAAU,SAAS,EAAiD;AACpE,oBAAU,QAAQ,MAAM,CAAC;AAAA,QAC7B,OACK;AACD,oBAAU,QAAQ,MAAM,GAAG,QAAQ,SAAS,CAAC;AAAA,QACjD;AAAA,MACJ,WACS,QAAQ;AAIb,mBAAWA,OAAM,MAAM;AAAA,MAC3B;AACA,YAAM;AAAA,QACF,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,WAAW,WACL,IACA;AAAA,QACN,KAAAC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,SAAS,MAAM,UAAU;AACzB,YAAM,WAAW,MAAM;AACvB,eAAS,MAAM;AACf,eAAS,MAAM;AACf,eAAS,MAAM,yBAAyB,SAAS,OAAO,MAAM,OAAO;AACrE,eAAS,SAAS,SAAS,OAAO,MAAM,GAAG,EAAE;AAAA,IACjD;AACA,UAAM,YAAYD,OAAM,KAAKA,OAAM,GAAG,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;AAC7D,QAAI;AACA,gBAAU,KAAK,MAAM;AAEzB,QAAI,YAAY,UAAU,KAAK;AAC3B,UAAI,UAAU,SAAS,MAAM,KACzB,mBAAmB,wBAAmD,SAAS,KAAK,IAAI,IAAI,MAAM,GAAG;AACrG,kBAAU;AACV,kBAAU,OAAO,UAAU,QAAQ,MAAM,GAAG,CAAC;AAAA,MACjD;AACA,UAA+C,UAAU,SAAS,MAAM,GAAG;AACvE,2BAAmB,wBAAmD,SAAS,GAAG;AAAA,MACtF;AAAA,IACJ;AACA,WAAO;AAAA,MACH,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK,SAAS;AAAA,QACV,MAAM;AAAA,QACN,SAAS,MAAM;AAAA,QACf,UAAU;AAAA,QAGV,WAAW;AAAA,QACX,KAAK,MAAM;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,CAAC,QAAQ,UAAU,WAAW,MAAM,IAAI,GAAG;AAC3C,cAAU,SAAS,EAAiC;AAAA,EACxD;AACA,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,OAAO,SAAS;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,MACf,KAAK,MAAM;AAAA,IACf;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,oBAAoB,SAAS;AAClC,QAAM,QAAQ,UAAU,OAAO;AAC/B,MAAI;AACJ,QAAM,QAAQ,QAAQ,OAAO;AAC7B,QAAM,WAAW,UAAU,OAAO,UAAU;AAC5C,MAAI,UAAU;AAEV,cAAU,SAAS,CAAC;AACpB,UAAM,WAAW,QAAQ,OAAO,QAAQ,KAAK;AAC7C,QAAI,aAAa,IAAI;AACjB,gBAAU,cAAc,SAAS,QAAQ,OAAO,QAAQ,CAAuB;AAAA,IACnF,OACK;AACD,gBAAU,cAAc,SAAS,UAAU,CAAuB;AAClE,gBAAU,SAAS,CAAC;AAAA,IACxB;AAAA,EACJ,OACK;AAED,UAAM,QAAQ,kBAAkB,KAAK,QAAQ,MAAM;AACnD,QAAI,CAAC,OAAO;AACR,aAAO;AAAA,IACX;AACA,UAAM,kBAAkB;AACxB,QAAI;AACJ,WAAQ,IAAI,gBAAgB,KAAK,MAAM,EAAE,GAAI;AACzC,gBAAU,SAAS,IAA2D,EAAE,KAAK;AAAA,IACzF;AACA,cAAU,cAAc,SAAS,MAAM,GAAG,QAAQ,CAAuB;AAAA,EAC7E;AACA,SAAO,EAAE,SAAS,UAAU,KAAK,aAAa,SAAS,KAAK,EAAE;AAClE;AACA,SAAS,mBAAmB,SAAS,MAAM;AACvC,QAAM,CAAC,MAAM,KAAK,IAAI,QAAQ,QAAQ;AACtC,QAAM,aAAa,QAAQ,OAAO,QAAQ,OAAO,KAAK,MAAM;AAC5D,MAAI,eAAe,IAAI;AACnB,cAAU,SAAS,EAAoC;AACvD,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,UAAU,OAAO;AAC/B,YAAU,SAAS,KAAK,MAAM;AAC9B,QAAM,aAAa,UAAU,OAAO;AACpC,QAAM,WAAW,UAAU,OAAO;AAClC,QAAM,mBAAmB,aAAa,KAAK;AAC3C,QAAM,aAAa,QAAQ,OAAO,MAAM,GAAG,gBAAgB;AAC3D,QAAM,iBAAiB,cAAc,SAAS,kBAAkB,IAAI;AACpE,QAAM,UAAU,eAAe,KAAK;AACpC,QAAM,cAAc,eAAe,QAAQ,OAAO;AAClD,MAAI,cAAc,GAAG;AACjB,gCAA4B,YAAY,YAAY,WAAW;AAAA,EACnE;AACA,QAAM,YAAY,oBAAoB,eAAe,SAAS,QAAQ,SAAS;AAC/E,8BAA4B,UAAU,YAAY,SAAS;AAC3D,YAAU,SAAS,MAAM,MAAM;AAC/B,SAAO;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MAEV,WAAW;AAAA,MACX;AAAA,MACA,KAAK,aAAa,SAAS,YAAY,QAAQ;AAAA,IACnD;AAAA,IACA,KAAK,aAAa,SAAS,KAAK;AAAA,EACpC;AACJ;AACA,SAAS,UAAU,SAAS,MAAM;AAC9B,QAAM,YAAY,SAAS,IAAgB,CAAC,KAAK,IAAI,CAAC,KAAK,QAAQ,QAAQ,WAAW,EAAE;AACxF,MAAI,WAAW,QAAQ,OAAO;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,UAAM,QAAQ,QAAQ,OAAO,QAAQ,UAAU,IAAI,CAAC;AACpD,QAAI,UAAU,MAAM,WAAW,OAAO;AAClC,iBAAW;AAAA,IACf;AAAA,EACJ;AACA,QAAM,QAAQ,UAAU,OAAO;AAC/B,QAAM,UAAU,cAAc,SAAS,UAAU,IAAI;AACrD,SAAO;AAAA,IACH,MAAM;AAAA,IACN;AAAA,IACA,KAAK,aAAa,SAAS,KAAK;AAAA,EACpC;AACJ;AAKA,SAAS,cAAc,SAAS,QAAQ,MAAM;AAC1C,QAAM,UAAU,QAAQ,OAAO,MAAM,GAAG,MAAM;AAC9C,YAAU,SAAS,MAAM;AACzB,MAAI,SAAS,KACT,SAAS,KACT,CAAC,QAAQ,SAAS,GAAG,GAAG;AACxB,WAAO;AAAA,EACX,OACK;AAED,WAAO,QAAQ,QAAQ,eAAe,SAAS,SAAS,CAAuB;AAAA,EACnF;AACJ;AACA,SAAS,UAAU,SAAS;AACxB,QAAM,EAAE,QAAQ,MAAM,OAAO,IAAI;AACjC,SAAO,EAAE,QAAQ,MAAM,OAAO;AAClC;AACA,SAAS,aAAa,SAAS,OAAO,KAAK;AACvC,QAAM,OAAO,UAAU,OAAO;AAC9B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,QAAQ,QAAQ,eAAe,MAAM,MAAM,QAAQ,IAAI,MAAM;AAAA,EACjE;AACJ;AACA,SAAS,KAAK,IAAI;AACd,SAAO,GAAG,GAAG,SAAS;AAC1B;AACA,SAAS,WAAW,QAAQ,cAAc;AACtC,SAAO,OAAO,WAAW,YAAY;AACzC;AACA,SAAS,UAAU,SAAS,oBAAoB;AAC5C,QAAM,EAAE,OAAO,IAAI;AACnB,8BAA4B,SAAS,QAAQ,kBAAkB;AAC/D,UAAQ,SAAS,OAAO,MAAM,kBAAkB;AACpD;AACA,SAAS,cAAc,SAAS;AAC5B,QAAM,QAAQ,gBAAgB,KAAK,QAAQ,MAAM;AACjD,MAAI,OAAO;AACP,cAAU,SAAS,MAAM,GAAG,MAAM;AAAA,EACtC;AACJ;AACA,SAAS,eAAe,SAAS,OAAO,oBAAoB;AACxD,SAAO,yBAAyB,OAAO,QAAQ,eAAe,MAAM,MAAM,QAAQ,kBAAkB,GAAG,kBAAkB;AAC7H;AACA,SAAS,UAAU,SAAS,MAAM,QAAQ,MAAM,UAAU,OAAO,GAAG;AAChE,MAAI,QAAQ;AACR,QAAI,UAAU;AACd,QAAI,UAAU;AAAA,EAClB;AACA,UAAQ,QAAQ,QAAQ,oBAAoB,MAAM;AAAA,IAC9C,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ;AAAA,EACZ,CAAC,CAAC;AACN;AACA,SAAS,MAAM,SAAS,MAAM,WAAW;AACrC,QAAM,IAAI,QAAQ;AAClB,UAAQ;AAAA,SACC;AACD,UAAI,WAAW,GAAG,IAAI,GAAG;AAErB,iBAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC5C,cAAI,qBAAqB,GAAG,UAAU,GAAG,GAAG,GAAG;AAC3C,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA;AAAA,SACC;AAAA,SACA,GAAiB;AAClB,YAAM,SAAS,KAAK,SAAS;AAC7B,UAAI,UAAU,qBAAqB,GAAG,OAAO,GAAG,GAAG;AAC/C,eAAO;AAAA,MACX;AACA;AAAA,IACJ;AAAA,SACK;AACD,UAAI,WAAW,GAAG,KAAK,GAAG;AACtB,eAAO;AAAA,MACX;AACA;AAAA;AAER,SAAO,CAAC;AACZ;AACA,SAAS,qBAAqB,QAAQ,KAAK;AACvC,SAAQ,WAAW,QAAQ,IAAI,KAC3B,OAAO,MAAM,GAAG,IAAI,IAAI,MAAM,EAAE,YAAY,MAAM,IAAI,YAAY,KAClE,gBAAgB,KAAK,OAAO,IAAI,IAAI,WAAW,GAAG;AAC1D;AAEA,SAAS,YAAY,MAAM,SAAS;AAChC;AAAA,IAAK;AAAA,IAAM;AAAA,IAGX,oBAAoB,MAAM,KAAK,SAAS,EAAE;AAAA,EAAC;AAC/C;AACA,SAAS,oBAAoB,MAAM,OAAO;AACtC,QAAM,EAAE,SAAS,IAAI;AACrB,SAAQ,SAAS,WAAW,KACxB,MAAM,SAAS,KACf,CAAC,aAAa,KAAK;AAC3B;AACA,SAAS,KAAK,MAAM,SAAS,iBAAiB,OAAO;AACjD,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,gBAAgB,SAAS;AAC/B,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAM,QAAQ,SAAS;AAEvB,QAAI,MAAM,SAAS,KACf,MAAM,YAAY,GAAiB;AACnC,YAAM,eAAe,iBACf,IACA,gBAAgB,OAAO,OAAO;AACpC,UAAI,eAAe,GAAsB;AACrC,YAAI,gBAAgB,GAAmB;AACnC,gBAAM,YAAY,YACd,MAAqB,OAAyC,mBAAmB;AACrF,gBAAM,cAAc,QAAQ,MAAM,MAAM,WAAW;AACnD;AACA;AAAA,QACJ;AAAA,MACJ,OACK;AAGD,cAAM,cAAc,MAAM;AAC1B,YAAI,YAAY,SAAS,IAAqB;AAC1C,gBAAM,OAAO,aAAa,WAAW;AACrC,eAAK,CAAC,QACF,SAAS,OACT,SAAS,MACT,8BAA8B,OAAO,OAAO,KACxC,GAAmB;AACvB,kBAAM,QAAQ,aAAa,KAAK;AAChC,gBAAI,OAAO;AACP,0BAAY,QAAQ,QAAQ,MAAM,KAAK;AAAA,YAC3C;AAAA,UACJ;AACA,cAAI,YAAY,cAAc;AAC1B,wBAAY,eAAe,QAAQ,MAAM,YAAY,YAAY;AAAA,UACrE;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,WACS,MAAM,SAAS,MACpB,gBAAgB,MAAM,SAAS,OAAO,KAAK,GAAmB;AAC9D,YAAM,cAAc,QAAQ,MAAM,MAAM,WAAW;AACnD;AAAA,IACJ;AAEA,QAAI,MAAM,SAAS,GAAiB;AAChC,YAAMD,eAAc,MAAM,YAAY;AACtC,UAAIA,cAAa;AACb,gBAAQ,OAAO;AAAA,MACnB;AACA,WAAK,OAAO,OAAO;AACnB,UAAIA,cAAa;AACb,gBAAQ,OAAO;AAAA,MACnB;AAAA,IACJ,WACS,MAAM,SAAS,IAAc;AAElC,WAAK,OAAO,SAAS,MAAM,SAAS,WAAW,CAAC;AAAA,IACpD,WACS,MAAM,SAAS,GAAY;AAChC,eAASG,KAAI,GAAGA,KAAI,MAAM,SAAS,QAAQA,MAAK;AAE5C,aAAK,MAAM,SAASA,KAAI,SAAS,MAAM,SAASA,IAAG,SAAS,WAAW,CAAC;AAAA,MAC5E;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,gBAAgB,QAAQ,gBAAgB;AACxC,YAAQ,eAAe,UAAU,SAAS,IAAI;AAAA,EAClD;AAEA,MAAI,gBACA,iBAAiB,iBACjB,KAAK,SAAS,KACd,KAAK,YAAY,KACjB,KAAK,eACL,KAAK,YAAY,SAAS,MAC1B,QAAQ,KAAK,YAAY,QAAQ,GAAG;AACpC,SAAK,YAAY,WAAW,QAAQ,MAAM,sBAAsB,KAAK,YAAY,QAAQ,CAAC;AAAA,EAC9F;AACJ;AACA,SAAS,gBAAgB,MAAM,SAAS;AACpC,QAAM,EAAE,cAAc,IAAI;AAC1B,UAAQ,KAAK;AAAA,SACJ;AACD,UAAI,KAAK,YAAY,GAAiB;AAClC,eAAO;AAAA,MACX;AACA,YAAM,SAAS,cAAc,IAAI,IAAI;AACrC,UAAI,WAAW,QAAW;AACtB,eAAO;AAAA,MACX;AACA,YAAM,cAAc,KAAK;AACzB,UAAI,YAAY,SAAS,IAAqB;AAC1C,eAAO;AAAA,MACX;AACA,UAAI,YAAY,WACZ,KAAK,QAAQ,SACb,KAAK,QAAQ,iBAAiB;AAC9B,eAAO;AAAA,MACX;AACA,YAAM,OAAO,aAAa,WAAW;AACrC,UAAI,CAAC,MAAM;AACP,YAAIC,cAAa;AAMjB,cAAM,qBAAqB,8BAA8B,MAAM,OAAO;AACtE,YAAI,uBAAuB,GAAsB;AAC7C,wBAAc,IAAI,MAAM,CAAoB;AAC5C,iBAAO;AAAA,QACX;AACA,YAAI,qBAAqBA,aAAY;AACjC,UAAAA,cAAa;AAAA,QACjB;AAEA,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,gBAAM,YAAY,gBAAgB,KAAK,SAAS,IAAI,OAAO;AAC3D,cAAI,cAAc,GAAsB;AACpC,0BAAc,IAAI,MAAM,CAAoB;AAC5C,mBAAO;AAAA,UACX;AACA,cAAI,YAAYA,aAAY;AACxB,YAAAA,cAAa;AAAA,UACjB;AAAA,QACJ;AAKA,YAAIA,cAAa,GAAwB;AACrC,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,kBAAM,IAAI,KAAK,MAAM;AACrB,gBAAI,EAAE,SAAS,KAAqB,EAAE,SAAS,UAAU,EAAE,KAAK;AAC5D,oBAAM,UAAU,gBAAgB,EAAE,KAAK,OAAO;AAC9C,kBAAI,YAAY,GAAsB;AAClC,8BAAc,IAAI,MAAM,CAAoB;AAC5C,uBAAO;AAAA,cACX;AACA,kBAAI,UAAUA,aAAY;AACtB,gBAAAA,cAAa;AAAA,cACjB;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAIA,YAAI,YAAY,SAAS;AAErB,mBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,kBAAM,IAAI,KAAK,MAAM;AACrB,gBAAI,EAAE,SAAS,GAAmB;AAC9B,4BAAc,IAAI,MAAM,CAAoB;AAC5C,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,kBAAQ,aAAa,UAAU;AAC/B,kBAAQ,aAAa,oBAAoB,QAAQ,OAAO,YAAY,WAAW,CAAC;AAChF,sBAAY,UAAU;AACtB,kBAAQ,OAAO,eAAe,QAAQ,OAAO,YAAY,WAAW,CAAC;AAAA,QACzE;AACA,sBAAc,IAAI,MAAMA,WAAU;AAClC,eAAOA;AAAA,MACX,OACK;AACD,sBAAc,IAAI,MAAM,CAAoB;AAC5C,eAAO;AAAA,MACX;AAAA,SACC;AAAA,SACA;AACD,aAAO;AAAA,SACN;AAAA,SACA;AAAA,SACA;AACD,aAAO;AAAA,SACN;AAAA,SACA;AACD,aAAO,gBAAgB,KAAK,SAAS,OAAO;AAAA,SAC3C;AACD,aAAO,KAAK;AAAA,SACX;AACD,UAAI,aAAa;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAM,QAAQ,KAAK,SAAS;AAC5B,YAAI,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG;AACpC;AAAA,QACJ;AACA,cAAM,YAAY,gBAAgB,OAAO,OAAO;AAChD,YAAI,cAAc,GAAsB;AACpC,iBAAO;AAAA,QACX,WACS,YAAY,YAAY;AAC7B,uBAAa;AAAA,QACjB;AAAA,MACJ;AACA,aAAO;AAAA;AAEP,UAAK;AAAwC;AAC7C,aAAO;AAAA;AAEnB;AAOA,SAAS,4BAA4B,OAAO,SAAS;AACjD,MAAI,MAAM,SAAS,MACf,CAAC,SAAS,MAAM,MAAM,KACtB,sBAAsB,IAAI,MAAM,MAAM,GAAG;AACzC,UAAM,MAAM,MAAM,UAAU;AAC5B,QAAI,IAAI,SAAS,GAA2B;AACxC,aAAO,gBAAgB,KAAK,OAAO;AAAA,IACvC,WACS,IAAI,SAAS,IAA6B;AAE/C,aAAO,4BAA4B,KAAK,OAAO;AAAA,IACnD;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,8BAA8B,MAAM,SAAS;AAClD,MAAI,aAAa;AACjB,QAAM,QAAQ,aAAa,IAAI;AAC/B,MAAI,SAAS,MAAM,SAAS,IAA+B;AACvD,UAAM,EAAE,WAAW,IAAI;AACvB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,YAAM,EAAE,KAAK,MAAM,IAAI,WAAW;AAClC,YAAM,UAAU,gBAAgB,KAAK,OAAO;AAC5C,UAAI,YAAY,GAAsB;AAClC,eAAO;AAAA,MACX;AACA,UAAI,UAAU,YAAY;AACtB,qBAAa;AAAA,MACjB;AACA,UAAI;AACJ,UAAI,MAAM,SAAS,GAA2B;AAC1C,oBAAY,gBAAgB,OAAO,OAAO;AAAA,MAC9C,WACS,MAAM,SAAS,IAA6B;AAIjD,oBAAY,4BAA4B,OAAO,OAAO;AAAA,MAC1D,OACK;AACD,oBAAY;AAAA,MAChB;AACA,UAAI,cAAc,GAAsB;AACpC,eAAO;AAAA,MACX;AACA,UAAI,YAAY,YAAY;AACxB,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,QAAM,cAAc,KAAK;AACzB,MAAI,YAAY,SAAS,IAAqB;AAC1C,WAAO,YAAY;AAAA,EACvB;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,QAAM,OAAO,KAAK;AAClB,SAAO,OAAO,SAAS,MAAM,EAAE,IAAI;AACvC;AAEA,SAAS,uBAAuB,MAAM,EAAE,WAAW,IAAI,oBAAoB,OAAO,aAAAC,eAAc,OAAO,gBAAgB,OAAO,iBAAiB,CAAC,GAAG,sBAAsB,CAAC,GAAG,iBAAiB,MAAM,qBAAqB,MAAM,kBAAkB,MAAM,oBAAoB,CAAC,GAAG,UAAU,MAAM,UAAU,MAAM,MAAM,OAAO,QAAQ,OAAO,aAAa,IAAI,kBAAkB,WAAW,SAAS,OAAO,OAAO,OAAO,UAAU,gBAAgB,SAAS,eAAe,aAAa,GAAG;AACrd,QAAM,YAAY,SAAS,QAAQ,SAAS,EAAE,EAAE,MAAM,iBAAiB;AACvE,QAAM,UAAU;AAAA,IAEZ,UAAU,aAAa,WAAW,SAAW,UAAU,EAAE,CAAC;AAAA,IAC1D;AAAA,IACA,aAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IAEA;AAAA,IACA,SAAS,oBAAI,IAAI;AAAA,IACjB,YAAY,oBAAI,IAAI;AAAA,IACpB,YAAY,oBAAI,IAAI;AAAA,IACpB,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,eAAe,oBAAI,IAAI;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa,uBAAO,OAAO,IAAI;AAAA,IAC/B,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,OAAO;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,SAAS;AAAA,IAET,OAAO,MAAM;AACT,YAAM,QAAQ,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAC3C,cAAQ,QAAQ,IAAI,MAAM,QAAQ,CAAC;AACnC,aAAO;AAAA,IACX;AAAA,IACA,aAAa,MAAM;AACf,YAAM,QAAQ,QAAQ,QAAQ,IAAI,IAAI;AACtC,UAAI,OAAO;AACP,cAAM,eAAe,QAAQ;AAC7B,YAAI,CAAC,cAAc;AACf,kBAAQ,QAAQ,OAAO,IAAI;AAAA,QAC/B,OACK;AACD,kBAAQ,QAAQ,IAAI,MAAM,YAAY;AAAA,QAC1C;AAAA,MACJ;AAAA,IACJ;AAAA,IACA,aAAa,MAAM;AACf,aAAO,IAAI,cAAc,QAAQ,OAAO,IAAI;AAAA,IAChD;AAAA,IACA,YAAY,MAAM;AAEd,UAAK,MAAwC;AACzC,YAAI,CAAC,QAAQ,aAAa;AACtB,gBAAM,IAAI,MAAM,yCAAyC;AAAA,QAC7D;AACA,YAAI,CAAC,QAAQ,QAAQ;AACjB,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC/C;AAAA,MACJ;AACA,cAAQ,OAAO,SAAS,QAAQ,cAAc,QAAQ,cAAc;AAAA,IACxE;AAAA,IACA,WAAW,MAAM;AACb,UAA+C,CAAC,QAAQ,QAAQ;AAC5D,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,YAAM,OAAO,QAAQ,OAAO;AAC5B,YAAM,eAAe,OACf,KAAK,QAAQ,IAAI,IACjB,QAAQ,cACJ,QAAQ,aACR;AAEV,UAA+C,eAAe,GAAG;AAC7D,cAAM,IAAI,MAAM,qDAAqD;AAAA,MACzE;AACA,UAAI,CAAC,QAAQ,SAAS,QAAQ,aAAa;AAEvC,gBAAQ,cAAc;AACtB,gBAAQ,cAAc;AAAA,MAC1B,OACK;AAED,YAAI,QAAQ,aAAa,cAAc;AACnC,kBAAQ;AACR,kBAAQ,cAAc;AAAA,QAC1B;AAAA,MACJ;AACA,cAAQ,OAAO,SAAS,OAAO,cAAc,CAAC;AAAA,IAClD;AAAA,IACA,eAAe,MAAM;AAAA,IAAE;AAAA,IACvB,eAAe,KAAK;AAAA,IACpB;AAAA,IACA,kBAAkB,KAAK;AAAA,IACvB;AAAA,IACA,MAAM,KAAK;AACP,UAAI,SAAS,GAAG;AACZ,cAAM,uBAAuB,GAAG;AACpC,cAAQ,OAAO,KAAK,GAAG;AACvB,YAAM,aAAa,uBAAuB,YAAY,QAAQ,OAAO,UAAU,OAAO,IAAI,KAAK,CAAiB;AAChH,iBAAW,UAAU;AACrB,aAAO;AAAA,IACX;AAAA,IACA,MAAM,KAAK,UAAU,OAAO;AACxB,aAAO,sBAAsB,QAAQ,UAAU,KAAK,OAAO;AAAA,IAC/D;AAAA,EACJ;AACA;AACI,YAAQ,UAAU,oBAAI,IAAI;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,UAAU,MAAM,SAAS;AAC9B,QAAM,UAAU,uBAAuB,MAAM,OAAO;AACpD,eAAa,MAAM,OAAO;AAC1B,MAAI,QAAQ,aAAa;AACrB,gBAAY,MAAM,OAAO;AAAA,EAC7B;AACA,MAAI,CAAC,QAAQ,KAAK;AACd,sBAAkB,MAAM,OAAO;AAAA,EACnC;AAEA,OAAK,UAAU,CAAC,GAAG,QAAQ,QAAQ,KAAK,CAAC;AACzC,OAAK,aAAa,CAAC,GAAG,QAAQ,UAAU;AACxC,OAAK,aAAa,CAAC,GAAG,QAAQ,UAAU;AACxC,OAAK,UAAU,QAAQ;AACvB,OAAK,SAAS,QAAQ;AACtB,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,QAAQ;AACtB;AACI,SAAK,UAAU,CAAC,GAAG,QAAQ,OAAO;AAAA,EACtC;AACJ;AACA,SAAS,kBAAkB,MAAM,SAAS;AACtC,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,EAAE,SAAS,IAAI;AACrB,MAAI,SAAS,WAAW,GAAG;AACvB,UAAM,QAAQ,SAAS;AAEvB,QAAI,oBAAoB,MAAM,KAAK,KAAK,MAAM,aAAa;AAGvD,YAAM,cAAc,MAAM;AAC1B,UAAI,YAAY,SAAS,IAAqB;AAC1C,kBAAU,aAAa,OAAO;AAAA,MAClC;AACA,WAAK,cAAc;AAAA,IACvB,OACK;AAID,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ,WACS,SAAS,SAAS,GAAG;AAE1B,QAAI,YAAY;AAChB,QAAI,gBAAgB,eAAe;AAGnC,QACI,SAAS,OAAO,OAAK,EAAE,SAAS,CAAe,EAAE,WAAW,GAAG;AAC/D,mBAAa;AACb,uBAAiB,KAAK,eAAe;AAAA,IACzC;AACA,SAAK,cAAc,gBAAgB,SAAS,OAAO,QAAQ,GAAG,QAAW,KAAK,UAAU,aAAc,OAAyC,OAAO,qBAAqB,KAAK,QAAW,QAAW,MAAM,QAAW,KAAuB;AAAA,EAClP;AACK;AACT;AACA,SAAS,iBAAiB,QAAQ,SAAS;AACvC,MAAI,IAAI;AACR,QAAM,cAAc,MAAM;AACtB;AAAA,EACJ;AACA,SAAO,IAAI,OAAO,SAAS,QAAQ,KAAK;AACpC,UAAM,QAAQ,OAAO,SAAS;AAC9B,QAAI,SAAS,KAAK;AACd;AACJ,YAAQ,SAAS;AACjB,YAAQ,aAAa;AACrB,YAAQ,gBAAgB;AACxB,iBAAa,OAAO,OAAO;AAAA,EAC/B;AACJ;AACA,SAAS,aAAa,MAAM,SAAS;AACjC,UAAQ,cAAc;AAEtB,QAAM,EAAE,eAAe,IAAI;AAC3B,QAAM,UAAU,CAAC;AACjB,WAASF,KAAI,GAAGA,KAAI,eAAe,QAAQA,MAAK;AAC5C,UAAM,SAAS,eAAeA,IAAG,MAAM,OAAO;AAC9C,QAAI,QAAQ;AACR,UAAI,QAAQ,MAAM,GAAG;AACjB,gBAAQ,KAAK,GAAG,MAAM;AAAA,MAC1B,OACK;AACD,gBAAQ,KAAK,MAAM;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,CAAC,QAAQ,aAAa;AAEtB;AAAA,IACJ,OACK;AAED,aAAO,QAAQ;AAAA,IACnB;AAAA,EACJ;AACA,UAAQ,KAAK;AAAA,SACJ;AACD,UAAI,CAAC,QAAQ,KAAK;AAGd,gBAAQ,OAAO,cAAc;AAAA,MACjC;AACA;AAAA,SACC;AAED,UAAI,CAAC,QAAQ,KAAK;AACd,gBAAQ,OAAO,iBAAiB;AAAA,MACpC;AACA;AAAA,SAEC;AACD,eAASA,KAAI,GAAGA,KAAI,KAAK,SAAS,QAAQA,MAAK;AAC3C,qBAAa,KAAK,SAASA,KAAI,OAAO;AAAA,MAC1C;AACA;AAAA,SACC;AAAA,SACA;AAAA,SACA;AAAA,SACA;AACD,uBAAiB,MAAM,OAAO;AAC9B;AAAA;AAGR,UAAQ,cAAc;AACtB,MAAI,IAAI,QAAQ;AAChB,SAAO,KAAK;AACR,YAAQ,GAAG;AAAA,EACf;AACJ;AACA,SAAS,mCAAmC,MAAM,IAAI;AAClD,QAAM,UAAU,SAAS,IAAI,IACvB,CAAC,MAAM,MAAM,OACb,CAAC,MAAM,KAAK,KAAK,CAAC;AACxB,SAAO,CAAC,MAAM,YAAY;AACtB,QAAI,KAAK,SAAS,GAAiB;AAC/B,YAAM,EAAE,MAAM,IAAI;AAGlB,UAAI,KAAK,YAAY,KAAoB,MAAM,KAAK,OAAO,GAAG;AAC1D;AAAA,MACJ;AACA,YAAM,UAAU,CAAC;AACjB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,cAAM,OAAO,MAAM;AACnB,YAAI,KAAK,SAAS,KAAqB,QAAQ,KAAK,IAAI,GAAG;AAIvD,gBAAM,OAAO,GAAG,CAAC;AACjB;AACA,gBAAM,SAAS,GAAG,MAAM,MAAM,OAAO;AACrC,cAAI;AACA,oBAAQ,KAAK,MAAM;AAAA,QAC3B;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAIA,SAAS,qBAAqB,KAAK,EAAE,OAAO,YAAY,oBAAoB,SAAS,UAAU,YAAY,OAAO,WAAW,qBAAqB,UAAU,MAAM,kBAAkB,OAAO,oBAAoB,OAAO,oBAAoB,OAAO,uBAAuB,uBAAuB,MAAM,OAAO,OAAO,OAAO,QAAQ,MAAM,GAAG;AACvU,QAAM,UAAU;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,IAAI,IAAI;AAAA,IAChB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,MAAM;AAAA,IACN,KAAK;AAAA,IACL,OAAO,KAAK;AACR,aAAO,IAAI,cAAc;AAAA,IAC7B;AAAA,IACA,KAAK,MAAM,MAAM;AACb,cAAQ,QAAQ;AAAA,IACpB;AAAA,IACA,SAAS;AACL,cAAQ,EAAE,QAAQ,WAAW;AAAA,IACjC;AAAA,IACA,SAAS,iBAAiB,OAAO;AAC7B,UAAI,gBAAgB;AAChB,UAAE,QAAQ;AAAA,MACd,OACK;AACD,gBAAQ,EAAE,QAAQ,WAAW;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,UAAU;AACN,cAAQ,QAAQ,WAAW;AAAA,IAC/B;AAAA,EACJ;AACA,WAAS,QAAQ,GAAG;AAChB,YAAQ,KAAK,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,EACtC;AACA,SAAO;AACX;AACA,SAAS,SAAS,KAAK,UAAU,CAAC,GAAG;AACjC,QAAM,UAAU,qBAAqB,KAAK,OAAO;AACjD,MAAI,QAAQ;AACR,YAAQ,iBAAiB,OAAO;AACpC,QAAM,EAAE,MAAM,MAAM,mBAAmB,QAAQ,UAAU,SAAS,SAAS,IAAI,IAAI;AACnF,QAAM,aAAa,IAAI,QAAQ,SAAS;AACxC,QAAM,eAAe,CAAC,qBAAqB,SAAS;AAIpD,QAAM,kBAAkB;AACxB;AACI,wBAAoB,KAAK,eAAe;AAAA,EAC5C;AAEA,QAAM,eAAe,MAAM,cAAc;AACzC,QAAM,OAAO,MAAM,CAAC,QAAQ,SAAS,WAAW,QAAQ,IAAI,CAAC,QAAQ,QAAQ;AAC7E,QAAM,YAAY,KAAK,KAAK,IAAI;AAChC;AACI,SAAK,YAAY,gBAAgB,cAAc;AAAA,EACnD;AACA,SAAO;AACP,MAAI,cAAc;AACd,SAAK,eAAe;AACpB,WAAO;AAGP,QAAI,YAAY;AACZ,WAAK,WAAW,IAAI,QAAQ,IAAI,WAAW,EAAE,KAAK,IAAI,YAAY;AAClE,WAAK;AAAA,CAAI;AACT,cAAQ;AAAA,IACZ;AAAA,EACJ;AAEA,MAAI,IAAI,WAAW,QAAQ;AACvB,cAAU,IAAI,YAAY,aAAa,OAAO;AAC9C,QAAI,IAAI,WAAW,UAAU,IAAI,QAAQ,GAAG;AACxC,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,IAAI,WAAW,QAAQ;AACvB,cAAU,IAAI,YAAY,aAAa,OAAO;AAC9C,QAAI,IAAI,QAAQ,GAAG;AACf,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,MAAI,IAAI,WAAW,IAAI,QAAQ,QAAQ;AACnC,YAAQ;AACR,cAAU,IAAI,SAAS,UAAU,OAAO;AACxC,YAAQ;AAAA,EACZ;AACA,MAAI,IAAI,QAAQ,GAAG;AACf,SAAK,MAAM;AACX,aAAS,IAAI,GAAG,IAAI,IAAI,OAAO,KAAK;AAChC,WAAK,GAAG,IAAI,IAAI,OAAO,UAAU,GAAG;AAAA,IACxC;AAAA,EACJ;AACA,MAAI,IAAI,WAAW,UAAU,IAAI,WAAW,UAAU,IAAI,OAAO;AAC7D,SAAK;AAAA,CAAI;AACT,YAAQ;AAAA,EACZ;AAEA,MAAI,CAAC,KAAK;AACN,SAAK,SAAS;AAAA,EAClB;AACA,MAAI,IAAI,aAAa;AACjB,YAAQ,IAAI,aAAa,OAAO;AAAA,EACpC,OACK;AACD,SAAK,MAAM;AAAA,EACf;AACA,MAAI,cAAc;AACd,aAAS;AACT,SAAK,GAAG;AAAA,EACZ;AACA,WAAS;AACT,OAAK,GAAG;AACR,SAAO;AAAA,IACH;AAAA,IACA,MAAM,QAAQ;AAAA,IACd,UAAU;AAAA,IAEV,KAAK,QAAQ,MAAM,QAAQ,IAAI,OAAO,IAAI;AAAA,EAC9C;AACJ;AACA,SAAS,oBAAoB,KAAK,SAAS;AACvC,QAAM,EAAE,KAAK,mBAAmB,MAAM,SAAS,mBAAmB,mBAAmB,qBAAqB,IAAI;AAC9G,QAAM,aAAa;AAKnB,MAAI,IAAI,QAAQ,SAAS,GAAG;AACxB;AAGI,WAAK,gBAAgB;AAAA,CAAc;AAInC,UAAI,IAAI,OAAO,QAAQ;AACnB,cAAM,gBAAgB;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,EACK,OAAO,YAAU,IAAI,QAAQ,SAAS,MAAM,CAAC,EAC7C,IAAI,WAAW,EACf,KAAK,IAAI;AACd,aAAK,WAAW;AAAA,CAA0B;AAAA,MAC9C;AAAA,IACJ;AAAA,EACJ;AACA,YAAU,IAAI,QAAQ,OAAO;AAC7B,UAAQ;AACR,OAAK,SAAS;AAClB;AACA,SAAS,UAAU,QAAQ,MAAM,EAAE,QAAQ,MAAM,SAAS,KAAK,GAAG;AAC9D,QAAM,WAAW,OAAO,SAAS,WAC3B,iBACA,SAAS,cACL,oBACA,iBAAiB;AAC3B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,QAAI,KAAK,OAAO;AAEhB,UAAM,qBAAqB,GAAG,SAAS,QAAQ;AAC/C,QAAI,oBAAoB;AACpB,WAAK,GAAG,MAAM,GAAG,EAAE;AAAA,IACvB;AACA,SAAK,SAAS,eAAe,IAAI,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,IAAI,qBAAqB,WAAW,MAAM,OAAO,MAAM,IAAI;AACpI,QAAI,IAAI,OAAO,SAAS,GAAG;AACvB,cAAQ;AAAA,IACZ;AAAA,EACJ;AACJ;AACA,SAAS,UAAU,QAAQ,SAAS;AAChC,MAAI,CAAC,OAAO,QAAQ;AAChB;AAAA,EACJ;AACA,UAAQ,OAAO;AACf,QAAM,EAAE,MAAM,SAAS,QAAQ,SAAS,KAAK,IAAI;AACjD,UAAQ;AACR,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAM,MAAM,OAAO;AACnB,QAAI,KAAK;AACL,WAAK,kBAAkB,IAAI,OAAO,IAAI;AACtC,cAAQ,KAAK,OAAO;AACpB,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,UAAQ,OAAO;AACnB;AACA,SAAS,SAAS,GAAG;AACjB,SAAQ,SAAS,CAAC,KACd,EAAE,SAAS,KACX,EAAE,SAAS,KACX,EAAE,SAAS,KACX,EAAE,SAAS;AACnB;AACA,SAAS,mBAAmB,OAAO,SAAS;AACxC,QAAM,aAAa,MAAM,SAAS,KACgB,MAAM,KAAK,OAAK,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC5F,UAAQ,KAAK,GAAG;AAChB,gBAAc,QAAQ,OAAO;AAC7B,cAAY,OAAO,SAAS,UAAU;AACtC,gBAAc,QAAQ,SAAS;AAC/B,UAAQ,KAAK,GAAG;AACpB;AACA,SAAS,YAAY,OAAO,SAAS,aAAa,OAAO,QAAQ,MAAM;AACnE,QAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM;AACnB,QAAI,SAAS,IAAI,GAAG;AAChB,WAAK,IAAI;AAAA,IACb,WACS,QAAQ,IAAI,GAAG;AACpB,yBAAmB,MAAM,OAAO;AAAA,IACpC,OACK;AACD,cAAQ,MAAM,OAAO;AAAA,IACzB;AACA,QAAI,IAAI,MAAM,SAAS,GAAG;AACtB,UAAI,YAAY;AACZ,iBAAS,KAAK,GAAG;AACjB,gBAAQ;AAAA,MACZ,OACK;AACD,iBAAS,KAAK,IAAI;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,QAAQ,MAAM,SAAS;AAC5B,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,KAAK,IAAI;AACjB;AAAA,EACJ;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,KAAK,QAAQ,OAAO,IAAI,CAAC;AACjC;AAAA,EACJ;AACA,UAAQ,KAAK;AAAA,SACJ;AAAA,SACA;AAAA,SACA;AACD,MACI,OAAO,KAAK,eAAe,MAAM,sFACQ;AAC7C,cAAQ,KAAK,aAAa,OAAO;AACjC;AAAA,SACC;AACD,cAAQ,MAAM,OAAO;AACrB;AAAA,SACC;AACD,oBAAc,MAAM,OAAO;AAC3B;AAAA,SACC;AACD,uBAAiB,MAAM,OAAO;AAC9B;AAAA,SACC;AACD,cAAQ,KAAK,aAAa,OAAO;AACjC;AAAA,SACC;AACD,4BAAsB,MAAM,OAAO;AACnC;AAAA,SACC;AACD,iBAAW,MAAM,OAAO;AACxB;AAAA,SACC;AACD,mBAAa,MAAM,OAAO;AAC1B;AAAA,SACC;AACD,wBAAkB,MAAM,OAAO;AAC/B;AAAA,SACC;AACD,0BAAoB,MAAM,OAAO;AACjC;AAAA,SACC;AACD,yBAAmB,MAAM,OAAO;AAChC;AAAA,SACC;AACD,4BAAsB,MAAM,OAAO;AACnC;AAAA,SACC;AACD,+BAAyB,MAAM,OAAO;AACtC;AAAA,SACC;AACD,yBAAmB,MAAM,OAAO;AAChC;AAAA,SACC;AACD,kBAAY,KAAK,MAAM,SAAS,MAAM,KAAK;AAC3C;AAAA,SAEC;AACD;AAAA,SACC;AACD;AAAA,SACC;AACD;AAAA,SACC;AACD;AAAA,SACC;AACD;AAAA,SAEC;AAED;AAAA;AAEA,UAAK,MAAwC;AACzC,eAAO,OAAO,gCAAgC,KAAK,MAAM;AAEzD,cAAM,kBAAkB;AACxB,eAAO;AAAA,MACX;AAAA;AAEZ;AACA,SAAS,QAAQ,MAAM,SAAS;AAC5B,UAAQ,KAAK,KAAK,UAAU,KAAK,OAAO,GAAG,IAAI;AACnD;AACA,SAAS,cAAc,MAAM,SAAS;AAClC,QAAM,EAAE,SAAS,SAAS,IAAI;AAC9B,UAAQ,KAAK,WAAW,KAAK,UAAU,OAAO,IAAI,SAAS,IAAI;AACnE;AACA,SAAS,iBAAiB,MAAM,SAAS;AACrC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,MAAI;AACA,SAAK,eAAe;AACxB,OAAK,GAAG,OAAO,iBAAiB,IAAI;AACpC,UAAQ,KAAK,SAAS,OAAO;AAC7B,OAAK,GAAG;AACZ;AACA,SAAS,sBAAsB,MAAM,SAAS;AAC1C,WAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,UAAM,QAAQ,KAAK,SAAS;AAC5B,QAAI,SAAS,KAAK,GAAG;AACjB,cAAQ,KAAK,KAAK;AAAA,IACtB,OACK;AACD,cAAQ,OAAO,OAAO;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,SAAS,2BAA2B,MAAM,SAAS;AAC/C,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,KAAK,SAAS,GAA6B;AAC3C,SAAK,GAAG;AACR,0BAAsB,MAAM,OAAO;AACnC,SAAK,GAAG;AAAA,EACZ,WACS,KAAK,UAAU;AAEpB,UAAM,OAAO,mBAAmB,KAAK,OAAO,IACtC,KAAK,UACL,KAAK,UAAU,KAAK,OAAO;AACjC,SAAK,MAAM,IAAI;AAAA,EACnB,OACK;AACD,SAAK,IAAI,KAAK,YAAY,IAAI;AAAA,EAClC;AACJ;AACA,SAAS,WAAW,MAAM,SAAS;AAC/B,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,MAAI,MAAM;AACN,SAAK,eAAe;AAAA,EACxB;AACA,OAAK,GAAG,OAAO,cAAc,KAAK,KAAK,UAAU,KAAK,OAAO,MAAM,IAAI;AAC3E;AACA,SAAS,aAAa,MAAM,SAAS;AACjC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,QAAM,EAAE,KAAK,OAAO,UAAU,WAAW,cAAc,YAAY,SAAS,iBAAiB,aAAAH,aAAY,IAAI;AAC7G,MAAI,YAAY;AACZ,SAAK,OAAO,eAAe,IAAI,GAAG;AAAA,EACtC;AACA,MAAI,SAAS;AACT,SAAK,IAAI,OAAO,UAAU,KAAK,kBAAkB,SAAS,OAAO;AAAA,EACrE;AACA,MAAI,MAAM;AACN,SAAK,eAAe;AAAA,EACxB;AACA,QAAM,aAAa,UACb,oBAAoB,QAAQ,OAAOA,YAAW,IAC9C,eAAe,QAAQ,OAAOA,YAAW;AAC/C,OAAK,OAAO,UAAU,IAAI,KAAK,IAAI;AACnC,cAAY,gBAAgB,CAAC,KAAK,OAAO,UAAU,WAAW,YAAY,CAAC,GAAG,OAAO;AACrF,OAAK,GAAG;AACR,MAAI,SAAS;AACT,SAAK,GAAG;AAAA,EACZ;AACA,MAAI,YAAY;AACZ,SAAK,IAAI;AACT,YAAQ,YAAY,OAAO;AAC3B,SAAK,GAAG;AAAA,EACZ;AACJ;AACA,SAAS,gBAAgB,MAAM;AAC3B,MAAI,IAAI,KAAK;AACb,SAAO,KAAK;AACR,QAAI,KAAK,MAAM;AACX;AAAA,EACR;AACA,SAAO,KAAK,MAAM,GAAG,IAAI,CAAC,EAAE,IAAI,SAAO,OAAO,MAAM;AACxD;AAEA,SAAS,kBAAkB,MAAM,SAAS;AACtC,QAAM,EAAE,MAAM,QAAQ,KAAK,IAAI;AAC/B,QAAM,SAAS,SAAS,KAAK,MAAM,IAAI,KAAK,SAAS,OAAO,KAAK,MAAM;AACvE,MAAI,MAAM;AACN,SAAK,eAAe;AAAA,EACxB;AACA,OAAK,SAAS,KAAK,IAAI;AACvB,cAAY,KAAK,WAAW,OAAO;AACnC,OAAK,GAAG;AACZ;AACA,SAAS,oBAAoB,MAAM,SAAS;AACxC,QAAM,EAAE,MAAM,QAAQ,UAAU,QAAQ,IAAI;AAC5C,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI,CAAC,WAAW,QAAQ;AACpB,SAAK,MAAM,IAAI;AACf;AAAA,EACJ;AACA,QAAM,aAAa,WAAW,SAAS,KAE/B,WAAW,KAAK,OAAK,EAAE,MAAM,SAAS,CAAyB;AACvE,OAAK,aAAa,MAAM,IAAI;AAC5B,gBAAc,OAAO;AACrB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAM,EAAE,KAAK,MAAM,IAAI,WAAW;AAElC,+BAA2B,KAAK,OAAO;AACvC,SAAK,IAAI;AAET,YAAQ,OAAO,OAAO;AACtB,QAAI,IAAI,WAAW,SAAS,GAAG;AAE3B,WAAK,GAAG;AACR,cAAQ;AAAA,IACZ;AAAA,EACJ;AACA,gBAAc,SAAS;AACvB,OAAK,aAAa,MAAM,IAAI;AAChC;AACA,SAAS,mBAAmB,MAAM,SAAS;AACvC,qBAAmB,KAAK,UAAU,OAAO;AAC7C;AACA,SAAS,sBAAsB,MAAM,SAAS;AAC1C,QAAM,EAAE,MAAM,QAAQ,SAAS,IAAI;AACnC,QAAM,EAAE,QAAQ,SAAS,MAAM,SAAS,OAAO,IAAI;AACnD,MAAI,QAAQ;AAER,SAAK,IAAI,cAAc,YAAY;AAAA,EACvC;AACA,OAAK,KAAK,IAAI;AACd,MAAI,QAAQ,MAAM,GAAG;AACjB,gBAAY,QAAQ,OAAO;AAAA,EAC/B,WACS,QAAQ;AACb,YAAQ,QAAQ,OAAO;AAAA,EAC3B;AACA,OAAK,OAAO;AACZ,MAAI,WAAW,MAAM;AACjB,SAAK,GAAG;AACR,WAAO;AAAA,EACX;AACA,MAAI,SAAS;AACT,QAAI,SAAS;AACT,WAAK,SAAS;AAAA,IAClB;AACA,QAAI,QAAQ,OAAO,GAAG;AAClB,yBAAmB,SAAS,OAAO;AAAA,IACvC,OACK;AACD,cAAQ,SAAS,OAAO;AAAA,IAC5B;AAAA,EACJ,WACS,MAAM;AACX,YAAQ,MAAM,OAAO;AAAA,EACzB;AACA,MAAI,WAAW,MAAM;AACjB,aAAS;AACT,SAAK,GAAG;AAAA,EACZ;AACA,MAAI,QAAQ;AACR,QAAI,KAAK,iBAAiB;AACtB,WAAK,mBAAmB;AAAA,IAC5B;AACA,SAAK,GAAG;AAAA,EACZ;AACJ;AACA,SAAS,yBAAyB,MAAM,SAAS;AAC7C,QAAM,EAAE,MAAM,YAAY,WAAW,SAAS,YAAY,IAAI;AAC9D,QAAM,EAAE,MAAM,QAAQ,UAAU,QAAQ,IAAI;AAC5C,MAAI,KAAK,SAAS,GAA2B;AACzC,UAAM,cAAc,CAAC,mBAAmB,KAAK,OAAO;AACpD,mBAAe,KAAK,GAAG;AACvB,kBAAc,MAAM,OAAO;AAC3B,mBAAe,KAAK,GAAG;AAAA,EAC3B,OACK;AACD,SAAK,GAAG;AACR,YAAQ,MAAM,OAAO;AACrB,SAAK,GAAG;AAAA,EACZ;AACA,iBAAe,OAAO;AACtB,UAAQ;AACR,iBAAe,KAAK,GAAG;AACvB,OAAK,IAAI;AACT,UAAQ,YAAY,OAAO;AAC3B,UAAQ;AACR,iBAAe,QAAQ;AACvB,iBAAe,KAAK,GAAG;AACvB,OAAK,IAAI;AACT,QAAM,WAAW,UAAU,SAAS;AACpC,MAAI,CAAC,UAAU;AACX,YAAQ;AAAA,EACZ;AACA,UAAQ,WAAW,OAAO;AAC1B,MAAI,CAAC,UAAU;AACX,YAAQ;AAAA,EACZ;AACA,iBAAe,SAAS,IAA0B;AACtD;AACA,SAAS,mBAAmB,MAAM,SAAS;AACvC,QAAM,EAAE,MAAM,QAAQ,QAAQ,UAAU,QAAQ,IAAI;AACpD,OAAK,UAAU,KAAK,aAAa;AACjC,MAAI,KAAK,SAAS;AACd,WAAO;AACP,SAAK,GAAG,OAAO,kBAAkB,QAAQ;AACzC,YAAQ;AAAA,EACZ;AACA,OAAK,UAAU,KAAK,WAAW;AAC/B,UAAQ,KAAK,OAAO,OAAO;AAC3B,MAAI,KAAK,SAAS;AACd,SAAK,GAAG;AACR,YAAQ;AACR,SAAK,GAAG,OAAO,kBAAkB,OAAO;AACxC,YAAQ;AACR,SAAK,UAAU,KAAK,QAAQ;AAC5B,aAAS;AAAA,EACb;AACA,OAAK,GAAG;AACZ;AAEA,SAAS,gBAAgB,MAAM,cAAc,aAAa,OAAO,cAAc,CAAC,GAAG,WAAW,uBAAO,OAAO,IAAI,GAAG;AAC/G;AACI;AAAA,EACJ;AACJ;AACA,SAAS,uBAAuB,IAAI,QAAQ,aAAa;AACrD;AACI,WAAO;AAAA,EACX;AACJ;AACA,SAAS,0BAA0B,QAAQ,aAAa;AACpD,MAAI,WACC,OAAO,SAAS,oBAAoB,OAAO,SAAS,iBAAiB;AACtE,QAAI,IAAI,YAAY;AACpB,WAAO,KAAK;AACR,YAAM,IAAI,YAAY;AACtB,UAAI,EAAE,SAAS,wBAAwB;AACnC,eAAO;AAAA,MACX,WACS,EAAE,SAAS,oBAAoB,CAAC,EAAE,KAAK,SAAS,SAAS,GAAG;AACjE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,MAAM,SAAS;AACvC,aAAW,KAAK,KAAK,QAAQ;AACzB,eAAW,MAAM,mBAAmB,CAAC,GAAG;AACpC,cAAQ,EAAE;AAAA,IACd;AAAA,EACJ;AACJ;AACA,SAAS,sBAAsB,OAAO,SAAS;AAC3C,aAAW,QAAQ,MAAM,MAAM;AAC3B,QAAI,KAAK,SAAS,uBAAuB;AACrC,UAAI,KAAK;AACL;AACJ,iBAAW,QAAQ,KAAK,cAAc;AAClC,mBAAW,MAAM,mBAAmB,KAAK,EAAE,GAAG;AAC1C,kBAAQ,EAAE;AAAA,QACd;AAAA,MACJ;AAAA,IACJ,WACS,KAAK,SAAS,yBACnB,KAAK,SAAS,oBAAoB;AAClC,UAAI,KAAK,WAAW,CAAC,KAAK;AACtB;AACJ,cAAQ,KAAK,EAAE;AAAA,IACnB;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,OAAO,QAAQ,CAAC,GAAG;AAC3C,UAAQ,MAAM;AAAA,SACL;AACD,YAAM,KAAK,KAAK;AAChB;AAAA,SACC;AACD,UAAI,SAAS;AACb,aAAO,OAAO,SAAS,oBAAoB;AACvC,iBAAS,OAAO;AAAA,MACpB;AACA,YAAM,KAAK,MAAM;AACjB;AAAA,SACC;AACD,iBAAW,QAAQ,MAAM,YAAY;AACjC,YAAI,KAAK,SAAS,eAAe;AAC7B,6BAAmB,KAAK,UAAU,KAAK;AAAA,QAC3C,OACK;AACD,6BAAmB,KAAK,OAAO,KAAK;AAAA,QACxC;AAAA,MACJ;AACA;AAAA,SACC;AACD,YAAM,SAAS,QAAQ,aAAW;AAC9B,YAAI;AACA,6BAAmB,SAAS,KAAK;AAAA,MACzC,CAAC;AACD;AAAA,SACC;AACD,yBAAmB,MAAM,UAAU,KAAK;AACxC;AAAA,SACC;AACD,yBAAmB,MAAM,MAAM,KAAK;AACpC;AAAA;AAER,SAAO;AACX;AAyBA,SAAS,0BAA0B,MAAM,SAAS,WAAW,OAAO,kBAAkB,OAAO;AACzF,QAAM,MAAM,KAAK;AAGjB,MAAI,CAAC,IAAI,KAAK,GAAG;AACb;AAAA,EACJ;AACA,MAAI;AACA,QAAI,SAAS,kBACP,IAAI,SACJ,UAAU,WAAW,IAAI,eAAe,IAAI,QAAQ;AAAA,EAC9D,SACO,GAAP;AACI,QAAI,UAAU,EAAE;AAChB,UAAM,eAAe,IAChB,QAAQ,eAAe,EAAE,EACzB,MAAM,mBAAmB;AAC9B,QAAI,cAAc;AACd,gBAAU,qDAAqD,aAAa;AAAA,IAChF;AACA,YAAQ,QAAQ,oBAAoB,IAA+B,KAAK,KAAK,QAAW,OAAO,CAAC;AAAA,EACpG;AACJ;AAiCA,SAAS,kBAAkB,MAAM,SAGjC,WAAW,OAEX,kBAAkB,OAAO,YAAY,OAAO,OAAO,QAAQ,WAAW,GAAG;AACrE;AACI,QAAK,MAAwC;AAEzC,gCAA0B,MAAM,SAAS,UAAU,eAAe;AAAA,IACtE;AACA,WAAO;AAAA,EACX;AACJ;AA+BA,SAAS,UAAU,MAAM,KAAK,SAAS,gBAAgB;AACnD,MAAI,IAAI,SAAS,WACZ,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,QAAQ,KAAK,IAAI;AACvC,UAAM,MAAM,IAAI,MAAM,IAAI,IAAI,MAAM,KAAK;AACzC,YAAQ,QAAQ,oBAAoB,IAA+B,IAAI,GAAG,CAAC;AAC3E,QAAI,MAAM,uBAAuB,QAAQ,OAAO,GAAG;AAAA,EACvD;AACA,MAAuD,IAAI,KAAK;AAC5D,8BAA0B,IAAI,KAAK,OAAO;AAAA,EAC9C;AACA,MAAI,IAAI,SAAS,MAAM;AACnB,UAAM,SAAS,eAAe,MAAM,GAAG;AACvC,UAAM,SAAS;AAAA,MACX,MAAM;AAAA,MACN,KAAK,KAAK;AAAA,MACV,UAAU,CAAC,MAAM;AAAA,IACrB;AACA,YAAQ,YAAY,MAAM;AAC1B,QAAI,gBAAgB;AAChB,aAAO,eAAe,QAAQ,QAAQ,IAAI;AAAA,IAC9C;AAAA,EACJ,OACK;AAED,UAAM,WAAW,QAAQ,OAAO;AAChC,UAAM,WAAW,CAAC;AAClB,QAAI,IAAI,SAAS,QAAQ,IAAI;AAC7B,WAAO,OAAO,IAAI;AACd,YAAM,UAAU,SAAS;AACzB,UAA+C,WAAW,QAAQ,SAAS,GAAiB;AACxF,gBAAQ,WAAW,OAAO;AAC1B,iBAAS,QAAQ,OAAO;AACxB;AAAA,MACJ;AACA,UAAI,WACA,QAAQ,SAAS,KACjB,CAAC,QAAQ,QAAQ,KAAK,EAAE,QAAQ;AAChC,gBAAQ,WAAW,OAAO;AAC1B;AAAA,MACJ;AACA,UAAI,WAAW,QAAQ,SAAS,GAAY;AAExC,YAAI,IAAI,SAAS,aACb,QAAQ,SAAS,QAAQ,SAAS,SAAS,GAAG,cAAc,QAAW;AACvE,kBAAQ,QAAQ,oBAAoB,IAAkC,KAAK,GAAG,CAAC;AAAA,QACnF;AAEA,gBAAQ,WAAW;AACnB,cAAM,SAAS,eAAe,MAAM,GAAG;AACvC,YACI,SAAS,UAET,EAAE,QAAQ,UACN,QAAQ,OAAO,SAAS,KACxB,cAAc,QAAQ,OAAO,KAAK,YAAY,IAAI;AACtD,iBAAO,WAAW,CAAC,GAAG,UAAU,GAAG,OAAO,QAAQ;AAAA,QACtD;AAEA,YAAK,MAAiD;AAClD,gBAAM,MAAM,OAAO;AACnB,cAAI,KAAK;AACL,oBAAQ,SAAS,QAAQ,CAAC,EAAE,QAAQ,MAAM;AACtC,kBAAI,UAAU,SAAS,GAAG,GAAG;AACzB,wBAAQ,QAAQ,oBAAoB,IAA0B,OAAO,QAAQ,GAAG,CAAC;AAAA,cACrF;AAAA,YACJ,CAAC;AAAA,UACL;AAAA,QACJ;AACA,gBAAQ,SAAS,KAAK,MAAM;AAC5B,cAAM,SAAS,kBAAkB,eAAe,SAAS,QAAQ,KAAK;AAGtE,qBAAa,QAAQ,OAAO;AAE5B,YAAI;AACA,iBAAO;AAGX,gBAAQ,cAAc;AAAA,MAC1B,OACK;AACD,gBAAQ,QAAQ,oBAAoB,IAAkC,KAAK,GAAG,CAAC;AAAA,MACnF;AACA;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,MAAM,KAAK;AAC/B,QAAM,eAAe,KAAK,YAAY;AACtC,SAAO;AAAA,IACH,MAAM;AAAA,IACN,KAAK,KAAK;AAAA,IACV,WAAW,IAAI,SAAS,SAAS,SAAY,IAAI;AAAA,IACjD,UAAU,gBAAgB,CAAC,QAAQ,MAAM,KAAK,IAAI,KAAK,WAAW,CAAC,IAAI;AAAA,IACvE,SAAS,SAAS,MAAM,KAAK;AAAA,IAC7B;AAAA,EACJ;AACJ;AACA,SAAS,2BAA2B,QAAQ,UAAU,SAAS;AAC3D,MAAI,OAAO,WAAW;AAClB,WAAO;AAAA,MAA4B,OAAO;AAAA,MAAW,0BAA0B,QAAQ,UAAU,OAAO;AAAA,MAGxG,qBAAqB,QAAQ,OAAO,cAAc,GAAG;AAAA,QAChD,OAAyC,WAAW;AAAA,QACrD;AAAA,MACJ,CAAC;AAAA,IAAC;AAAA,EACN,OACK;AACD,WAAO,0BAA0B,QAAQ,UAAU,OAAO;AAAA,EAC9D;AACJ;AACA,SAAS,0BAA0B,QAAQ,UAAU,SAAS;AAC1D,QAAM,EAAE,OAAO,IAAI;AACnB,QAAM,cAAc,qBAAqB,OAAO,uBAAuB,GAAG,YAAY,OAAO,SAAS,CAAiB,CAAC;AACxH,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,aAAa,SAAS;AAC5B,QAAM,sBAAsB,SAAS,WAAW,KAAK,WAAW,SAAS;AACzE,MAAI,qBAAqB;AACrB,QAAI,SAAS,WAAW,KAAK,WAAW,SAAS,IAAc;AAE3D,YAAM,YAAY,WAAW;AAC7B,iBAAW,WAAW,aAAa,OAAO;AAC1C,aAAO;AAAA,IACX,OACK;AACD,UAAI,YAAY;AAChB,UAAI,gBAAgB,eAAe;AAGnC,UACI,CAAC,OAAO,gBACR,SAAS,OAAO,OAAK,EAAE,SAAS,CAAe,EAAE,WAAW,GAAG;AAC/D,qBAAa;AACb,yBAAiB,KAAK,eAAe;AAAA,MACzC;AACA,aAAO,gBAAgB,SAAS,OAAO,QAAQ,GAAG,uBAAuB,CAAC,WAAW,CAAC,GAAG,UAAU,aAAc,OAAyC,OAAO,qBAAqB,KAAK,QAAW,QAAW,MAAM,OAAO,OAAyB,OAAO,GAAG;AAAA,IACrQ;AAAA,EACJ,OACK;AACD,UAAM,MAAM,WAAW;AACvB,UAAM,YAAY,mBAAmB,GAAG;AAExC,QAAI,UAAU,SAAS,IAAqB;AACxC,gBAAU,WAAW,OAAO;AAAA,IAChC;AAEA,eAAW,WAAW,aAAa,OAAO;AAC1C,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,GAAG,GAAG;AACrB,MAAI,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM;AACzB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,GAAmB;AAC9B,QAAI,EAAE,MAAM,YAAY,EAAE,MAAM,SAAS;AACrC,aAAO;AAAA,IACX;AAAA,EACJ,OACK;AAED,UAAM,MAAM,EAAE;AACd,UAAM,YAAY,EAAE;AACpB,QAAI,IAAI,SAAS,UAAU,MAAM;AAC7B,aAAO;AAAA,IACX;AACA,QAAI,IAAI,SAAS,KACb,IAAI,aAAa,UAAU,YAC3B,IAAI,YAAY,UAAU,SAAS;AACnC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,MAAM;AAC9B,SAAO,MAAM;AACT,QAAI,KAAK,SAAS,IAAoC;AAClD,UAAI,KAAK,UAAU,SAAS,IAAoC;AAC5D,eAAO,KAAK;AAAA,MAChB,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ,WACS,KAAK,SAAS,IAA8B;AACjD,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACJ;AAyHA,SAAS,WAAW,MAAM,KAAK,SAAS,gBAAgB;AACpD,MAAI,CAAC,IAAI,KAAK;AACV,YAAQ,QAAQ,oBAAoB,IAAgC,IAAI,GAAG,CAAC;AAC5E;AAAA,EACJ;AACA,QAAM,cAAc;AAAA,IAGpB,IAAI;AAAA,IAAK;AAAA,EAAO;AAChB,MAAI,CAAC,aAAa;AACd,YAAQ,QAAQ,oBAAoB,IAAuC,IAAI,GAAG,CAAC;AACnF;AAAA,EACJ;AACA,QAAM,EAAE,gBAAgB,mBAAmB,OAAO,IAAI;AACtD,QAAM,EAAE,QAAQ,OAAO,KAAK,MAAM,IAAI;AACtC,QAAM,UAAU;AAAA,IACZ,MAAM;AAAA,IACN,KAAK,IAAI;AAAA,IACT;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB;AAAA,IACA,UAAU,eAAe,IAAI,IAAI,KAAK,WAAW,CAAC,IAAI;AAAA,EAC1D;AACA,UAAQ,YAAY,OAAO;AAE3B,SAAO;AACP,QAAM,SAAS,kBAAkB,eAAe,OAAO;AACvD,SAAO,MAAM;AACT,WAAO;AACP,QAAI;AACA,aAAO;AAAA,EACf;AACJ;AAMA,SAAS,mBAAmB,OAAO,SAAS;AACxC,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,MAAM;AAClB,QAAM,UAAU,IAAI,MAAM,UAAU;AACpC,MAAI,CAAC;AACD;AACJ,QAAM,CAAC,EAAE,KAAK,GAAG,IAAI;AACrB,QAAM,SAAS;AAAA,IACX,QAAQ,sBAAsB,KAAK,IAAI,KAAK,GAAG,IAAI,QAAQ,KAAK,IAAI,MAAM,CAAC;AAAA,IAC3E,OAAO;AAAA,IACP,KAAK;AAAA,IACL,OAAO;AAAA,EACX;AACA,MAA+C,MAAM;AACjD,8BAA0B,OAAO,QAAQ,OAAO;AAAA,EACpD;AACA,MAAI,eAAe,IAAI,KAAK,EAAE,QAAQ,eAAe,EAAE,EAAE,KAAK;AAC9D,QAAM,gBAAgB,IAAI,QAAQ,YAAY;AAC9C,QAAM,gBAAgB,aAAa,MAAM,aAAa;AACtD,MAAI,eAAe;AACf,mBAAe,aAAa,QAAQ,eAAe,EAAE,EAAE,KAAK;AAC5D,UAAM,aAAa,cAAc,GAAG,KAAK;AACzC,QAAI;AACJ,QAAI,YAAY;AACZ,kBAAY,IAAI,QAAQ,YAAY,gBAAgB,aAAa,MAAM;AACvE,aAAO,MAAM,sBAAsB,KAAK,YAAY,SAAS;AAC7D,UAA+C,MAAM;AACjD,kCAA0B,OAAO,KAAK,SAAS,IAAI;AAAA,MACvD;AAAA,IACJ;AACA,QAAI,cAAc,IAAI;AAClB,YAAM,eAAe,cAAc,GAAG,KAAK;AAC3C,UAAI,cAAc;AACd,eAAO,QAAQ,sBAAsB,KAAK,cAAc,IAAI,QAAQ,cAAc,OAAO,MACnF,YAAY,WAAW,SACvB,gBAAgB,aAAa,MAAM,CAAC;AAC1C,YAA+C,MAAM;AACjD,oCAA0B,OAAO,OAAO,SAAS,IAAI;AAAA,QACzD;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,cAAc;AACd,WAAO,QAAQ,sBAAsB,KAAK,cAAc,aAAa;AACrE,QAA+C,MAAM;AACjD,gCAA0B,OAAO,OAAO,SAAS,IAAI;AAAA,IACzD;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,OAAO,SAAS,QAAQ;AACnD,SAAO,uBAAuB,SAAS,OAAO,cAAc,OAAO,QAAQ,QAAQ,MAAM,CAAC;AAC9F;AACA,SAAS,oBAAoB,EAAE,OAAO,KAAK,MAAM,GAAG,WAAW,CAAC,GAAG;AAC/D,SAAO,iBAAiB,CAAC,OAAO,KAAK,OAAO,GAAG,QAAQ,CAAC;AAC5D;AACA,SAAS,iBAAiB,MAAM;AAC5B,MAAI,IAAI,KAAK;AACb,SAAO,KAAK;AACR,QAAI,KAAK;AACL;AAAA,EACR;AACA,SAAO,KACF,MAAM,GAAG,IAAI,CAAC,EACd,IAAI,CAAC,KAAKG,OAAM,OAAO,uBAAuB,IAAI,OAAOA,KAAI,CAAC,GAAG,KAAK,CAAC;AAChF;AAmDA,SAAS,WAAW,MAAM,SAAS,cAAc,mBAAmB;AAChE,UAAQ,OAAO,QAAQ;AACvB,QAAM,EAAE,UAAU,IAAI,IAAI;AAC1B,QAAM,kBAAkB,CAAC;AACzB,QAAM,eAAe,CAAC;AAGtB,MAAI,kBAAkB,QAAQ,OAAO,QAAQ,KAAK,QAAQ,OAAO,OAAO;AAGxE,QAAM,kBAAkB,QAAQ,MAAM,QAAQ,IAAI;AAClD,MAAI,iBAAiB;AACjB,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,OAAO,CAAC,YAAY,GAAG,GAAG;AAC1B,wBAAkB;AAAA,IACtB;AACA,oBAAgB,KAAK,qBAAqB,OAAO,uBAAuB,WAAW,IAAI,GAAG,YAAY,KAAK,UAAU,GAAG,CAAC,CAAC;AAAA,EAC9H;AAGA,MAAI,mBAAmB;AACvB,MAAI,sBAAsB;AAC1B,QAAM,0BAA0B,CAAC;AACjC,QAAM,gBAAgB,oBAAI,IAAI;AAC9B,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAM,cAAc,SAAS;AAC7B,QAAI;AACJ,QAAI,CAAC,eAAe,WAAW,KAC3B,EAAE,UAAU,QAAQ,aAAa,QAAQ,IAAI,IAAI;AAEjD,UAAI,YAAY,SAAS,GAAiB;AACtC,gCAAwB,KAAK,WAAW;AAAA,MAC5C;AACA;AAAA,IACJ;AACA,QAAI,iBAAiB;AAEjB,cAAQ,QAAQ,oBAAoB,IAAoC,QAAQ,GAAG,CAAC;AACpF;AAAA,IACJ;AACA,uBAAmB;AACnB,UAAM,EAAE,UAAU,cAAc,KAAK,QAAQ,IAAI;AACjD,UAAM,EAAE,KAAK,WAAW,uBAAuB,WAAW,IAAI,GAAG,KAAK,WAAW,KAAK,OAAO,IAAI;AAEjG,QAAI;AACJ,QAAI,YAAY,QAAQ,GAAG;AACvB,uBAAiB,WAAW,SAAS,UAAU;AAAA,IACnD,OACK;AACD,wBAAkB;AAAA,IACtB;AACA,UAAM,eAAe,YAAY,WAAW,cAAc,OAAO;AAEjE,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAK,MAAM,QAAQ,aAAa,IAAI,GAAI;AACpC,wBAAkB;AAClB,mBAAa,KAAK,4BAA4B,IAAI,KAAK,iBAAiB,UAAU,YAAY,GAAG,eAAe,CAAC;AAAA,IACrH,WACU,QAAQ,QAAQ,aAAa,gBAAgB,IAAqB,GAAI;AAE5E,UAAI,IAAI;AACR,UAAI;AACJ,aAAO,KAAK;AACR,eAAO,SAAS;AAChB,YAAI,KAAK,SAAS,GAAiB;AAC/B;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,QAAQ,eAAe,IAAI,KAAK,QAAQ,MAAM,IAAI,GAAG;AAErD,iBAAS,OAAO,GAAG,CAAC;AACpB;AAEA,YAAI,cAAc,aAAa,aAAa,SAAS;AACrD,eAAO,YAAY,UAAU,SAAS,IAAoC;AACtE,wBAAc,YAAY;AAAA,QAC9B;AACA,oBAAY,YAAY,MAAM,MACxB,4BAA4B,MAAM,KAAK,iBAAiB,UAAU,YAAY,GAAG,eAAe,IAChG,iBAAiB,UAAU,YAAY;AAAA,MACjD,OACK;AACD,gBAAQ,QAAQ,oBAAoB,IAAkC,MAAM,GAAG,CAAC;AAAA,MACpF;AAAA,IACJ,WACU,OAAO,QAAQ,aAAa,KAAK,GAAI;AAC3C,wBAAkB;AAClB,YAAM,cAAc,KAAK,eACrB,mBAAmB,KAAK,KAAK,OAAO;AACxC,UAAI,aAAa;AAGb,qBAAa,KAAK,qBAAqB,QAAQ,OAAO,WAAW,GAAG;AAAA,UAChE,YAAY;AAAA,UACZ,yBAAyB,oBAAoB,WAAW,GAAG,iBAAiB,UAAU,YAAY,GAAG,IAAwB;AAAA,QACjI,CAAC,CAAC;AAAA,MACN,OACK;AACD,gBAAQ,QAAQ,oBAAoB,IAAuC,KAAK,GAAG,CAAC;AAAA,MACxF;AAAA,IACJ,OACK;AAED,UAAI,gBAAgB;AAChB,YAAI,cAAc,IAAI,cAAc,GAAG;AACnC,kBAAQ,QAAQ,oBAAoB,IAAwC,MAAM,CAAC;AACnF;AAAA,QACJ;AACA,sBAAc,IAAI,cAAc;AAChC,YAAI,mBAAmB,WAAW;AAC9B,gCAAsB;AAAA,QAC1B;AAAA,MACJ;AACA,sBAAgB,KAAK,qBAAqB,UAAU,YAAY,CAAC;AAAA,IACrE;AAAA,EACJ;AACA,MAAI,CAAC,iBAAiB;AAClB,UAAM,2BAA2B,CAAC,OAAOG,cAAa;AAClD,YAAM,KAAK,YAAY,OAAOA,WAAU,GAAG;AAC3C,UAAI,QAAQ,cAAc;AACtB,WAAG,kBAAkB;AAAA,MACzB;AACA,aAAO,qBAAqB,WAAW,EAAE;AAAA,IAC7C;AACA,QAAI,CAAC,kBAAkB;AAEnB,sBAAgB,KAAK,yBAAyB,QAAW,QAAQ,CAAC;AAAA,IACtE,WACS,wBAAwB,UAI7B,wBAAwB,KAAK,CAAAC,UAAQ,uBAAuBA,KAAI,CAAC,GAAG;AAEpE,UAAI,qBAAqB;AACrB,gBAAQ,QAAQ,oBAAoB,IAAoD,wBAAwB,GAAG,GAAG,CAAC;AAAA,MAC3H,OACK;AACD,wBAAgB,KAAK,yBAAyB,QAAW,uBAAuB,CAAC;AAAA,MACrF;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,WAAW,kBACX,IACA,kBAAkB,KAAK,QAAQ,IAC3B,IACA;AACV,MAAI,QAAQ,uBAAuB,gBAAgB,OAAO;AAAA,IAAqB;AAAA,IAG/E,uBAAuB,YAAa,OAAyC,OAAO,cAAc,iBAAiB,KAAK,KAAK;AAAA,EAAC,CAAC,GAAG,GAAG;AACrI,MAAI,aAAa,QAAQ;AACrB,YAAQ,qBAAqB,QAAQ,OAAO,YAAY,GAAG;AAAA,MACvD;AAAA,MACA,sBAAsB,YAAY;AAAA,IACtC,CAAC;AAAA,EACL;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,iBAAiB,MAAM,IAAI;AAChC,SAAO,uBAAuB;AAAA,IAC1B,qBAAqB,QAAQ,IAAI;AAAA,IACjC,qBAAqB,MAAM,EAAE;AAAA,EACjC,CAAC;AACL;AACA,SAAS,kBAAkB,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,UAAM,QAAQ,SAAS;AACvB,YAAQ,MAAM;AAAA,WACL;AACD,YAAI,MAAM,YAAY,KAClB,kBAAkB,MAAM,QAAQ,GAAG;AACnC,iBAAO;AAAA,QACX;AACA;AAAA,WACC;AACD,YAAI,kBAAkB,MAAM,QAAQ;AAChC,iBAAO;AACX;AAAA,WACC;AAAA,WACA;AACD,YAAI,kBAAkB,MAAM,QAAQ;AAChC,iBAAO;AACX;AAAA;AAAA,EAEZ;AACA,SAAO;AACX;AACA,SAAS,uBAAuB,MAAM;AAClC,MAAI,KAAK,SAAS,KAAgB,KAAK,SAAS;AAC5C,WAAO;AACX,SAAO,KAAK,SAAS,IACf,CAAC,CAAC,KAAK,QAAQ,KAAK,IACpB,uBAAuB,KAAK,OAAO;AAC7C;AA2IA,SAAS,qBAAqB,MAAM,SAAS,MAAM,OAAO;AACtD,MAAI,EAAE,IAAI,IAAI;AAEd,QAAM,oBAAoB,eAAe,GAAG;AAC5C,QAAM,SAAS,SAAS,MAAM,IAAI;AAClC,MAAI,QAAQ;AACR,QAAI,qBACC,gBAAgB,0BAAuD,OAAO,GAAI;AACnF,YAAM,MAAM,OAAO,SAAS,IACtB,OAAO,SAAS,uBAAuB,OAAO,MAAM,SAAS,IAAI,IACjE,OAAO;AACb,UAAI,KAAK;AACL,eAAO,qBAAqB,QAAQ,OAAO,yBAAyB,GAAG;AAAA,UACnE;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,WACS,OAAO,SAAS,KACrB,OAAO,MAAM,QAAQ,WAAW,MAAM,GAAG;AAKzC,YAAM,OAAO,MAAM,QAAQ,MAAM,CAAC;AAAA,IACtC;AAAA,EACJ;AAEA,QAAM,QAAQ,CAAC,qBAAqB,QAAQ,MAAM,IAAI;AACtD,MAAI,SAAS,MAAM,KAAK;AACpB,WAAO,qBAAqB,QAAQ,OAAO,yBAAyB,GAAG;AAAA,MACnE,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AAEA,QAAM,UAAU,gBAAgB,GAAG,KAAK,QAAQ,mBAAmB,GAAG;AACtE,MAAI,SAAS;AAGT,QAAI,CAAC;AACD,cAAQ,OAAO,OAAO;AAC1B,WAAO;AAAA,EACX;AAEA,UAAQ,OAAO,iBAAiB;AAChC,UAAQ,WAAW,IAAI,GAAG;AAC1B,SAAO,eAAe,KAAK,WAAW;AAC1C;AACA,SAAS,WAAW,MAAM,SAAS,QAAQ,KAAK,OAAOP,cAAa,oBAAoB,MAAM,OAAO;AACjG,QAAM,EAAE,KAAK,KAAK,YAAY,SAAS,IAAI;AAC3C,MAAI,aAAa,CAAC;AAClB,QAAM,YAAY,CAAC;AACnB,QAAM,oBAAoB,CAAC;AAC3B,QAAM,cAAc,SAAS,SAAS;AACtC,MAAI,iBAAiB;AAErB,MAAI,YAAY;AAChB,MAAI,SAAS;AACb,MAAI,kBAAkB;AACtB,MAAI,kBAAkB;AACtB,MAAI,2BAA2B;AAC/B,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,QAAM,mBAAmB,CAAC;AAC1B,QAAM,mBAAmB,CAAC,EAAE,KAAK,MAAM,MAAM;AACzC,QAAI,YAAY,GAAG,GAAG;AAClB,YAAM,OAAO,IAAI;AACjB,YAAM,iBAAiB,KAAK,IAAI;AAChC,UAAI,mBACC,CAACA,gBAAe,uBAGjB,KAAK,YAAY,MAAM,aAEvB,SAAS,yBAET,CAAC,eAAe,IAAI,GAAG;AACvB,mCAA2B;AAAA,MAC/B;AACA,UAAI,kBAAkB,eAAe,IAAI,GAAG;AACxC,uBAAe;AAAA,MACnB;AACA,UAAI,MAAM,SAAS,OACb,MAAM,SAAS,KACb,MAAM,SAAS,MACf,gBAAgB,OAAO,OAAO,IAAI,GAAI;AAE1C;AAAA,MACJ;AACA,UAAI,SAAS,OAAO;AAChB,iBAAS;AAAA,MACb,WACS,SAAS,SAAS;AACvB,0BAAkB;AAAA,MACtB,WACS,SAAS,SAAS;AACvB,0BAAkB;AAAA,MACtB,WACS,SAAS,SAAS,CAAC,iBAAiB,SAAS,IAAI,GAAG;AACzD,yBAAiB,KAAK,IAAI;AAAA,MAC9B;AAEA,UAAIA,iBACC,SAAS,WAAW,SAAS,YAC9B,CAAC,iBAAiB,SAAS,IAAI,GAAG;AAClC,yBAAiB,KAAK,IAAI;AAAA,MAC9B;AAAA,IACJ,OACK;AACD,uBAAiB;AAAA,IACrB;AAAA,EACJ;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAEnC,UAAM,OAAO,MAAM;AACnB,QAAI,KAAK,SAAS,GAAmB;AACjC,YAAM,EAAE,KAAK,MAAM,MAAM,IAAI;AAC7B,UAAI,WAAW;AACf,UAAI,SAAS,OAAO;AAChB,iBAAS;AACT,YAAI,QAAQ,OAAO,OAAO,GAAG;AACzB,qBAAW,KAAK,qBAAqB,uBAAuB,WAAW,IAAI,GAAG,uBAAuB,MAAM,CAAC,CAAC;AAAA,QACjH;AAAA,MACJ;AAEA,UAAI,SAAS,SACR,eAAe,GAAG,KACd,SAAS,MAAM,QAAQ,WAAW,MAAM,KACxC,gBAAgB,0BAAuD,OAAO,IAAK;AACxF;AAAA,MACJ;AACA,iBAAW,KAAK,qBAAqB,uBAAuB,MAAM,MAAM,cAAc,KAAK,GAAG,KAAK,MAAM,CAAC,GAAG,uBAAuB,QAAQ,MAAM,UAAU,IAAI,UAAU,QAAQ,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,IACvM,OACK;AAED,YAAM,EAAE,MAAM,KAAK,KAAK,IAAI,IAAI;AAChC,YAAM,UAAU,SAAS;AACzB,YAAM,QAAQ,SAAS;AAEvB,UAAI,SAAS,QAAQ;AACjB,YAAI,CAACA,cAAa;AACd,kBAAQ,QAAQ,oBAAoB,IAA6B,GAAG,CAAC;AAAA,QACzE;AACA;AAAA,MACJ;AAEA,UAAI,SAAS,UAAU,SAAS,QAAQ;AACpC;AAAA,MACJ;AAEA,UAAI,SAAS,QACR,WACG,cAAc,KAAK,IAAI,MACtB,eAAe,GAAG,KACd,gBAAgB,0BAAuD,OAAO,IAAM;AAC7F;AAAA,MACJ;AAEA,UAAI,SAAS,KAAK;AACd;AAAA,MACJ;AACA,UAEC,WAAW,cAAc,KAAK,KAAK,KAG/B,SAAS,eAAe,cAAc,KAAK,mBAAmB,GAAI;AACnE,yBAAiB;AAAA,MACrB;AACA,UAAI,WAAW,cAAc,KAAK,KAAK,KAAK,QAAQ,OAAO,OAAO,GAAG;AACjE,mBAAW,KAAK,qBAAqB,uBAAuB,WAAW,IAAI,GAAG,uBAAuB,MAAM,CAAC,CAAC;AAAA,MACjH;AAEA,UAAI,CAAC,QAAQ,WAAW,QAAQ;AAC5B,yBAAiB;AACjB,YAAI,KAAK;AACL,cAAI,WAAW,QAAQ;AACnB,sBAAU,KAAK,uBAAuB,iBAAiB,UAAU,GAAG,UAAU,CAAC;AAC/E,yBAAa,CAAC;AAAA,UAClB;AACA,cAAI,SAAS;AACT;AAEI,kBAAK,MAAwC;AACzC,sBAAM,qBAAqB,UAAU,KAAK,CAAAQ,SAAO;AAC7C,sBAAIA,KAAI,SAAS,IAA+B;AAC5C,2BAAOA,KAAI,WAAW,KAAK,CAAC,EAAE,IAAI,MAAM;AACpC,0BAAI,IAAI,SAAS,KACb,CAAC,IAAI,UAAU;AACf,+BAAO;AAAA,sBACX;AACA,6BAAQ,IAAI,YAAY,WACpB,IAAI,YAAY,WAChB,CAAC,KAAK,IAAI,OAAO;AAAA,oBACzB,CAAC;AAAA,kBACL,OACK;AAED,2BAAO;AAAA,kBACX;AAAA,gBACJ,CAAC;AACD,oBAAI,oBAAoB;AACpB,qCAAmB,gCAAmE,SAAS,GAAG;AAAA,gBACtG;AAAA,cACJ;AACA,kBAAI,gBAAgB,gCAAmE,OAAO,GAAG;AAC7F,0BAAU,QAAQ,GAAG;AACrB;AAAA,cACJ;AAAA,YACJ;AACA,sBAAU,KAAK,GAAG;AAAA,UACtB,OACK;AAED,sBAAU,KAAK;AAAA,cACX,MAAM;AAAA,cACN;AAAA,cACA,QAAQ,QAAQ,OAAO,WAAW;AAAA,cAClC,WAAW,CAAC,GAAG;AAAA,YACnB,CAAC;AAAA,UACL;AAAA,QACJ,OACK;AACD,kBAAQ,QAAQ,oBAAoB,UAC9B,KACA,IAA+B,GAAG,CAAC;AAAA,QAC7C;AACA;AAAA,MACJ;AACA,YAAM,qBAAqB,QAAQ,oBAAoB;AACvD,UAAI,oBAAoB;AAEpB,cAAM,EAAE,OAAAC,QAAO,YAAY,IAAI,mBAAmB,MAAM,MAAM,OAAO;AACrE,SAAC,OAAOA,OAAM,QAAQ,gBAAgB;AACtC,mBAAW,KAAK,GAAGA,MAAK;AACxB,YAAI,aAAa;AACb,4BAAkB,KAAK,IAAI;AAC3B,cAAI,SAAS,WAAW,GAAG;AACvB,+BAAmB,IAAI,MAAM,WAAW;AAAA,UAC5C;AAAA,QACJ;AAAA,MACJ,WACS,CAAC,mBAAmB,IAAI,GAAG;AAEhC,0BAAkB,KAAK,IAAI;AAG3B,YAAI,aAAa;AACb,2BAAiB;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,kBAAkB;AAEtB,MAAI,UAAU,QAAQ;AAClB,QAAI,WAAW,QAAQ;AACnB,gBAAU,KAAK,uBAAuB,iBAAiB,UAAU,GAAG,UAAU,CAAC;AAAA,IACnF;AACA,QAAI,UAAU,SAAS,GAAG;AACtB,wBAAkB,qBAAqB,QAAQ,OAAO,WAAW,GAAG,WAAW,UAAU;AAAA,IAC7F,OACK;AAED,wBAAkB,UAAU;AAAA,IAChC;AAAA,EACJ,WACS,WAAW,QAAQ;AACxB,sBAAkB,uBAAuB,iBAAiB,UAAU,GAAG,UAAU;AAAA,EACrF;AAEA,MAAI,gBAAgB;AAChB,iBAAa;AAAA,EACjB,OACK;AACD,QAAI,mBAAmB,CAACT,cAAa;AACjC,mBAAa;AAAA,IACjB;AACA,QAAI,mBAAmB,CAACA,cAAa;AACjC,mBAAa;AAAA,IACjB;AACA,QAAI,iBAAiB,QAAQ;AACzB,mBAAa;AAAA,IACjB;AACA,QAAI,0BAA0B;AAC1B,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,MAAI,CAAC,mBACA,cAAc,KAAK,cAAc,QACjC,UAAU,gBAAgB,kBAAkB,SAAS,IAAI;AAC1D,iBAAa;AAAA,EACjB;AAEA,MAAI,CAAC,QAAQ,SAAS,iBAAiB;AACnC,YAAQ,gBAAgB;AAAA,WACf;AAGD,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,iBAAS,IAAI,GAAG,IAAI,gBAAgB,WAAW,QAAQ,KAAK;AACxD,gBAAM,MAAM,gBAAgB,WAAW,GAAG;AAC1C,cAAI,YAAY,GAAG,GAAG;AAClB,gBAAI,IAAI,YAAY,SAAS;AACzB,8BAAgB;AAAA,YACpB,WACS,IAAI,YAAY,SAAS;AAC9B,8BAAgB;AAAA,YACpB;AAAA,UACJ,WACS,CAAC,IAAI,cAAc;AACxB,4BAAgB;AAAA,UACpB;AAAA,QACJ;AACA,cAAM,YAAY,gBAAgB,WAAW;AAC7C,cAAM,YAAY,gBAAgB,WAAW;AAE7C,YAAI,CAAC,eAAe;AAChB,cAAI,aAAa,CAAC,YAAY,UAAU,KAAK,GAAG;AAC5C,sBAAU,QAAQ,qBAAqB,QAAQ,OAAO,eAAe,GAAG,CAAC,UAAU,KAAK,CAAC;AAAA,UAC7F;AACA,cAAI,cAGC,mBACI,UAAU,MAAM,SAAS,KACtB,UAAU,MAAM,QAAQ,KAAK,EAAE,OAAO,OAG1C,UAAU,MAAM,SAAS,KAA+B;AAC5D,sBAAU,QAAQ,qBAAqB,QAAQ,OAAO,eAAe,GAAG,CAAC,UAAU,KAAK,CAAC;AAAA,UAC7F;AAAA,QACJ,OACK;AAED,4BAAkB,qBAAqB,QAAQ,OAAO,eAAe,GAAG,CAAC,eAAe,CAAC;AAAA,QAC7F;AACA;AAAA,WACC;AAED;AAAA;AAGA,0BAAkB,qBAAqB,QAAQ,OAAO,eAAe,GAAG;AAAA,UACpE,qBAAqB,QAAQ,OAAO,oBAAoB,GAAG;AAAA,YACvD;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AACD;AAAA;AAAA,EAEZ;AACA,SAAO;AAAA,IACH,OAAO;AAAA,IACP,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAOA,SAAS,iBAAiB,YAAY;AAClC,QAAM,aAAa,oBAAI,IAAI;AAC3B,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAM,OAAO,WAAW;AAExB,QAAI,KAAK,IAAI,SAAS,KAA+B,CAAC,KAAK,IAAI,UAAU;AACrE,cAAQ,KAAK,IAAI;AACjB;AAAA,IACJ;AACA,UAAM,OAAO,KAAK,IAAI;AACtB,UAAM,WAAW,WAAW,IAAI,IAAI;AACpC,QAAI,UAAU;AACV,UAAI,SAAS,WAAW,SAAS,WAAW,KAAK,IAAI,GAAG;AACpD,qBAAa,UAAU,IAAI;AAAA,MAC/B;AAAA,IAEJ,OACK;AACD,iBAAW,IAAI,MAAM,IAAI;AACzB,cAAQ,KAAK,IAAI;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,UAAU,UAAU;AACtC,MAAI,SAAS,MAAM,SAAS,IAA8B;AACtD,aAAS,MAAM,SAAS,KAAK,SAAS,KAAK;AAAA,EAC/C,OACK;AACD,aAAS,QAAQ,sBAAsB,CAAC,SAAS,OAAO,SAAS,KAAK,GAAG,SAAS,GAAG;AAAA,EACzF;AACJ;AACA,SAAS,mBAAmB,KAAK,SAAS;AACtC,QAAM,UAAU,CAAC;AACjB,QAAM,UAAU,mBAAmB,IAAI,GAAG;AAC1C,MAAI,SAAS;AAET,YAAQ,KAAK,QAAQ,aAAa,OAAO,CAAC;AAAA,EAC9C,OACK;AACD;AAEI,cAAQ,OAAO,iBAAiB;AAChC,cAAQ,WAAW,IAAI,IAAI,IAAI;AAC/B,cAAQ,KAAK,eAAe,IAAI,MAAM,WAAW,CAAC;AAAA,IACtD;AAAA,EACJ;AACA,QAAM,EAAE,IAAI,IAAI;AAChB,MAAI,IAAI;AACJ,YAAQ,KAAK,IAAI,GAAG;AACxB,MAAI,IAAI,KAAK;AACT,QAAI,CAAC,IAAI,KAAK;AACV,cAAQ,KAAK,QAAQ;AAAA,IACzB;AACA,YAAQ,KAAK,IAAI,GAAG;AAAA,EACxB;AACA,MAAI,OAAO,KAAK,IAAI,SAAS,EAAE,QAAQ;AACnC,QAAI,CAAC,IAAI,KAAK;AACV,UAAI,CAAC,IAAI,KAAK;AACV,gBAAQ,KAAK,QAAQ;AAAA,MACzB;AACA,cAAQ,KAAK,QAAQ;AAAA,IACzB;AACA,UAAM,iBAAiB,uBAAuB,QAAQ,OAAO,GAAG;AAChE,YAAQ,KAAK,uBAAuB,IAAI,UAAU,IAAI,cAAY,qBAAqB,UAAU,cAAc,CAAC,GAAG,GAAG,CAAC;AAAA,EAC3H;AACA,SAAO,sBAAsB,SAAS,IAAI,GAAG;AACjD;AACA,SAAS,0BAA0B,OAAO;AACtC,MAAI,mBAAmB;AACvB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC1C,wBAAoB,KAAK,UAAU,MAAM,EAAE;AAC3C,QAAI,IAAI,IAAI;AACR,0BAAoB;AAAA,EAC5B;AACA,SAAO,mBAAmB;AAC9B;AACA,SAAS,eAAe,KAAK;AACzB,SAAO,QAAQ,eAAe,QAAQ;AAC1C;AAgDA,SAAS,kBAAkB,MAAM,SAAS;AACtC,MAAI,WAAW;AACf,MAAI,YAAY;AAChB,QAAM,eAAe,CAAC;AACtB,WAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAM,IAAI,KAAK,MAAM;AACrB,QAAI,EAAE,SAAS,GAAmB;AAC9B,UAAI,EAAE,OAAO;AACT,YAAI,EAAE,SAAS,QAAQ;AACnB,qBAAW,KAAK,UAAU,EAAE,MAAM,OAAO;AAAA,QAC7C,OACK;AACD,YAAE,OAAOU,UAAS,EAAE,IAAI;AACxB,uBAAa,KAAK,CAAC;AAAA,QACvB;AAAA,MACJ;AAAA,IACJ,OACK;AACD,UAAI,EAAE,SAAS,UAAU,cAAc,EAAE,KAAK,MAAM,GAAG;AACnD,YAAI,EAAE;AACF,qBAAW,EAAE;AAAA,MACrB,OACK;AACD,YAAI,EAAE,SAAS,UAAU,EAAE,OAAO,YAAY,EAAE,GAAG,GAAG;AAClD,YAAE,IAAI,UAAUA,UAAS,EAAE,IAAI,OAAO;AAAA,QAC1C;AACA,qBAAa,KAAK,CAAC;AAAA,MACvB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,aAAa,SAAS,GAAG;AACzB,UAAM,EAAE,OAAO,WAAW,IAAI,WAAW,MAAM,SAAS,cAAc,OAAO,KAAK;AAClF,gBAAY;AACZ,QAAI,WAAW,QAAQ;AACnB,cAAQ,QAAQ,oBAAoB,IAAuD,WAAW,GAAG,GAAG,CAAC;AAAA,IACjH;AAAA,EACJ;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;AAySA,SAAS,qBAAqB,QAAQ,CAAC,GAAG;AACtC,SAAO,EAAE,MAAM;AACnB;AAsBA,SAAS,cAAc,MAAM,SAAS;AAClC,MAAI,KAAK,SAAS,GAA2B;AACzC,gBAAY,MAAM,OAAO;AAAA,EAC7B,OACK;AACD,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,YAAM,QAAQ,KAAK,SAAS;AAC5B,UAAI,OAAO,UAAU;AACjB;AACJ,UAAI,MAAM,SAAS,GAA2B;AAC1C,oBAAY,OAAO,OAAO;AAAA,MAC9B,WACS,MAAM,SAAS,GAA6B;AACjD,sBAAc,MAAM,OAAO;AAAA,MAC/B,WACS,MAAM,SAAS,GAAuB;AAC3C,sBAAc,MAAM,SAAS,OAAO;AAAA,MACxC;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,YAAY,MAAM,SAAS;AAChC,QAAM,MAAM,KAAK;AACjB,MAAI,WAAW;AACf,MAAI,WAAW;AACf,MAAI,mBAAmB;AACvB,MAAI,UAAU;AACd,MAAI,QAAQ;AACZ,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,kBAAkB;AACtB,MAAI,GAAG,MAAM,GAAG,YAAY,UAAU,CAAC;AACvC,OAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC7B,WAAO;AACP,QAAI,IAAI,WAAW,CAAC;AACpB,QAAI,UAAU;AACV,UAAI,MAAM,MAAQ,SAAS;AACvB,mBAAW;AAAA,IACnB,WACS,UAAU;AACf,UAAI,MAAM,MAAQ,SAAS;AACvB,mBAAW;AAAA,IACnB,WACS,kBAAkB;AACvB,UAAI,MAAM,MAAQ,SAAS;AACvB,2BAAmB;AAAA,IAC3B,WACS,SAAS;AACd,UAAI,MAAM,MAAQ,SAAS;AACvB,kBAAU;AAAA,IAClB,WACS,MAAM,OACX,IAAI,WAAW,IAAI,CAAC,MAAM,OAC1B,IAAI,WAAW,IAAI,CAAC,MAAM,OAC1B,CAAC,SACD,CAAC,UACD,CAAC,OAAO;AACR,UAAI,eAAe,QAAW;AAE1B,0BAAkB,IAAI;AACtB,qBAAa,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK;AAAA,MACtC,OACK;AACD,mBAAW;AAAA,MACf;AAAA,IACJ,OACK;AACD,cAAQ;AAAA,aACC;AACD,qBAAW;AACX;AAAA,aACC;AACD,qBAAW;AACX;AAAA,aACC;AACD,6BAAmB;AACnB;AAAA,aACC;AACD;AACA;AAAA,aACC;AACD;AACA;AAAA,aACC;AACD;AACA;AAAA,aACC;AACD;AACA;AAAA,aACC;AACD;AACA;AAAA,aACC;AACD;AACA;AAAA;AAER,UAAI,MAAM,IAAM;AAEZ,YAAI,IAAI,IAAI;AACZ,YAAI;AAEJ,eAAO,KAAK,GAAG,KAAK;AAChB,cAAI,IAAI,OAAO,CAAC;AAChB,cAAI,MAAM;AACN;AAAA,QACR;AACA,YAAI,CAAC,KAAK,CAAC,oBAAoB,KAAK,CAAC,GAAG;AACpC,oBAAU;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,eAAe,QAAW;AAC1B,iBAAa,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK;AAAA,EACtC,WACS,oBAAoB,GAAG;AAC5B,eAAW;AAAA,EACf;AACA,WAAS,aAAa;AAClB,YAAQ,KAAK,IAAI,MAAM,iBAAiB,CAAC,EAAE,KAAK,CAAC;AACjD,sBAAkB,IAAI;AAAA,EAC1B;AACA,MAAI,QAAQ,QAAQ;AAChB,IACI,gBAAgB,mBAA0C,SAAS,KAAK,GAAG;AAC/E,SAAK,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACjC,mBAAa,WAAW,YAAY,QAAQ,IAAI,OAAO;AAAA,IAC3D;AACA,SAAK,UAAU;AAAA,EACnB;AACJ;AACA,SAAS,WAAW,KAAK,QAAQ,SAAS;AACtC,UAAQ,OAAO,cAAc;AAC7B,QAAM,IAAI,OAAO,QAAQ,GAAG;AAC5B,MAAI,IAAI,GAAG;AACP,YAAQ,QAAQ,IAAI,MAAM;AAC1B,WAAO,GAAG,eAAe,QAAQ,QAAQ,KAAK;AAAA,EAClD,OACK;AACD,UAAM,OAAO,OAAO,MAAM,GAAG,CAAC;AAC9B,UAAM,OAAO,OAAO,MAAM,IAAI,CAAC;AAC/B,YAAQ,QAAQ,IAAI,IAAI;AACxB,WAAO,GAAG,eAAe,MAAM,QAAQ,KAAK,MAAM,SAAS,MAAM,MAAM,OAAO;AAAA,EAClF;AACJ;AA6BA,SAAS,uBAAuB,mBAAmB;AAC/C,SAAO;AAAA,IACH;AAAA,MACI;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAI,CAAC,eAAe;AAAA,MACpB,GAAK,OACK,CAAC,mBAAmB,IACpB,CAAC;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA;AAAA,MACI,IAAI;AAAA,MACJ,MAAM;AAAA,MACN,OAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAGA,SAAS,YAAY,UAAU,UAAU,CAAC,GAAG;AACzC,QAAM,UAAU,QAAQ,WAAW;AACnC,QAAM,eAAe,QAAQ,SAAS;AAEtC;AACI,QAAI,QAAQ,sBAAsB,MAAM;AACpC,cAAQ,oBAAoB,EAAkC,CAAC;AAAA,IACnE,WACS,cAAc;AACnB,cAAQ,oBAAoB,EAAoC,CAAC;AAAA,IACrE;AAAA,EACJ;AACA,QAAM,oBAAoB;AAC1B,MAAI,QAAQ,eAAe;AACvB,YAAQ,oBAAoB,EAAsC,CAAC;AAAA,EACvE;AACA,MAAI,QAAQ,WAAW,CAAC,cAAc;AAClC,YAAQ,oBAAoB,EAAiC,CAAC;AAAA,EAClE;AACA,QAAM,MAAM,SAAS,QAAQ,IAAI,UAAU,UAAU,OAAO,IAAI;AAChE,QAAM,CAAC,gBAAgB,mBAAmB,IAAI,uBAAuB;AACrE,YAAU,KAAK,OAAO,CAAC,GAAG,SAAS;AAAA,IAC/B;AAAA,IACA,gBAAgB;AAAA,MACZ,GAAG;AAAA,MACH,GAAI,QAAQ,kBAAkB,CAAC;AAAA,IACnC;AAAA,IACA,qBAAqB;AAAA,MAAO,CAAC;AAAA,MAAG;AAAA,MAAqB,QAAQ,uBAAuB,CAAC;AAAA,IACrF;AAAA,EACJ,CAAC,CAAC;AACF,SAAO,SAAS,KAAK,OAAO,CAAC,GAAG,SAAS;AAAA,IACrC;AAAA,EACJ,CAAC,CAAC;AACN;AAxvJA,IAkBM,eA+DA,UACA,UACA,UACA,YACA,iBACA,YACA,cACA,sBACA,cACA,sBACA,gBACA,aACA,eACA,mBACA,2BACA,mBACA,gBACA,iBACA,aACA,aACA,cACA,mBACA,aACA,iBACA,iBACA,iBACA,sBACA,aACA,UACA,YACA,gBACA,oBACA,eACA,cACA,UACA,OACA,QACA,WACA,cAIA,eAmDA,SAiLA,aACA,eAeA,iBACA,oBACA,uBACA,kBACA,cAOA,2BAsEA,wBAEA,oBAkGA,gBAwKA,iBA+FA,UACA,WAOA,sBAuUA,4BAorBA,uBAwWA,iBACA,aAmoBA,gBAGA,kBAGA,qBAIA,qBAQA,eA8BA,qBA8CA,aA4NA,cA0JA,YAGA,eACA,eAoEA,iBAQA,iBAkBA,qBAoBA,mBA8MA,oBAEA,kBAykBA,qBAOA,YAIAA,WAIA,qBAsEA,SACA,aA8EA,eA4CA,cAiBA,eAoFA,MACA,eAkBA,gBAwDA,qBACA,iBAqKA,QACA,eAsFA;AA1vJN;AAAA;AAAA;AACA;AAiBA,IAAM,gBAAgB;AAAA,MAElB,CAAC,IAA0C;AAAA,MAC3C,CAAC,IAAgC;AAAA,MACjC,CAAC,IAA8B;AAAA,MAC/B,CAAC,IAAkC;AAAA,MACnC,CAAC,IAAwC;AAAA,MACzC,CAAC,IAA8B;AAAA,MAC/B,CAAC,IAAuB;AAAA,MACxB,CAAC,IAAyB;AAAA,MAC1B,CAAC,IAA+C;AAAA,MAChD,CAAC,IAAqB;AAAA,MACtB,CAAC,KAAsC;AAAA,MACvC,CAAC,KAAsC;AAAA,MACvC,CAAC,KAA+C;AAAA,MAChD,CAAC,KAAmC;AAAA,MACpC,CAAC,KAAgC;AAAA,MACjC,CAAC,KAAiD;AAAA,MAClD,CAAC,KAA0B;AAAA,MAC3B,CAAC,KAAkD;AAAA,MACnD,CAAC,KAA4D;AAAA,MAC7D,CAAC,KAAwD;AAAA,MACzD,CAAC,KAAwD;AAAA,MACzD,CAAC,KAAqC;AAAA,MACtC,CAAC,KAAqC;AAAA,MAEtC,CAAC,KAA6B;AAAA,MAC9B,CAAC,KAA6B;AAAA,MAC9B,CAAC,KAAuC;AAAA,MACxC,CAAC,KAAoD;AAAA,MAErD,CAAC,KAAoC;AAAA,MAErC,CAAC,KAAgC;AAAA,MACjC,CAAC,KAA2B;AAAA,MAC5B,CAAC,KAAmC;AAAA,MACpC,CAAC,KAAiC;AAAA,MAClC,CAAC,KAAwC;AAAA,MACzC,CAAC,KAA0C;AAAA,MAC3C,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAgC;AAAA,MACjC,CAAC,KAAwD;AAAA,MACzD,CAAC,KAAqC;AAAA,MAGtC,CAAC,KAAyC;AAAA,MAC1C,CAAC,KAAqD;AAAA,MAEtD,CAAC,KAA8B;AAAA,MAC/B,CAAC,KAAmC;AAAA,MACpC,CAAC,KAA0C;AAAA,MAC3C,CAAC,KAAuC;AAAA,MACxC,CAAC,KAAgC;AAAA,MACjC,CAAC,KAAyC;AAAA,MAE1C,CAAC,KAAqC;AAAA,MACtC,CAAC,KAAuC;AAAA,MACxC,CAAC,KAAyC;AAAA,MAC1C,CAAC,KAAoC;AAAA,MAErC,CAAC,KAA4B;AAAA,IACjC;AAEA,IAAM,WAAW,OAAQ,OAAyC,aAAa,EAAE;AACjF,IAAM,WAAW,OAAQ,OAAyC,aAAa,EAAE;AACjF,IAAM,WAAW,OAAQ,OAAyC,aAAa,EAAE;AACjF,IAAM,aAAa,OAAQ,OAAyC,cAAc,EAAE;AACpF,IAAM,kBAAkB,OAAQ,OAAyC,mBAAmB,EAAE;AAC9F,IAAM,aAAa,OAAQ,OAAyC,cAAc,EAAE;AACpF,IAAM,eAAe,OAAQ,OAAyC,gBAAgB,EAAE;AACxF,IAAM,uBAAuB,OAAQ,OAAyC,uBAAuB,EAAE;AACvG,IAAM,eAAe,OAAQ,OAAyC,gBAAgB,EAAE;AACxF,IAAM,uBAAuB,OAAQ,OAAyC,uBAAuB,EAAE;AACvG,IAAM,iBAAiB,OAAQ,OAAyC,uBAAuB,EAAE;AACjG,IAAM,cAAc,OAAQ,OAAyC,oBAAoB,EAAE;AAC3F,IAAM,gBAAgB,OAAQ,OAAyC,sBAAsB,EAAE;AAC/F,IAAM,oBAAoB,OAAQ,OAAyC,qBAAqB,EAAE;AAClG,IAAM,4BAA4B,OAAQ,OAAyC,4BAA4B,EAAE;AACjH,IAAM,oBAAoB,OAAQ,OAAyC,qBAAqB,EAAE;AAClG,IAAM,iBAAiB,OAAQ,OAAyC,kBAAkB,EAAE;AAC5F,IAAM,kBAAkB,OAAQ,OAAyC,mBAAmB,EAAE;AAC9F,IAAM,cAAc,OAAQ,OAAyC,eAAe,EAAE;AACtF,IAAM,cAAc,OAAQ,OAAyC,eAAe,EAAE;AACtF,IAAM,eAAe,OAAQ,OAAyC,gBAAgB,EAAE;AACxF,IAAM,oBAAoB,OAAQ,OAAyC,oBAAoB,EAAE;AACjG,IAAM,cAAc,OAAQ,OAAyC,eAAe,EAAE;AACtF,IAAM,kBAAkB,OAAQ,OAAyC,mBAAmB,EAAE;AAC9F,IAAM,kBAAkB,OAAQ,OAAyC,mBAAmB,EAAE;AAC9F,IAAM,kBAAkB,OAAQ,OAAyC,mBAAmB,EAAE;AAC9F,IAAM,uBAAuB,OAAQ,OAAyC,uBAAuB,EAAE;AACvG,IAAM,cAAc,OAAQ,OAAyC,eAAe,EAAE;AACtF,IAAM,WAAW,OAAQ,OAAyC,aAAa,EAAE;AACjF,IAAM,aAAa,OAAQ,OAAyC,eAAe,EAAE;AACrF,IAAM,iBAAiB,OAAQ,OAAyC,iBAAiB,EAAE;AAC3F,IAAM,qBAAqB,OAAQ,OAAyC,qBAAqB,EAAE;AACnG,IAAM,gBAAgB,OAAQ,OAAyC,gBAAgB,EAAE;AACzF,IAAM,eAAe,OAAQ,OAAyC,eAAe,EAAE;AACvF,IAAM,WAAW,OAAQ,OAAyC,YAAY,EAAE;AAChF,IAAM,QAAQ,OAAQ,OAAyC,UAAU,EAAE;AAC3E,IAAM,SAAS,OAAQ,OAAyC,UAAU,EAAE;AAC5E,IAAM,YAAY,OAAQ,OAAyC,aAAa,EAAE;AAClF,IAAM,eAAe,OAAQ,OAAyC,eAAe,EAAE;AAIvF,IAAM,gBAAgB;AAAA,MAClB,CAAC,WAAW;AAAA,MACZ,CAAC,WAAW;AAAA,MACZ,CAAC,WAAW;AAAA,MACZ,CAAC,aAAa;AAAA,MACd,CAAC,kBAAkB;AAAA,MACnB,CAAC,aAAa;AAAA,MACd,CAAC,eAAe;AAAA,MAChB,CAAC,uBAAuB;AAAA,MACxB,CAAC,eAAe;AAAA,MAChB,CAAC,uBAAuB;AAAA,MACxB,CAAC,iBAAiB;AAAA,MAClB,CAAC,cAAc;AAAA,MACf,CAAC,gBAAgB;AAAA,MACjB,CAAC,oBAAoB;AAAA,MACrB,CAAC,4BAA4B;AAAA,MAC7B,CAAC,oBAAoB;AAAA,MACrB,CAAC,iBAAiB;AAAA,MAClB,CAAC,kBAAkB;AAAA,MACnB,CAAC,cAAc;AAAA,MACf,CAAC,cAAc;AAAA,MACf,CAAC,eAAe;AAAA,MAChB,CAAC,oBAAoB;AAAA,MACrB,CAAC,cAAc;AAAA,MACf,CAAC,kBAAkB;AAAA,MACnB,CAAC,kBAAkB;AAAA,MACnB,CAAC,kBAAkB;AAAA,MACnB,CAAC,uBAAuB;AAAA,MACxB,CAAC,cAAc;AAAA,MACf,CAAC,WAAW;AAAA,MACZ,CAAC,aAAa;AAAA,MACd,CAAC,iBAAiB;AAAA,MAClB,CAAC,qBAAqB;AAAA,MACtB,CAAC,gBAAgB;AAAA,MACjB,CAAC,eAAe;AAAA,MAChB,CAAC,WAAW;AAAA,MACZ,CAAC,QAAQ;AAAA,MACT,CAAC,SAAS;AAAA,MACV,CAAC,YAAY;AAAA,MACb,CAAC,eAAe;AAAA,IACpB;AAWA,IAAM,UAAU;AAAA,MACZ,QAAQ;AAAA,MACR,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,MACvC,KAAK,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,IACzC;AA6KA,IAAM,cAAc,CAAC,MAAM,EAAE,SAAS,KAA6B,EAAE;AACrE,IAAM,gBAAgB,CAAC,KAAK,aAAa,QAAQ,YAAY,QAAQ,UAAU,QAAQ;AAevF,IAAM,kBAAkB;AACxB,IAAM,qBAAqB,CAAC,SAAS,CAAC,gBAAgB,KAAK,IAAI;AAC/D,IAAM,wBAAwB;AAC9B,IAAM,mBAAmB;AACzB,IAAM,eAAe;AAOrB,IAAM,4BAA4B,CAAC,SAAS;AAExC,aAAO,KAAK,KAAK,EAAE,QAAQ,cAAc,OAAK,EAAE,KAAK,CAAC;AACtD,UAAI,QAAQ;AACZ,UAAI,aAAa,CAAC;AAClB,UAAI,0BAA0B;AAC9B,UAAI,yBAAyB;AAC7B,UAAI,oBAAoB;AACxB,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,cAAM,OAAO,KAAK,OAAO,CAAC;AAC1B,gBAAQ;AAAA,eACC;AACD,gBAAI,SAAS,KAAK;AACd,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR;AAAA,YACJ,WACS,SAAS,KAAK;AACnB,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR;AAAA,YACJ,WACS,EAAE,MAAM,IAAI,wBAAwB,kBAAkB,KAAK,IAAI,GAAG;AACvE,qBAAO;AAAA,YACX;AACA;AAAA,eACC;AACD,gBAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAC9C,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR,kCAAoB;AAAA,YACxB,WACS,SAAS,KAAK;AACnB;AAAA,YACJ,WACS,SAAS,KAAK;AACnB,kBAAI,CAAC,EAAE,yBAAyB;AAC5B,wBAAQ,WAAW,IAAI;AAAA,cAC3B;AAAA,YACJ;AACA;AAAA,eACC;AACD,gBAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAC9C,yBAAW,KAAK,KAAK;AACrB,sBAAQ;AACR,kCAAoB;AAAA,YACxB,WACS,SAAS,KAAK;AACnB;AAAA,YACJ,WACS,SAAS,KAAK;AAEnB,kBAAI,MAAM,KAAK,SAAS,GAAG;AACvB,uBAAO;AAAA,cACX;AACA,kBAAI,CAAC,EAAE,wBAAwB;AAC3B,wBAAQ,WAAW,IAAI;AAAA,cAC3B;AAAA,YACJ;AACA;AAAA,eACC;AACD,gBAAI,SAAS,mBAAmB;AAC5B,sBAAQ,WAAW,IAAI;AACvB,kCAAoB;AAAA,YACxB;AACA;AAAA;AAAA,MAEZ;AACA,aAAO,CAAC,2BAA2B,CAAC;AAAA,IACxC;AACA,IAAM,yBAAyB;AAE/B,IAAM,qBAAqB;AAkG3B,IAAM,iBAAiB,oBAAI,IAAI,CAAC,iBAAiB,oBAAoB,CAAC;AAwKtE,IAAM,kBAAkB;AAAA,MACpB,CAAC,2BAAwD;AAAA,QACrD,SAAS;AAAA,QAGT,MAAM;AAAA,MACV;AAAA,MACA,CAAC,yBAAoD;AAAA,QACjD,SAAS,SAAO,2FACkB,6CACjB;AAAA,QACjB,MAAM;AAAA,MACV;AAAA,MACA,CAAC,yBAAoD;AAAA,QACjD,SAAS;AAAA,MAEb;AAAA,MACA,CAAC,iCAAoE;AAAA,QACjE,SAAS;AAAA,QAKT,MAAM;AAAA,MACV;AAAA,MACA,CAAC,yBAAoD;AAAA,QACjD,SAAS;AAAA,QACT,MAAM;AAAA,MACV;AAAA,MACA,CAAC,mCAAwE;AAAA,QACrE,SAAS;AAAA,QAKT,MAAM;AAAA,MACV;AAAA,MACA,CAAC,6BAA4D;AAAA,QACzD,SAAS;AAAA,MAEb;AAAA,MACA,CAAC,6BAA4D;AAAA,QACzD,SAAS;AAAA,QACT,MAAM;AAAA,MACV;AAAA,MACA,CAAC,oBAA2C;AAAA,QACxC,SAAS;AAAA,QAGT,MAAM;AAAA,MACV;AAAA,IACJ;AA4CA,IAAM,WAAW;AACjB,IAAM,YAAY;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,IACV;AACA,IAAM,uBAAuB;AAAA,MACzB,YAAY,CAAC,MAAM,IAAI;AAAA,MACvB,cAAc,MAAM;AAAA,MACpB,aAAa,MAAM;AAAA,MACnB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,gBAAgB,CAAC,YAAY,QAAQ,QAAQ,UAAU,CAAC,GAAG,OAAO,UAAU,GAAG;AAAA,MAC/E,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAW;AAAA,IACf;AA4TA,IAAM,6BAA2C,QAAQ,0BAA0B;AAorBnF,IAAM,wBAAwB,oBAAI,IAAI;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAmWD,IAAM,kBAAkB;AACxB,IAAM,cAAc,CAAC,MAAM,GAAG,cAAc,QAAQ,cAAc;AAmoBlE,IAAM,iBAAiB,CAAC,SAAS;AAC7B,aAAO,8CAA8C,KAAK,KAAK,IAAI;AAAA,IACvE;AACA,IAAM,mBAAmB,CAAC,SAAS,SAC9B,KAAK,SAAS,oBAAoB,KAAK,SAAS,mBACjD,CAAC,KAAK;AACV,IAAM,sBAAsB,CAAC,MAAM,WAAW,iBAAiB,MAAM,KAAK,OAAO,QAAQ;AAIzF,IAAM,sBAAsB,IAAI,OAAO,QAClC,6MAGI,MAAM,GAAG,EACT,KAAK,SAAS,IACnB,KAAK;AAET,IAAM,gBAAgB;AA8BtB,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC3C,UAAI,KAAK,SAAS,GAAuB;AACrC,aAAK,UAAU,kBAAkB,KAAK,SAAS,OAAO;AAAA,MAC1D,WACS,KAAK,SAAS,GAAiB;AAEpC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,gBAAM,MAAM,KAAK,MAAM;AAEvB,cAAI,IAAI,SAAS,KAAqB,IAAI,SAAS,OAAO;AACtD,kBAAM,MAAM,IAAI;AAChB,kBAAM,MAAM,IAAI;AAGhB,gBAAI,OACA,IAAI,SAAS,KACb,EAAE,IAAI,SAAS,QAAQ,MAAM;AAC7B,kBAAI,MAAM;AAAA,gBAAkB;AAAA,gBAAK;AAAA,gBAEjC,IAAI,SAAS;AAAA,cAAM;AAAA,YACvB;AACA,gBAAI,OAAO,IAAI,SAAS,KAA6B,CAAC,IAAI,UAAU;AAChE,kBAAI,MAAM,kBAAkB,KAAK,OAAO;AAAA,YAC5C;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAmBA,IAAM,cAAc,mCAAmC,uBAAuB,CAAC,MAAM,KAAK,YAAY;AAClG,aAAO,UAAU,MAAM,KAAK,SAAS,CAAC,QAAQ,QAAQ,WAAW;AAI7D,cAAM,WAAW,QAAQ,OAAO;AAChC,YAAI,IAAI,SAAS,QAAQ,MAAM;AAC/B,YAAI,MAAM;AACV,eAAO,OAAO,GAAG;AACb,gBAAM,UAAU,SAAS;AACzB,cAAI,WAAW,QAAQ,SAAS,GAAY;AACxC,mBAAO,QAAQ,SAAS;AAAA,UAC5B;AAAA,QACJ;AAGA,eAAO,MAAM;AACT,cAAI,QAAQ;AACR,mBAAO,cAAc,2BAA2B,QAAQ,KAAK,OAAO;AAAA,UACxE,OACK;AAED,kBAAM,kBAAkB,mBAAmB,OAAO,WAAW;AAC7D,4BAAgB,YAAY,2BAA2B,QAAQ,MAAM,OAAO,SAAS,SAAS,GAAG,OAAO;AAAA,UAC5G;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAiMD,IAAM,eAAe,mCAAmC,OAAO,CAAC,MAAM,KAAK,YAAY;AACnF,YAAM,EAAE,QAAQ,aAAa,IAAI;AACjC,aAAO,WAAW,MAAM,KAAK,SAAS,aAAW;AAG7C,cAAM,YAAY,qBAAqB,OAAO,WAAW,GAAG;AAAA,UACxD,QAAQ;AAAA,QACZ,CAAC;AACD,cAAM,aAAa,eAAe,IAAI;AACtC,cAAM,OAAO,QAAQ,MAAM,MAAM;AACjC,cAAM,UAAU,SAAS,MAAM,KAAK;AACpC,cAAM,SAAS,YACV,QAAQ,SAAS,IACZ,uBAAuB,QAAQ,MAAM,SAAS,IAAI,IAClD,QAAQ;AAClB,cAAM,cAAc,UAAU,qBAAqB,OAAO,MAAM,IAAI;AACpE,cAAM,mBAAmB,QAAQ,OAAO,SAAS,KAC7C,QAAQ,OAAO,YAAY;AAC/B,cAAM,eAAe,mBACf,KACA,UACI,MACA;AACV,gBAAQ,cAAc,gBAAgB,SAAS,OAAO,QAAQ,GAAG,QAAW,WAAW,gBACjF,OAAyC,OAAO,eAAe,qBAAqB,KAAK,QAAW,QAAW,MAAoB,CAAC,kBAAwC,OAAyB,KAAK,GAAG;AACnN,eAAO,MAAM;AAET,cAAI;AACJ,gBAAM,EAAE,SAAS,IAAI;AAErB,cAA0D,YAAY;AAClE,iBAAK,SAAS,KAAK,OAAK;AACpB,kBAAI,EAAE,SAAS,GAAiB;AAC5B,sBAAM,MAAM,SAAS,GAAG,KAAK;AAC7B,oBAAI,KAAK;AACL,0BAAQ,QAAQ,oBAAoB,IAAyC,IAAI,GAAG,CAAC;AACrF,yBAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL;AACA,gBAAM,sBAAsB,SAAS,WAAW,KAAK,SAAS,GAAG,SAAS;AAC1E,gBAAM,aAAa,aAAa,IAAI,IAC9B,OACA,cACE,KAAK,SAAS,WAAW,KACzB,aAAa,KAAK,SAAS,EAAE,IAC3B,KAAK,SAAS,KACd;AACV,cAAI,YAAY;AAEZ,yBAAa,WAAW;AACxB,gBAAI,cAAc,aAAa;AAI3B,yBAAW,YAAY,aAAa,OAAO;AAAA,YAC/C;AAAA,UACJ,WACS,qBAAqB;AAG1B,yBAAa,gBAAgB,SAAS,OAAO,QAAQ,GAAG,cAAc,uBAAuB,CAAC,WAAW,CAAC,IAAI,QAAW,KAAK,UAAU,MAClI,OACI,OAAO,eAAe,WACtB,KAAK,QAAW,QAAW,MAAM,QAAW,KAAuB;AAAA,UACjF,OACK;AAGD,yBAAa,SAAS,GACjB;AACL,gBAAI,cAAc,aAAa;AAC3B,yBAAW,YAAY,aAAa,OAAO;AAAA,YAC/C;AACA,gBAAI,WAAW,YAAY,CAAC,kBAAkB;AAC1C,kBAAI,WAAW,SAAS;AAEpB,6BAAa,UAAU;AACvB,6BAAa,oBAAoB,QAAQ,OAAO,WAAW,WAAW,CAAC;AAAA,cAC3E,OACK;AAED,6BAAa,eAAe,QAAQ,OAAO,WAAW,WAAW,CAAC;AAAA,cACtE;AAAA,YACJ;AACA,uBAAW,UAAU,CAAC;AACtB,gBAAI,WAAW,SAAS;AACpB,qBAAO,UAAU;AACjB,qBAAO,oBAAoB,QAAQ,OAAO,WAAW,WAAW,CAAC;AAAA,YACrE,OACK;AACD,qBAAO,eAAe,QAAQ,OAAO,WAAW,WAAW,CAAC;AAAA,YAChE;AAAA,UACJ;AACA,cAAI,MAAM;AACN,kBAAM,OAAO,yBAAyB,oBAAoB,QAAQ,aAAa;AAAA,cAC3E,uBAAuB,SAAS;AAAA,YACpC,CAAC,CAAC;AACF,iBAAK,OAAO,qBAAqB;AAAA,cAC7B,yBAAyB,CAAC,mBAAmB,KAAK,KAAK,GAAG,CAAC;AAAA,cAC3D,yBAAyB;AAAA,gBACrB;AAAA,gBACA,GAAI,SAAS,CAAC,wBAAwB,MAAM,IAAI,CAAC;AAAA,gBACjD,OAAO,QAAQ,aAAa,YAAY;AAAA,cAC5C,CAAC;AAAA,cACD,yBAAyB,CAAC,kBAAkB,UAAU,CAAC;AAAA,cACvD,uBAAuB,oBAAoB;AAAA,cAC3C,uBAAuB,cAAc;AAAA,YACzC,CAAC;AACD,sBAAU,UAAU,KAAK,MAAM,uBAAuB,QAAQ,GAAG,uBAAuB,OAAO,QAAQ,QAAQ,CAAC,CAAC;AAAA,UACrH,OACK;AACD,sBAAU,UAAU,KAAK,yBAAyB,oBAAoB,QAAQ,WAAW,GAAG,YAAY,IAAwB,CAAC;AAAA,UACrI;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAqCD,IAAM,aAAa;AAGnB,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AAoEtB,IAAM,kBAAkB,uBAAuB,aAAa,KAAK;AAQjE,IAAM,kBAAkB,CAAC,MAAM,YAAY;AACvC,UAAI,KAAK,SAAS,MACb,KAAK,YAAY,KACd,KAAK,YAAY,IAAmB;AAGxC,cAAM,QAAQ,QAAQ,MAAM,MAAM;AAClC,YAAI,OAAO;AACP,gBAAM;AACN,kBAAQ,OAAO;AACf,iBAAO,MAAM;AACT,oBAAQ,OAAO;AAAA,UACnB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAGA,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC3C,UAAI;AACJ,UAAI,eAAe,IAAI,KACnB,KAAK,MAAM,KAAK,OAAO,MACtB,OAAO,QAAQ,MAAM,KAAK,IAAI;AAC/B,cAAM,SAAU,KAAK,cAAc,mBAAmB,KAAK,KAAK,OAAO;AACvE,YAAI,QAAQ;AACR,gBAAM,EAAE,OAAO,KAAK,MAAM,IAAI;AAC9B,gBAAM,EAAE,gBAAgB,kBAAkB,IAAI;AAC9C,mBAAS,eAAe,KAAK;AAC7B,iBAAO,eAAe,GAAG;AACzB,mBAAS,eAAe,KAAK;AAC7B,iBAAO,MAAM;AACT,qBAAS,kBAAkB,KAAK;AAChC,mBAAO,kBAAkB,GAAG;AAC5B,qBAAS,kBAAkB,KAAK;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,IAAM,oBAAoB,CAAC,OAAO,UAAU,QAAQ,yBAAyB,OAAO,UAAU,OAAqB,MAAmB,SAAS,SAAS,SAAS,GAAG,MAAM,GAAG;AA8M7K,IAAM,qBAAqB,oBAAI,QAAQ;AAEvC,IAAM,mBAAmB,CAAC,MAAM,YAAY;AAGxC,aAAO,SAAS,uBAAuB;AACnC,eAAO,QAAQ;AACf,YAAI,EAAE,KAAK,SAAS,MACf,KAAK,YAAY,KACd,KAAK,YAAY,KAAqB;AAC1C;AAAA,QACJ;AACA,cAAM,EAAE,KAAK,MAAM,IAAI;AACvB,cAAMV,eAAc,KAAK,YAAY;AAGrC,YAAI,WAAWA,eACT,qBAAqB,MAAM,OAAO,IAClC,IAAI;AACV,cAAM,qBAAqB,SAAS,QAAQ,KAAK,SAAS,WAAW;AACrE,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,YAAY;AAChB,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,iBAEJ,sBACI,aAAa,YACb,aAAa,YACZ,CAACA,iBAKG,QAAQ,SAAS,QAAQ;AAElC,YAAI,MAAM,SAAS,GAAG;AAClB,gBAAM,mBAAmB,WAAW,MAAM,SAAS,QAAWA,cAAa,kBAAkB;AAC7F,uBAAa,iBAAiB;AAC9B,sBAAY,iBAAiB;AAC7B,6BAAmB,iBAAiB;AACpC,gBAAM,aAAa,iBAAiB;AACpC,4BACI,cAAc,WAAW,SACnB,sBAAsB,WAAW,IAAI,SAAO,mBAAmB,KAAK,OAAO,CAAC,CAAC,IAC7E;AACV,cAAI,iBAAiB,gBAAgB;AACjC,6BAAiB;AAAA,UACrB;AAAA,QACJ;AAEA,YAAI,KAAK,SAAS,SAAS,GAAG;AAC1B,cAAI,aAAa,YAAY;AAOzB,6BAAiB;AAEjB,yBAAa;AACb,gBAA+C,KAAK,SAAS,SAAS,GAAG;AACrE,sBAAQ,QAAQ,oBAAoB,IAAwC;AAAA,gBACxE,OAAO,KAAK,SAAS,GAAG,IAAI;AAAA,gBAC5B,KAAK,KAAK,SAAS,KAAK,SAAS,SAAS,GAAG,IAAI;AAAA,gBACjD,QAAQ;AAAA,cACZ,CAAC,CAAC;AAAA,YACN;AAAA,UACJ;AACA,gBAAM,qBAAqBA,gBAEvB,aAAa,YAEb,aAAa;AACjB,cAAI,oBAAoB;AACpB,kBAAM,EAAE,OAAO,gBAAgB,IAAI,WAAW,MAAM,OAAO;AAC3D,4BAAgB;AAChB,gBAAI,iBAAiB;AACjB,2BAAa;AAAA,YACjB;AAAA,UACJ,WACS,KAAK,SAAS,WAAW,KAAK,aAAa,UAAU;AAC1D,kBAAM,QAAQ,KAAK,SAAS;AAC5B,kBAAM,OAAO,MAAM;AAEnB,kBAAM,sBAAsB,SAAS,KACjC,SAAS;AACb,gBAAI,uBACA,gBAAgB,OAAO,OAAO,MAAM,GAAsB;AAC1D,2BAAa;AAAA,YACjB;AAGA,gBAAI,uBAAuB,SAAS,GAAc;AAC9C,8BAAgB;AAAA,YACpB,OACK;AACD,8BAAgB,KAAK;AAAA,YACzB;AAAA,UACJ,OACK;AACD,4BAAgB,KAAK;AAAA,UACzB;AAAA,QACJ;AAEA,YAAI,cAAc,GAAG;AACjB,cAAK,MAAwC;AACzC,gBAAI,YAAY,GAAG;AAEf,+BAAiB,YAAY,OAAO,eAAe;AAAA,YACvD,OACK;AAED,oBAAM,YAAY,OAAO,KAAK,cAAc,EACvC,IAAI,MAAM,EACV,OAAO,OAAK,IAAI,KAAK,YAAY,CAAC,EAClC,IAAI,OAAK,eAAe,EAAE,EAC1B,KAAK,IAAI;AACd,+BAAiB,YAAY,OAAO;AAAA,YACxC;AAAA,UACJ,OACK;AACD,6BAAiB,OAAO,SAAS;AAAA,UACrC;AACA,cAAI,oBAAoB,iBAAiB,QAAQ;AAC7C,gCAAoB,0BAA0B,gBAAgB;AAAA,UAClE;AAAA,QACJ;AACA,aAAK,cAAc,gBAAgB,SAAS,UAAU,YAAY,eAAe,gBAAgB,mBAAmB,iBAAiB,CAAC,CAAC,gBAAgB,OAA6BA,cAAa,KAAK,GAAG;AAAA,MAC7M;AAAA,IACJ;AAicA,IAAC,OACK,OAAO,OAAO,CAAC,CAAC,IAChB,CAAC;AACP,IAAC,OAAyC,OAAO,OAAO,CAAC,CAAC,IAAI,CAAC;AAC/D,IAAM,sBAAsB,CAAC,OAAO;AAChC,YAAM,QAAQ,uBAAO,OAAO,IAAI;AAChC,aAAQ,CAAC,QAAQ;AACb,cAAM,MAAM,MAAM;AAClB,eAAO,QAAQ,MAAM,OAAO,GAAG,GAAG;AAAA,MACtC;AAAA,IACJ;AACA,IAAM,aAAa;AAInB,IAAMU,YAAW,oBAAoB,CAAC,QAAQ;AAC1C,aAAO,IAAI,QAAQ,YAAY,CAAC,GAAG,MAAO,IAAI,EAAE,YAAY,IAAI,EAAG;AAAA,IACvE,CAAC;AAED,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC3C,UAAI,aAAa,IAAI,GAAG;AACpB,cAAM,EAAE,UAAU,IAAI,IAAI;AAC1B,cAAM,EAAE,UAAU,UAAU,IAAI,kBAAkB,MAAM,OAAO;AAC/D,cAAM,WAAW;AAAA,UACb,QAAQ,oBAAoB,gBAAgB;AAAA,UAC5C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AACA,YAAI,cAAc;AAClB,YAAI,WAAW;AACX,mBAAS,KAAK;AACd,wBAAc;AAAA,QAClB;AACA,YAAI,SAAS,QAAQ;AACjB,mBAAS,KAAK,yBAAyB,CAAC,GAAG,UAAU,OAAO,OAAO,GAAG;AACtE,wBAAc;AAAA,QAClB;AACA,YAAI,QAAQ,WAAW,CAAC,QAAQ,SAAS;AACrC,wBAAc;AAAA,QAClB;AACA,iBAAS,OAAO,WAAW;AAC3B,aAAK,cAAc,qBAAqB,QAAQ,OAAO,WAAW,GAAG,UAAU,GAAG;AAAA,MACtF;AAAA,IACJ;AA4CA,IAAM,UAAU;AAChB,IAAM,cAAc,CAAC,KAAK,MAAM,SAAS,cAAc;AACnD,YAAM,EAAE,KAAK,WAAW,IAAI,IAAI;AAChC,UAAI,CAAC,IAAI,OAAO,CAAC,UAAU,QAAQ;AAC/B,gBAAQ,QAAQ,oBAAoB,IAA+B,GAAG,CAAC;AAAA,MAC3E;AACA,UAAI;AACJ,UAAI,IAAI,SAAS,GAA2B;AACxC,YAAI,IAAI,UAAU;AACd,cAAI,UAAU,IAAI;AAElB,cAAI,QAAQ,WAAW,MAAM,GAAG;AAC5B,sBAAU,SAAS,QAAQ,MAAM,CAAC;AAAA,UACtC;AAEA,sBAAY,uBAAuB,aAAa,SAAW,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG;AAAA,QACvF,OACK;AAED,sBAAY,yBAAyB;AAAA,YACjC,GAAG,QAAQ,aAAa,cAAc;AAAA,YACtC;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,OACK;AAED,oBAAY;AACZ,kBAAU,SAAS,QAAQ,GAAG,QAAQ,aAAa,cAAc,IAAI;AACrE,kBAAU,SAAS,KAAK,GAAG;AAAA,MAC/B;AAEA,UAAI,MAAM,IAAI;AACd,UAAI,OAAO,CAAC,IAAI,QAAQ,KAAK,GAAG;AAC5B,cAAM;AAAA,MACV;AACA,UAAI,cAAc,QAAQ,iBAAiB,CAAC,OAAO,CAAC,QAAQ;AAC5D,UAAI,KAAK;AACL,cAAM,cAAc,mBAAmB,IAAI,OAAO;AAClD,cAAM,oBAAoB,EAAE,eAAe,QAAQ,KAAK,IAAI,OAAO;AACnE,cAAM,wBAAwB,IAAI,QAAQ,SAAS,GAAG;AACtD,YAA+C,MAAM;AACjD,oCAA0B,KAAK,SAAS,OAAO,qBAAqB;AAAA,QACxE;AACA,YAAI,qBAAsB,eAAe,aAAc;AAEnD,gBAAM,yBAAyB;AAAA,YAC3B,GAAG,oBACG,WACA,GAAG,oBAAoB,wBAAwB,MAAM;AAAA,YAC3D;AAAA,YACA,wBAAwB,MAAM;AAAA,UAClC,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,MAAM;AAAA,QACN,OAAO;AAAA,UACH,qBAAqB,WAAW,OAAO,uBAAuB,YAAY,OAAO,GAAG,CAAC;AAAA,QACzF;AAAA,MACJ;AAEA,UAAI,WAAW;AACX,cAAM,UAAU,GAAG;AAAA,MACvB;AACA,UAAI,aAAa;AAIb,YAAI,MAAM,GAAG,QAAQ,QAAQ,MAAM,IAAI,MAAM,GAAG,KAAK;AAAA,MACzD;AAEA,UAAI,MAAM,QAAQ,OAAM,EAAE,IAAI,eAAe,IAAK;AAClD,aAAO;AAAA,IACX;AAKA,IAAM,gBAAgB,CAAC,KAAK,OAAO,YAAY;AAC3C,YAAM,EAAE,KAAK,WAAW,IAAI,IAAI;AAChC,YAAM,MAAM,IAAI;AAChB,UAAI,IAAI,SAAS,GAA2B;AACxC,YAAI,SAAS,QAAQ,GAAG;AACxB,YAAI,SAAS,KAAK,SAAS;AAAA,MAC/B,WACS,CAAC,IAAI,UAAU;AACpB,YAAI,UAAU,GAAG,IAAI;AAAA,MACzB;AAEA,UAAI,UAAU,SAAS,OAAO,GAAG;AAC7B,YAAI,IAAI,SAAS,GAA2B;AACxC,cAAI,IAAI,UAAU;AACd,gBAAI,UAAU,SAAW,IAAI,OAAO;AAAA,UACxC,OACK;AACD,gBAAI,UAAU,GAAG,QAAQ,aAAa,QAAQ,KAAK,IAAI;AAAA,UAC3D;AAAA,QACJ,OACK;AACD,cAAI,SAAS,QAAQ,GAAG,QAAQ,aAAa,QAAQ,IAAI;AACzD,cAAI,SAAS,KAAK,GAAG;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,CAAC,QAAQ,OAAO;AAChB,YAAI,UAAU,SAAS,MAAM,GAAG;AAC5B,uBAAa,KAAK,GAAG;AAAA,QACzB;AACA,YAAI,UAAU,SAAS,MAAM,GAAG;AAC5B,uBAAa,KAAK,GAAG;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,CAAC,OACA,IAAI,SAAS,KAA6B,CAAC,IAAI,QAAQ,KAAK,GAAI;AACjE,gBAAQ,QAAQ,oBAAoB,IAAiC,GAAG,CAAC;AACzE,eAAO;AAAA,UACH,OAAO,CAAC,qBAAqB,KAAK,uBAAuB,IAAI,MAAM,GAAG,CAAC,CAAC;AAAA,QAC5E;AAAA,MACJ;AACA,aAAO;AAAA,QACH,OAAO,CAAC,qBAAqB,KAAK,GAAG,CAAC;AAAA,MAC1C;AAAA,IACJ;AACA,IAAM,eAAe,CAAC,KAAK,WAAW;AAClC,UAAI,IAAI,SAAS,GAA2B;AACxC,YAAI,IAAI,UAAU;AACd,cAAI,UAAU,SAAS,IAAI;AAAA,QAC/B,OACK;AACD,cAAI,UAAU,KAAK,YAAY,IAAI;AAAA,QACvC;AAAA,MACJ,OACK;AACD,YAAI,SAAS,QAAQ,IAAI,aAAa;AACtC,YAAI,SAAS,KAAK,GAAG;AAAA,MACzB;AAAA,IACJ;AAIA,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACrC,UAAI,KAAK,SAAS,KACd,KAAK,SAAS,KACd,KAAK,SAAS,MACd,KAAK,SAAS,IAAoB;AAGlC,eAAO,MAAM;AACT,gBAAM,WAAW,KAAK;AACtB,cAAI,mBAAmB;AACvB,cAAI,UAAU;AACd,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,kBAAM,QAAQ,SAAS;AACvB,gBAAI,OAAO,KAAK,GAAG;AACf,wBAAU;AACV,uBAAS,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAC1C,sBAAM,OAAO,SAAS;AACtB,oBAAI,OAAO,IAAI,GAAG;AACd,sBAAI,CAAC,kBAAkB;AACnB,uCAAmB,SAAS,KAAK,yBAAyB,CAAC,KAAK,GAAG,MAAM,GAAG;AAAA,kBAChF;AAEA,mCAAiB,SAAS,KAAK,OAAO,IAAI;AAC1C,2BAAS,OAAO,GAAG,CAAC;AACpB;AAAA,gBACJ,OACK;AACD,qCAAmB;AACnB;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AACA,cAAI,CAAC,WAKA,SAAS,WAAW,MAChB,KAAK,SAAS,KACV,KAAK,SAAS,KACX,KAAK,YAAY,KAMjB,CAAC,KAAK,MAAM,KAAK,OAAK,EAAE,SAAS,KAC7B,CAAC,QAAQ,oBAAoB,EAAE,KAAK,KAIxC,EAAE,KAAK,QAAQ,cAAgB;AAC3C;AAAA,UACJ;AAGA,mBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,kBAAM,QAAQ,SAAS;AACvB,gBAAI,OAAO,KAAK,KAAK,MAAM,SAAS,GAA6B;AAC7D,oBAAM,WAAW,CAAC;AAGlB,kBAAI,MAAM,SAAS,KAAgB,MAAM,YAAY,KAAK;AACtD,yBAAS,KAAK,KAAK;AAAA,cACvB;AAEA,kBAAI,CAAC,QAAQ,OACT,gBAAgB,OAAO,OAAO,MAAM,GAAsB;AAC1D,yBAAS,KAAK,KACR,OAAyC,OAAO,eAAe,UAAqB,GAAG;AAAA,cACjG;AACA,uBAAS,KAAK;AAAA,gBACV,MAAM;AAAA,gBACN,SAAS;AAAA,gBACT,KAAK,MAAM;AAAA,gBACX,aAAa,qBAAqB,QAAQ,OAAO,WAAW,GAAG,QAAQ;AAAA,cAC3E;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAM,OAAO,oBAAI,QAAQ;AACzB,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACrC,UAAI,KAAK,SAAS,KAAmB,QAAQ,MAAM,QAAQ,IAAI,GAAG;AAC9D,YAAI,KAAK,IAAI,IAAI,KAAK,QAAQ,SAAS;AACnC;AAAA,QACJ;AACA,aAAK,IAAI,IAAI;AACb,gBAAQ,UAAU;AAClB,gBAAQ,OAAO,kBAAkB;AACjC,eAAO,MAAM;AACT,kBAAQ,UAAU;AAClB,gBAAM,MAAM,QAAQ;AACpB,cAAI,IAAI,aAAa;AACjB,gBAAI,cAAc,QAAQ,MAAM,IAAI,aAAa,IAAkB;AAAA,UACvE;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAM,iBAAiB,CAAC,KAAK,MAAM,YAAY;AAC3C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACN,gBAAQ,QAAQ,oBAAoB,IAAkC,IAAI,GAAG,CAAC;AAC9E,eAAO,qBAAqB;AAAA,MAChC;AACA,YAAM,SAAS,IAAI,IAAI;AACvB,YAAM,YAAY,IAAI,SAAS,IAA4B,IAAI,UAAU;AAGzE,cAAQ,gBAAgB;AACxB,YAAM,WAAW;AACjB,UAAI,CAAC,UAAU,KAAK,KACf,CAAC,mBAAmB,SAAS,KAAK,CAAC,UAAW;AAC/C,gBAAQ,QAAQ,oBAAoB,IAAyC,IAAI,GAAG,CAAC;AACrF,eAAO,qBAAqB;AAAA,MAChC;AACA,YAAM,WAAW,MAAM,MAAM,uBAAuB,cAAc,IAAI;AACtE,YAAM,YAAY,MACZ,YAAY,GAAG,IACX,YAAY,IAAI,YAChB,yBAAyB,CAAC,kBAAkB,GAAG,CAAC,IACpD;AACN,UAAI;AACJ,YAAM,WAAW,QAAQ,OAAO,kBAAkB;AAClD;AACI,wBAAgB,yBAAyB;AAAA,UACrC,GAAG;AAAA,UACH;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AACA,YAAM,QAAQ;AAAA,QAEV,qBAAqB,UAAU,IAAI,GAAG;AAAA,QAEtC,qBAAqB,WAAW,aAAa;AAAA,MACjD;AAEA,UAAI,IAAI,UAAU,UAAU,KAAK,YAAY,GAAmB;AAC5D,cAAM,YAAY,IAAI,UACjB,IAAI,QAAM,mBAAmB,CAAC,IAAI,IAAI,KAAK,UAAU,CAAC,KAAK,QAAQ,EACnE,KAAK,IAAI;AACd,cAAM,eAAe,MACf,YAAY,GAAG,IACX,GAAG,IAAI,qBACP,yBAAyB,CAAC,KAAK,gBAAgB,CAAC,IACpD;AACN,cAAM,KAAK,qBAAqB,cAAc,uBAAuB,KAAK,eAAe,OAAO,IAAI,KAAK,CAAiB,CAAC,CAAC;AAAA,MAChI;AACA,aAAO,qBAAqB,KAAK;AAAA,IACrC;AAKA,IAAM,sBAAsB;AAC5B,IAAM,kBAAkB,CAAC,MAAM,YAAY;AACvC,UAAI,CAAC,gBAAgB,mBAA0C,OAAO,GAAG;AACrE;AAAA,MACJ;AACA,UAAI,KAAK,SAAS,GAAuB;AAGrC,sBAAc,KAAK,SAAS,OAAO;AAAA,MACvC;AACA,UAAI,KAAK,SAAS,GAAiB;AAC/B,aAAK,MAAM,QAAQ,CAAC,SAAS;AACzB,cAAI,KAAK,SAAS,KACd,KAAK,SAAS,SACd,KAAK,KAAK;AACV,0BAAc,KAAK,KAAK,OAAO;AAAA,UACnC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAmJA,IAAM,SAAS,oBAAI,QAAQ;AAC3B,IAAM,gBAAgB,CAAC,MAAM,YAAY;AACrC,UAAI,KAAK,SAAS,GAAiB;AAC/B,cAAM,MAAM,QAAQ,MAAM,MAAM;AAChC,YAAI,CAAC,OAAO,OAAO,IAAI,IAAI,GAAG;AAC1B;AAAA,QACJ;AACA,eAAO,IAAI,IAAI;AACf,eAAO,MAAM;AACT,gBAAM,cAAc,KAAK,eACrB,QAAQ,YAAY;AACxB,cAAI,eAAe,YAAY,SAAS,IAAqB;AAEzD,gBAAI,KAAK,YAAY,GAAmB;AACpC,wBAAU,aAAa,OAAO;AAAA,YAClC;AACA,iBAAK,cAAc,qBAAqB,QAAQ,OAAO,SAAS,GAAG;AAAA,cAC/D,IAAI;AAAA,cACJ,yBAAyB,QAAW,WAAW;AAAA,cAC/C;AAAA,cACA,OAAO,QAAQ,QAAQ;AAAA,YAC3B,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AA8DA,IAAM,yBAAyB,OAAO,EAAE,OAAO,CAAC,EAAE;AAAA;AAAA;;;AC1vJlD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,SAAS,kBAAkB,KAAK,SAAS,OAAO;AAC5C,MAAI,CAAC,SAAS;AACV,cAAU,SAAS,cAAc,KAAK;AAAA,EAC1C;AACA,MAAI,QAAQ;AACR,YAAQ,YAAY,aAAa,IAAI,QAAQ,MAAM,QAAQ;AAC3D,WAAO,QAAQ,SAAS,GAAG,aAAa,KAAK;AAAA,EACjD,OACK;AACD,YAAQ,YAAY;AACpB,WAAO,QAAQ;AAAA,EACnB;AACJ;AAiGA,SAAS,uBAAuB,MAAM,KAAK;AACvC,SAAO,oBAAoB,MAAM,KAAM,OAAkD,mBAAmB,MAAS;AACzH;AAqSA,SAAS,oBAAoB,MAAM;AAE/B,QAAM,WAAY,KAAK,WAAW,KAAK,SAAS,OAAO,OAAK,EAAE,SAAS,KACnE,EAAE,EAAE,SAAS,KAAgB,CAAC,EAAE,QAAQ,KAAK,EAAE;AACnD,QAAM,QAAQ,SAAS;AACvB,SAAQ,SAAS,WAAW,KACxB,MAAM,SAAS,MACd,MAAM,SAAS,KAAc,MAAM,SAAS,KAAK,mBAAmB;AAC7E;AAuBA,SAAS,QAAQ,UAAU,UAAU,CAAC,GAAG;AACrC,SAAO,YAAY,UAAU,OAAO,CAAC,GAAG,eAAe,SAAS;AAAA,IAC5D,gBAAgB;AAAA,MAIZ;AAAA,MACA,GAAG;AAAA,MACH,GAAI,QAAQ,kBAAkB,CAAC;AAAA,IACnC;AAAA,IACA,qBAAqB,OAAO,CAAC,GAAG,wBAAwB,QAAQ,uBAAuB,CAAC,CAAC;AAAA,IACzF,gBAAgB;AAAA,EACpB,CAAC,CAAC;AACN;AACA,SAAS,MAAM,UAAU,UAAU,CAAC,GAAG;AACnC,SAAO,UAAU,UAAU,OAAO,CAAC,GAAG,eAAe,OAAO,CAAC;AACjE;AAheA,IAIM,eACA,kBACA,cACA,gBACA,iBACA,qBACA,gBACA,QACA,YACA,kBAeF,SAeE,oBACA,eAwEA,gBAiBA,gBAQA,kBAcA,gBAgBA,gBAoBAC,iBAmFA,uBACA,kBAQA,kBACA,iBACA,kBA+CA,gBAcAC,cAwCA,eAWA,qBA8CA,sBASA,mBAIA;AAxcN;AAAA;AAAA;AACA;AACA;AAEA,IAAM,gBAAgB,OAAQ,OAAyC,gBAAgB,EAAE;AACzF,IAAM,mBAAmB,OAAQ,OAAyC,mBAAmB,EAAE;AAC/F,IAAM,eAAe,OAAQ,OAAyC,eAAe,EAAE;AACvF,IAAM,iBAAiB,OAAQ,OAAyC,iBAAiB,EAAE;AAC3F,IAAM,kBAAkB,OAAQ,OAAyC,kBAAkB,EAAE;AAC7F,IAAM,sBAAsB,OAAQ,OAAyC,sBAAsB,EAAE;AACrG,IAAM,iBAAiB,OAAQ,OAAyC,iBAAiB,EAAE;AAC3F,IAAM,SAAS,OAAQ,OAAyC,UAAU,EAAE;AAC5E,IAAM,aAAa,OAAQ,OAAyC,eAAe,EAAE;AACrF,IAAM,mBAAmB,OAAQ,OAAyC,oBAAoB,EAAE;AAChG,2BAAuB;AAAA,MACnB,CAAC,gBAAgB;AAAA,MACjB,CAAC,mBAAmB;AAAA,MACpB,CAAC,eAAe;AAAA,MAChB,CAAC,iBAAiB;AAAA,MAClB,CAAC,kBAAkB;AAAA,MACnB,CAAC,sBAAsB;AAAA,MACvB,CAAC,iBAAiB;AAAA,MAClB,CAAC,SAAS;AAAA,MACV,CAAC,aAAa;AAAA,MACd,CAAC,mBAAmB;AAAA,IACxB,CAAC;AAkBD,IAAM,qBAAmC,QAAQ,gCAAgC,IAAI;AACrF,IAAM,gBAAgB;AAAA,MAClB;AAAA,MACA,aAAa,SAAO,UAAU,GAAG,KAAK,SAAS,GAAG;AAAA,MAClD,UAAU,SAAO,QAAQ;AAAA,MACzB,gBAAgB;AAAA,MAChB,oBAAoB,CAAC,QAAQ;AACzB,YAAI,cAAc,KAAK,YAAY,GAAG;AAClC,iBAAO;AAAA,QACX,WACS,cAAc,KAAK,iBAAiB,GAAG;AAC5C,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MAEA,aAAa,KAAK,QAAQ;AACtB,YAAI,KAAK,SAAS,OAAO,KAAK;AAC9B,YAAI,UAAU,OAAO,GAAiB;AAClC,cAAI,OAAO,QAAQ,kBAAkB;AACjC,gBAAI,QAAQ,OAAO;AACf,qBAAO;AAAA,YACX;AACA,gBAAI,OAAO,MAAM,KAAK,OAAK,EAAE,SAAS,KAClC,EAAE,SAAS,cACX,EAAE,SAAS,SACV,EAAE,MAAM,YAAY,eACjB,EAAE,MAAM,YAAY,wBAAwB,GAAG;AACnD,mBAAK;AAAA,YACT;AAAA,UACJ,WACS,qBAAqB,KAAK,OAAO,GAAG,KACzC,QAAQ,YACR,QAAQ,cAAc;AACtB,iBAAK;AAAA,UACT;AAAA,QACJ,WACS,UAAU,OAAO,GAAa;AACnC,cAAI,OAAO,QAAQ,mBACf,OAAO,QAAQ,UACf,OAAO,QAAQ,SAAS;AACxB,iBAAK;AAAA,UACT;AAAA,QACJ;AACA,YAAI,OAAO,GAAc;AACrB,cAAI,QAAQ,OAAO;AACf,mBAAO;AAAA,UACX;AACA,cAAI,QAAQ,QAAQ;AAChB,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,MAEA,YAAY,EAAE,KAAK,GAAG,GAAG;AACrB,YAAI,OAAO,GAAc;AACrB,cAAI,QAAQ,cAAc,QAAQ,SAAS;AACvC,mBAAO;AAAA,UACX;AACA,cAAI,mBAAmB,GAAG,GAAG;AACzB,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAQA,IAAM,iBAAiB,UAAQ;AAC3B,UAAI,KAAK,SAAS,GAAiB;AAC/B,aAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AACzB,cAAI,EAAE,SAAS,KAAqB,EAAE,SAAS,WAAW,EAAE,OAAO;AAE/D,iBAAK,MAAM,KAAK;AAAA,cACZ,MAAM;AAAA,cACN,MAAM;AAAA,cACN,KAAK,uBAAuB,SAAS,MAAM,EAAE,GAAG;AAAA,cAChD,KAAK,eAAe,EAAE,MAAM,SAAS,EAAE,GAAG;AAAA,cAC1C,WAAW,CAAC;AAAA,cACZ,KAAK,EAAE;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,IAAM,iBAAiB,CAAC,SAAS,QAAQ;AACrC,YAAM,aAAa,iBAAiB,OAAO;AAC3C,aAAO,uBAAuB,KAAK,UAAU,UAAU,GAAG,OAAO,KAAK,CAAqB;AAAA,IAC/F;AAKA,IAAM,mBAAmB;AAAA,MACrB,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAwC;AAAA,MACzC,CAAC,KAAoC;AAAA,MACrC,CAAC,KAA2C;AAAA,MAC5C,CAAC,KAAuC;AAAA,MACxC,CAAC,KAAkC;AAAA,MACnC,CAAC,KAAyC;AAAA,MAC1C,CAAC,KAAqC;AAAA,IAC1C;AAEA,IAAM,iBAAiB,CAAC,KAAK,MAAM,YAAY;AAC3C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACN,gBAAQ,QAAQ,uBAAuB,IAAiC,GAAG,CAAC;AAAA,MAChF;AACA,UAAI,KAAK,SAAS,QAAQ;AACtB,gBAAQ,QAAQ,uBAAuB,IAAiC,GAAG,CAAC;AAC5E,aAAK,SAAS,SAAS;AAAA,MAC3B;AACA,aAAO;AAAA,QACH,OAAO;AAAA,UACH,qBAAqB,uBAAuB,aAAa,MAAM,GAAG,GAAG,OAAO,uBAAuB,IAAI,IAAI,CAAC;AAAA,QAChH;AAAA,MACJ;AAAA,IACJ;AAEA,IAAM,iBAAiB,CAAC,KAAK,MAAM,YAAY;AAC3C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACN,gBAAQ,QAAQ,uBAAuB,IAAiC,GAAG,CAAC;AAAA,MAChF;AACA,UAAI,KAAK,SAAS,QAAQ;AACtB,gBAAQ,QAAQ,uBAAuB,IAAiC,GAAG,CAAC;AAC5E,aAAK,SAAS,SAAS;AAAA,MAC3B;AACA,aAAO;AAAA,QACH,OAAO;AAAA,UACH,qBAAqB,uBAAuB,eAAe,IAAI,GAAG,MAC5D,gBAAgB,KAAK,OAAO,IAAI,IAC5B,MACA,qBAAqB,QAAQ,aAAa,iBAAiB,GAAG,CAAC,GAAG,GAAG,GAAG,IAC5E,uBAAuB,IAAI,IAAI,CAAC;AAAA,QAC1C;AAAA,MACJ;AAAA,IACJ;AAEA,IAAMD,kBAAiB,CAAC,KAAK,MAAM,YAAY;AAC3C,YAAM,aAAa,eAAiB,KAAK,MAAM,OAAO;AAEtD,UAAI,CAAC,WAAW,MAAM,UAAU,KAAK,YAAY,GAAmB;AAChE,eAAO;AAAA,MACX;AACA,UAAI,IAAI,KAAK;AACT,gBAAQ,QAAQ,uBAAuB,IAAmC,IAAI,IAAI,GAAG,CAAC;AAAA,MAC1F;AACA,eAAS,uBAAuB;AAC5B,cAAM,QAAQ,SAAS,MAAM,OAAO;AACpC,YAAI,OAAO;AACP,kBAAQ,QAAQ,uBAAuB,IAAsC,MAAM,GAAG,CAAC;AAAA,QAC3F;AAAA,MACJ;AACA,YAAM,EAAE,IAAI,IAAI;AAChB,YAAM,kBAAkB,QAAQ,gBAAgB,GAAG;AACnD,UAAI,QAAQ,WACR,QAAQ,cACR,QAAQ,YACR,iBAAiB;AACjB,YAAI,iBAAiB;AACrB,YAAI,gBAAgB;AACpB,YAAI,QAAQ,WAAW,iBAAiB;AACpC,gBAAM,OAAO,SAAS,MAAM,MAAM;AAClC,cAAI,MAAM;AACN,gBAAI,KAAK,SAAS,GAAmB;AAEjC,+BAAiB;AAAA,YACrB,WACS,KAAK,OAAO;AACjB,sBAAQ,KAAK,MAAM;AAAA,qBACV;AACD,mCAAiB;AACjB;AAAA,qBACC;AACD,mCAAiB;AACjB;AAAA,qBACC;AACD,kCAAgB;AAChB,0BAAQ,QAAQ,uBAAuB,IAA0C,IAAI,GAAG,CAAC;AACzF;AAAA;AAGA,kBAA2C,qBAAqB;AAChE;AAAA;AAAA,YAEZ;AAAA,UACJ,WACS,mBAAmB,IAAI,GAAG;AAG/B,6BAAiB;AAAA,UACrB,OACK;AAED,YAA2C,qBAAqB;AAAA,UACpE;AAAA,QACJ,WACS,QAAQ,UAAU;AACvB,2BAAiB;AAAA,QACrB,OACK;AAED,UAA2C,qBAAqB;AAAA,QACpE;AAIA,YAAI,CAAC,eAAe;AAChB,qBAAW,cAAc,QAAQ,OAAO,cAAc;AAAA,QAC1D;AAAA,MACJ,OACK;AACD,gBAAQ,QAAQ,uBAAuB,IAAuC,IAAI,GAAG,CAAC;AAAA,MAC1F;AAGA,iBAAW,QAAQ,WAAW,MAAM,OAAO,OAAK,EAAE,EAAE,IAAI,SAAS,KAC7D,EAAE,IAAI,YAAY,aAAa;AACnC,aAAO;AAAA,IACX;AAEA,IAAM,wBAAsC,QAAQ,sBAAsB;AAC1E,IAAM,mBAAiC;AAAA,MAEvC;AAAA,IAIY;AAEZ,IAAM,mBAAiC,QAAQ,YAAY;AAC3D,IAAM,kBAAgC,QAAQ,gCAAgC,IAAI;AAClF,IAAM,mBAAmB,CAAC,KAAK,WAAW,SAAS,QAAQ;AACvD,YAAM,eAAe,CAAC;AACtB,YAAM,kBAAkB,CAAC;AACzB,YAAM,uBAAuB,CAAC;AAC9B,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,cAAM,WAAW,UAAU;AAC3B,YAAI,aAAa,YACb,mBAAmB,wBAAmD,SAAS,GAAG,GAAG;AACrF,+BAAqB,KAAK,QAAQ;AAAA,QACtC,WACS,sBAAsB,QAAQ,GAAG;AAGtC,+BAAqB,KAAK,QAAQ;AAAA,QACtC,OACK;AAED,cAAI,iBAAiB,QAAQ,GAAG;AAC5B,gBAAI,YAAY,GAAG,GAAG;AAClB,kBAAI,gBAAgB,IAAI,OAAO,GAAG;AAC9B,6BAAa,KAAK,QAAQ;AAAA,cAC9B,OACK;AACD,gCAAgB,KAAK,QAAQ;AAAA,cACjC;AAAA,YACJ,OACK;AACD,2BAAa,KAAK,QAAQ;AAC1B,8BAAgB,KAAK,QAAQ;AAAA,YACjC;AAAA,UACJ,OACK;AACD,gBAAI,iBAAiB,QAAQ,GAAG;AAC5B,8BAAgB,KAAK,QAAQ;AAAA,YACjC,OACK;AACD,2BAAa,KAAK,QAAQ;AAAA,YAC9B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,IAAM,iBAAiB,CAAC,KAAK,UAAU;AACnC,YAAM,gBAAgB,YAAY,GAAG,KAAK,IAAI,QAAQ,YAAY,MAAM;AACxE,aAAO,gBACD,uBAAuB,OAAO,IAAI,IAClC,IAAI,SAAS,IACT,yBAAyB;AAAA,QACvB;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,QACtB;AAAA,QACA;AAAA,MACJ,CAAC,IACC;AAAA,IACd;AACA,IAAMC,eAAc,CAAC,KAAK,MAAM,YAAY;AACxC,aAAO,YAAc,KAAK,MAAM,SAAS,gBAAc;AACnD,cAAM,EAAE,UAAU,IAAI;AACtB,YAAI,CAAC,UAAU;AACX,iBAAO;AACX,YAAI,EAAE,KAAK,OAAO,WAAW,IAAI,WAAW,MAAM;AAClD,cAAM,EAAE,cAAc,iBAAiB,qBAAqB,IAAI,iBAAiB,KAAK,WAAW,SAAS,IAAI,GAAG;AAEjH,YAAI,gBAAgB,SAAS,OAAO,GAAG;AACnC,gBAAM,eAAe,KAAK,eAAe;AAAA,QAC7C;AACA,YAAI,gBAAgB,SAAS,QAAQ,GAAG;AACpC,gBAAM,eAAe,KAAK,WAAW;AAAA,QACzC;AACA,YAAI,gBAAgB,QAAQ;AACxB,uBAAa,qBAAqB,QAAQ,OAAO,mBAAmB,GAAG;AAAA,YACnE;AAAA,YACA,KAAK,UAAU,eAAe;AAAA,UAClC,CAAC;AAAA,QACL;AACA,YAAI,aAAa,WAEZ,CAAC,YAAY,GAAG,KAAK,gBAAgB,IAAI,OAAO,IAAI;AACrD,uBAAa,qBAAqB,QAAQ,OAAO,cAAc,GAAG;AAAA,YAC9D;AAAA,YACA,KAAK,UAAU,YAAY;AAAA,UAC/B,CAAC;AAAA,QACL;AACA,YAAI,qBAAqB,QAAQ;AAC7B,gBAAM,kBAAkB,qBAAqB,IAAI,UAAU,EAAE,KAAK,EAAE;AACpE,gBAAM,YAAY,GAAG,IACf,uBAAuB,GAAG,IAAI,UAAU,mBAAmB,IAAI,IAC/D,yBAAyB,CAAC,KAAK,KAAK,QAAQ,kBAAkB,CAAC;AAAA,QACzE;AACA,eAAO;AAAA,UACH,OAAO,CAAC,qBAAqB,KAAK,UAAU,CAAC;AAAA,QACjD;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,IAAM,gBAAgB,CAAC,KAAK,MAAM,YAAY;AAC1C,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI,CAAC,KAAK;AACN,gBAAQ,QAAQ,uBAAuB,IAAiC,GAAG,CAAC;AAAA,MAChF;AACA,aAAO;AAAA,QACH,OAAO,CAAC;AAAA,QACR,aAAa,QAAQ,OAAO,MAAM;AAAA,MACtC;AAAA,IACJ;AAEA,IAAM,sBAAsB,CAAC,MAAM,YAAY;AAC3C,UAAI,KAAK,SAAS,KACd,KAAK,YAAY,GAAmB;AACpC,cAAM,YAAY,QAAQ,mBAAmB,KAAK,GAAG;AACrD,YAAI,cAAc,YAAY;AAC1B,iBAAO,MAAM;AACT,gBAAI,CAAC,KAAK,SAAS,QAAQ;AACvB;AAAA,YACJ;AAEA,gBAAI,oBAAoB,IAAI,GAAG;AAC3B,sBAAQ,QAAQ,uBAAuB,IAAwC;AAAA,gBAC3E,OAAO,KAAK,SAAS,GAAG,IAAI;AAAA,gBAC5B,KAAK,KAAK,SAAS,KAAK,SAAS,SAAS,GAAG,IAAI;AAAA,gBACjD,QAAQ;AAAA,cACZ,CAAC,CAAC;AAAA,YACN;AAGA,kBAAM,QAAQ,KAAK,SAAS;AAC5B,gBAAI,MAAM,SAAS,GAAiB;AAChC,yBAAW,KAAK,MAAM,OAAO;AACzB,oBAAI,EAAE,SAAS,KAAqB,EAAE,SAAS,QAAQ;AACnD,uBAAK,MAAM,KAAK;AAAA,oBACZ,MAAM;AAAA,oBACN,MAAM;AAAA,oBACN,OAAO;AAAA,oBACP,KAAK,KAAK;AAAA,kBACd,CAAC;AAAA,gBACL;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAWA,IAAM,uBAAuB,CAAC,MAAM,YAAY;AAC5C,UAAI,KAAK,SAAS,KACd,KAAK,YAAY,MAChB,KAAK,QAAQ,YAAY,KAAK,QAAQ,UAAU;AACjD,gBAAQ,QAAQ,uBAAuB,IAAoC,KAAK,GAAG,CAAC;AACpF,gBAAQ,WAAW;AAAA,MACvB;AAAA,IACJ;AAEA,IAAM,oBAAoB;AAAA,MACtB;AAAA,MACA,GAAK,OAAyC,CAAC,mBAAmB,IAAI,CAAC;AAAA,IAC3E;AACA,IAAM,yBAAyB;AAAA,MAC3B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAOD;AAAA,MACP,IAAIC;AAAA,MACJ,MAAM;AAAA,IACV;AAAA;AAAA;;;AC/cA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAE5D,QAAI,cAAc;AAClB,QAAI,aAAa;AACjB,QAAI,SAAS;AAEb,aAAS,kBAAkB,GAAG;AAC5B,UAAI,KAAK,EAAE;AAAY,eAAO;AAC9B,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACL,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAClC,YAAE,KAAK,EAAE;AAAA,QACX,CAAC;AAAA,MACH;AACA,QAAE,aAAa;AACf,aAAO,OAAO,OAAO,CAAC;AAAA,IACxB;AAEA,QAAI,wBAAqC,kBAAkB,UAAU;AAGrE,QAAM,eAAe,uBAAO,OAAO,IAAI;AACvC,aAAS,kBAAkB,UAAU,SAAS;AAC1C,UAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC5B,YAAI,SAAS,UAAU;AACnB,qBAAW,SAAS;AAAA,QACxB,OACK;AACD,qBAAW,KAAK,6BAA6B,QAAQ;AACrD,iBAAO,OAAO;AAAA,QAClB;AAAA,MACJ;AACA,YAAM,MAAM;AACZ,YAAM,SAAS,aAAa;AAC5B,UAAI,QAAQ;AACR,eAAO;AAAA,MACX;AACA,UAAI,SAAS,OAAO,KAAK;AACrB,cAAM,KAAK,SAAS,cAAc,QAAQ;AAC1C,YAAI,CAAC,IAAI;AACL,qBAAW,KAAK,2CAA2C,UAAU;AAAA,QACzE;AAKA,mBAAW,KAAK,GAAG,YAAY;AAAA,MACnC;AACA,YAAM,EAAE,KAAK,IAAI,YAAY,QAAQ,UAAU,OAAO,OAAO;AAAA,QACzD,aAAa;AAAA,QACb;AAAA,QACA,QAAQ,OAAK,QAAQ,GAAG,IAAI;AAAA,MAChC,GAAG,OAAO,CAAC;AACX,eAAS,QAAQ,KAAK,YAAY,OAAO;AACrC,cAAM,UAAU,YACV,IAAI,UACJ,+BAA+B,IAAI;AACzC,cAAM,YAAY,IAAI,OAClB,OAAO,kBAAkB,UAAU,IAAI,IAAI,MAAM,QAAQ,IAAI,IAAI,IAAI,MAAM;AAC/E,mBAAW,KAAK,YAAY,GAAG;AAAA,EAAY,cAAc,OAAO;AAAA,MACpE;AAKA,YAAM,SAAU,IAAI,SAAS,OAAO,IAAI,EAAE,qBAAqB;AAC/D,aAAO,MAAM;AACb,aAAQ,aAAa,OAAO;AAAA,IAChC;AACA,eAAW,wBAAwB,iBAAiB;AAEpD,WAAO,KAAK,UAAU,EAAE,QAAQ,SAAU,GAAG;AAC3C,UAAI,MAAM;AAAW,gBAAQ,KAAK,WAAW;AAAA,IAC/C,CAAC;AACD,YAAQ,UAAU;AAAA;AAAA;;;AC5ElB;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["isComponent", "match", "loc", "i", "returnType", "hoistStatic", "children", "node", "arg", "props", "camelize", "transformModel", "transformOn"]}