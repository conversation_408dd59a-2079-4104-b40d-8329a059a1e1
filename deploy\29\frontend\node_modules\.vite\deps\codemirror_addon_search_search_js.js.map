{"version": 3, "sources": ["../../codemirror/addon/search/search.js", "dep:codemirror_addon_search_search_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n// Define search commands. Depends on dialog.js or another\n// implementation of the openDialog method.\n\n// Replace works a little oddly -- it will do the replace on the next\n// Ctrl-G (or whatever is bound to findNext) press. You prevent a\n// replace by making sure the match is no longer selected when hitting\n// Ctrl-G.\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"./searchcursor\"), require(\"../dialog/dialog\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"./searchcursor\", \"../dialog/dialog\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  // default search panel location\n  CodeMirror.defineOption(\"search\", {bottom: false});\n\n  function searchOverlay(query, caseInsensitive) {\n    if (typeof query == \"string\")\n      query = new RegExp(query.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, \"\\\\$&\"), caseInsensitive ? \"gi\" : \"g\");\n    else if (!query.global)\n      query = new RegExp(query.source, query.ignoreCase ? \"gi\" : \"g\");\n\n    return {token: function(stream) {\n      query.lastIndex = stream.pos;\n      var match = query.exec(stream.string);\n      if (match && match.index == stream.pos) {\n        stream.pos += match[0].length || 1;\n        return \"searching\";\n      } else if (match) {\n        stream.pos = match.index;\n      } else {\n        stream.skipToEnd();\n      }\n    }};\n  }\n\n  function SearchState() {\n    this.posFrom = this.posTo = this.lastQuery = this.query = null;\n    this.overlay = null;\n  }\n\n  function getSearchState(cm) {\n    return cm.state.search || (cm.state.search = new SearchState());\n  }\n\n  function queryCaseInsensitive(query) {\n    return typeof query == \"string\" && query == query.toLowerCase();\n  }\n\n  function getSearchCursor(cm, query, pos) {\n    // Heuristic: if the query string is all lowercase, do a case insensitive search.\n    return cm.getSearchCursor(query, pos, {caseFold: queryCaseInsensitive(query), multiline: true});\n  }\n\n  function persistentDialog(cm, text, deflt, onEnter, onKeyDown) {\n    cm.openDialog(text, onEnter, {\n      value: deflt,\n      selectValueOnOpen: true,\n      closeOnEnter: false,\n      onClose: function() { clearSearch(cm); },\n      onKeyDown: onKeyDown,\n      bottom: cm.options.search.bottom\n    });\n  }\n\n  function dialog(cm, text, shortText, deflt, f) {\n    if (cm.openDialog) cm.openDialog(text, f, {value: deflt, selectValueOnOpen: true, bottom: cm.options.search.bottom});\n    else f(prompt(shortText, deflt));\n  }\n\n  function confirmDialog(cm, text, shortText, fs) {\n    if (cm.openConfirm) cm.openConfirm(text, fs);\n    else if (confirm(shortText)) fs[0]();\n  }\n\n  function parseString(string) {\n    return string.replace(/\\\\([nrt\\\\])/g, function(match, ch) {\n      if (ch == \"n\") return \"\\n\"\n      if (ch == \"r\") return \"\\r\"\n      if (ch == \"t\") return \"\\t\"\n      if (ch == \"\\\\\") return \"\\\\\"\n      return match\n    })\n  }\n\n  function parseQuery(query) {\n    var isRE = query.match(/^\\/(.*)\\/([a-z]*)$/);\n    if (isRE) {\n      try { query = new RegExp(isRE[1], isRE[2].indexOf(\"i\") == -1 ? \"\" : \"i\"); }\n      catch(e) {} // Not a regular expression after all, do a string search\n    } else {\n      query = parseString(query)\n    }\n    if (typeof query == \"string\" ? query == \"\" : query.test(\"\"))\n      query = /x^/;\n    return query;\n  }\n\n  function startSearch(cm, state, query) {\n    state.queryText = query;\n    state.query = parseQuery(query);\n    cm.removeOverlay(state.overlay, queryCaseInsensitive(state.query));\n    state.overlay = searchOverlay(state.query, queryCaseInsensitive(state.query));\n    cm.addOverlay(state.overlay);\n    if (cm.showMatchesOnScrollbar) {\n      if (state.annotate) { state.annotate.clear(); state.annotate = null; }\n      state.annotate = cm.showMatchesOnScrollbar(state.query, queryCaseInsensitive(state.query));\n    }\n  }\n\n  function doSearch(cm, rev, persistent, immediate) {\n    var state = getSearchState(cm);\n    if (state.query) return findNext(cm, rev);\n    var q = cm.getSelection() || state.lastQuery;\n    if (q instanceof RegExp && q.source == \"x^\") q = null\n    if (persistent && cm.openDialog) {\n      var hiding = null\n      var searchNext = function(query, event) {\n        CodeMirror.e_stop(event);\n        if (!query) return;\n        if (query != state.queryText) {\n          startSearch(cm, state, query);\n          state.posFrom = state.posTo = cm.getCursor();\n        }\n        if (hiding) hiding.style.opacity = 1\n        findNext(cm, event.shiftKey, function(_, to) {\n          var dialog\n          if (to.line < 3 && document.querySelector &&\n              (dialog = cm.display.wrapper.querySelector(\".CodeMirror-dialog\")) &&\n              dialog.getBoundingClientRect().bottom - 4 > cm.cursorCoords(to, \"window\").top)\n            (hiding = dialog).style.opacity = .4\n        })\n      };\n      persistentDialog(cm, getQueryDialog(cm), q, searchNext, function(event, query) {\n        var keyName = CodeMirror.keyName(event)\n        var extra = cm.getOption('extraKeys'), cmd = (extra && extra[keyName]) || CodeMirror.keyMap[cm.getOption(\"keyMap\")][keyName]\n        if (cmd == \"findNext\" || cmd == \"findPrev\" ||\n          cmd == \"findPersistentNext\" || cmd == \"findPersistentPrev\") {\n          CodeMirror.e_stop(event);\n          startSearch(cm, getSearchState(cm), query);\n          cm.execCommand(cmd);\n        } else if (cmd == \"find\" || cmd == \"findPersistent\") {\n          CodeMirror.e_stop(event);\n          searchNext(query, event);\n        }\n      });\n      if (immediate && q) {\n        startSearch(cm, state, q);\n        findNext(cm, rev);\n      }\n    } else {\n      dialog(cm, getQueryDialog(cm), \"Search for:\", q, function(query) {\n        if (query && !state.query) cm.operation(function() {\n          startSearch(cm, state, query);\n          state.posFrom = state.posTo = cm.getCursor();\n          findNext(cm, rev);\n        });\n      });\n    }\n  }\n\n  function findNext(cm, rev, callback) {cm.operation(function() {\n    var state = getSearchState(cm);\n    var cursor = getSearchCursor(cm, state.query, rev ? state.posFrom : state.posTo);\n    if (!cursor.find(rev)) {\n      cursor = getSearchCursor(cm, state.query, rev ? CodeMirror.Pos(cm.lastLine()) : CodeMirror.Pos(cm.firstLine(), 0));\n      if (!cursor.find(rev)) return;\n    }\n    cm.setSelection(cursor.from(), cursor.to());\n    cm.scrollIntoView({from: cursor.from(), to: cursor.to()}, 20);\n    state.posFrom = cursor.from(); state.posTo = cursor.to();\n    if (callback) callback(cursor.from(), cursor.to())\n  });}\n\n  function clearSearch(cm) {cm.operation(function() {\n    var state = getSearchState(cm);\n    state.lastQuery = state.query;\n    if (!state.query) return;\n    state.query = state.queryText = null;\n    cm.removeOverlay(state.overlay);\n    if (state.annotate) { state.annotate.clear(); state.annotate = null; }\n  });}\n\n  function el(tag, attrs) {\n    var element = tag ? document.createElement(tag) : document.createDocumentFragment();\n    for (var key in attrs) {\n      element[key] = attrs[key];\n    }\n    for (var i = 2; i < arguments.length; i++) {\n      var child = arguments[i]\n      element.appendChild(typeof child == \"string\" ? document.createTextNode(child) : child);\n    }\n    return element;\n  }\n\n  function getQueryDialog(cm)  {\n    var label = el(\"label\", {className: \"CodeMirror-search-label\"},\n                   cm.phrase(\"Search:\"),\n                   el(\"input\", {type: \"text\", \"style\": \"width: 10em\", className: \"CodeMirror-search-field\",\n                                id: \"CodeMirror-search-field\"}));\n    label.setAttribute(\"for\",\"CodeMirror-search-field\");\n    return el(\"\", null, label, \" \",\n              el(\"span\", {style: \"color: #666\", className: \"CodeMirror-search-hint\"},\n                 cm.phrase(\"(Use /re/ syntax for regexp search)\")));\n  }\n  function getReplaceQueryDialog(cm) {\n    return el(\"\", null, \" \",\n              el(\"input\", {type: \"text\", \"style\": \"width: 10em\", className: \"CodeMirror-search-field\"}), \" \",\n              el(\"span\", {style: \"color: #666\", className: \"CodeMirror-search-hint\"},\n                 cm.phrase(\"(Use /re/ syntax for regexp search)\")));\n  }\n  function getReplacementQueryDialog(cm) {\n    return el(\"\", null,\n              el(\"span\", {className: \"CodeMirror-search-label\"}, cm.phrase(\"With:\")), \" \",\n              el(\"input\", {type: \"text\", \"style\": \"width: 10em\", className: \"CodeMirror-search-field\"}));\n  }\n  function getDoReplaceConfirm(cm) {\n    return el(\"\", null,\n              el(\"span\", {className: \"CodeMirror-search-label\"}, cm.phrase(\"Replace?\")), \" \",\n              el(\"button\", {}, cm.phrase(\"Yes\")), \" \",\n              el(\"button\", {}, cm.phrase(\"No\")), \" \",\n              el(\"button\", {}, cm.phrase(\"All\")), \" \",\n              el(\"button\", {}, cm.phrase(\"Stop\")));\n  }\n\n  function replaceAll(cm, query, text) {\n    cm.operation(function() {\n      for (var cursor = getSearchCursor(cm, query); cursor.findNext();) {\n        if (typeof query != \"string\") {\n          var match = cm.getRange(cursor.from(), cursor.to()).match(query);\n          cursor.replace(text.replace(/\\$(\\d)/g, function(_, i) {return match[i];}));\n        } else cursor.replace(text);\n      }\n    });\n  }\n\n  function replace(cm, all) {\n    if (cm.getOption(\"readOnly\")) return;\n    var query = cm.getSelection() || getSearchState(cm).lastQuery;\n    var dialogText = all ? cm.phrase(\"Replace all:\") : cm.phrase(\"Replace:\")\n    var fragment = el(\"\", null,\n                      el(\"span\", {className: \"CodeMirror-search-label\"}, dialogText),\n                      getReplaceQueryDialog(cm))\n    dialog(cm, fragment, dialogText, query, function(query) {\n      if (!query) return;\n      query = parseQuery(query);\n      dialog(cm, getReplacementQueryDialog(cm), cm.phrase(\"Replace with:\"), \"\", function(text) {\n        text = parseString(text)\n        if (all) {\n          replaceAll(cm, query, text)\n        } else {\n          clearSearch(cm);\n          var cursor = getSearchCursor(cm, query, cm.getCursor(\"from\"));\n          var advance = function() {\n            var start = cursor.from(), match;\n            if (!(match = cursor.findNext())) {\n              cursor = getSearchCursor(cm, query);\n              if (!(match = cursor.findNext()) ||\n                  (start && cursor.from().line == start.line && cursor.from().ch == start.ch)) return;\n            }\n            cm.setSelection(cursor.from(), cursor.to());\n            cm.scrollIntoView({from: cursor.from(), to: cursor.to()});\n            confirmDialog(cm, getDoReplaceConfirm(cm), cm.phrase(\"Replace?\"),\n                          [function() {doReplace(match);}, advance,\n                           function() {replaceAll(cm, query, text)}]);\n          };\n          var doReplace = function(match) {\n            cursor.replace(typeof query == \"string\" ? text :\n                           text.replace(/\\$(\\d)/g, function(_, i) {return match[i];}));\n            advance();\n          };\n          advance();\n        }\n      });\n    });\n  }\n\n  CodeMirror.commands.find = function(cm) {clearSearch(cm); doSearch(cm);};\n  CodeMirror.commands.findPersistent = function(cm) {clearSearch(cm); doSearch(cm, false, true);};\n  CodeMirror.commands.findPersistentNext = function(cm) {doSearch(cm, false, true, true);};\n  CodeMirror.commands.findPersistentPrev = function(cm) {doSearch(cm, true, true, true);};\n  CodeMirror.commands.findNext = doSearch;\n  CodeMirror.commands.findPrev = function(cm) {doSearch(cm, true);};\n  CodeMirror.commands.clearSearch = clearSearch;\n  CodeMirror.commands.replace = replace;\n  CodeMirror.commands.replaceAll = function(cm) {replace(cm, true);};\n});\n", "export default require(\"./node_modules/codemirror/addon/search/search.js\");"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAWA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,wBAA2B,gBAA2B;AAAA,eACpF,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,kBAAkB,kBAAkB,GAAG,GAAG;AAAA;AAE1E,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAGA,MAAAA,YAAW,aAAa,UAAU,EAAC,QAAQ,MAAK,CAAC;AAEjD,eAAS,cAAc,OAAO,iBAAiB;AAC7C,YAAI,OAAO,SAAS;AAClB,kBAAQ,IAAI,OAAO,MAAM,QAAQ,uCAAuC,MAAM,GAAG,kBAAkB,OAAO,GAAG;AAAA,iBACtG,CAAC,MAAM;AACd,kBAAQ,IAAI,OAAO,MAAM,QAAQ,MAAM,aAAa,OAAO,GAAG;AAEhE,eAAO,EAAC,OAAO,SAAS,QAAQ;AAC9B,gBAAM,YAAY,OAAO;AACzB,cAAI,QAAQ,MAAM,KAAK,OAAO,MAAM;AACpC,cAAI,SAAS,MAAM,SAAS,OAAO,KAAK;AACtC,mBAAO,OAAO,MAAM,GAAG,UAAU;AACjC,mBAAO;AAAA,UACT,WAAW,OAAO;AAChB,mBAAO,MAAM,MAAM;AAAA,UACrB,OAAO;AACL,mBAAO,UAAU;AAAA,UACnB;AAAA,QACF,EAAC;AAAA,MACH;AAEA,eAAS,cAAc;AACrB,aAAK,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ;AAC1D,aAAK,UAAU;AAAA,MACjB;AAEA,eAAS,eAAe,IAAI;AAC1B,eAAO,GAAG,MAAM,WAAW,GAAG,MAAM,SAAS,IAAI,YAAY;AAAA,MAC/D;AAEA,eAAS,qBAAqB,OAAO;AACnC,eAAO,OAAO,SAAS,YAAY,SAAS,MAAM,YAAY;AAAA,MAChE;AAEA,eAAS,gBAAgB,IAAI,OAAO,KAAK;AAEvC,eAAO,GAAG,gBAAgB,OAAO,KAAK,EAAC,UAAU,qBAAqB,KAAK,GAAG,WAAW,KAAI,CAAC;AAAA,MAChG;AAEA,eAAS,iBAAiB,IAAI,MAAM,OAAO,SAAS,WAAW;AAC7D,WAAG,WAAW,MAAM,SAAS;AAAA,UAC3B,OAAO;AAAA,UACP,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,SAAS,WAAW;AAAE,wBAAY,EAAE;AAAA,UAAG;AAAA,UACvC;AAAA,UACA,QAAQ,GAAG,QAAQ,OAAO;AAAA,QAC5B,CAAC;AAAA,MACH;AAEA,eAAS,OAAO,IAAI,MAAM,WAAW,OAAO,GAAG;AAC7C,YAAI,GAAG;AAAY,aAAG,WAAW,MAAM,GAAG,EAAC,OAAO,OAAO,mBAAmB,MAAM,QAAQ,GAAG,QAAQ,OAAO,OAAM,CAAC;AAAA;AAC9G,YAAE,OAAO,WAAW,KAAK,CAAC;AAAA,MACjC;AAEA,eAAS,cAAc,IAAI,MAAM,WAAW,IAAI;AAC9C,YAAI,GAAG;AAAa,aAAG,YAAY,MAAM,EAAE;AAAA,iBAClC,QAAQ,SAAS;AAAG,aAAG,GAAG;AAAA,MACrC;AAEA,eAAS,YAAY,QAAQ;AAC3B,eAAO,OAAO,QAAQ,gBAAgB,SAAS,OAAO,IAAI;AACxD,cAAI,MAAM;AAAK,mBAAO;AACtB,cAAI,MAAM;AAAK,mBAAO;AACtB,cAAI,MAAM;AAAK,mBAAO;AACtB,cAAI,MAAM;AAAM,mBAAO;AACvB,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,eAAS,WAAW,OAAO;AACzB,YAAI,OAAO,MAAM,MAAM,oBAAoB;AAC3C,YAAI,MAAM;AACR,cAAI;AAAE,oBAAQ,IAAI,OAAO,KAAK,IAAI,KAAK,GAAG,QAAQ,GAAG,KAAK,KAAK,KAAK,GAAG;AAAA,UAAG,SACpE,GAAN;AAAA,UAAU;AAAA,QACZ,OAAO;AACL,kBAAQ,YAAY,KAAK;AAAA,QAC3B;AACA,YAAI,OAAO,SAAS,WAAW,SAAS,KAAK,MAAM,KAAK,EAAE;AACxD,kBAAQ;AACV,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,IAAI,OAAO,OAAO;AACrC,cAAM,YAAY;AAClB,cAAM,QAAQ,WAAW,KAAK;AAC9B,WAAG,cAAc,MAAM,SAAS,qBAAqB,MAAM,KAAK,CAAC;AACjE,cAAM,UAAU,cAAc,MAAM,OAAO,qBAAqB,MAAM,KAAK,CAAC;AAC5E,WAAG,WAAW,MAAM,OAAO;AAC3B,YAAI,GAAG,wBAAwB;AAC7B,cAAI,MAAM,UAAU;AAAE,kBAAM,SAAS,MAAM;AAAG,kBAAM,WAAW;AAAA,UAAM;AACrE,gBAAM,WAAW,GAAG,uBAAuB,MAAM,OAAO,qBAAqB,MAAM,KAAK,CAAC;AAAA,QAC3F;AAAA,MACF;AAEA,eAAS,SAAS,IAAI,KAAK,YAAY,WAAW;AAChD,YAAI,QAAQ,eAAe,EAAE;AAC7B,YAAI,MAAM;AAAO,iBAAO,SAAS,IAAI,GAAG;AACxC,YAAI,IAAI,GAAG,aAAa,KAAK,MAAM;AACnC,YAAI,aAAa,UAAU,EAAE,UAAU;AAAM,cAAI;AACjD,YAAI,cAAc,GAAG,YAAY;AAC/B,cAAI,SAAS;AACb,cAAI,aAAa,SAAS,OAAO,OAAO;AACtC,YAAAA,YAAW,OAAO,KAAK;AACvB,gBAAI,CAAC;AAAO;AACZ,gBAAI,SAAS,MAAM,WAAW;AAC5B,0BAAY,IAAI,OAAO,KAAK;AAC5B,oBAAM,UAAU,MAAM,QAAQ,GAAG,UAAU;AAAA,YAC7C;AACA,gBAAI;AAAQ,qBAAO,MAAM,UAAU;AACnC,qBAAS,IAAI,MAAM,UAAU,SAAS,GAAG,IAAI;AAC3C,kBAAIC;AACJ,kBAAI,GAAG,OAAO,KAAK,SAAS,kBACvBA,UAAS,GAAG,QAAQ,QAAQ,cAAc,oBAAoB,MAC/DA,QAAO,sBAAsB,EAAE,SAAS,IAAI,GAAG,aAAa,IAAI,QAAQ,EAAE;AAC5E,iBAAC,SAASA,SAAQ,MAAM,UAAU;AAAA,YACtC,CAAC;AAAA,UACH;AACA,2BAAiB,IAAI,eAAe,EAAE,GAAG,GAAG,YAAY,SAAS,OAAO,OAAO;AAC7E,gBAAI,UAAUD,YAAW,QAAQ,KAAK;AACtC,gBAAI,QAAQ,GAAG,UAAU,WAAW,GAAG,MAAO,SAAS,MAAM,YAAaA,YAAW,OAAO,GAAG,UAAU,QAAQ,GAAG;AACpH,gBAAI,OAAO,cAAc,OAAO,cAC9B,OAAO,wBAAwB,OAAO,sBAAsB;AAC5D,cAAAA,YAAW,OAAO,KAAK;AACvB,0BAAY,IAAI,eAAe,EAAE,GAAG,KAAK;AACzC,iBAAG,YAAY,GAAG;AAAA,YACpB,WAAW,OAAO,UAAU,OAAO,kBAAkB;AACnD,cAAAA,YAAW,OAAO,KAAK;AACvB,yBAAW,OAAO,KAAK;AAAA,YACzB;AAAA,UACF,CAAC;AACD,cAAI,aAAa,GAAG;AAClB,wBAAY,IAAI,OAAO,CAAC;AACxB,qBAAS,IAAI,GAAG;AAAA,UAClB;AAAA,QACF,OAAO;AACL,iBAAO,IAAI,eAAe,EAAE,GAAG,eAAe,GAAG,SAAS,OAAO;AAC/D,gBAAI,SAAS,CAAC,MAAM;AAAO,iBAAG,UAAU,WAAW;AACjD,4BAAY,IAAI,OAAO,KAAK;AAC5B,sBAAM,UAAU,MAAM,QAAQ,GAAG,UAAU;AAC3C,yBAAS,IAAI,GAAG;AAAA,cAClB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AAAA,MACF;AAEA,eAAS,SAAS,IAAI,KAAK,UAAU;AAAC,WAAG,UAAU,WAAW;AAC5D,cAAI,QAAQ,eAAe,EAAE;AAC7B,cAAI,SAAS,gBAAgB,IAAI,MAAM,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK;AAC/E,cAAI,CAAC,OAAO,KAAK,GAAG,GAAG;AACrB,qBAAS,gBAAgB,IAAI,MAAM,OAAO,MAAMA,YAAW,IAAI,GAAG,SAAS,CAAC,IAAIA,YAAW,IAAI,GAAG,UAAU,GAAG,CAAC,CAAC;AACjH,gBAAI,CAAC,OAAO,KAAK,GAAG;AAAG;AAAA,UACzB;AACA,aAAG,aAAa,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC;AAC1C,aAAG,eAAe,EAAC,MAAM,OAAO,KAAK,GAAG,IAAI,OAAO,GAAG,EAAC,GAAG,EAAE;AAC5D,gBAAM,UAAU,OAAO,KAAK;AAAG,gBAAM,QAAQ,OAAO,GAAG;AACvD,cAAI;AAAU,qBAAS,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC;AAAA,QACnD,CAAC;AAAA,MAAE;AAEH,eAAS,YAAY,IAAI;AAAC,WAAG,UAAU,WAAW;AAChD,cAAI,QAAQ,eAAe,EAAE;AAC7B,gBAAM,YAAY,MAAM;AACxB,cAAI,CAAC,MAAM;AAAO;AAClB,gBAAM,QAAQ,MAAM,YAAY;AAChC,aAAG,cAAc,MAAM,OAAO;AAC9B,cAAI,MAAM,UAAU;AAAE,kBAAM,SAAS,MAAM;AAAG,kBAAM,WAAW;AAAA,UAAM;AAAA,QACvE,CAAC;AAAA,MAAE;AAEH,eAAS,GAAG,KAAK,OAAO;AACtB,YAAI,UAAU,MAAM,SAAS,cAAc,GAAG,IAAI,SAAS,uBAAuB;AAClF,iBAAS,OAAO,OAAO;AACrB,kBAAQ,OAAO,MAAM;AAAA,QACvB;AACA,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,cAAI,QAAQ,UAAU;AACtB,kBAAQ,YAAY,OAAO,SAAS,WAAW,SAAS,eAAe,KAAK,IAAI,KAAK;AAAA,QACvF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,eAAe,IAAK;AAC3B,YAAI,QAAQ;AAAA,UAAG;AAAA,UAAS,EAAC,WAAW,0BAAyB;AAAA,UAC9C,GAAG,OAAO,SAAS;AAAA,UACnB,GAAG,SAAS;AAAA,YAAC,MAAM;AAAA,YAAQ,SAAS;AAAA,YAAe,WAAW;AAAA,YACjD,IAAI;AAAA,UAAyB,CAAC;AAAA,QAAC;AAC3D,cAAM,aAAa,OAAM,yBAAyB;AAClD,eAAO;AAAA,UAAG;AAAA,UAAI;AAAA,UAAM;AAAA,UAAO;AAAA,UACjB;AAAA,YAAG;AAAA,YAAQ,EAAC,OAAO,eAAe,WAAW,yBAAwB;AAAA,YAClE,GAAG,OAAO,qCAAqC;AAAA,UAAC;AAAA,QAAC;AAAA,MAChE;AACA,eAAS,sBAAsB,IAAI;AACjC,eAAO;AAAA,UAAG;AAAA,UAAI;AAAA,UAAM;AAAA,UACV,GAAG,SAAS,EAAC,MAAM,QAAQ,SAAS,eAAe,WAAW,0BAAyB,CAAC;AAAA,UAAG;AAAA,UAC3F;AAAA,YAAG;AAAA,YAAQ,EAAC,OAAO,eAAe,WAAW,yBAAwB;AAAA,YAClE,GAAG,OAAO,qCAAqC;AAAA,UAAC;AAAA,QAAC;AAAA,MAChE;AACA,eAAS,0BAA0B,IAAI;AACrC,eAAO;AAAA,UAAG;AAAA,UAAI;AAAA,UACJ,GAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAG,GAAG,OAAO,OAAO,CAAC;AAAA,UAAG;AAAA,UACxE,GAAG,SAAS,EAAC,MAAM,QAAQ,SAAS,eAAe,WAAW,0BAAyB,CAAC;AAAA,QAAC;AAAA,MACrG;AACA,eAAS,oBAAoB,IAAI;AAC/B,eAAO;AAAA,UAAG;AAAA,UAAI;AAAA,UACJ,GAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAG,GAAG,OAAO,UAAU,CAAC;AAAA,UAAG;AAAA,UAC3E,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,KAAK,CAAC;AAAA,UAAG;AAAA,UACpC,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,IAAI,CAAC;AAAA,UAAG;AAAA,UACnC,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,KAAK,CAAC;AAAA,UAAG;AAAA,UACpC,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,MAAM,CAAC;AAAA,QAAC;AAAA,MAC/C;AAEA,eAAS,WAAW,IAAI,OAAO,MAAM;AACnC,WAAG,UAAU,WAAW;AACtB,mBAAS,SAAS,gBAAgB,IAAI,KAAK,GAAG,OAAO,SAAS,KAAI;AAChE,gBAAI,OAAO,SAAS,UAAU;AAC5B,kBAAI,QAAQ,GAAG,SAAS,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC,EAAE,MAAM,KAAK;AAC/D,qBAAO,QAAQ,KAAK,QAAQ,WAAW,SAAS,GAAG,GAAG;AAAC,uBAAO,MAAM;AAAA,cAAG,CAAC,CAAC;AAAA,YAC3E;AAAO,qBAAO,QAAQ,IAAI;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH;AAEA,eAAS,QAAQ,IAAI,KAAK;AACxB,YAAI,GAAG,UAAU,UAAU;AAAG;AAC9B,YAAI,QAAQ,GAAG,aAAa,KAAK,eAAe,EAAE,EAAE;AACpD,YAAI,aAAa,MAAM,GAAG,OAAO,cAAc,IAAI,GAAG,OAAO,UAAU;AACvE,YAAI,WAAW;AAAA,UAAG;AAAA,UAAI;AAAA,UACJ,GAAG,QAAQ,EAAC,WAAW,0BAAyB,GAAG,UAAU;AAAA,UAC7D,sBAAsB,EAAE;AAAA,QAAC;AAC3C,eAAO,IAAI,UAAU,YAAY,OAAO,SAASE,QAAO;AACtD,cAAI,CAACA;AAAO;AACZ,UAAAA,SAAQ,WAAWA,MAAK;AACxB,iBAAO,IAAI,0BAA0B,EAAE,GAAG,GAAG,OAAO,eAAe,GAAG,IAAI,SAAS,MAAM;AACvF,mBAAO,YAAY,IAAI;AACvB,gBAAI,KAAK;AACP,yBAAW,IAAIA,QAAO,IAAI;AAAA,YAC5B,OAAO;AACL,0BAAY,EAAE;AACd,kBAAI,SAAS,gBAAgB,IAAIA,QAAO,GAAG,UAAU,MAAM,CAAC;AAC5D,kBAAI,UAAU,WAAW;AACvB,oBAAI,QAAQ,OAAO,KAAK,GAAG;AAC3B,oBAAI,EAAE,QAAQ,OAAO,SAAS,IAAI;AAChC,2BAAS,gBAAgB,IAAIA,MAAK;AAClC,sBAAI,EAAE,QAAQ,OAAO,SAAS,MACzB,SAAS,OAAO,KAAK,EAAE,QAAQ,MAAM,QAAQ,OAAO,KAAK,EAAE,MAAM,MAAM;AAAK;AAAA,gBACnF;AACA,mBAAG,aAAa,OAAO,KAAK,GAAG,OAAO,GAAG,CAAC;AAC1C,mBAAG,eAAe,EAAC,MAAM,OAAO,KAAK,GAAG,IAAI,OAAO,GAAG,EAAC,CAAC;AACxD;AAAA,kBAAc;AAAA,kBAAI,oBAAoB,EAAE;AAAA,kBAAG,GAAG,OAAO,UAAU;AAAA,kBACjD;AAAA,oBAAC,WAAW;AAAC,gCAAU,KAAK;AAAA,oBAAE;AAAA,oBAAG;AAAA,oBAChC,WAAW;AAAC,iCAAW,IAAIA,QAAO,IAAI;AAAA,oBAAC;AAAA,kBAAC;AAAA,gBAAC;AAAA,cAC1D;AACA,kBAAI,YAAY,SAAS,OAAO;AAC9B,uBAAO,QAAQ,OAAOA,UAAS,WAAW,OAC3B,KAAK,QAAQ,WAAW,SAAS,GAAG,GAAG;AAAC,yBAAO,MAAM;AAAA,gBAAG,CAAC,CAAC;AACzE,wBAAQ;AAAA,cACV;AACA,sBAAQ;AAAA,YACV;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAEA,MAAAF,YAAW,SAAS,OAAO,SAAS,IAAI;AAAC,oBAAY,EAAE;AAAG,iBAAS,EAAE;AAAA,MAAE;AACvE,MAAAA,YAAW,SAAS,iBAAiB,SAAS,IAAI;AAAC,oBAAY,EAAE;AAAG,iBAAS,IAAI,OAAO,IAAI;AAAA,MAAE;AAC9F,MAAAA,YAAW,SAAS,qBAAqB,SAAS,IAAI;AAAC,iBAAS,IAAI,OAAO,MAAM,IAAI;AAAA,MAAE;AACvF,MAAAA,YAAW,SAAS,qBAAqB,SAAS,IAAI;AAAC,iBAAS,IAAI,MAAM,MAAM,IAAI;AAAA,MAAE;AACtF,MAAAA,YAAW,SAAS,WAAW;AAC/B,MAAAA,YAAW,SAAS,WAAW,SAAS,IAAI;AAAC,iBAAS,IAAI,IAAI;AAAA,MAAE;AAChE,MAAAA,YAAW,SAAS,cAAc;AAClC,MAAAA,YAAW,SAAS,UAAU;AAC9B,MAAAA,YAAW,SAAS,aAAa,SAAS,IAAI;AAAC,gBAAQ,IAAI,IAAI;AAAA,MAAE;AAAA,IACnE,CAAC;AAAA;AAAA;;;ACtSD,IAAO,4CAAQ;", "names": ["CodeMirror", "dialog", "query"]}