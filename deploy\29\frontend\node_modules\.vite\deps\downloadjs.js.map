{"version": 3, "sources": ["../../downloadjs/download.js", "dep:downloadjs"], "sourcesContent": ["//download.js v4.2, by danda<PERSON>; 2008-2016. [MIT] see http://danml.com/download.html for tests/usage\n// v1 landed a FF+Chrome compat way of downloading strings to local un-named files, upgraded to use a hidden frame and optional mime\n// v2 added named files via a[download], msSaveBlob, IE (10+) support, and window.URL support for larger+faster saves than dataURLs\n// v3 added dataURL and Blob Input, bind-toggle arity, and legacy dataURL fallback was improved with force-download mime and base64 support. 3.1 improved safari handling.\n// v4 adds AMD/UMD, commonJS, and plain browser support\n// v4.1 adds url download capability via solo URL argument (same domain/CORS only)\n// v4.2 adds semantic variable names, long (over 2MB) dataURL support, and hidden by default temp anchors\n// https://github.com/rndme/download\n\n(function (root, factory) {\n\tif (typeof define === 'function' && define.amd) {\n\t\t// AMD. Register as an anonymous module.\n\t\tdefine([], factory);\n\t} else if (typeof exports === 'object') {\n\t\t// Node. Does not work with strict CommonJS, but\n\t\t// only CommonJS-like environments that support module.exports,\n\t\t// like Node.\n\t\tmodule.exports = factory();\n\t} else {\n\t\t// Browser globals (root is window)\n\t\troot.download = factory();\n  }\n}(this, function () {\n\n\treturn function download(data, strFileName, strMimeType) {\n\n\t\tvar self = window, // this script is only for browsers anyway...\n\t\t\tdefaultMime = \"application/octet-stream\", // this default mime also triggers iframe downloads\n\t\t\tmimeType = strMimeType || defaultMime,\n\t\t\tpayload = data,\n\t\t\turl = !strFileName && !strMimeType && payload,\n\t\t\tanchor = document.createElement(\"a\"),\n\t\t\ttoString = function(a){return String(a);},\n\t\t\tmyBlob = (self.Blob || self.MozBlob || self.WebKitBlob || toString),\n\t\t\tfileName = strFileName || \"download\",\n\t\t\tblob,\n\t\t\treader;\n\t\t\tmyBlob= myBlob.call ? myBlob.bind(self) : Blob ;\n\t  \n\t\tif(String(this)===\"true\"){ //reverse arguments, allowing download.bind(true, \"text/xml\", \"export.xml\") to act as a callback\n\t\t\tpayload=[payload, mimeType];\n\t\t\tmimeType=payload[0];\n\t\t\tpayload=payload[1];\n\t\t}\n\n\n\t\tif(url && url.length< 2048){ // if no filename and no mime, assume a url was passed as the only argument\n\t\t\tfileName = url.split(\"/\").pop().split(\"?\")[0];\n\t\t\tanchor.href = url; // assign href prop to temp anchor\n\t\t  \tif(anchor.href.indexOf(url) !== -1){ // if the browser determines that it's a potentially valid url path:\n        \t\tvar ajax=new XMLHttpRequest();\n        \t\tajax.open( \"GET\", url, true);\n        \t\tajax.responseType = 'blob';\n        \t\tajax.onload= function(e){ \n\t\t\t\t  download(e.target.response, fileName, defaultMime);\n\t\t\t\t};\n        \t\tsetTimeout(function(){ ajax.send();}, 0); // allows setting custom ajax headers using the return:\n\t\t\t    return ajax;\n\t\t\t} // end if valid url?\n\t\t} // end if url?\n\n\n\t\t//go ahead and download dataURLs right away\n\t\tif(/^data:([\\w+-]+\\/[\\w+.-]+)?[,;]/.test(payload)){\n\t\t\n\t\t\tif(payload.length > (1024*1024*1.999) && myBlob !== toString ){\n\t\t\t\tpayload=dataUrlToBlob(payload);\n\t\t\t\tmimeType=payload.type || defaultMime;\n\t\t\t}else{\t\t\t\n\t\t\t\treturn navigator.msSaveBlob ?  // IE10 can't do a[download], only Blobs:\n\t\t\t\t\tnavigator.msSaveBlob(dataUrlToBlob(payload), fileName) :\n\t\t\t\t\tsaver(payload) ; // everyone else can save dataURLs un-processed\n\t\t\t}\n\t\t\t\n\t\t}else{//not data url, is it a string with special needs?\n\t\t\tif(/([\\x80-\\xff])/.test(payload)){\t\t\t  \n\t\t\t\tvar i=0, tempUiArr= new Uint8Array(payload.length), mx=tempUiArr.length;\n\t\t\t\tfor(i;i<mx;++i) tempUiArr[i]= payload.charCodeAt(i);\n\t\t\t \tpayload=new myBlob([tempUiArr], {type: mimeType});\n\t\t\t}\t\t  \n\t\t}\n\t\tblob = payload instanceof myBlob ?\n\t\t\tpayload :\n\t\t\tnew myBlob([payload], {type: mimeType}) ;\n\n\n\t\tfunction dataUrlToBlob(strUrl) {\n\t\t\tvar parts= strUrl.split(/[:;,]/),\n\t\t\ttype= parts[1],\n\t\t\tdecoder= parts[2] == \"base64\" ? atob : decodeURIComponent,\n\t\t\tbinData= decoder( parts.pop() ),\n\t\t\tmx= binData.length,\n\t\t\ti= 0,\n\t\t\tuiArr= new Uint8Array(mx);\n\n\t\t\tfor(i;i<mx;++i) uiArr[i]= binData.charCodeAt(i);\n\n\t\t\treturn new myBlob([uiArr], {type: type});\n\t\t }\n\n\t\tfunction saver(url, winMode){\n\n\t\t\tif ('download' in anchor) { //html5 A[download]\n\t\t\t\tanchor.href = url;\n\t\t\t\tanchor.setAttribute(\"download\", fileName);\n\t\t\t\tanchor.className = \"download-js-link\";\n\t\t\t\tanchor.innerHTML = \"downloading...\";\n\t\t\t\tanchor.style.display = \"none\";\n\t\t\t\tdocument.body.appendChild(anchor);\n\t\t\t\tsetTimeout(function() {\n\t\t\t\t\tanchor.click();\n\t\t\t\t\tdocument.body.removeChild(anchor);\n\t\t\t\t\tif(winMode===true){setTimeout(function(){ self.URL.revokeObjectURL(anchor.href);}, 250 );}\n\t\t\t\t}, 66);\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t// handle non-a[download] safari as best we can:\n\t\t\tif(/(Version)\\/(\\d+)\\.(\\d+)(?:\\.(\\d+))?.*Safari\\//.test(navigator.userAgent)) {\n\t\t\t\tif(/^data:/.test(url))\turl=\"data:\"+url.replace(/^data:([\\w\\/\\-\\+]+)/, defaultMime);\n\t\t\t\tif(!window.open(url)){ // popup blocked, offer direct download:\n\t\t\t\t\tif(confirm(\"Displaying New Document\\n\\nUse Save As... to download, then click back to return to this page.\")){ location.href=url; }\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\t//do iframe dataURL download (old ch+FF):\n\t\t\tvar f = document.createElement(\"iframe\");\n\t\t\tdocument.body.appendChild(f);\n\n\t\t\tif(!winMode && /^data:/.test(url)){ // force a mime that will download:\n\t\t\t\turl=\"data:\"+url.replace(/^data:([\\w\\/\\-\\+]+)/, defaultMime);\n\t\t\t}\n\t\t\tf.src=url;\n\t\t\tsetTimeout(function(){ document.body.removeChild(f); }, 333);\n\n\t\t}//end saver\n\n\n\n\n\t\tif (navigator.msSaveBlob) { // IE10+ : (has Blob, but not a[download] or URL)\n\t\t\treturn navigator.msSaveBlob(blob, fileName);\n\t\t}\n\n\t\tif(self.URL){ // simple fast and modern way using Blob and URL:\n\t\t\tsaver(self.URL.createObjectURL(blob), true);\n\t\t}else{\n\t\t\t// handle non-Blob()+non-URL browsers:\n\t\t\tif(typeof blob === \"string\" || blob.constructor===toString ){\n\t\t\t\ttry{\n\t\t\t\t\treturn saver( \"data:\" +  mimeType   + \";base64,\"  +  self.btoa(blob)  );\n\t\t\t\t}catch(y){\n\t\t\t\t\treturn saver( \"data:\" +  mimeType   + \",\" + encodeURIComponent(blob)  );\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Blob but not URL support:\n\t\t\treader=new FileReader();\n\t\t\treader.onload=function(e){\n\t\t\t\tsaver(this.result);\n\t\t\t};\n\t\t\treader.readAsDataURL(blob);\n\t\t}\n\t\treturn true;\n\t}; /* end download() */\n}));\n", "export default require(\"./node_modules/downloadjs/download.js\");"], "mappings": ";;;;;AAAA;AAAA;AASA,KAAC,SAAU,MAAM,SAAS;AACzB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE/C,eAAO,CAAC,GAAG,OAAO;AAAA,MACnB,WAAW,OAAO,YAAY,UAAU;AAIvC,eAAO,UAAU,QAAQ;AAAA,MAC1B,OAAO;AAEN,aAAK,WAAW,QAAQ;AAAA,MACxB;AAAA,IACF,GAAE,SAAM,WAAY;AAEnB,aAAO,SAAS,SAAS,MAAM,aAAa,aAAa;AAExD,YAAI,OAAO,QACV,cAAc,4BACd,WAAW,eAAe,aAC1B,UAAU,MACV,MAAM,CAAC,eAAe,CAAC,eAAe,SACtC,SAAS,SAAS,cAAc,GAAG,GACnC,WAAW,SAAS,GAAE;AAAC,iBAAO,OAAO,CAAC;AAAA,QAAE,GACxC,SAAU,KAAK,QAAQ,KAAK,WAAW,KAAK,cAAc,UAC1D,WAAW,eAAe,YAC1B,MACA;AACA,iBAAQ,OAAO,OAAO,OAAO,KAAK,IAAI,IAAI;AAE3C,YAAG,OAAO,IAAI,MAAI,QAAO;AACxB,oBAAQ,CAAC,SAAS,QAAQ;AAC1B,qBAAS,QAAQ;AACjB,oBAAQ,QAAQ;AAAA,QACjB;AAGA,YAAG,OAAO,IAAI,SAAQ,MAAK;AAC1B,qBAAW,IAAI,MAAM,GAAG,EAAE,IAAI,EAAE,MAAM,GAAG,EAAE;AAC3C,iBAAO,OAAO;AACZ,cAAG,OAAO,KAAK,QAAQ,GAAG,MAAM,IAAG;AAC9B,gBAAI,OAAK,IAAI,eAAe;AAC5B,iBAAK,KAAM,OAAO,KAAK,IAAI;AAC3B,iBAAK,eAAe;AACpB,iBAAK,SAAQ,SAAS,GAAE;AAC5B,uBAAS,EAAE,OAAO,UAAU,UAAU,WAAW;AAAA,YACnD;AACM,uBAAW,WAAU;AAAE,mBAAK,KAAK;AAAA,YAAE,GAAG,CAAC;AAC1C,mBAAO;AAAA,UACX;AAAA,QACD;AAIA,YAAG,iCAAiC,KAAK,OAAO,GAAE;AAEjD,cAAG,QAAQ,SAAU,OAAK,OAAK,SAAU,WAAW,UAAU;AAC7D,sBAAQ,cAAc,OAAO;AAC7B,uBAAS,QAAQ,QAAQ;AAAA,UAC1B,OAAK;AACJ,mBAAO,UAAU,aAChB,UAAU,WAAW,cAAc,OAAO,GAAG,QAAQ,IACrD,MAAM,OAAO;AAAA,UACf;AAAA,QAED,OAAK;AACJ,cAAG,gBAAgB,KAAK,OAAO,GAAE;AAChC,gBAAI,IAAE,GAAG,YAAW,IAAI,WAAW,QAAQ,MAAM,GAAG,KAAG,UAAU;AACjE,iBAAI,GAAE,IAAE,IAAG,EAAE;AAAG,wBAAU,KAAI,QAAQ,WAAW,CAAC;AACjD,sBAAQ,IAAI,OAAO,CAAC,SAAS,GAAG,EAAC,MAAM,SAAQ,CAAC;AAAA,UAClD;AAAA,QACD;AACA,eAAO,mBAAmB,SACzB,UACA,IAAI,OAAO,CAAC,OAAO,GAAG,EAAC,MAAM,SAAQ,CAAC;AAGvC,iBAAS,cAAc,QAAQ;AAC9B,cAAI,QAAO,OAAO,MAAM,OAAO,GAC/B,OAAM,MAAM,IACZ,UAAS,MAAM,MAAM,WAAW,OAAO,oBACvC,UAAS,QAAS,MAAM,IAAI,CAAE,GAC9BA,MAAI,QAAQ,QACZC,KAAG,GACH,QAAO,IAAI,WAAWD,GAAE;AAExB,eAAIC,IAAEA,KAAED,KAAG,EAAEC;AAAG,kBAAMA,MAAI,QAAQ,WAAWA,EAAC;AAE9C,iBAAO,IAAI,OAAO,CAAC,KAAK,GAAG,EAAC,KAAU,CAAC;AAAA,QACvC;AAED,iBAAS,MAAMC,MAAK,SAAQ;AAE3B,cAAI,cAAc,QAAQ;AACzB,mBAAO,OAAOA;AACd,mBAAO,aAAa,YAAY,QAAQ;AACxC,mBAAO,YAAY;AACnB,mBAAO,YAAY;AACnB,mBAAO,MAAM,UAAU;AACvB,qBAAS,KAAK,YAAY,MAAM;AAChC,uBAAW,WAAW;AACrB,qBAAO,MAAM;AACb,uBAAS,KAAK,YAAY,MAAM;AAChC,kBAAG,YAAU,MAAK;AAAC,2BAAW,WAAU;AAAE,uBAAK,IAAI,gBAAgB,OAAO,IAAI;AAAA,gBAAE,GAAG,GAAI;AAAA,cAAE;AAAA,YAC1F,GAAG,EAAE;AACL,mBAAO;AAAA,UACR;AAGA,cAAG,gDAAgD,KAAK,UAAU,SAAS,GAAG;AAC7E,gBAAG,SAAS,KAAKA,IAAG;AAAG,cAAAA,OAAI,UAAQA,KAAI,QAAQ,uBAAuB,WAAW;AACjF,gBAAG,CAAC,OAAO,KAAKA,IAAG,GAAE;AACpB,kBAAG,QAAQ,gGAAgG,GAAE;AAAE,yBAAS,OAAKA;AAAA,cAAK;AAAA,YACnI;AACA,mBAAO;AAAA,UACR;AAGA,cAAI,IAAI,SAAS,cAAc,QAAQ;AACvC,mBAAS,KAAK,YAAY,CAAC;AAE3B,cAAG,CAAC,WAAW,SAAS,KAAKA,IAAG,GAAE;AACjC,YAAAA,OAAI,UAAQA,KAAI,QAAQ,uBAAuB,WAAW;AAAA,UAC3D;AACA,YAAE,MAAIA;AACN,qBAAW,WAAU;AAAE,qBAAS,KAAK,YAAY,CAAC;AAAA,UAAG,GAAG,GAAG;AAAA,QAE5D;AAKA,YAAI,UAAU,YAAY;AACzB,iBAAO,UAAU,WAAW,MAAM,QAAQ;AAAA,QAC3C;AAEA,YAAG,KAAK,KAAI;AACX,gBAAM,KAAK,IAAI,gBAAgB,IAAI,GAAG,IAAI;AAAA,QAC3C,OAAK;AAEJ,cAAG,OAAO,SAAS,YAAY,KAAK,gBAAc,UAAU;AAC3D,gBAAG;AACF,qBAAO,MAAO,UAAW,WAAa,aAAe,KAAK,KAAK,IAAI,CAAG;AAAA,YACvE,SAAO,GAAN;AACA,qBAAO,MAAO,UAAW,WAAa,MAAM,mBAAmB,IAAI,CAAG;AAAA,YACvE;AAAA,UACD;AAGA,mBAAO,IAAI,WAAW;AACtB,iBAAO,SAAO,SAAS,GAAE;AACxB,kBAAM,KAAK,MAAM;AAAA,UAClB;AACA,iBAAO,cAAc,IAAI;AAAA,QAC1B;AACA,eAAO;AAAA,MACR;AAAA,IACD,CAAC;AAAA;AAAA;;;ACtKD,IAAO,qBAAQ;", "names": ["mx", "i", "url"]}