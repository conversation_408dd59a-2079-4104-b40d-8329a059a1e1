{"version": 3, "sources": ["../../../packages/locale/lang/zh-tw.ts", "dep:element-plus_lib_locale_lang_zh-tw"], "sourcesContent": ["export default {\n  name: 'zh-tw',\n  el: {\n    colorpicker: {\n      confirm: '確認',\n      clear: '清空',\n      defaultLabel: '色彩選擇器',\n      description: '目前色彩為 {color}。按一下 Enter 以選擇新色彩。',\n    },\n    datepicker: {\n      now: '現在',\n      today: '今天',\n      cancel: '取消',\n      clear: '清空',\n      confirm: '確認',\n      dateTablePrompt: '使用方向鍵與 Enter 鍵以選擇日期',\n      monthTablePrompt: '使用方向鍵與 Enter 鍵以選擇月份',\n      yearTablePrompt: '使用方向鍵與 Enter 鍵以選擇年份',\n      selectedDate: '已選日期',\n      selectDate: '選擇日期',\n      selectTime: '選擇時間',\n      startDate: '開始日期',\n      startTime: '開始時間',\n      endDate: '結束日期',\n      endTime: '結束時間',\n      prevYear: '前一年',\n      nextYear: '後一年',\n      prevMonth: '上個月',\n      nextMonth: '下個月',\n      year: '年',\n      month1: '1 月',\n      month2: '2 月',\n      month3: '3 月',\n      month4: '4 月',\n      month5: '5 月',\n      month6: '6 月',\n      month7: '7 月',\n      month8: '8 月',\n      month9: '9 月',\n      month10: '10 月',\n      month11: '11 月',\n      month12: '12 月',\n      // week: '周次',\n      weeks: {\n        sun: '日',\n        mon: '一',\n        tue: '二',\n        wed: '三',\n        thu: '四',\n        fri: '五',\n        sat: '六',\n      },\n      weeksFull: {\n        sun: '星期日',\n        mon: '星期一',\n        tue: '星期二',\n        wed: '星期三',\n        thu: '星期四',\n        fri: '星期五',\n        sat: '星期六',\n      },\n      months: {\n        jan: '一月',\n        feb: '二月',\n        mar: '三月',\n        apr: '四月',\n        may: '五月',\n        jun: '六月',\n        jul: '七月',\n        aug: '八月',\n        sep: '九月',\n        oct: '十月',\n        nov: '十一月',\n        dec: '十二月',\n      },\n    },\n    inputNumber: {\n      decrease: '減少數值',\n      increase: '增加數值',\n    },\n    select: {\n      loading: '載入中',\n      noMatch: '無相符資料',\n      noData: '無資料',\n      placeholder: '請選擇',\n    },\n    dropdown: {\n      toggleDropdown: '切換下拉選單',\n    },\n    cascader: {\n      noMatch: '無相符資料',\n      loading: '載入中',\n      placeholder: '請選擇',\n      noData: '無資料',\n    },\n    pagination: {\n      goto: '前往',\n      pagesize: '項/頁',\n      total: '共 {total} 項',\n      pageClassifier: '頁',\n      deprecationWarning:\n        '偵測到已過時的使用方式，請參閱 el-pagination 說明文件以了解更多資訊',\n    },\n    dialog: {\n      close: '關閉此對話框',\n    },\n    drawer: {\n      close: '關閉此對話框',\n    },\n    messagebox: {\n      title: '提示',\n      confirm: '確定',\n      cancel: '取消',\n      error: '輸入的資料不符規定!',\n      close: '關閉此對話框',\n    },\n    upload: {\n      deleteTip: '按一下 Delete 鍵以刪除',\n      delete: '刪除',\n      preview: '查看圖片',\n      continue: '繼續上傳',\n    },\n    slider: {\n      defaultLabel: '滑桿介於 {min} 至 {max}',\n      defaultRangeStartLabel: '選擇起始值',\n      defaultRangeEndLabel: '選擇結束值',\n    },\n    table: {\n      emptyText: '暫無資料',\n      confirmFilter: '篩選',\n      resetFilter: '重置',\n      clearFilter: '全部',\n      sumText: '合計',\n    },\n    tree: {\n      emptyText: '暫無資料',\n    },\n    transfer: {\n      noMatch: '無相符資料',\n      noData: '無資料',\n      titles: ['列表 1', '列表 2'],\n      filterPlaceholder: '請輸入搜尋內容',\n      noCheckedFormat: '共 {total} 項',\n      hasCheckedFormat: '已選 {checked}/{total} 項',\n    },\n    image: {\n      error: '載入失敗',\n    },\n    pageHeader: {\n      title: '返回',\n    },\n    popconfirm: {\n      confirmButtonText: '確認',\n      cancelButtonText: '取消',\n    },\n  },\n}\n", "export default require(\"./node_modules/element-plus/lib/locale/lang/zh-tw.js\");"], "mappings": ";;;;;;;;;AAAA,QAAA,OAAe;MACb,MAAM;MACN,IAAI;QACF,aAAa;UACX,SAAS;UACT,OAAO;UACP,cAAc;UACd,aAAa;QACnB;QACI,YAAY;UACV,KAAK;UACL,OAAO;UACP,QAAQ;UACR,OAAO;UACP,SAAS;UACT,iBAAiB;UACjB,kBAAkB;UAClB,iBAAiB;UACjB,cAAc;UACd,YAAY;UACZ,YAAY;UACZ,WAAW;UACX,WAAW;UACX,SAAS;UACT,SAAS;UACT,UAAU;UACV,UAAU;UACV,WAAW;UACX,WAAW;UACX,MAAM;UACN,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,SAAS;UACT,SAAS;UACT,SAAS;UACT,OAAO;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;UACb;UACM,WAAW;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;UACb;UACM,QAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;UACb;QACA;QACI,aAAa;UACX,UAAU;UACV,UAAU;QAChB;QACI,QAAQ;UACN,SAAS;UACT,SAAS;UACT,QAAQ;UACR,aAAa;QACnB;QACI,UAAU;UACR,gBAAgB;QACtB;QACI,UAAU;UACR,SAAS;UACT,SAAS;UACT,aAAa;UACb,QAAQ;QACd;QACI,YAAY;UACV,MAAM;UACN,UAAU;UACV,OAAO;UACP,gBAAgB;UAChB,oBAAoB;QAC1B;QACI,QAAQ;UACN,OAAO;QACb;QACI,QAAQ;UACN,OAAO;QACb;QACI,YAAY;UACV,OAAO;UACP,SAAS;UACT,QAAQ;UACR,OAAO;UACP,OAAO;QACb;QACI,QAAQ;UACN,WAAW;UACX,QAAQ;UACR,SAAS;UACT,UAAU;QAChB;QACI,QAAQ;UACN,cAAc;UACd,wBAAwB;UACxB,sBAAsB;QAC5B;QACI,OAAO;UACL,WAAW;UACX,eAAe;UACf,aAAa;UACb,aAAa;UACb,SAAS;QACf;QACI,MAAM;UACJ,WAAW;QACjB;QACI,UAAU;UACR,SAAS;UACT,QAAQ;UACR,QAAQ,CAAC,kBAAkB,gBAAgB;UAC3C,mBAAmB;UACnB,iBAAiB;UACjB,kBAAkB;QACxB;QACI,OAAO;UACL,OAAO;QACb;QACI,YAAY;UACV,OAAO;QACb;QACI,YAAY;UACV,mBAAmB;UACnB,kBAAkB;QACxB;MACA;IACA;;;;;;AC1JA,IAAO,6CAAQ;", "names": []}