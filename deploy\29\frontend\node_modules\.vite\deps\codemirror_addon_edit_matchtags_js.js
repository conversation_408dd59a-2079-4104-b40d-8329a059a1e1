import {
  require_xml_fold
} from "./chunk-2C3PHAT4.js";
import {
  require_codemirror
} from "./chunk-2G24OWTA.js";
import {
  __commonJS
} from "./chunk-J43GMYXM.js";

// node_modules/codemirror/addon/edit/matchtags.js
var require_matchtags = __commonJS({
  "node_modules/codemirror/addon/edit/matchtags.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror(), require_xml_fold());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror", "../fold/xml-fold"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      CodeMirror2.defineOption("matchTags", false, function(cm, val, old) {
        if (old && old != CodeMirror2.Init) {
          cm.off("cursorActivity", doMatchTags);
          cm.off("viewportChange", maybeUpdateMatch);
          clear(cm);
        }
        if (val) {
          cm.state.matchBothTags = typeof val == "object" && val.bothTags;
          cm.on("cursorActivity", doMatchTags);
          cm.on("viewportChange", maybeUpdateMatch);
          doMatchTags(cm);
        }
      });
      function clear(cm) {
        if (cm.state.tagHit)
          cm.state.tagHit.clear();
        if (cm.state.tagOther)
          cm.state.tagOther.clear();
        cm.state.tagHit = cm.state.tagOther = null;
      }
      function doMatchTags(cm) {
        cm.state.failedTagMatch = false;
        cm.operation(function() {
          clear(cm);
          if (cm.somethingSelected())
            return;
          var cur = cm.getCursor(), range = cm.getViewport();
          range.from = Math.min(range.from, cur.line);
          range.to = Math.max(cur.line + 1, range.to);
          var match = CodeMirror2.findMatchingTag(cm, cur, range);
          if (!match)
            return;
          if (cm.state.matchBothTags) {
            var hit = match.at == "open" ? match.open : match.close;
            if (hit)
              cm.state.tagHit = cm.markText(hit.from, hit.to, { className: "CodeMirror-matchingtag" });
          }
          var other = match.at == "close" ? match.open : match.close;
          if (other)
            cm.state.tagOther = cm.markText(other.from, other.to, { className: "CodeMirror-matchingtag" });
          else
            cm.state.failedTagMatch = true;
        });
      }
      function maybeUpdateMatch(cm) {
        if (cm.state.failedTagMatch)
          doMatchTags(cm);
      }
      CodeMirror2.commands.toMatchingTag = function(cm) {
        var found = CodeMirror2.findMatchingTag(cm, cm.getCursor());
        if (found) {
          var other = found.at == "close" ? found.open : found.close;
          if (other)
            cm.extendSelection(other.to, other.from);
        }
      };
    });
  }
});

// dep:codemirror_addon_edit_matchtags_js
var codemirror_addon_edit_matchtags_js_default = require_matchtags();
export {
  codemirror_addon_edit_matchtags_js_default as default
};
//# sourceMappingURL=codemirror_addon_edit_matchtags_js.js.map
