<template>
    <div class="system-edit-dept-container">
        <el-dialog :title="'【' + tableMsg.projectName + '】' + '项目设备列表'" v-model="isShowDialog" width="769px">
            <el-table ref="tableRef" :border="true" v-loading="tableData.loading" :data="tableData.data" width="100%">
                <el-table-column prop="name" label="设备名称" min-width="120"></el-table-column>
                <el-table-column prop="imei" label="IMEI" min-width="120"></el-table-column>
                <el-table-column prop="manufactor" label="厂家"  align="center"></el-table-column>
                <el-table-column prop="model" label="型号" align="center"></el-table-column>
                <el-table-column prop="tac" label="tac码" align="center"></el-table-column>
                <el-table-column prop="status" label="设备状态" align="center">
                    <template #default="scope">
                        <el-tag type="info" size="small" v-if="scope.row.status == 0">离线</el-tag>
                        <el-tag type="success" size="small" v-else>在线</el-tag>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="getInstanceList" />

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
  
<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';
import { ElMessage } from 'element-plus';
import api from '/@/api/project';

interface TableDataRow {
    id: string;
    name: string;
    imei: string;
    manufactor: string;
    model: string;
    status: number;
}
interface TableMsg {
    projectId: string;
    projectName: string;
}

interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            projectId: string;
        }
    },
    tableMsg: TableMsg;
    isShowDialog: boolean;
}

export default defineComponent({
    name: 'InstanceList',
    setup(prop, { emit }) {
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                total: 0,
                loading: false,
                param: {
                    pageNum: 1,
                    pageSize: 10,
                    projectId: ''
                },
            },
            tableMsg: {
                projectId: '',
                projectName: ''
            },
            isShowDialog: false
        });

        const getInstanceList = () => {
            state.tableData.loading = true;
            api.project.getInstances(state.tableData.param).then((res: any) => {
                state.tableData.data = res.instance;
                state.tableData.total = res.total;
            }).finally(() => (state.tableData.loading = false));
        };

        // 打开弹窗
        const openDialog = (row: TableMsg) => {
            if (row && typeof row === 'object') {
                state.tableMsg.projectId = row.projectId;
                state.tableMsg.projectName = row.projectName;
                state.tableData.param.projectId = row.projectId;
            }
            getInstanceList();
            state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };

        return {
            getInstanceList,
            openDialog,
            closeDialog,
            onCancel,
            ...toRefs(state),
        };
    },
});
</script>
  