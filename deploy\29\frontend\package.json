{"name": "vue-next-admin", "version": "2.0.2", "description": "vue3 vite next admin template", "author": "lyt_20201208", "license": "MIT", "scripts": {"dev": "vite --force", "build": "vite build", "build:open": "vite build --mode open", "deploy:zip": "npm run build && npm run zipAndUpload && npm run unzip && npm run success", "zipAndUpload": "cd dist && rm -rf zhgy.sagoo.cn.zip && zip -r -q zhgy.sagoo.cn.zip ./ && ssh iot 'sudo rm /www/wwwroot/zhgy.sagoo.cn-old.zip' | ssh iot 'sudo mv /www/wwwroot/zhgy.sagoo.cn.zip /www/wwwroot/zhgy.sagoo.cn-old.zip' | scp -r -O zhgy.sagoo.cn.zip iot:/www/wwwroot", "unzip": "ssh iot 'cd /www/wwwroot/ && sudo unzip -q -o -d ./zhgy.sagoo.cn zhgy.sagoo.cn.zip'", "success": "echo '\\033[31m 部署完成 \\033[0m'", "deploy": "npm run build && npm run deploy:rm && npm run deploy:scp && npm run deploy:auth | npm run success", "deploy:rm": "ssh iot 'rm -rf /www/wwwroot/zhgy.sagoo.cn-copy/* && rm -rf /www/wwwroot/zhgy.sagoo.cn-pre/*'", "deploy:scp": "scp -r -O ./dist/* iot:/www/wwwroot/zhgy.sagoo.cn-pre/ && ssh iot 'cd /www/wwwroot/zhgy.sagoo.cn/ && mv `ls | grep -v plugin` ../zhgy.sagoo.cn-copy && mv ../zhgy.sagoo.cn-pre/* ./'", "deploy:auth": "ssh iot 'chown -R www /www/wwwroot/zhgy.sagoo.cn/'", "deploy:heating": "npm run build && npm run deploy:heating:rm && npm run deploy:heating:scp && npm run deploy:heating:auth", "deploy:heating:rm": "ssh heating 'rm -rf /www/wwwroot/huanbao.lngxny.com-copy/* && rm -rf /www/wwwroot/huanbao.lngxny.com-pre/*'", "deploy:heating:scp": "scp -r -O ./dist/* heating:/www/wwwroot/huanbao.lngxny.com-pre/ && ssh heating 'cd /www/wwwroot/huanbao.lngxny.com/ && mv `ls | grep -v plugin` ../huanbao.lngxny.com-copy && mv ../huanbao.lngxny.com-pre/* ./'", "deploy:heating:auth": "ssh heating 'chown -R www /www/wwwroot/huanbao.lngxny.com/'", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/"}, "dependencies": {"@antv/g2plot": "^2.4.20", "@element-plus/icons-vue": "^2.0.9", "all": "0.0.0", "axios": "^0.26.0", "clipboard": "^2.0.11", "codemirror": "^5.65.9", "countup.js": "^2.1.0", "cropperjs": "^1.5.12", "dayjs": "^1.11.8", "downloadjs": "^1.4.7", "echarts": "^5.3.3", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.0.0", "element-plus": "2.2.28", "event-source-polyfill": "^1.0.31", "jsplumb": "^2.15.6", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pako": "^1.0.11", "print-js": "^1.6.0", "qrcodejs2-fixes": "^0.0.2", "screenfull": "^6.0.1", "sortablejs": "^1.14.0", "splitpanes": "^3.1.1", "uuid": "^9.0.0", "vform3-builds": "^3.0.8", "vue": "^3.2.37", "vue-baidu-map-3x": "^1.0.18", "vue-clipboard3": "^1.0.1", "vue-codemirror": "^6.1.1", "vue-grid-layout": "^3.0.0-beta1", "vue-i18n": "9.1.10", "vue-router": "^4.0.13", "vue3-cron": "^1.1.8", "vue3-json-viewer": "^2.2.2", "vuex": "^4.0.2", "wangeditor": "^4.7.12", "xlsx-with-styles": "^0.17.2"}, "devDependencies": {"@types/node": "^17.0.21", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.10.7", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "@vitejs/plugin-vue": "^2.2.4", "@vue/compiler-sfc": "^3.2.31", "dotenv": "^16.0.0", "eslint": "^8.10.0", "eslint-plugin-vue": "^8.5.0", "prettier": "^2.5.1", "sass": "^1.49.9", "sass-loader": "^12.6.0", "typescript": "^4.6.2", "vite": "^2.9.16", "vue-eslint-parser": "^8.3.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://gitee.com/lyt-top/vue-next-admin/issues"}, "engines": {"node": ">=12.0.0", "npm": ">= 6.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus", "vue-next-admin", "next-admin"], "repository": {"type": "git", "url": "https://gitee.com/lyt-top/vue-next-admin.git"}}