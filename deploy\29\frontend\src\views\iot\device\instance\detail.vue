<template>
  <div class="system-dic-container">
    <div class="content">
      <div class="cont_box">
        <div class="title">设备：{{ detail.name }}</div>
        <div class="pro-status"><span :class="detail.status == 1 ? 'on' : 'off'"></span>{{ detail.status == 1 ? '在线' :
          '离线' }}</div>
      </div>
    </div>

    <div class="content-box">
      <el-tabs v-model="activeName" class="demo-tabs">

        <el-tab-pane label="运行状态" name="1">
          <div style=" display: flex; padding: 10px;flex-wrap: wrap;">
            <div class="ant-card">
              <div class="ant-card-body">
                <div class="cardflex">
                  <div>设备状态</div>
                </div>
                <div class="statusname" style="color: #52c41a" v-if="detail.status == 0">离线</div>
                <div class="statusname" style="color: #f5222d" v-if="detail.status == 1">在线</div>
                <div class="cardflex comtest">
                  <div> 最近一次在线时间</div>
                </div>
                <div class="statusname" style="font-size:20px;">{{ detail.lastOnlineTime ||
                      '-' }}</div>
              </div>
            </div>

            <div class="ant-card">
              <div class="ant-card-body">
                <div class="cardflex">
                  <div>温度</div>
                </div>
                <div class="cardflex comtest">
                  <div>
                    <img src="/imgs/wendu.png" style="width: 160%;height: 120%;">
                  </div>
                  <div style="font-size: 50px;height:100px;line-height:100px;" v-if="detail.status == 1">{{ detail.temperature }}℃</div>
                  <div style="font-size: 50px;height:100px;line-height:100px;" v-if="detail.status == 0">——</div>
                </div>

                <div class="cardflex comtest">
                  <div>
                      <img src="/imgs/cpu.png" style="width: 80%;height: 80%;">
                  </div>
                  <div style="font-size: 50px;height:100px;line-height:100px;" v-if="detail.status == 1">{{ detail.cpu }}%</div>
                  <div style="font-size: 50px;height:100px;line-height:100px;" v-if="detail.status == 0">——</div>
                </div>

                <div class="cardflex comtest">
                  <div>
                    <img src="/imgs/cunchu.png" style="width: 80%;height: 80%;">
                  </div>
                  <div style="font-size: 50px;height:100px;line-height:100px;" v-if="detail.status == 1">{{ detail.store }}%</div>
                  <div style="font-size: 50px;height:100px;line-height:100px;" v-if="detail.status == 0">——</div>
                </div>
              </div>
            </div>

            <div class="ant-card">
              <div class="ant-card-body">
                <div class="cardflex">
                  <div>射频器件运行情况</div>
                </div>
                <div class="cardflex comtest">
                  <div>
                    <img src="/imgs/shepinqi.png" style="width: 60%;height: 60%;">
                  </div>
                  <div style="font-size: 30px;height:100px;line-height:100px;">{{ detail.rf }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="设备信息" name="2">
          <div class="pro-box">
            <div class="protitle">设备详情</div>
          </div>

          <div class="ant-descriptions-view">
            <table>
              <tbody>
                <tr class="ant-descriptions-row">
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">设备名称</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.name }}</td>
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">项目名称</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.projectName }}</td>
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">IMEI</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.imei }}</td>

                </tr>
                <tr class="ant-descriptions-row">
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">设备厂家</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.manufactor }}</td>
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">设备型号</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.model }}</td>
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">设备tac码</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.tac }}</td>
                </tr>
                <tr class="ant-descriptions-row">
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">设备状态</th>
                  <td class="ant-descriptions-item-content" colspan="1">
                    <el-tag type="info" size="small" v-if="detail.status==0">离线</el-tag>
                    <el-tag type="success" size="small" v-if="detail.status==1">在线</el-tag>
                  </td>
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">创建时间</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.createdAt }}</td>
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">最近上线时间</th>
                  <td class="ant-descriptions-item-content" colspan="1">{{ detail.lastOnlineTime }}</td>
                </tr>
                <tr class="ant-descriptions-row">
                  <th class="ant-descriptions-item-label ant-descriptions-item-colon">备注</th>
                  <td class="ant-descriptions-item-content" colspan="5">{{ detail.remark }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="远程控制" name="3">
          <div style=" display: flex; padding: 10px;flex-wrap: wrap;">
            <div class="ant-card" @click="resetInstance"
              style="height: 130px;line-height: 130px;font-size:24px;text-align: center; cursor:pointer;">
              <el-icon style="font-size: 18px;"><ele-Refresh /></el-icon>
              重启设备
            </div>

            <div class="ant-card" @click="testSignal"
              style="height: 130px;line-height: 130px;font-size:24px;text-align: center; cursor:pointer;">
              <el-icon style="font-size: 18px;"><ele-Odometer /></el-icon>
              信号强度检测
            </div>

            <div class="ant-card" @click="setHeartbeat"
              style="height: 130px;line-height: 130px;font-size:24px;text-align: center; cursor:pointer;">
              <el-icon style="font-size: 18px;"><ele-Setting /></el-icon>
              心跳
            </div>
          </div>
        </el-tab-pane>

      </el-tabs>
    </div>

    <el-dialog v-model="dialogVisible" :title="percentageTitle" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false" width="569px">
      <div style="text-align: center;">
        <el-progress type="dashboard" :percentage="percentage" :stroke-width="10">
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage }}%</span>
            <span class="percentage-label">{{ percentageLabel }}</span>
          </template>
        </el-progress>
        <div v-show="signalVisible">网络信号强度为：<span style="color: red;font-weight: bold;">{{ signalNum }}dBm</span></div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onDialogClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogHeartbeatVisible" title="心跳周期设置" :close-on-click-modal="false"
      :close-on-press-escape="false" width="569px">
      <el-form ref="formRef" :model="ruleForm" :rules="rules" size="default" label-width="120px">
        <el-row :gutter="35">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
            <el-form-item label="心跳周期（S）" prop="cycle">
              <el-input v-model="ruleForm.cycle" type="number"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="onHeartbeatDialogClose" size="default">取消</el-button>
          <el-button type="primary" @click="onHeartbeatDialogConfirm" size="default">确定</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>
<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent, unref } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import 'vue3-json-viewer/dist/index.css';
import api from '/@/api/device';
import { Check } from '@element-plus/icons-vue'

import { useRoute } from 'vue-router';

interface TableDataState {
  //ids: number[];
  detail: any;
  //isShowDialog: boolean;
  dialogVisible: boolean;
  dialogHeartbeatVisible: boolean;
  signalVisible: boolean;
  activeName: string;
  percentage: number;
  percentageLabel: string;
  percentageTitle: string;
  percentageTimer: any;
  signalNum: number;
}

interface RuleFormState {
  cycle: string;
}

interface EditFormState {
  ruleForm: RuleFormState;
  rules: object;
}

const baseForm: RuleFormState = {
  cycle: ''
};

export default defineComponent({
  name: 'deviceEditPro',
  components: { Check },
  setup(prop, context) {
    const route = useRoute();
    const formRef = ref<HTMLElement | null>(null);
    const state = reactive<TableDataState>({
      dialogHeartbeatVisible: false,
      dialogVisible: false,
      signalVisible: false,
      activeName: '1', // 当前tab
      detail: {},
      percentage: 0,
      percentageLabel: '',
      percentageTitle: '',
      percentageTimer: null,
      signalNum: 0
    });
    const editState = reactive<EditFormState>({
      ruleForm: {
        ...baseForm
      },
      rules: {
        cycle: [{ required: true, message: '心跳周期不能为空', trigger: 'blur' }]
      }
    });

    onMounted(() => {
      var param = route.params && route.params.id;
      var id = '';
      if (param && typeof param === 'string') {
        id = param
      } else {
        id = param[0]
      }
      api.instance.detail(id).then((res: any) => {
        state.detail = res.data;
      });
    });

    const resetInstance = () => {
      if(state.detail.status == 0) {
        ElMessage.warning('设备已离线无法重启');
        return
      }
      state.percentage = 0;
      state.percentageLabel = '正在重启';
      state.percentageTitle = '重启设备进度';
      let random = Math.floor(Math.random() * (11)) + 5; //5~15的一个整数
      random *= 1000;
      let addNum = Math.round(100 / (random / 100));
      state.dialogVisible = true;
      state.percentageTimer = setInterval(() => {
        console.log('计数中');
        if (state.percentage + addNum > 100) {
          state.percentage = 100;
        } else {
          state.percentage += addNum;
        }
        if (state.percentage == 100) {
          clearInterval(state.percentageTimer);
          state.percentageLabel = '重启成功';
          state.percentageTimer = null;
        }
      }, 100);
    };

    const testSignal = () => {
      if(state.detail.status == 0) {
        ElMessage.warning('设备已离线无法检测网络信号');
        return
      }
      state.percentage = 0;
      state.percentageLabel = '正在检测';
      state.percentageTitle = '网络信号等级检测';
      let random = Math.floor(Math.random() * (11)) + 5; //5~15的一个整数
      random *= 1000;
      let addNum = Math.round(100 / (random / 100));
      state.dialogVisible = true;
      state.percentageTimer = setInterval(() => {
        if (state.percentage + addNum > 100) {
          state.percentage = 100;
        } else {
          state.percentage += addNum;
        }
        if (state.percentage == 100) {
          clearInterval(state.percentageTimer);
          state.percentageLabel = '检测完毕';
          state.percentageTimer = null;
          state.signalVisible = true;
          state.signalNum = Math.floor(Math.random() * (151)) + (-90);
        }
      }, 100);
    };

    const onDialogClose = () => {
      clearInterval(state.percentageTimer);
      state.percentageTimer = null;
      state.dialogVisible = false;
      state.signalVisible = false;
    };

    const setHeartbeat = () => {
      if(state.detail.status == 0) {
        ElMessage.warning('设备已离线无法设置心跳周期');
        return
      }
      resetForm();
      state.dialogHeartbeatVisible = true;
    };

    const onHeartbeatDialogConfirm = () => {
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.validate((valid: boolean) => {
        if (valid) {
          ElMessage.success('设置成功');
          state.dialogHeartbeatVisible = false;
        }
      });
    };

    const onHeartbeatDialogClose = () => {
      state.dialogHeartbeatVisible = false;
    };

    const resetForm = () => {
      editState.ruleForm = {
        ...baseForm,
      };
      const formWrap = unref(formRef) as any;
      if (!formWrap) return;
      formWrap.resetFields();
    };

    return {
      Check,
      onDialogClose,
      resetInstance,
      testSignal,
      setHeartbeat,
      onHeartbeatDialogConfirm,
      onHeartbeatDialogClose,
      formRef,
      resetForm,
      ...toRefs(state),
      ...toRefs(editState),
    };
  },
});
</script>
<style>
.content {
  background: #fff;
  width: 100%;
  padding: 20px;
}

.content-box {
  background: #fff;
  width: 100%;
  padding: 20px;
  margin-top: 20px;
}

.cont_box {
  display: flex;
}

.cont_box .title {
  font-size: 24px;
}

.cont_box .pro-status {
  line-height: 40px;
  margin-left: 30px;
}

.cont_box .pro-status .on {
  background: #52c41a;
}

.cont_box .pro-status .off {
  background: #c41a1a;
}

.cont_box .pro-status span {
  position: relative;
  top: -1px;
  display: inline-block;
  width: 6px;
  height: 6px;
  vertical-align: middle;
  border-radius: 50%;
  margin-right: 5px;
}

.cont_box .pro-option {
  line-height: 40px;
  margin-left: 10px;
  color: #1890ff;
  cursor: pointer;
}

.content-box .pro-box {
  display: flex;
  padding: 10px;
  justify-content: space-between;

}

.content-box .pro-box .protitle {
  font-size: 18px;
  font-weight: bold;
  line-height: 35px;
}

.content-box .pro-box .buttonedit {
  border: 0px;
  color: #1890ff;
}

table {
  border-collapse: collapse;
  text-indent: initial;
  border-spacing: 2px;
}

tbody {
  box-sizing: border-box;
  display: table-row-group;
  vertical-align: middle;
  border-color: inherit;
}

tr {
  display: table-row;
  vertical-align: inherit;
  border-color: inherit;
}

.ant-descriptions-view {
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
}

.ant-descriptions-view {
  border: 1px solid #e8e8e8;
}

.ant-descriptions-view table {
  width: 100%;
  table-layout: fixed;
}

.ant-descriptions-view>table {
  table-layout: auto;
}

.ant-descriptions-row {
  border-bottom: 1px solid #e8e8e8;
}

.ant-descriptions-item-label {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
}

.ant-descriptions-item-label {
  padding: 16px 24px;
  border-right: 1px solid #e8e8e8;
}

.ant-descriptions-item-label {
  background-color: #fafafa;
}

.ant-descriptions-item-content {
  padding: 16px 24px;
  border-right: 1px solid #e8e8e8;
  display: table-cell;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
}

.wu-box {
  border: #e8e8e8 solid 1px;
  padding: 20px;
  width: 100%;
}

.wu-box .wu-title {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 20px;
  border-bottom: #e8e8e8 1px solid;
}

.wu-box .wu-title .title {
  font-size: 18px;
}

.ant-card {
  box-sizing: border-box;
  margin: 10px;
  width: 23.2%;
  height: 230px;
  font-size: 14px;
  font-variant: tabular-nums;
  border: 1px solid var(--next-border-color-light);

  line-height: 1.5;
  list-style: none;
  font-feature-settings: 'tnum';
  position: relative;
  border-radius: 2px;
  transition: all 0.3s;
}

.ant-card-body {
  padding: 24px;
  zoom: 1;
}

.cardflex {
  display: flex;
  justify-content: space-between;
}

.statusname {
  font-size: 30px;
  margin-top: 10px;
  margin-bottom: 15px;
}

.comtest {
  margin-top: 20px;
  height: 30px;
  line-height: 30px;
}

.percentage-value {
  display: block;
  margin-top: 10px;
  font-size: 28px;
}

.percentage-label {
  display: block;
  margin-top: 10px;
  font-size: 16px;
}

.demo-progress .el-progress--line {
  margin-bottom: 15px;
  width: 450px;
  padding: 10px;
}

.demo-progress .el-progress--circle {
  margin-right: 15px;
}
</style>


