<template>
    <div class="system-edit-dept-container">
        <el-dialog :title="(ruleForm.id ? '修改' : '添加') + '固件'" v-model="isShowDialog" width="769px">
            <el-form ref="formRef" :model="ruleForm" :rules="rules" size="default" label-width="90px">
                <el-row :gutter="35">

                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="固件名" prop="name">
                            <el-input v-model="ruleForm.name" placeholder="请输入固件名" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="版本" prop="version">
                            <el-input v-model="ruleForm.version" placeholder="请输入版本" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="项目名称" prop="projectId">
                            <el-select v-model="ruleForm.projectId" placeholder="请选择项目名称" filterable clearable value-key="id">
                                <el-option v-for="item in projectData" :key="item.id" :label="item.name" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="制定文件" prop="fileAddress">
                            <el-input disabled v-if="ruleForm.fileAddress" size="default" v-model="ruleForm.fileAddress"></el-input>
				            <uploadFile @update="updateFile" url="/common/singleFile"></uploadFile>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="发布情况" prop="status">
							<el-switch v-model="ruleForm.status" inline-prompt :active-value="1" :inactive-value="0" active-text="已发布" inactive-text="未发布" :disabled="true"></el-switch>
						</el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="ruleForm.remark" type="textarea" placeholder="请输入备注"
                                maxlength="150"></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" size="default">{{ ruleForm.id ? '修 改' : '添 加' }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';
import api from '/@/api/firmware';
import api_ from '/@/api/project';
import { ElMessage } from 'element-plus';
import uploadFile from '/@/components/upload/uploadFile.vue'

interface RuleFormState {
    id: string;
    name: string;
    version: string;
    projectId: string;
    projectName: string;
    fileAddress: string;
    remark: string;
    status: number;
    create_by: string;
    created_at: string;
    update_by: string;
    updated_at: string;
}
interface AllProjectList {
    id: string;
    name: string;
}
interface FirmwareState {
    isShowDialog: boolean;
    ruleForm: RuleFormState;
    projectData: AllProjectList[];
    rules: object;
}

const baseForm: RuleFormState = {
    id: '',
    name: '',
    version: '',
    projectId: '',
    projectName: '',
    fileAddress: '',
    remark: '',
    status: 0,
    create_by: '',
    created_at: '',
    update_by: '',
    updated_at: ''
};

export default defineComponent({
    name: 'EditFirmware',
    components: { uploadFile },
    setup(prop, { emit }) {
        const formRef = ref<HTMLElement | null>(null);
        const state = reactive<FirmwareState>({
            isShowDialog: false,
            ruleForm: {
                ...baseForm,
            },
            projectData: [], // 项目数据
            rules: {
                name: [{ required: true, message: '固件名不能为空', trigger: 'blur' }],
                version: [{ required: true, message: '版本不能为空', trigger: 'blur' }],
                projectId: [{ required: true, message: '项目名称不能为空', trigger: 'change' }],
                fileAddress: [{ required: true, message: '制定文件不能为空', trigger: 'change' }],
            },
        });

        // 打开弹窗
        const openDialog = (row?: RuleFormState | string) => {
            resetForm();
            api_.project.getOptionList({ status: 0 }).then((res: any) => {
				state.projectData = res.project || [];
			});
            if (row && typeof row === 'object') {
                state.ruleForm = row;
            } else if (row && typeof row === 'string') {
                state.ruleForm.id = row;
            }
            state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };
        const updateFile = (url: string) => {
	        console.log('文件上传成功')
            console.log(url)
	        state.ruleForm.fileAddress = url
        };
        // 新增
        const onSubmit = () => {
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.validate((valid: boolean) => {
                if (valid) {
                    console.log(state.ruleForm);
                    if (!state.ruleForm.id) {
                        //添加
                        api.firmware.add(state.ruleForm).then(() => {
                            ElMessage.success('固件添加成功');
                            closeDialog(); // 关闭弹窗
                            emit('getFirmwareList');
                        });
                    } else {
                        //修改
                        api.firmware.edit(state.ruleForm).then(() => {
                            ElMessage.success('固件修改成功');
                            closeDialog(); // 关闭弹窗
                            emit('getFirmwareList');
                        });
                    }
                }
            });
        };
        const resetForm = () => {
            state.ruleForm = {
                ...baseForm,
            };
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.resetFields();
        };
        return {
            openDialog,
            closeDialog,
            onCancel,
            onSubmit,
            updateFile,
            formRef,
            ...toRefs(state),
        };
    },
});
</script>

<style>
    .el-form-item .el-form-item__content .el-select {
        width: 100%;
    }
</style>
