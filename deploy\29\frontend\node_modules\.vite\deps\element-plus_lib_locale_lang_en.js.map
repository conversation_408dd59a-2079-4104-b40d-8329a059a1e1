{"version": 3, "sources": ["../../../packages/locale/lang/en.ts", "dep:element-plus_lib_locale_lang_en"], "sourcesContent": ["export default {\n  name: 'en',\n  el: {\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Clear',\n      defaultLabel: 'color picker',\n      description:\n        'current color is {color}. press enter to select a new color.',\n    },\n    datepicker: {\n      now: 'Now',\n      today: 'Today',\n      cancel: 'Cancel',\n      clear: 'Clear',\n      confirm: 'OK',\n      dateTablePrompt:\n        'Use the arrow keys and enter to select the day of the month',\n      monthTablePrompt: 'Use the arrow keys and enter to select the month',\n      yearTablePrompt: 'Use the arrow keys and enter to select the year',\n      selectedDate: 'Selected date',\n      selectDate: 'Select date',\n      selectTime: 'Select time',\n      startDate: 'Start Date',\n      startTime: 'Start Time',\n      endDate: 'End Date',\n      endTime: 'End Time',\n      prevYear: 'Previous Year',\n      nextYear: 'Next Year',\n      prevMonth: 'Previous Month',\n      nextMonth: 'Next Month',\n      year: '',\n      month1: 'January',\n      month2: 'February',\n      month3: 'March',\n      month4: 'April',\n      month5: 'May',\n      month6: 'June',\n      month7: 'July',\n      month8: 'August',\n      month9: 'September',\n      month10: 'October',\n      month11: 'November',\n      month12: 'December',\n      week: 'week',\n      weeks: {\n        sun: 'Sun',\n        mon: 'Mon',\n        tue: 'Tue',\n        wed: 'Wed',\n        thu: 'Thu',\n        fri: 'Fri',\n        sat: 'Sat',\n      },\n      weeksFull: {\n        sun: 'Sunday',\n        mon: 'Monday',\n        tue: 'Tuesday',\n        wed: 'Wednesday',\n        thu: 'Thursday',\n        fri: 'Friday',\n        sat: 'Saturday',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    inputNumber: {\n      decrease: 'decrease number',\n      increase: 'increase number',\n    },\n    select: {\n      loading: 'Loading',\n      noMatch: 'No matching data',\n      noData: 'No data',\n      placeholder: 'Select',\n    },\n    dropdown: {\n      toggleDropdown: 'Toggle Dropdown',\n    },\n    cascader: {\n      noMatch: 'No matching data',\n      loading: 'Loading',\n      placeholder: 'Select',\n      noData: 'No data',\n    },\n    pagination: {\n      goto: 'Go to',\n      pagesize: '/page',\n      total: 'Total {total}',\n      pageClassifier: '',\n      deprecationWarning:\n        'Deprecated usages detected, please refer to the el-pagination documentation for more details',\n    },\n    dialog: {\n      close: 'Close this dialog',\n    },\n    drawer: {\n      close: 'Close this dialog',\n    },\n    messagebox: {\n      title: 'Message',\n      confirm: 'OK',\n      cancel: 'Cancel',\n      error: 'Illegal input',\n      close: 'Close this dialog',\n    },\n    upload: {\n      deleteTip: 'press delete to remove',\n      delete: 'Delete',\n      preview: 'Preview',\n      continue: 'Continue',\n    },\n    slider: {\n      defaultLabel: 'slider between {min} and {max}',\n      defaultRangeStartLabel: 'pick start value',\n      defaultRangeEndLabel: 'pick end value',\n    },\n    table: {\n      emptyText: 'No Data',\n      confirmFilter: 'Confirm',\n      resetFilter: 'Reset',\n      clearFilter: 'All',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'No Data',\n    },\n    transfer: {\n      noMatch: 'No matching data',\n      noData: 'No data',\n      titles: ['List 1', 'List 2'], // to be translated\n      filterPlaceholder: 'Enter keyword', // to be translated\n      noCheckedFormat: '{total} items', // to be translated\n      hasCheckedFormat: '{checked}/{total} checked', // to be translated\n    },\n    image: {\n      error: 'FAILED',\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes',\n      cancelButtonText: 'No',\n    },\n  },\n}\n", "export default require(\"./node_modules/element-plus/lib/locale/lang/en.js\");"], "mappings": ";;;;;;;;;AAAA,QAAA,UAAe;MACb,MAAM;MACN,IAAI;QACF,aAAa;UACX,SAAS;UACT,OAAO;UACP,cAAc;UACd,aAAa;QACnB;QACI,YAAY;UACV,KAAK;UACL,OAAO;UACP,QAAQ;UACR,OAAO;UACP,SAAS;UACT,iBAAiB;UACjB,kBAAkB;UAClB,iBAAiB;UACjB,cAAc;UACd,YAAY;UACZ,YAAY;UACZ,WAAW;UACX,WAAW;UACX,SAAS;UACT,SAAS;UACT,UAAU;UACV,UAAU;UACV,WAAW;UACX,WAAW;UACX,MAAM;UACN,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,QAAQ;UACR,SAAS;UACT,SAAS;UACT,SAAS;UACT,MAAM;UACN,OAAO;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;UACb;UACM,WAAW;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;UACb;UACM,QAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;UACb;QACA;QACI,aAAa;UACX,UAAU;UACV,UAAU;QAChB;QACI,QAAQ;UACN,SAAS;UACT,SAAS;UACT,QAAQ;UACR,aAAa;QACnB;QACI,UAAU;UACR,gBAAgB;QACtB;QACI,UAAU;UACR,SAAS;UACT,SAAS;UACT,aAAa;UACb,QAAQ;QACd;QACI,YAAY;UACV,MAAM;UACN,UAAU;UACV,OAAO;UACP,gBAAgB;UAChB,oBAAoB;QAC1B;QACI,QAAQ;UACN,OAAO;QACb;QACI,QAAQ;UACN,OAAO;QACb;QACI,YAAY;UACV,OAAO;UACP,SAAS;UACT,QAAQ;UACR,OAAO;UACP,OAAO;QACb;QACI,QAAQ;UACN,WAAW;UACX,QAAQ;UACR,SAAS;UACT,UAAU;QAChB;QACI,QAAQ;UACN,cAAc;UACd,wBAAwB;UACxB,sBAAsB;QAC5B;QACI,OAAO;UACL,WAAW;UACX,eAAe;UACf,aAAa;UACb,aAAa;UACb,SAAS;QACf;QACI,MAAM;UACJ,WAAW;QACjB;QACI,UAAU;UACR,SAAS;UACT,QAAQ;UACR,QAAQ,CAAC,UAAU,QAAQ;UAC3B,mBAAmB;UACnB,iBAAiB;UACjB,kBAAkB;QACxB;QACI,OAAO;UACL,OAAO;QACb;QACI,YAAY;UACV,OAAO;QACb;QACI,YAAY;UACV,mBAAmB;UACnB,kBAAkB;QACxB;MACA;IACA;;;;;;AC3JA,IAAO,0CAAQ;", "names": []}