import { get, post, del, put, file } from '/@/utils/request';

export default {
    firmware: {
        getList: (params: object) => get('/firmware/list', params),
        getOptionList: (params: object) => get('/firmware/optionList', params),
        add: (data: object) => post('/firmware/add', data),
        edit: (data: object) => put('/firmware/edit', data),
        del: (id: string) => del('/firmware/del', { id }),
        //发布
        release: (id: string) => put('/firmware/release', { id }),
        //升级日志列表
        getUpgradeList: (params: object) => get('/firmware/upgradeList', params),
        getFailList: (params: object) => get('/firmware/failList', params),
        //重新升级
        reRelease: (id: string) => put('/firmware/reRelease', { id })
    }
}