<template>
    <div class="system-dept-container">
        <el-card shadow="hover">
            <div class="system-dept-search mb15">
                <el-form :inline="true">
                    <el-form-item label="厂家">
                        <el-input size="default" v-model="tableData.param.manufactor" placeholder="请输入厂家" class="w-50" clearable />
                    </el-form-item>
                    <el-form-item label="型号">
                        <el-input size="default" v-model="tableData.param.model" placeholder="请输入型号" class="w-50" clearable />
                    </el-form-item>
                    <el-form-item label="tac码">
                        <el-input size="default" v-model="tableData.param.tac" placeholder="请输入tac码" class="w-50" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTacList">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" type="default" class="ml10" @click="onOpenAdd" v-auth="'add'">
                            <el-icon>
                                <ele-FolderAdd />
                            </el-icon>
                            新增tac码
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="tableData.data" style="width: 100%" row-key="id" default-expand-all v-loading="tableData.loading">
                <el-table-column label="序号" width="60" align="center" type="index"></el-table-column>
                <el-table-column prop="manufactor" label="厂家" align="center"></el-table-column>
                <el-table-column prop="model" label="型号" align="center"></el-table-column>
                <el-table-column prop="tac" label="tac码" align="center"></el-table-column>
                <el-table-column label="操作" align="center" width="120" fixed="right">
                    <template #default="scope">
                        <el-button size="small" text type="warning" @click="onOpenEdit(scope.row)" v-auth="'edit'">修改</el-button>
                        <el-button size="small" text type="danger" @click="onTabelRowDel(scope.row)" v-auth="'del'">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="getTacList" />
        </el-card>
        <EditTac ref="editDeptRef" @getTacList="getTacList" />
    </div>
</template>
  
<script lang="ts">
import { ref, toRefs, reactive, onMounted, defineComponent, h } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditTac from './component/edit.vue';
import api from '/@/api/device';


// 定义接口来定义对象的类型
interface TableDataRow {
    id: string;
    manufactor: string;
    model: string;
    tac: string;
    //status: number;
    create_by: string;
    created_at: string;
    update_by: string;
    updated_at: string;
}

interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            manufactor: string;
            model: string;
            tac: string;
            //status: number;
        };
    };
}


export default defineComponent({
    name: 'tac',
    components: { EditTac },
    setup() {
        const editDeptRef = ref();
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                total: 0,
                loading: false,
                param: {
                    pageNum: 1,
                    pageSize: 10,
                    manufactor: '',
                    model: '',
                    tac: '',
                    //status: ''
                }
            }
        });
        // 初始化表格数据
        const initTableData = () => {
            getTacList();
        };
        const getTacList = () => {
            state.tableData.loading = true;
            api.tac.getList(state.tableData.param).then((res: any) => {
                state.tableData.data = res.tac;
                state.tableData.total = res.total;
            }).finally(() => (state.tableData.loading = false));
        };
        // 打开新增tac码弹窗
        const onOpenAdd = (row?: TableDataRow) => {
            editDeptRef.value.openDialog(row?.id);
        };
        // 打开编辑tac码弹窗
        const onOpenEdit = (row: TableDataRow) => {
            editDeptRef.value.openDialog({ ...row });
        };
        // 删除当前行
        const onTabelRowDel = (row: TableDataRow) => {
            ElMessageBox.confirm(`此操作将永久删除tac码：${row.tac}, 是否继续?`, '提示', {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.tac.del(row.id).then(() => {
                    ElMessage.success('删除成功');
                    getTacList();
                });
            }).catch(() => {
                //ElMessage.success('删除失败');
            });
        };
        // 页面加载时
        onMounted(() => {
            initTableData();
        });
        return {
            editDeptRef,
            getTacList,
            onOpenAdd,
            onOpenEdit,
            onTabelRowDel,
            ...toRefs(state),
        };
    },
});
</script>
  