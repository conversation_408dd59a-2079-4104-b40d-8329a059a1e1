{"version": 3, "sources": ["../../codemirror/addon/display/fullscreen.js", "dep:codemirror_addon_display_fullscreen_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineOption(\"fullScreen\", false, function(cm, val, old) {\n    if (old == CodeMirror.Init) old = false;\n    if (!old == !val) return;\n    if (val) setFullscreen(cm);\n    else setNormal(cm);\n  });\n\n  function setFullscreen(cm) {\n    var wrap = cm.getWrapperElement();\n    cm.state.fullScreenRestore = {scrollTop: window.pageYOffset, scrollLeft: window.pageXOffset,\n                                  width: wrap.style.width, height: wrap.style.height};\n    wrap.style.width = \"\";\n    wrap.style.height = \"auto\";\n    wrap.className += \" CodeMirror-fullscreen\";\n    document.documentElement.style.overflow = \"hidden\";\n    cm.refresh();\n  }\n\n  function setNormal(cm) {\n    var wrap = cm.getWrapperElement();\n    wrap.className = wrap.className.replace(/\\s*CodeMirror-fullscreen\\b/, \"\");\n    document.documentElement.style.overflow = \"\";\n    var info = cm.state.fullScreenRestore;\n    wrap.style.width = info.width; wrap.style.height = info.height;\n    window.scrollTo(info.scrollLeft, info.scrollTop);\n    cm.refresh();\n  }\n});\n", "export default require(\"./node_modules/codemirror/addon/display/fullscreen.js\");"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,aAAa,cAAc,OAAO,SAAS,IAAI,KAAK,KAAK;AAClE,YAAI,OAAOA,YAAW;AAAM,gBAAM;AAClC,YAAI,CAAC,OAAO,CAAC;AAAK;AAClB,YAAI;AAAK,wBAAc,EAAE;AAAA;AACpB,oBAAU,EAAE;AAAA,MACnB,CAAC;AAED,eAAS,cAAc,IAAI;AACzB,YAAI,OAAO,GAAG,kBAAkB;AAChC,WAAG,MAAM,oBAAoB;AAAA,UAAC,WAAW,OAAO;AAAA,UAAa,YAAY,OAAO;AAAA,UAClD,OAAO,KAAK,MAAM;AAAA,UAAO,QAAQ,KAAK,MAAM;AAAA,QAAM;AAChF,aAAK,MAAM,QAAQ;AACnB,aAAK,MAAM,SAAS;AACpB,aAAK,aAAa;AAClB,iBAAS,gBAAgB,MAAM,WAAW;AAC1C,WAAG,QAAQ;AAAA,MACb;AAEA,eAAS,UAAU,IAAI;AACrB,YAAI,OAAO,GAAG,kBAAkB;AAChC,aAAK,YAAY,KAAK,UAAU,QAAQ,8BAA8B,EAAE;AACxE,iBAAS,gBAAgB,MAAM,WAAW;AAC1C,YAAI,OAAO,GAAG,MAAM;AACpB,aAAK,MAAM,QAAQ,KAAK;AAAO,aAAK,MAAM,SAAS,KAAK;AACxD,eAAO,SAAS,KAAK,YAAY,KAAK,SAAS;AAC/C,WAAG,QAAQ;AAAA,MACb;AAAA,IACF,CAAC;AAAA;AAAA;;;ACxCD,IAAO,iDAAQ;", "names": ["CodeMirror"]}