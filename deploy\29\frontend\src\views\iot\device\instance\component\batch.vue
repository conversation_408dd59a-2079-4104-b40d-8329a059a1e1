<template>
    <div class="system-edit-dic-container">
        <el-dialog title="批量添加设备" v-model="isShowDialog" width="769px">
            <el-form :model="ruleForm" ref="formRef" :rules="rules" size="default" label-width="110px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="设备名称前缀" prop="name">
                            <el-input v-model="ruleForm.name" placeholder="请输入设备名称前缀" clearable />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="项目名称" prop="projectId">
                            <el-select v-model="ruleForm.projectId" placeholder="请选择所属项目" filterable clearable
                                value-key="id" :disabled="ruleForm.id != ''" style="width:100%;">
                                <el-option v-for="item in projectData" :key="item.id" :label="item.name"
                                    :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="厂家" prop="manufactor">
                            <el-select v-model="ruleForm.manufactor" placeholder="请选择厂家" filterable clearable
                                style="width:100%;" @change="handleManufactorChange">
                                <el-option v-for="item in manufactorData" :key="item" :label="item"
                                    :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="型号" prop="model">
                            <el-select v-model="ruleForm.model" placeholder="请选择型号" filterable clearable
                                style="width:100%;">
                                <el-option v-for="item in modelData" :key="item" :label="item" :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="设备状态" prop="status">
                            <el-switch v-model="ruleForm.status" inline-prompt :active-value="1" :inactive-value="0"
                                active-text="在线" inactive-text="离线" :disabled="true"></el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="ruleForm.remark" type="textarea" placeholder="请输入备注"
                                maxlength="150"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" size="default">添 加</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
  
<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref, nextTick } from 'vue';
import api from '/@/api/device';
import apiProject from '/@/api/project';
import { ElMessage } from "element-plus";

interface RuleFormState {
    id: string;
    projectId: string;
    projectName: string;
    name: string;
    imei: string;
    selectVal: any[];
    manufactor: string;
    model: string;
    tac: string;
    status: number;
    remark: string;
    create_by: string;
    created_at: string;
    update_by: string;
    updated_at: string;
}
interface AllProjectList {
    id: string;
    name: string;
}

const form: RuleFormState = {
    id: '',
    projectId: '',
    projectName: '',
    name: '',
    imei: '',
    selectVal: [],
    manufactor: '',
    model: '',
    tac: '',
    status: 0,
    remark: '',
    create_by: '',
    created_at: '',
    update_by: '',
    updated_at: ''
}


interface DicState {
    projectData: any[];
    //product: any;
    isShowDialog: boolean;
    ruleForm: RuleFormState;
    rules: {}
}

export default defineComponent({
    name: 'deviceEditPro',
    //components: { tagVue, },
    setup(prop, { emit }) {
        const formRef = ref<HTMLElement | null>(null);
        const tacList = ref([]);
        const manufactorData = ref([]);
        const modelData = ref([]);
        const state = reactive<DicState>({
            isShowDialog: false,
            ruleForm: {
                ...form
            },
            projectData: [], // 项目数据
            rules: {
                name: [{ required: true, message: "设备名称前缀不能为空", trigger: "blur" }],
                projectId: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
                manufactor: [{ required: true, message: '厂家不能为空', trigger: 'blur' }],
                model: [{ required: true, message: '型号不能为空', trigger: 'blur' }],
            }
        });
        //厂家选项修改时触发
        const handleManufactorChange = (value: any) => {
            // console.log("选中厂家：", value);
            api.tac.getModelList({ manufactor: value }).then((res: any) => {
                modelData.value = res.model || []
            });
        }
        // 打开弹窗
        const openDialog = (row: RuleFormState | null) => {
            resetForm();
            api.tac.getManufactorList({}).then((res: any) => {
                manufactorData.value = res.manufactor || []
            });
            apiProject.project.getOptionList({ status: 0 }).then((res: any) => {
                state.projectData = res.project || [];
            });
            state.isShowDialog = true;
        };
        const resetForm = () => {
            state.ruleForm = {
                ...form
            }
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };
        // 新增
        const onSubmit = () => {
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.validate((valid: boolean) => {
                if (valid) {
                    //提交批量新增
                    api.instance.batchAdd(state.ruleForm).then(() => {
                        ElMessage.success('批量添加设备成功');
                        closeDialog(); // 关闭弹窗
                        emit('getInstanceList')
                    })
                    // if (state.ruleForm.id !== '') {
                    //     //修改
                    //     api.instance.edit(state.ruleForm).then(() => {
                    //         ElMessage.success('设备修改成功');
                    //         closeDialog(); // 关闭弹窗
                    //         emit('getInstanceList')
                    //     })
                    // } else {
                    //     //添加
                    //     api.instance.add(state.ruleForm).then(() => {
                    //         ElMessage.success('设备添加成功');
                    //         closeDialog(); // 关闭弹窗
                    //         emit('getInstanceList')
                    //     })
                    // }
                }
            });
        };

        return {
            //value,
            //props,
            tacList,
            manufactorData,
            modelData,
            handleManufactorChange,
            //handleChange,
            openDialog,
            closeDialog,
            onCancel,
            onSubmit,
            formRef,
            ...toRefs(state),
        };
    },
});
</script>
  
<style lang="scss" scoped>
.tags-wrapper {
    .tag {
        margin: 8px 0;
    }

    .tags {
        .el-button {
            margin-left: 10px;
        }
    }
}
</style>
  