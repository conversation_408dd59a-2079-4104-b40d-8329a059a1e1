import {
  require_codemirror
} from "./chunk-2G24OWTA.js";
import {
  __commonJS
} from "./chunk-J43GMYXM.js";

// node_modules/codemirror/addon/display/fullscreen.js
var require_fullscreen = __commonJS({
  "node_modules/codemirror/addon/display/fullscreen.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      "use strict";
      CodeMirror2.defineOption("fullScreen", false, function(cm, val, old) {
        if (old == CodeMirror2.Init)
          old = false;
        if (!old == !val)
          return;
        if (val)
          setFullscreen(cm);
        else
          setNormal(cm);
      });
      function setFullscreen(cm) {
        var wrap = cm.getWrapperElement();
        cm.state.fullScreenRestore = {
          scrollTop: window.pageYOffset,
          scrollLeft: window.pageXOffset,
          width: wrap.style.width,
          height: wrap.style.height
        };
        wrap.style.width = "";
        wrap.style.height = "auto";
        wrap.className += " CodeMirror-fullscreen";
        document.documentElement.style.overflow = "hidden";
        cm.refresh();
      }
      function setNormal(cm) {
        var wrap = cm.getWrapperElement();
        wrap.className = wrap.className.replace(/\s*CodeMirror-fullscreen\b/, "");
        document.documentElement.style.overflow = "";
        var info = cm.state.fullScreenRestore;
        wrap.style.width = info.width;
        wrap.style.height = info.height;
        window.scrollTo(info.scrollLeft, info.scrollTop);
        cm.refresh();
      }
    });
  }
});

// dep:codemirror_addon_display_fullscreen_js
var codemirror_addon_display_fullscreen_js_default = require_fullscreen();
export {
  codemirror_addon_display_fullscreen_js_default as default
};
//# sourceMappingURL=codemirror_addon_display_fullscreen_js.js.map
