{"version": 3, "sources": ["../../codemirror/addon/scroll/simplescrollbars.js", "dep:codemirror_addon_scroll_simplescrollbars_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  function Bar(cls, orientation, scroll) {\n    this.orientation = orientation;\n    this.scroll = scroll;\n    this.screen = this.total = this.size = 1;\n    this.pos = 0;\n\n    this.node = document.createElement(\"div\");\n    this.node.className = cls + \"-\" + orientation;\n    this.inner = this.node.appendChild(document.createElement(\"div\"));\n\n    var self = this;\n    CodeMirror.on(this.inner, \"mousedown\", function(e) {\n      if (e.which != 1) return;\n      CodeMirror.e_preventDefault(e);\n      var axis = self.orientation == \"horizontal\" ? \"pageX\" : \"pageY\";\n      var start = e[axis], startpos = self.pos;\n      function done() {\n        CodeMirror.off(document, \"mousemove\", move);\n        CodeMirror.off(document, \"mouseup\", done);\n      }\n      function move(e) {\n        if (e.which != 1) return done();\n        self.moveTo(startpos + (e[axis] - start) * (self.total / self.size));\n      }\n      CodeMirror.on(document, \"mousemove\", move);\n      CodeMirror.on(document, \"mouseup\", done);\n    });\n\n    CodeMirror.on(this.node, \"click\", function(e) {\n      CodeMirror.e_preventDefault(e);\n      var innerBox = self.inner.getBoundingClientRect(), where;\n      if (self.orientation == \"horizontal\")\n        where = e.clientX < innerBox.left ? -1 : e.clientX > innerBox.right ? 1 : 0;\n      else\n        where = e.clientY < innerBox.top ? -1 : e.clientY > innerBox.bottom ? 1 : 0;\n      self.moveTo(self.pos + where * self.screen);\n    });\n\n    function onWheel(e) {\n      var moved = CodeMirror.wheelEventPixels(e)[self.orientation == \"horizontal\" ? \"x\" : \"y\"];\n      var oldPos = self.pos;\n      self.moveTo(self.pos + moved);\n      if (self.pos != oldPos) CodeMirror.e_preventDefault(e);\n    }\n    CodeMirror.on(this.node, \"mousewheel\", onWheel);\n    CodeMirror.on(this.node, \"DOMMouseScroll\", onWheel);\n  }\n\n  Bar.prototype.setPos = function(pos, force) {\n    if (pos < 0) pos = 0;\n    if (pos > this.total - this.screen) pos = this.total - this.screen;\n    if (!force && pos == this.pos) return false;\n    this.pos = pos;\n    this.inner.style[this.orientation == \"horizontal\" ? \"left\" : \"top\"] =\n      (pos * (this.size / this.total)) + \"px\";\n    return true\n  };\n\n  Bar.prototype.moveTo = function(pos) {\n    if (this.setPos(pos)) this.scroll(pos, this.orientation);\n  }\n\n  var minButtonSize = 10;\n\n  Bar.prototype.update = function(scrollSize, clientSize, barSize) {\n    var sizeChanged = this.screen != clientSize || this.total != scrollSize || this.size != barSize\n    if (sizeChanged) {\n      this.screen = clientSize;\n      this.total = scrollSize;\n      this.size = barSize;\n    }\n\n    var buttonSize = this.screen * (this.size / this.total);\n    if (buttonSize < minButtonSize) {\n      this.size -= minButtonSize - buttonSize;\n      buttonSize = minButtonSize;\n    }\n    this.inner.style[this.orientation == \"horizontal\" ? \"width\" : \"height\"] =\n      buttonSize + \"px\";\n    this.setPos(this.pos, sizeChanged);\n  };\n\n  function SimpleScrollbars(cls, place, scroll) {\n    this.addClass = cls;\n    this.horiz = new Bar(cls, \"horizontal\", scroll);\n    place(this.horiz.node);\n    this.vert = new Bar(cls, \"vertical\", scroll);\n    place(this.vert.node);\n    this.width = null;\n  }\n\n  SimpleScrollbars.prototype.update = function(measure) {\n    if (this.width == null) {\n      var style = window.getComputedStyle ? window.getComputedStyle(this.horiz.node) : this.horiz.node.currentStyle;\n      if (style) this.width = parseInt(style.height);\n    }\n    var width = this.width || 0;\n\n    var needsH = measure.scrollWidth > measure.clientWidth + 1;\n    var needsV = measure.scrollHeight > measure.clientHeight + 1;\n    this.vert.node.style.display = needsV ? \"block\" : \"none\";\n    this.horiz.node.style.display = needsH ? \"block\" : \"none\";\n\n    if (needsV) {\n      this.vert.update(measure.scrollHeight, measure.clientHeight,\n                       measure.viewHeight - (needsH ? width : 0));\n      this.vert.node.style.bottom = needsH ? width + \"px\" : \"0\";\n    }\n    if (needsH) {\n      this.horiz.update(measure.scrollWidth, measure.clientWidth,\n                        measure.viewWidth - (needsV ? width : 0) - measure.barLeft);\n      this.horiz.node.style.right = needsV ? width + \"px\" : \"0\";\n      this.horiz.node.style.left = measure.barLeft + \"px\";\n    }\n\n    return {right: needsV ? width : 0, bottom: needsH ? width : 0};\n  };\n\n  SimpleScrollbars.prototype.setScrollTop = function(pos) {\n    this.vert.setPos(pos);\n  };\n\n  SimpleScrollbars.prototype.setScrollLeft = function(pos) {\n    this.horiz.setPos(pos);\n  };\n\n  SimpleScrollbars.prototype.clear = function() {\n    var parent = this.horiz.node.parentNode;\n    parent.removeChild(this.horiz.node);\n    parent.removeChild(this.vert.node);\n  };\n\n  CodeMirror.scrollbarModel.simple = function(place, scroll) {\n    return new SimpleScrollbars(\"CodeMirror-simplescroll\", place, scroll);\n  };\n  CodeMirror.scrollbarModel.overlay = function(place, scroll) {\n    return new SimpleScrollbars(\"CodeMirror-overlayscroll\", place, scroll);\n  };\n});\n", "export default require(\"./node_modules/codemirror/addon/scroll/simplescrollbars.js\");"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,eAAS,IAAI,KAAK,aAAa,QAAQ;AACrC,aAAK,cAAc;AACnB,aAAK,SAAS;AACd,aAAK,SAAS,KAAK,QAAQ,KAAK,OAAO;AACvC,aAAK,MAAM;AAEX,aAAK,OAAO,SAAS,cAAc,KAAK;AACxC,aAAK,KAAK,YAAY,MAAM,MAAM;AAClC,aAAK,QAAQ,KAAK,KAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AAEhE,YAAI,OAAO;AACX,QAAAA,YAAW,GAAG,KAAK,OAAO,aAAa,SAAS,GAAG;AACjD,cAAI,EAAE,SAAS;AAAG;AAClB,UAAAA,YAAW,iBAAiB,CAAC;AAC7B,cAAI,OAAO,KAAK,eAAe,eAAe,UAAU;AACxD,cAAI,QAAQ,EAAE,OAAO,WAAW,KAAK;AACrC,mBAAS,OAAO;AACd,YAAAA,YAAW,IAAI,UAAU,aAAa,IAAI;AAC1C,YAAAA,YAAW,IAAI,UAAU,WAAW,IAAI;AAAA,UAC1C;AACA,mBAAS,KAAKC,IAAG;AACf,gBAAIA,GAAE,SAAS;AAAG,qBAAO,KAAK;AAC9B,iBAAK,OAAO,YAAYA,GAAE,QAAQ,UAAU,KAAK,QAAQ,KAAK,KAAK;AAAA,UACrE;AACA,UAAAD,YAAW,GAAG,UAAU,aAAa,IAAI;AACzC,UAAAA,YAAW,GAAG,UAAU,WAAW,IAAI;AAAA,QACzC,CAAC;AAED,QAAAA,YAAW,GAAG,KAAK,MAAM,SAAS,SAAS,GAAG;AAC5C,UAAAA,YAAW,iBAAiB,CAAC;AAC7B,cAAI,WAAW,KAAK,MAAM,sBAAsB,GAAG;AACnD,cAAI,KAAK,eAAe;AACtB,oBAAQ,EAAE,UAAU,SAAS,OAAO,KAAK,EAAE,UAAU,SAAS,QAAQ,IAAI;AAAA;AAE1E,oBAAQ,EAAE,UAAU,SAAS,MAAM,KAAK,EAAE,UAAU,SAAS,SAAS,IAAI;AAC5E,eAAK,OAAO,KAAK,MAAM,QAAQ,KAAK,MAAM;AAAA,QAC5C,CAAC;AAED,iBAAS,QAAQ,GAAG;AAClB,cAAI,QAAQA,YAAW,iBAAiB,CAAC,EAAE,KAAK,eAAe,eAAe,MAAM;AACpF,cAAI,SAAS,KAAK;AAClB,eAAK,OAAO,KAAK,MAAM,KAAK;AAC5B,cAAI,KAAK,OAAO;AAAQ,YAAAA,YAAW,iBAAiB,CAAC;AAAA,QACvD;AACA,QAAAA,YAAW,GAAG,KAAK,MAAM,cAAc,OAAO;AAC9C,QAAAA,YAAW,GAAG,KAAK,MAAM,kBAAkB,OAAO;AAAA,MACpD;AAEA,UAAI,UAAU,SAAS,SAAS,KAAK,OAAO;AAC1C,YAAI,MAAM;AAAG,gBAAM;AACnB,YAAI,MAAM,KAAK,QAAQ,KAAK;AAAQ,gBAAM,KAAK,QAAQ,KAAK;AAC5D,YAAI,CAAC,SAAS,OAAO,KAAK;AAAK,iBAAO;AACtC,aAAK,MAAM;AACX,aAAK,MAAM,MAAM,KAAK,eAAe,eAAe,SAAS,SAC1D,OAAO,KAAK,OAAO,KAAK,SAAU;AACrC,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,SAAS,SAAS,KAAK;AACnC,YAAI,KAAK,OAAO,GAAG;AAAG,eAAK,OAAO,KAAK,KAAK,WAAW;AAAA,MACzD;AAEA,UAAI,gBAAgB;AAEpB,UAAI,UAAU,SAAS,SAAS,YAAY,YAAY,SAAS;AAC/D,YAAI,cAAc,KAAK,UAAU,cAAc,KAAK,SAAS,cAAc,KAAK,QAAQ;AACxF,YAAI,aAAa;AACf,eAAK,SAAS;AACd,eAAK,QAAQ;AACb,eAAK,OAAO;AAAA,QACd;AAEA,YAAI,aAAa,KAAK,UAAU,KAAK,OAAO,KAAK;AACjD,YAAI,aAAa,eAAe;AAC9B,eAAK,QAAQ,gBAAgB;AAC7B,uBAAa;AAAA,QACf;AACA,aAAK,MAAM,MAAM,KAAK,eAAe,eAAe,UAAU,YAC5D,aAAa;AACf,aAAK,OAAO,KAAK,KAAK,WAAW;AAAA,MACnC;AAEA,eAAS,iBAAiB,KAAK,OAAO,QAAQ;AAC5C,aAAK,WAAW;AAChB,aAAK,QAAQ,IAAI,IAAI,KAAK,cAAc,MAAM;AAC9C,cAAM,KAAK,MAAM,IAAI;AACrB,aAAK,OAAO,IAAI,IAAI,KAAK,YAAY,MAAM;AAC3C,cAAM,KAAK,KAAK,IAAI;AACpB,aAAK,QAAQ;AAAA,MACf;AAEA,uBAAiB,UAAU,SAAS,SAAS,SAAS;AACpD,YAAI,KAAK,SAAS,MAAM;AACtB,cAAI,QAAQ,OAAO,mBAAmB,OAAO,iBAAiB,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK;AACjG,cAAI;AAAO,iBAAK,QAAQ,SAAS,MAAM,MAAM;AAAA,QAC/C;AACA,YAAI,QAAQ,KAAK,SAAS;AAE1B,YAAI,SAAS,QAAQ,cAAc,QAAQ,cAAc;AACzD,YAAI,SAAS,QAAQ,eAAe,QAAQ,eAAe;AAC3D,aAAK,KAAK,KAAK,MAAM,UAAU,SAAS,UAAU;AAClD,aAAK,MAAM,KAAK,MAAM,UAAU,SAAS,UAAU;AAEnD,YAAI,QAAQ;AACV,eAAK,KAAK;AAAA,YAAO,QAAQ;AAAA,YAAc,QAAQ;AAAA,YAC9B,QAAQ,cAAc,SAAS,QAAQ;AAAA,UAAE;AAC1D,eAAK,KAAK,KAAK,MAAM,SAAS,SAAS,QAAQ,OAAO;AAAA,QACxD;AACA,YAAI,QAAQ;AACV,eAAK,MAAM;AAAA,YAAO,QAAQ;AAAA,YAAa,QAAQ;AAAA,YAC7B,QAAQ,aAAa,SAAS,QAAQ,KAAK,QAAQ;AAAA,UAAO;AAC5E,eAAK,MAAM,KAAK,MAAM,QAAQ,SAAS,QAAQ,OAAO;AACtD,eAAK,MAAM,KAAK,MAAM,OAAO,QAAQ,UAAU;AAAA,QACjD;AAEA,eAAO,EAAC,OAAO,SAAS,QAAQ,GAAG,QAAQ,SAAS,QAAQ,EAAC;AAAA,MAC/D;AAEA,uBAAiB,UAAU,eAAe,SAAS,KAAK;AACtD,aAAK,KAAK,OAAO,GAAG;AAAA,MACtB;AAEA,uBAAiB,UAAU,gBAAgB,SAAS,KAAK;AACvD,aAAK,MAAM,OAAO,GAAG;AAAA,MACvB;AAEA,uBAAiB,UAAU,QAAQ,WAAW;AAC5C,YAAI,SAAS,KAAK,MAAM,KAAK;AAC7B,eAAO,YAAY,KAAK,MAAM,IAAI;AAClC,eAAO,YAAY,KAAK,KAAK,IAAI;AAAA,MACnC;AAEA,MAAAA,YAAW,eAAe,SAAS,SAAS,OAAO,QAAQ;AACzD,eAAO,IAAI,iBAAiB,2BAA2B,OAAO,MAAM;AAAA,MACtE;AACA,MAAAA,YAAW,eAAe,UAAU,SAAS,OAAO,QAAQ;AAC1D,eAAO,IAAI,iBAAiB,4BAA4B,OAAO,MAAM;AAAA,MACvE;AAAA,IACF,CAAC;AAAA;AAAA;;;ACvJD,IAAO,sDAAQ;", "names": ["CodeMirror", "e"]}