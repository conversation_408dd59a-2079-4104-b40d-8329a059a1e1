const reg_tel = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
const reg_tac = /^\d{8}$/;
const reg_threshold = /^\d+$/;

export const phoneValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback('手机号不能为空')
  }

  if (!reg_tel.test(value)) {
    callback(new Error('请填写正确手机号'))
  }
  callback()
}

export const tacValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback('tac码不能为空')
  }

  if (!reg_tac.test(value)) {
    callback(new Error('请填写8位数字的tac码'))
  }
  callback()
}

export const thresholdValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback('设备数阈值不能为空')
  }

  if (!reg_threshold.test(value)) {
    callback(new Error('请填写数字类型的阈值'))
  }
  
  if (value <= 0) {
    callback(new Error('阈值不能小于1'))
  }
  callback()
}


export const ruleRequired = (message = '不能为空', trigger = 'blur') => {
  return { required: true, message, trigger }
}