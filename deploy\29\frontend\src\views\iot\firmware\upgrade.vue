<template>
    <div class="system-dept-container">
        <el-card shadow="hover">
            <div class="system-dept-search mb15">
                <el-form :inline="true">
                    <el-form-item label="固件名">
                        <el-select v-model="tableData.param.firmwareId" size="mini" placeholder="请选择固件" filterable clearable>
                            <el-option v-for="item in firmwareData" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="项目名称">
                        <el-select v-model="tableData.param.projectId" size="mini" placeholder="请选择项目" filterable clearable>
                            <el-option v-for="item in projectData" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getUpgradeList">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="tableData.data" style="width: 100%" row-key="id" default-expand-all v-loading="tableData.loading">
                <el-table-column label="序号" width="60" align="center" type="index"></el-table-column>
                <el-table-column prop="firmwareName" label="固件名" min-width="180" show-overflow-tooltip></el-table-column>
                <el-table-column prop="version" label="版本型号" align="center"></el-table-column>
                <el-table-column prop="projectName" label="项目名" align="center" min-width="180"></el-table-column>
                <el-table-column prop="upgradeNum" label="升级任务数" align="center" min-width="120"></el-table-column>
                <el-table-column prop="successNum" label="成功升级数" align="center" min-width="120"></el-table-column>
                <el-table-column prop="failNum" label="失败升级数" align="center" min-width="120"></el-table-column>
                <el-table-column prop="releaseAt" label="升级起始时间" align="center" min-width="160"></el-table-column>
                <el-table-column prop="finishAt" label="升级完成时间" align="center" min-width="180">
                    <template #default="scope">
                        <el-tag type="success" size="small" v-if="scope.row.finishAt">{{ scope.row.finishAt }}</el-tag>
                        <el-tag type="warning" size="small" v-else>正在更新</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="120" fixed="right">
                    <template #default="scope">
                        <el-button size="small" text type="danger" @click="onOpenDetail(scope.row)">查看失败设备</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="getUpgradeList" />
        </el-card>
        <UpgradeFailList ref="failListRef" @getUpgradeList="getUpgradeList"/>
    </div>
</template>

<script lang="ts">
import { ref, toRefs, reactive, onMounted, defineComponent, h } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import UpgradeFailList from './component/failList.vue';
import api from '/@/api/firmware';
import projectApi from '/@/api/project';

// 定义接口来定义对象的类型
interface TableDataRow {
    id: string;
    firmwareName: string;
    version: string;
    projectId: string;
    projectName: string;
    upgradeNum: number;
    successNum: number;
    failNum: number;
    finishAt: string;
    create_by: string;
    createTime: string;
    update_by: string;
    updated_at: string;
}
interface AllProjectList {
    id: string;
    name: string;
}
interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            firmwareId: string;
            projectId: string;
        };
    };
    projectData: AllProjectList[];
    firmwareData: AllProjectList[];
}


export default defineComponent({
    name: 'upgrade',
    components: { UpgradeFailList },
    setup() {
        const failListRef = ref();
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                total: 0,
                loading: false,
                param: {
                    pageNum: 1,
                    pageSize: 10,
                    firmwareId: '',
                    projectId: ''
                },
            },
            //项目数据
            projectData: [],
            firmwareData: []
        });
        // 初始化表格数据
        const initTableData = () => {
            getUpgradeList();
            getFirmwareData();
            getProjectData();
        };
        const getUpgradeList = () => {
            state.tableData.loading = true;
            api.firmware.getUpgradeList(state.tableData.param).then((res: any) => {
                state.tableData.data = res.firmwareLog;
                state.tableData.total = res.total;
            }).finally(() => (state.tableData.loading = false));
        };
        const getProjectData = () => {
            projectApi.project.getOptionList({}).then((res: any) => {
				state.projectData = res.project || [];
			});
        };
        const getFirmwareData = () => {
            api.firmware.getOptionList({}).then((res: any) => {
				state.firmwareData = res.firmware || [];
			});
        }
        const onOpenDetail = (row: TableDataRow) => {
            failListRef.value.openDialog({ ...row });
        };
        // 页面加载时
        onMounted(() => {
            initTableData();
        });
        return {
            failListRef,
            getUpgradeList,
            onOpenDetail,
            ...toRefs(state),
        };
    },
});
</script>
