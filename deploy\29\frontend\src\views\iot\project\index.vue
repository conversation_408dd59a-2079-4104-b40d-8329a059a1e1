<template>
    <div class="system-dept-container">
        <el-card shadow="hover">
            <div class="system-dept-search mb15">
                <el-form :inline="true">
                    <el-form-item label="项目名">
                        <el-input size="default" v-model="tableData.param.name" placeholder="请输入项目名称" class="w-50"
                            clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getProjectList">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" type="default" class="ml10" @click="onOpenAdd" v-auth="'add'">
                            <el-icon>
                                <ele-FolderAdd />
                            </el-icon>
                            新增项目
                        </el-button>
                        <el-button size="default" type="success" plain class="ml10" @click="onDownTemplate" v-auth="'template'">
                            下载模板
                        </el-button>
                        <el-button size="default" type="success" class="ml10" @click="onRowUpload" v-auth="'upload'">
							<el-icon>
								<ele-Upload />
							</el-icon>
							导入项目
						</el-button>
                        <el-button size="default" type="primary" class="ml10" @click="onRowExport" v-auth="'download'">
                            <el-icon>
                                <ele-Download />
                            </el-icon>
                            导出项目
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="tableData.data" style="width: 100%" row-key="id" default-expand-all v-loading="tableData.loading">
                <el-table-column label="序号" width="60" align="center" type="index"></el-table-column>
                <el-table-column prop="name" label="项目名称" min-width="180" show-overflow-tooltip></el-table-column>
                <el-table-column prop="desc" label="项目简介" align="center" min-width="180"></el-table-column>
                <el-table-column prop="startDate" label="起始时间" align="center" min-width="120" :formatter="formatStartDate"></el-table-column>
                <el-table-column prop="endDate" label="结束时间" align="center" min-width="120" :formatter="formatEndDate"></el-table-column>
                <el-table-column prop="threshold" label="设备数上限" align="center" min-width="100"></el-table-column>
                <el-table-column prop="status" label="项目情况" align="center">
                    <template #default="scope">
                        <img v-if="scope.row.status == 0" src="/imgs/ontime.png" alt="正常" style="width: 50%;height: 50%">
                        <img v-else src="/imgs/offtime.png" alt="到期" style="width: 50%;height: 50%">

                    </template>
                </el-table-column>
                <el-table-column prop="company" label="上游公司" align="center" min-width="180"></el-table-column>
                <el-table-column prop="remark" label="备注" align="center" min-width="180"></el-table-column>
                <el-table-column label="操作" align="center" width="220" fixed="right">
                    <template #default="scope">
                        <el-button size="small" text type="warning" @click="onOpenEdit(scope.row)" v-auth="'edit'">修改</el-button>
                        <el-button size="small" text type="success" @click="onOpenInstance(scope.row)" v-auth="'show'">查看设备</el-button>
                        <el-button size="small" text type="primary" @click="onOpenTransfer(scope.row)" v-auth="'transfer'">转移设备</el-button>
                        <el-button size="small" text type="danger" @click="onTabelRowDel(scope.row)" v-auth="'del'">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="getProjectList" />
        </el-card>
        <EditProject ref="editDeptRef" @getProjectList="getProjectList" />
        <TransferInstance ref="transferProjectRef" @getProjectList="getProjectList" />
        <InstanceList ref="instanceListRef"/>
        <ErrorList ref="errorListRef" />
    </div>
</template>

<script lang="ts">
import { ref, toRefs, reactive, onMounted, defineComponent, h } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditProject from './component/edit.vue';
import TransferInstance from './component/transfer.vue';
import InstanceList from './component/list.vue';
import ErrorList from '/@/views/error/list.vue';
import api from '/@/api/project';
import downloadFile from '/@/utils/download';

let uploadFile: File | null = null

// 定义接口来定义对象的类型
interface TableDataRow {
    id: string;
    name: string;
    desc: string;
    startDate: string;
    endDate: string;
    status: string;
    company: string;
    remark: string;
    create_by: string;
    created_at: string;
    update_by: string;
    updated_at: string;
}
interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            name: string;
            status: string;
        };
    };
}
interface DataTransfer {
    id: string;
    name: string;
    targetId: string;
    targetName: string;
}
interface DataProject {
    projectId: string;
    projectName: string;
}


export default defineComponent({
    name: 'project',
    components: { EditProject, TransferInstance, InstanceList, ErrorList },
    setup() {
        const editDeptRef = ref();
        const transferProjectRef = ref();
        const instanceListRef = ref();
        const errorListRef = ref();
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                total: 0,
                loading: false,
                param: {
                    pageNum: 1,
                    pageSize: 10,
                    name: '',
                    status: '',
                },
            },
        });
        const dataTransfer = reactive<DataTransfer>({
            id: '',
            name: '',
            targetId: '',
            targetName: ''
        });
        const dataProject = reactive<DataProject>({
            projectId: '',
            projectName: ''
        });
        // 初始化表格数据
        const initTableData = () => {
            getProjectList();
        };
        const getProjectList = () => {
            state.tableData.loading = true;
            api.project.getList(state.tableData.param).then((res: any) => {
                state.tableData.data = res.project;
                state.tableData.total = res.total;
            }).finally(() => (state.tableData.loading = false));
        };
        // 打开新增项目弹窗
        const onOpenAdd = (row?: TableDataRow) => {
            editDeptRef.value.openDialog(row?.id);
        };
        // 打开编辑项目弹窗
        const onOpenEdit = (row: TableDataRow) => {
            editDeptRef.value.openDialog({ ...row });
        };
        // 删除当前行
        const onTabelRowDel = (row: TableDataRow) => {
            ElMessageBox.confirm(`此操作将永久删除项目：${row.name}, 是否继续?`, '提示', {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.project.del(row.id).then(() => {
                    ElMessage.success('删除成功');
                    getProjectList();
                });
            }).catch(() => {

            });
        };
        //转移项目设备
        const onOpenTransfer = (row: TableDataRow) => {
            dataTransfer.id = row.id;
            dataTransfer.name = row.name;
            transferProjectRef.value.openDialog(dataTransfer);
        };
        //查看项目设备列表
        const onOpenInstance = (row: TableDataRow) => {
            dataProject.projectId = row.id;
            dataProject.projectName = row.name;
            instanceListRef.value.openDialog(dataProject);
        };
        //导入项目
        const onRowUpload = () => {
            ElMessageBox({
			    title: '上传项目信息',
			    message: h('input', {
				    type: 'file',
				    accept: '.xls, .xlsx',
				    onchange: (file: any) => {
					    uploadFile = file.target.files[0]
				    },
			    }),
			    showCancelButton: true,
			    confirmButtonText: '上传',
			    cancelButtonText: '取消',
			    beforeClose: (action, instance, done) => {
				    if (action === 'confirm') {
					    if (!uploadFile) return ElMessage('请先上传文件！')

					    instance.confirmButtonLoading = true
					    instance.confirmButtonText = '上传中...'

					    const formData = new FormData()
					    formData.append('file', uploadFile)

					    api.project
						    .upload(formData)
						    .then((res: any) => {
							    ElMessage.success('上传成功')
                                state.tableData.param.pageNum = 1;
							    getProjectList();
                                errorListRef.value.openDialog(res.errorList);
							    done()
						    })
						    .finally(() => {
							    instance.confirmButtonLoading = false
						    })
				    } else {
					    done()
				    }
				    uploadFile = null
			    },
		    })
        };


        const formatStartDate = (row: any) => {
            const startDate = row.startDate;
            return startDate.substring(0, 10); // 只显示前10位字符，即年-月-日部分
        };

        const formatEndDate = (row: any) => {
            const endDate = row.endDate;
            return endDate.substring(0, 10); // 只显示前10位字符，即年-月-日部分
        };

        // 导出项目
        const onRowExport = () => {
            api.project.export(state.tableData.param).then((res: any) => downloadFile(res, "导出项目信息.xlsx"))
        };
        //下载模板
        const onDownTemplate = () => {
            const fileUrl = './template/项目模板.xlsx'; // 文件的URL或路径
            window.open(fileUrl, '_blank');
        };
        // 页面加载时
        onMounted(() => {
            initTableData();
        });
        return {
            editDeptRef,
            transferProjectRef,
            instanceListRef,
            errorListRef,
            getProjectList,
            onOpenAdd,
            onOpenEdit,
            onTabelRowDel,
            onOpenTransfer,
            onOpenInstance,
            onRowUpload,
            onRowExport,
            onDownTemplate,
            formatStartDate,
            formatEndDate,
            ...toRefs(state),
        };
    },
});
</script>
