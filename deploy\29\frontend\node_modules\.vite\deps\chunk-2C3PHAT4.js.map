{"version": 3, "sources": ["../../codemirror/addon/fold/xml-fold.js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var Pos = CodeMirror.Pos;\n  function cmp(a, b) { return a.line - b.line || a.ch - b.ch; }\n\n  var nameStartChar = \"A-Z_a-z\\\\u00C0-\\\\u00D6\\\\u00D8-\\\\u00F6\\\\u00F8-\\\\u02FF\\\\u0370-\\\\u037D\\\\u037F-\\\\u1FFF\\\\u200C-\\\\u200D\\\\u2070-\\\\u218F\\\\u2C00-\\\\u2FEF\\\\u3001-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFFD\";\n  var nameChar = nameStartChar + \"\\-\\:\\.0-9\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040\";\n  var xmlTagStart = new RegExp(\"<(/?)([\" + nameStartChar + \"][\" + nameChar + \"]*)\", \"g\");\n\n  function Iter(cm, line, ch, range) {\n    this.line = line; this.ch = ch;\n    this.cm = cm; this.text = cm.getLine(line);\n    this.min = range ? Math.max(range.from, cm.firstLine()) : cm.firstLine();\n    this.max = range ? Math.min(range.to - 1, cm.lastLine()) : cm.lastLine();\n  }\n\n  function tagAt(iter, ch) {\n    var type = iter.cm.getTokenTypeAt(Pos(iter.line, ch));\n    return type && /\\btag\\b/.test(type);\n  }\n\n  function nextLine(iter) {\n    if (iter.line >= iter.max) return;\n    iter.ch = 0;\n    iter.text = iter.cm.getLine(++iter.line);\n    return true;\n  }\n  function prevLine(iter) {\n    if (iter.line <= iter.min) return;\n    iter.text = iter.cm.getLine(--iter.line);\n    iter.ch = iter.text.length;\n    return true;\n  }\n\n  function toTagEnd(iter) {\n    for (;;) {\n      var gt = iter.text.indexOf(\">\", iter.ch);\n      if (gt == -1) { if (nextLine(iter)) continue; else return; }\n      if (!tagAt(iter, gt + 1)) { iter.ch = gt + 1; continue; }\n      var lastSlash = iter.text.lastIndexOf(\"/\", gt);\n      var selfClose = lastSlash > -1 && !/\\S/.test(iter.text.slice(lastSlash + 1, gt));\n      iter.ch = gt + 1;\n      return selfClose ? \"selfClose\" : \"regular\";\n    }\n  }\n  function toTagStart(iter) {\n    for (;;) {\n      var lt = iter.ch ? iter.text.lastIndexOf(\"<\", iter.ch - 1) : -1;\n      if (lt == -1) { if (prevLine(iter)) continue; else return; }\n      if (!tagAt(iter, lt + 1)) { iter.ch = lt; continue; }\n      xmlTagStart.lastIndex = lt;\n      iter.ch = lt;\n      var match = xmlTagStart.exec(iter.text);\n      if (match && match.index == lt) return match;\n    }\n  }\n\n  function toNextTag(iter) {\n    for (;;) {\n      xmlTagStart.lastIndex = iter.ch;\n      var found = xmlTagStart.exec(iter.text);\n      if (!found) { if (nextLine(iter)) continue; else return; }\n      if (!tagAt(iter, found.index + 1)) { iter.ch = found.index + 1; continue; }\n      iter.ch = found.index + found[0].length;\n      return found;\n    }\n  }\n  function toPrevTag(iter) {\n    for (;;) {\n      var gt = iter.ch ? iter.text.lastIndexOf(\">\", iter.ch - 1) : -1;\n      if (gt == -1) { if (prevLine(iter)) continue; else return; }\n      if (!tagAt(iter, gt + 1)) { iter.ch = gt; continue; }\n      var lastSlash = iter.text.lastIndexOf(\"/\", gt);\n      var selfClose = lastSlash > -1 && !/\\S/.test(iter.text.slice(lastSlash + 1, gt));\n      iter.ch = gt + 1;\n      return selfClose ? \"selfClose\" : \"regular\";\n    }\n  }\n\n  function findMatchingClose(iter, tag) {\n    var stack = [];\n    for (;;) {\n      var next = toNextTag(iter), end, startLine = iter.line, startCh = iter.ch - (next ? next[0].length : 0);\n      if (!next || !(end = toTagEnd(iter))) return;\n      if (end == \"selfClose\") continue;\n      if (next[1]) { // closing tag\n        for (var i = stack.length - 1; i >= 0; --i) if (stack[i] == next[2]) {\n          stack.length = i;\n          break;\n        }\n        if (i < 0 && (!tag || tag == next[2])) return {\n          tag: next[2],\n          from: Pos(startLine, startCh),\n          to: Pos(iter.line, iter.ch)\n        };\n      } else { // opening tag\n        stack.push(next[2]);\n      }\n    }\n  }\n  function findMatchingOpen(iter, tag) {\n    var stack = [];\n    for (;;) {\n      var prev = toPrevTag(iter);\n      if (!prev) return;\n      if (prev == \"selfClose\") { toTagStart(iter); continue; }\n      var endLine = iter.line, endCh = iter.ch;\n      var start = toTagStart(iter);\n      if (!start) return;\n      if (start[1]) { // closing tag\n        stack.push(start[2]);\n      } else { // opening tag\n        for (var i = stack.length - 1; i >= 0; --i) if (stack[i] == start[2]) {\n          stack.length = i;\n          break;\n        }\n        if (i < 0 && (!tag || tag == start[2])) return {\n          tag: start[2],\n          from: Pos(iter.line, iter.ch),\n          to: Pos(endLine, endCh)\n        };\n      }\n    }\n  }\n\n  CodeMirror.registerHelper(\"fold\", \"xml\", function(cm, start) {\n    var iter = new Iter(cm, start.line, 0);\n    for (;;) {\n      var openTag = toNextTag(iter)\n      if (!openTag || iter.line != start.line) return\n      var end = toTagEnd(iter)\n      if (!end) return\n      if (!openTag[1] && end != \"selfClose\") {\n        var startPos = Pos(iter.line, iter.ch);\n        var endPos = findMatchingClose(iter, openTag[2]);\n        return endPos && cmp(endPos.from, startPos) > 0 ? {from: startPos, to: endPos.from} : null\n      }\n    }\n  });\n  CodeMirror.findMatchingTag = function(cm, pos, range) {\n    var iter = new Iter(cm, pos.line, pos.ch, range);\n    if (iter.text.indexOf(\">\") == -1 && iter.text.indexOf(\"<\") == -1) return;\n    var end = toTagEnd(iter), to = end && Pos(iter.line, iter.ch);\n    var start = end && toTagStart(iter);\n    if (!end || !start || cmp(iter, pos) > 0) return;\n    var here = {from: Pos(iter.line, iter.ch), to: to, tag: start[2]};\n    if (end == \"selfClose\") return {open: here, close: null, at: \"open\"};\n\n    if (start[1]) { // closing tag\n      return {open: findMatchingOpen(iter, start[2]), close: here, at: \"close\"};\n    } else { // opening tag\n      iter = new Iter(cm, to.line, to.ch, range);\n      return {open: here, close: findMatchingClose(iter, start[2]), at: \"open\"};\n    }\n  };\n\n  CodeMirror.findEnclosingTag = function(cm, pos, range, tag) {\n    var iter = new Iter(cm, pos.line, pos.ch, range);\n    for (;;) {\n      var open = findMatchingOpen(iter, tag);\n      if (!open) break;\n      var forward = new Iter(cm, pos.line, pos.ch, range);\n      var close = findMatchingClose(forward, open.tag);\n      if (close) return {open: open, close: close};\n    }\n  };\n\n  // Used by addon/edit/closetag.js\n  CodeMirror.scanForClosingTag = function(cm, pos, name, end) {\n    var iter = new Iter(cm, pos.line, pos.ch, end ? {from: 0, to: end} : null);\n    return findMatchingClose(iter, name);\n  };\n});\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,oBAA+B;AAAA,eAC5B,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,sBAAsB,GAAG,GAAG;AAAA;AAEpC,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,UAAI,MAAMA,YAAW;AACrB,eAAS,IAAI,GAAG,GAAG;AAAE,eAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE;AAAA,MAAI;AAE5D,UAAI,gBAAgB;AACpB,UAAI,WAAW,gBAAgB;AAC/B,UAAI,cAAc,IAAI,OAAO,YAAY,gBAAgB,OAAO,WAAW,OAAO,GAAG;AAErF,eAAS,KAAK,IAAI,MAAM,IAAI,OAAO;AACjC,aAAK,OAAO;AAAM,aAAK,KAAK;AAC5B,aAAK,KAAK;AAAI,aAAK,OAAO,GAAG,QAAQ,IAAI;AACzC,aAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU;AACvE,aAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,KAAK,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG,SAAS;AAAA,MACzE;AAEA,eAAS,MAAM,MAAM,IAAI;AACvB,YAAI,OAAO,KAAK,GAAG,eAAe,IAAI,KAAK,MAAM,EAAE,CAAC;AACpD,eAAO,QAAQ,UAAU,KAAK,IAAI;AAAA,MACpC;AAEA,eAAS,SAAS,MAAM;AACtB,YAAI,KAAK,QAAQ,KAAK;AAAK;AAC3B,aAAK,KAAK;AACV,aAAK,OAAO,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI;AACvC,eAAO;AAAA,MACT;AACA,eAAS,SAAS,MAAM;AACtB,YAAI,KAAK,QAAQ,KAAK;AAAK;AAC3B,aAAK,OAAO,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI;AACvC,aAAK,KAAK,KAAK,KAAK;AACpB,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,MAAM;AACtB,mBAAS;AACP,cAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,KAAK,EAAE;AACvC,cAAI,MAAM,IAAI;AAAE,gBAAI,SAAS,IAAI;AAAG;AAAA;AAAe;AAAA,UAAQ;AAC3D,cAAI,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AAAE,iBAAK,KAAK,KAAK;AAAG;AAAA,UAAU;AACxD,cAAI,YAAY,KAAK,KAAK,YAAY,KAAK,EAAE;AAC7C,cAAI,YAAY,YAAY,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,YAAY,GAAG,EAAE,CAAC;AAC/E,eAAK,KAAK,KAAK;AACf,iBAAO,YAAY,cAAc;AAAA,QACnC;AAAA,MACF;AACA,eAAS,WAAW,MAAM;AACxB,mBAAS;AACP,cAAI,KAAK,KAAK,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,CAAC,IAAI;AAC7D,cAAI,MAAM,IAAI;AAAE,gBAAI,SAAS,IAAI;AAAG;AAAA;AAAe;AAAA,UAAQ;AAC3D,cAAI,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AAAE,iBAAK,KAAK;AAAI;AAAA,UAAU;AACpD,sBAAY,YAAY;AACxB,eAAK,KAAK;AACV,cAAI,QAAQ,YAAY,KAAK,KAAK,IAAI;AACtC,cAAI,SAAS,MAAM,SAAS;AAAI,mBAAO;AAAA,QACzC;AAAA,MACF;AAEA,eAAS,UAAU,MAAM;AACvB,mBAAS;AACP,sBAAY,YAAY,KAAK;AAC7B,cAAI,QAAQ,YAAY,KAAK,KAAK,IAAI;AACtC,cAAI,CAAC,OAAO;AAAE,gBAAI,SAAS,IAAI;AAAG;AAAA;AAAe;AAAA,UAAQ;AACzD,cAAI,CAAC,MAAM,MAAM,MAAM,QAAQ,CAAC,GAAG;AAAE,iBAAK,KAAK,MAAM,QAAQ;AAAG;AAAA,UAAU;AAC1E,eAAK,KAAK,MAAM,QAAQ,MAAM,GAAG;AACjC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,UAAU,MAAM;AACvB,mBAAS;AACP,cAAI,KAAK,KAAK,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,CAAC,IAAI;AAC7D,cAAI,MAAM,IAAI;AAAE,gBAAI,SAAS,IAAI;AAAG;AAAA;AAAe;AAAA,UAAQ;AAC3D,cAAI,CAAC,MAAM,MAAM,KAAK,CAAC,GAAG;AAAE,iBAAK,KAAK;AAAI;AAAA,UAAU;AACpD,cAAI,YAAY,KAAK,KAAK,YAAY,KAAK,EAAE;AAC7C,cAAI,YAAY,YAAY,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,MAAM,YAAY,GAAG,EAAE,CAAC;AAC/E,eAAK,KAAK,KAAK;AACf,iBAAO,YAAY,cAAc;AAAA,QACnC;AAAA,MACF;AAEA,eAAS,kBAAkB,MAAM,KAAK;AACpC,YAAI,QAAQ,CAAC;AACb,mBAAS;AACP,cAAI,OAAO,UAAU,IAAI,GAAG,KAAK,YAAY,KAAK,MAAM,UAAU,KAAK,MAAM,OAAO,KAAK,GAAG,SAAS;AACrG,cAAI,CAAC,QAAQ,EAAE,MAAM,SAAS,IAAI;AAAI;AACtC,cAAI,OAAO;AAAa;AACxB,cAAI,KAAK,IAAI;AACX,qBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE;AAAG,kBAAI,MAAM,MAAM,KAAK,IAAI;AACnE,sBAAM,SAAS;AACf;AAAA,cACF;AACA,gBAAI,IAAI,MAAM,CAAC,OAAO,OAAO,KAAK;AAAK,qBAAO;AAAA,gBAC5C,KAAK,KAAK;AAAA,gBACV,MAAM,IAAI,WAAW,OAAO;AAAA,gBAC5B,IAAI,IAAI,KAAK,MAAM,KAAK,EAAE;AAAA,cAC5B;AAAA,UACF,OAAO;AACL,kBAAM,KAAK,KAAK,EAAE;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AACA,eAAS,iBAAiB,MAAM,KAAK;AACnC,YAAI,QAAQ,CAAC;AACb,mBAAS;AACP,cAAI,OAAO,UAAU,IAAI;AACzB,cAAI,CAAC;AAAM;AACX,cAAI,QAAQ,aAAa;AAAE,uBAAW,IAAI;AAAG;AAAA,UAAU;AACvD,cAAI,UAAU,KAAK,MAAM,QAAQ,KAAK;AACtC,cAAI,QAAQ,WAAW,IAAI;AAC3B,cAAI,CAAC;AAAO;AACZ,cAAI,MAAM,IAAI;AACZ,kBAAM,KAAK,MAAM,EAAE;AAAA,UACrB,OAAO;AACL,qBAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,EAAE;AAAG,kBAAI,MAAM,MAAM,MAAM,IAAI;AACpE,sBAAM,SAAS;AACf;AAAA,cACF;AACA,gBAAI,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;AAAK,qBAAO;AAAA,gBAC7C,KAAK,MAAM;AAAA,gBACX,MAAM,IAAI,KAAK,MAAM,KAAK,EAAE;AAAA,gBAC5B,IAAI,IAAI,SAAS,KAAK;AAAA,cACxB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,MAAAA,YAAW,eAAe,QAAQ,OAAO,SAAS,IAAI,OAAO;AAC3D,YAAI,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC;AACrC,mBAAS;AACP,cAAI,UAAU,UAAU,IAAI;AAC5B,cAAI,CAAC,WAAW,KAAK,QAAQ,MAAM;AAAM;AACzC,cAAI,MAAM,SAAS,IAAI;AACvB,cAAI,CAAC;AAAK;AACV,cAAI,CAAC,QAAQ,MAAM,OAAO,aAAa;AACrC,gBAAI,WAAW,IAAI,KAAK,MAAM,KAAK,EAAE;AACrC,gBAAI,SAAS,kBAAkB,MAAM,QAAQ,EAAE;AAC/C,mBAAO,UAAU,IAAI,OAAO,MAAM,QAAQ,IAAI,IAAI,EAAC,MAAM,UAAU,IAAI,OAAO,KAAI,IAAI;AAAA,UACxF;AAAA,QACF;AAAA,MACF,CAAC;AACD,MAAAA,YAAW,kBAAkB,SAAS,IAAI,KAAK,OAAO;AACpD,YAAI,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK;AAC/C,YAAI,KAAK,KAAK,QAAQ,GAAG,KAAK,MAAM,KAAK,KAAK,QAAQ,GAAG,KAAK;AAAI;AAClE,YAAI,MAAM,SAAS,IAAI,GAAG,KAAK,OAAO,IAAI,KAAK,MAAM,KAAK,EAAE;AAC5D,YAAI,QAAQ,OAAO,WAAW,IAAI;AAClC,YAAI,CAAC,OAAO,CAAC,SAAS,IAAI,MAAM,GAAG,IAAI;AAAG;AAC1C,YAAI,OAAO,EAAC,MAAM,IAAI,KAAK,MAAM,KAAK,EAAE,GAAG,IAAQ,KAAK,MAAM,GAAE;AAChE,YAAI,OAAO;AAAa,iBAAO,EAAC,MAAM,MAAM,OAAO,MAAM,IAAI,OAAM;AAEnE,YAAI,MAAM,IAAI;AACZ,iBAAO,EAAC,MAAM,iBAAiB,MAAM,MAAM,EAAE,GAAG,OAAO,MAAM,IAAI,QAAO;AAAA,QAC1E,OAAO;AACL,iBAAO,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,KAAK;AACzC,iBAAO,EAAC,MAAM,MAAM,OAAO,kBAAkB,MAAM,MAAM,EAAE,GAAG,IAAI,OAAM;AAAA,QAC1E;AAAA,MACF;AAEA,MAAAA,YAAW,mBAAmB,SAAS,IAAI,KAAK,OAAO,KAAK;AAC1D,YAAI,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK;AAC/C,mBAAS;AACP,cAAI,OAAO,iBAAiB,MAAM,GAAG;AACrC,cAAI,CAAC;AAAM;AACX,cAAI,UAAU,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK;AAClD,cAAI,QAAQ,kBAAkB,SAAS,KAAK,GAAG;AAC/C,cAAI;AAAO,mBAAO,EAAC,MAAY,MAAY;AAAA,QAC7C;AAAA,MACF;AAGA,MAAAA,YAAW,oBAAoB,SAAS,IAAI,KAAK,MAAM,KAAK;AAC1D,YAAI,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,EAAC,MAAM,GAAG,IAAI,IAAG,IAAI,IAAI;AACzE,eAAO,kBAAkB,MAAM,IAAI;AAAA,MACrC;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["CodeMirror"]}