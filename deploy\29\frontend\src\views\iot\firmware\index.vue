<template>
    <div class="system-dept-container">
        <el-card shadow="hover">
            <div class="system-dept-search mb15">
                <el-form :inline="true">
                    <el-form-item label="固件名">
                        <el-input size="default" v-model="tableData.param.name" placeholder="请输入固件名" class="w-50" clearable />
                    </el-form-item>
                    <el-form-item label="项目名称">
                        <el-select v-model="tableData.param.projectId" size="mini" placeholder="请选择项目" filterable clearable>
                            <el-option v-for="item in projectData" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getFirmwareList">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" type="default" class="ml10" @click="onOpenAdd" v-auth="'add'">
                            <el-icon>
                                <ele-FolderAdd />
                            </el-icon>
                            新增固件
                        </el-button>
                        <!-- <el-button size="default" type="success" class="ml10" @click="onRowUpload" v-auth="'upload'">
							<el-icon>
								<ele-Upload />
							</el-icon>
							导入项目
						</el-button>
                        <el-button size="default" type="primary" class="ml10" @click="onRowExport" v-auth="'download'">
                            <el-icon>
                                <ele-Download />
                            </el-icon>
                            导出项目
                        </el-button> -->
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="tableData.data" style="width: 100%" row-key="id" default-expand-all v-loading="tableData.loading">
                <el-table-column label="序号" width="60" align="center" type="index"></el-table-column>
                <el-table-column prop="name" label="固件名" min-width="180" show-overflow-tooltip></el-table-column>
                <el-table-column prop="version" label="固件版本号" align="center"></el-table-column>
<!--                <el-table-column prop="projectName" label="项目名称" align="center" min-width="180"></el-table-column>-->
                <el-table-column prop="status" label="发布情况" align="center" >
                    <template #default="scope">
                        <el-tag type="info" size="small" v-if="scope.row.status == 0">未发布</el-tag>
                        <el-tag type="success" size="small" v-else>已发布</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="releaseAt" label="发布时间" align="center" min-width="140"></el-table-column>
                <el-table-column prop="remark" label="备注" align="center" min-width="180"></el-table-column>
                <el-table-column label="操作" align="center" min-width="160" fixed="right">
                    <template #default="scope">
                        <el-button size="small" text type="primary" @click="onOpenDownload(scope.row)" v-if="scope.row.fileAddress">下载制定文件</el-button>
                        <el-button size="small" text type="warning" @click="onOpenEdit(scope.row)" v-auth="'edit'">修改</el-button>
                        <el-button size="small" text type="primary" @click="onTabelRowRelease(scope.row)" v-if="scope.row.status == 0" v-auth="'release'">发布</el-button>
                        <el-button size="small" text type="danger" @click="onTabelRowDel(scope.row)" v-auth="'del'">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="getFirmwareList" />
        </el-card>
        <EditFirmware ref="editDeptRef" @getFirmwareList="getFirmwareList" />
    </div>
</template>

<script lang="ts">
import { ref, toRefs, reactive, onMounted, defineComponent, h } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import EditFirmware from './component/edit.vue';
import api from '/@/api/firmware';
import apiProject from '/@/api/project';

// 定义接口来定义对象的类型
interface TableDataRow {
    id: string;
    name: string;
    version: string;
    projectId: string;
    projectName: string;
    fileAddress: string;
    remark: string;
    status: string;
    create_by: string;
    createTime: string;
    update_by: string;
    updated_at: string;
}
interface AllProjectList {
    id: string;
    name: string;
}
interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
            name: string;
            projectId: string;
            status: string;
        };
    };
    projectData: AllProjectList[];
}


export default defineComponent({
    name: 'firmware',
    components: { EditFirmware },
    setup() {
        const editDeptRef = ref();
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                total: 0,
                loading: false,
                param: {
                    pageNum: 1,
                    pageSize: 10,
                    name: '',
                    projectId: '',
                    status: ''
                },
            },
            //项目数据
            projectData: []
        });
        // 初始化表格数据
        const initTableData = () => {
            getFirmwareList();
            getProjectData();
        };
        const getFirmwareList = () => {
            state.tableData.loading = true;
            api.firmware.getList(state.tableData.param).then((res: any) => {
                state.tableData.data = res.firmware;
                state.tableData.total = res.total;
            }).finally(() => (state.tableData.loading = false));
        };
        const getProjectData = () => {
            apiProject.project.getOptionList({ status: 0 }).then((res: any) => {
				state.projectData = res.project || [];
			});
        };
        // 打开新增固件弹窗
        const onOpenAdd = (row?: TableDataRow) => {
            editDeptRef.value.openDialog(row?.id);
        };
        // 打开编辑固件弹窗
        const onOpenEdit = (row: TableDataRow) => {
            editDeptRef.value.openDialog({ ...row });
        };
        // 删除当前行
        const onTabelRowDel = (row: TableDataRow) => {
            ElMessageBox.confirm(`此操作将永久删除固件：${row.name}, 是否继续?`, '提示', {
                confirmButtonText: '删除',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.firmware.del(row.id).then(() => {
                    ElMessage.success('删除成功');
                    getFirmwareList();
                });
            }).catch(() => {
                //ElMessage.success('删除失败');
            });
        };
        //发布
        const onTabelRowRelease = (row: TableDataRow) => {
            ElMessageBox.confirm(`此操作将发布固件：${row.name}, 是否继续?`, '提示', {
                confirmButtonText: '发布',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                api.firmware.release(row.id).then(() => {
                    ElMessage.success('发布成功');
                    getFirmwareList();
                });
            }).catch(() => {
                //ElMessage.success('发布失败');
            });
        };
        const onOpenDownload = (row: TableDataRow) => {
            window.open(row.fileAddress, '_blank');
        };
        // 页面加载时
        onMounted(() => {
            initTableData();
        });
        return {
            editDeptRef,
            getFirmwareList,
            getProjectData,
            onOpenAdd,
            onOpenEdit,
            onTabelRowDel,
            onTabelRowRelease,
            onOpenDownload,
            ...toRefs(state),
        };
    },
});
</script>
