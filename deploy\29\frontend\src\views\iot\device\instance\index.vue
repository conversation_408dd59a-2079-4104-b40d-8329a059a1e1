<template>
  <div class="system-dic-container">
    <el-card shadow="hover">
      <div class="system-user-search mb15">
        <el-form :model="tableData.param" ref="queryRef" :inline="true" label-width="68px">
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="tableData.param.name" placeholder="请输入设备名称" clearable size="default" />
          </el-form-item>
          <el-form-item label="IMEI" prop="imei">
            <el-input v-model="tableData.param.imei" placeholder="请输入IMEI" clearable size="default" />
          </el-form-item>
          <el-form-item label="项目名称">
            <el-select v-model="tableData.param.projectId" size="mini" placeholder="请选择项目" filterable clearable>
              <el-option v-for="item in projectData" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" style="width: 200px;" @keyup.enter.native="getInstanceList">
            <el-select v-model="tableData.param.status" placeholder="状态" clearable>
              <el-option label="在线" :value="1" />
              <el-option label="离线" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="default" type="primary" class="ml10" @click="getInstanceList">
              <el-icon>
                <ele-Search />
              </el-icon>
              查询
            </el-button>
            <el-button size="default" @click="resetQuery(queryRef)">
              <el-icon>
                <ele-Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button size="default" type="success" class="ml10" @click="onOpenAddDic" v-auth="'add'">
              <el-icon>
                <ele-FolderAdd />
              </el-icon>
              新增设备
            </el-button>
            <el-button size="default" type="success" class="ml10" @click="onOpenBatchAddDic" v-auth="'add'">
              <el-icon>
                <ele-FolderAdd />
              </el-icon>
              批量新增设备
            </el-button>
            <el-button size="default" type="success" plain class="ml10" @click="onDownTemplate" v-auth="'template'">
              下载模板
            </el-button>
            <el-button size="default" type="success" class="ml10" @click="onRowUpload" v-auth="'upload'">
              <el-icon>
                <ele-Upload />
              </el-icon>
              导入设备
            </el-button>
            <el-button size="default" type="primary" class="ml10" @click="onRowExport" v-auth="'download'">
              <el-icon>
                <ele-Download />
              </el-icon>
              导出设备
            </el-button>
            <el-button size="default" type="danger" class="ml10" @click="onRowDel" v-auth="'del'">
              <el-icon>
                <ele-Delete />
              </el-icon>
              删除
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table :data="tableData.data" style="width: 100%" @selection-change="handleSelectionChange"
        v-loading="tableData.loading">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" width="60" align="center" type="index"></el-table-column>
        <el-table-column label="设备名" prop="name" min-width="180" show-overflow-tooltip />
        <el-table-column label="项目名" prop="projectName" min-width="180" show-overflow-tooltip />
        <el-table-column label="IMEI" prop="imei" align="center" min-width="150" />
        <el-table-column label="厂家" prop="manufactor" align="center" min-width="90" />
        <el-table-column label="设备型号" prop="model" align="center" min-width="90" />
        <el-table-column label="tac码" prop="tac" min-width="120" align="center" />
        <el-table-column prop="status" label="设备运行情况" width="100" align="center">
          <template #default="scope">
            <img src="/imgs/offline.png" alt="离线" style="width: 50%;height: 50%" v-if="scope.row.status == 0">
            <img src="/imgs/online.png" alt="在线" style="width: 50%;height: 50%" v-if="scope.row.status == 1">
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" align="center" min-width="180" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <router-link :to="'/iotmanager/device/instance/' + scope.row.id" class="link-type"
              style="padding-right: 12px;font-size: 12px;color: #409eff;" v-auth="'detail'">
              <span>详情</span>
            </router-link>
            <el-button size="small" text type="warning" @click="onOpenEditDic(scope.row)" v-auth="'edit'">修改</el-button>
            <el-button size="small" text type="danger" @click="onRowDel(scope.row)" v-auth="'del'">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="tableData.total > 0" :total="tableData.total" v-model:page="tableData.param.pageNum"
        v-model:limit="tableData.param.pageSize" @pagination="getInstanceList" />
    </el-card>
    <EditDic ref="editDicRef" @getInstanceList="getInstanceList" />
    <BatchDic ref="batchDicRef" @getInstanceList="getInstanceList" />
    <ErrorList ref="errorListRef" />

  </div>
</template>

<script lang="ts">
import { toRefs, reactive, onMounted, ref, defineComponent, h } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import EditDic from './component/edit.vue';
import BatchDic from './component/batch.vue';
import ErrorList from '/@/views/error/list.vue';
import api from '/@/api/device';
import apiProject from '/@/api/project';
import downloadFile from '/@/utils/download';

let uploadFile: File | null = null

// 定义接口来定义对象的类型
interface TableDataRow {
  id: string;
  projectId: string;
  projectName: string;
  name: string;
  imei: string;
  manufactor: string;
  model: string;
  tac: string;
  codelist: any[];
  status: number;
  remark: string;
  create_by: string;
  created_at: string;
  update_by: string;
  updated_at: string;
}
interface AllProjectList {
  id: string;
  name: string;
}
interface TableDataState {
  ids: string[];
  tableData: {
    data: Array<TableDataRow>;
    total: number;
    loading: boolean;
    param: {
      pageNum: number;
      pageSize: number;
      name: string;
      imei: string;
      projectId: string;
      status: string;
    };
  };
  projectData: AllProjectList[];
}

export default defineComponent({
  name: 'deviceInstance',
  components: { EditDic, BatchDic, ErrorList },
  setup() {
    const addDicRef = ref();
    const editDicRef = ref();
    const batchDicRef = ref();
    const detailRef = ref();
    const queryRef = ref();
    const errorListRef = ref();
    const state = reactive<TableDataState>({
      ids: [],
      tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
          pageNum: 1,
          pageSize: 10,
          name: '',
          imei: '',
          status: '',
          projectId: '',
        },
      },
      //项目数据
      projectData: []
    });
    // 初始化表格数据
    const initTableData = () => {
      getInstanceList();
      getProjectData();
    };
    const getInstanceList = () => {
      state.tableData.loading = true;
      api.instance.getList(state.tableData.param).then((res: any) => {
        state.tableData.data = res.instance;
        state.tableData.total = res.total;
      }).finally(() => (state.tableData.loading = false));
    };
    const getProjectData = () => {
      apiProject.project.getOptionList({ status: 0 }).then((res: any) => {
        state.projectData = res.project || [];
      });
    };

    //查看详情
    const onOpenDetail = (row: TableDataRow) => {
      detailRef.value.openDialog(row);
    }
    // 打开新增设备弹窗
    const onOpenAddDic = () => {
      editDicRef.value.openDialog();
    };
    // 打开批量新增设备弹窗
    const onOpenBatchAddDic = () => {
      batchDicRef.value.openDialog();
    };
    // 打开修改设备弹窗
    const onOpenEditDic = (row: TableDataRow) => {
      editDicRef.value.openDialog(row);
    };
    //导入设备
    const onRowUpload = () => {
      ElMessageBox({
        title: '上传设备信息',
        message: h('input', {
          type: 'file',
          accept: '.xls, .xlsx',
          onchange: (file: any) => {
            uploadFile = file.target.files[0]
          },
        }),
        showCancelButton: true,
        confirmButtonText: '上传',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            if (!uploadFile) return ElMessage('请先上传文件！')

            instance.confirmButtonLoading = true
            instance.confirmButtonText = '上传中...'

            const formData = new FormData()
            formData.append('file', uploadFile)

            api.instance
              .upload(formData)
              .then((res: any) => {
                ElMessage.success('上传成功')
                state.tableData.param.pageNum = 1;
                getInstanceList();
                errorListRef.value.openDialog(res.errorList);
                done()
              })
              .finally(() => {
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
          uploadFile = null
        },
      })
    };

    // 导出项目
    const onRowExport = () => {
      api.instance.export(state.tableData.param).then((res: any) => downloadFile(res, "导出设备信息.xlsx"))
    };
    //下载模板
    const onDownTemplate = () => {
      const fileUrl = './template/设备模板.xlsx'; // 文件的URL或路径
      window.open(fileUrl, '_blank');
    };
    // 删除设备
    const onRowDel = (row?: TableDataRow) => {
      let msg = '你确定要删除所选数据？';
      let ids: string[] = [];
      if (row) {
        msg = `此操作将永久删除设备：“${row.name}”，是否继续?`;
        ids = [row.id];
      } else {
        ids = state.ids;
      }
      if (ids.length === 0) {
        ElMessage.error('请选择要删除的数据。');
        return;
      }
      ElMessageBox.confirm(msg, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          api.instance.del(ids).then(() => {
            ElMessage.success('删除成功');
            getInstanceList();
          });
        })
        .catch(() => { });
    };
    // 页面加载时
    onMounted(() => {
      initTableData();
    });
    /** 重置按钮操作 */
    const resetQuery = (formEl: FormInstance | undefined) => {
      if (!formEl) return;
      formEl.resetFields();
      getInstanceList();
    };
    // 多选框选中数据
    const handleSelectionChange = (selection: TableDataRow[]) => {
      state.ids = selection.map((item) => item.id);
    };

    return {
      addDicRef,
      editDicRef,
      batchDicRef,
      detailRef,
      queryRef,
      errorListRef,
      onOpenDetail,
      onOpenAddDic,
      onOpenBatchAddDic,
      onOpenEditDic,
      onRowDel,
      onRowUpload,
      onRowExport,
      onDownTemplate,
      getInstanceList,
      getProjectData,
      resetQuery,
      handleSelectionChange,
      ...toRefs(state),
    };
  },
});
</script>
