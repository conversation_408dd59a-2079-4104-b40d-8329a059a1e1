<template>
    <div class="system-edit-dept-container">
        <el-dialog :title="'导入失败列表'" v-model="isShowDialog" width="769px">
            <el-table ref="tableRef" :border="true" v-loading="tableData.loading" :data="tableData.list" width="100%">
                <el-table-column prop="row" label="行号" min-width="80"></el-table-column>
                <el-table-column prop="reason" label="失败原因" min-width="180"></el-table-column>
            </el-table>
            <pagination v-show="tableData.total>0" :total="tableData.total" v-model:page="tableData.param.pageNum" v-model:limit="tableData.param.pageSize" @pagination="pageChange" />

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">关 闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';

interface TableDataRow {
    row: number;
    reason: string;
}

interface TableDataState {
    tableData: {
        data: Array<TableDataRow>;
        list: Array<TableDataRow>;
        total: number;
        loading: boolean;
        param: {
            pageNum: number;
            pageSize: number;
        }
    },
    isShowDialog: boolean;
}

export default defineComponent({
    name: 'ErrorList',
    setup(prop) {
        const state = reactive<TableDataState>({
            tableData: {
                data: [],
                list: [],
                total: 0,
                loading: false,
                param: {
                    pageNum: 1,
                    pageSize: 10
                },
            },
            isShowDialog: false
        });

        // 打开弹窗
        const openDialog = (data: Array<TableDataRow>) => {
            if (data) {
                state.tableData.data = data;
                state.tableData.total = data.length;
                state.tableData.list = state.tableData.data.slice(
	                   (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize,
                       state.tableData.param.pageNum * state.tableData.param.pageSize
	             );
            }
            state.isShowDialog = true;
        };
        //切换分页
        const pageChange = () => {
            if (state.tableData.data) {
                 state.tableData.list = state.tableData.data.slice(
                   (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize,
                   state.tableData.param.pageNum * state.tableData.param.pageSize
                 );
             }
        }
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };
        return {
            openDialog,
            closeDialog,
            onCancel,
            pageChange,
            ...toRefs(state),
        };
    }
})
</script>