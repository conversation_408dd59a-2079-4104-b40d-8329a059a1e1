<template>
    <div class="system-edit-dept-container">
        <el-dialog :title="(ruleForm.id ? '修改' : '添加') + '项目'" v-model="isShowDialog" width="769px">
            <el-form ref="formRef" :model="ruleForm" :rules="rules" size="default" label-width="90px">
                <el-row :gutter="35">

                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="项目名称" prop="name">
                            <el-input v-model="ruleForm.name" placeholder="请输入项目名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="项目描述" prop="desc">
                            <el-input v-model="ruleForm.desc" type="textarea" placeholder="请输入项目描述"
                                maxlength="150"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="开始时间" prop="startDate">
                            <el-date-picker v-model="ruleForm.startDate" placeholder="请选择开始时间" type="date" value-format="YYYY-MM-DD"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="结束时间" prop="endDate">
                            <el-date-picker v-model="ruleForm.endDate" placeholder="请选择结束时间" type="date" value-format="YYYY-MM-DD"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="设备数阈值" prop="threshold">
                            <el-input v-model="ruleForm.threshold" placeholder="请输入设备数阈值" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="项目状态" prop="status">
							<el-switch v-model="ruleForm.status" inline-prompt :active-value="0" :inactive-value="1" active-text="正常" inactive-text="到期" :disabled="true"></el-switch>
						</el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="上游公司" prop="company">
                            <el-select v-model="ruleForm.company" placeholder="请选择上游公司" filterable clearable value-key="item" style="width:100%;" :disabled="ruleForm.id != ''">
                                <el-option v-for="item in companyData" :key="item" :label="item" :value="item"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="ruleForm.remark" type="textarea" placeholder="请输入备注"
                                maxlength="150"></el-input>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" size="default">{{ ruleForm.id ? '修 改' : '添 加' }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
  
<script lang="ts">
import { reactive, toRefs, defineComponent, ref, unref } from 'vue';
import api from '/@/api/project';
import { thresholdValidate } from '/@/utils/validator';
import { ElMessage } from 'element-plus';

interface RuleFormState {
    id: string;
    name: string;
    desc: string;
    startDate: string;
    endDate: string;
    threshold: number;
    status: number;
    company: string;
    remark: string;
    create_by: string;
    created_at: string;
    update_by: string;
    updated_at: string;
}
interface ProjectSate {
    isShowDialog: boolean;
    ruleForm: RuleFormState;
    companyData: string[];
    rules: object;
}

const baseForm: RuleFormState = {
    id: '',
    name: '',
    desc: '',
    startDate: '',
    endDate: '',
    threshold: 0,
    status: 0,
    company: '',
    remark: '',
    create_by: '',
    created_at: '',
    update_by: '',
    updated_at: ''
};

export default defineComponent({
    name: 'EditProject',
    setup(prop, { emit }) {
        const formRef = ref<HTMLElement | null>(null);
        const state = reactive<ProjectSate>({
            isShowDialog: false,
            ruleForm: {
                ...baseForm,
            },
            companyData: [],//上游公司数据
            rules: {
                name: [{ required: true, message: '项目名称不能为空', trigger: 'blur' }],
                company: [{ required: true, message: '上游公司不能为空', trigger: 'blur' }],
                startDate: [{ required: true, message: '开始时间不能为空', trigger: 'blur' }],
                endDate: [{ required: true, message: '结束时间不能为空', trigger: 'blur' }],
                threshold: [{ required: true, validator: thresholdValidate, trigger: 'blur' }]
            },
        });

        // 打开弹窗
        const openDialog = (row?: RuleFormState | number) => {
            resetForm();
            if (row && typeof row === 'object') {
                state.ruleForm = row;
                console.log(state.ruleForm);
                console.log(state.ruleForm.startDate);
            } else if (row && typeof row === 'string') {
                state.ruleForm.id = row;
            }
            //获取上游公司列表
            api.project.getCompanyList().then((res: any) => {
                state.companyData = res.company || [];
            });
            state.isShowDialog = true;
        };
        // 关闭弹窗
        const closeDialog = () => {
            state.isShowDialog = false;
        };
        // 取消
        const onCancel = () => {
            closeDialog();
        };
        // 新增
        const onSubmit = () => {
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.validate((valid: boolean) => {
                if (valid) {
                    console.log(state.ruleForm);
                    if (!state.ruleForm.id) {
                        //添加
                        api.project.add(state.ruleForm).then(() => {
                            ElMessage.success('项目添加成功');
                            closeDialog(); // 关闭弹窗
                            emit('getProjectList');
                        });
                    } else {
                        //修改
                        api.project.edit(state.ruleForm).then(() => {
                            ElMessage.success('项目修改成功');
                            closeDialog(); // 关闭弹窗
                            emit('getProjectList');
                        });
                    }
                }
            });
        };
        const resetForm = () => {
            state.ruleForm = {
                ...baseForm,
            };
            const formWrap = unref(formRef) as any;
            if (!formWrap) return;
            formWrap.resetFields();
        };
        return {
            openDialog,
            closeDialog,
            onCancel,
            onSubmit,
            formRef,
            ...toRefs(state),
        };
    },
});
</script>

<style>
    .el-form-item .el-form-item__content .el-input {
        width: 100%;
    }
</style>