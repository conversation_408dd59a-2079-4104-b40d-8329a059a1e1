{"version": 3, "sources": ["../../codemirror/addon/edit/matchtags.js", "dep:codemirror_addon_edit_matchtags_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../fold/xml-fold\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../fold/xml-fold\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  CodeMirror.defineOption(\"matchTags\", false, function(cm, val, old) {\n    if (old && old != CodeMirror.Init) {\n      cm.off(\"cursorActivity\", doMatchTags);\n      cm.off(\"viewportChange\", maybeUpdateMatch);\n      clear(cm);\n    }\n    if (val) {\n      cm.state.matchBothTags = typeof val == \"object\" && val.bothTags;\n      cm.on(\"cursorActivity\", doMatchTags);\n      cm.on(\"viewportChange\", maybeUpdateMatch);\n      doMatchTags(cm);\n    }\n  });\n\n  function clear(cm) {\n    if (cm.state.tagHit) cm.state.tagHit.clear();\n    if (cm.state.tagOther) cm.state.tagOther.clear();\n    cm.state.tagHit = cm.state.tagOther = null;\n  }\n\n  function doMatchTags(cm) {\n    cm.state.failedTagMatch = false;\n    cm.operation(function() {\n      clear(cm);\n      if (cm.somethingSelected()) return;\n      var cur = cm.getCursor(), range = cm.getViewport();\n      range.from = Math.min(range.from, cur.line); range.to = Math.max(cur.line + 1, range.to);\n      var match = CodeMirror.findMatchingTag(cm, cur, range);\n      if (!match) return;\n      if (cm.state.matchBothTags) {\n        var hit = match.at == \"open\" ? match.open : match.close;\n        if (hit) cm.state.tagHit = cm.markText(hit.from, hit.to, {className: \"CodeMirror-matchingtag\"});\n      }\n      var other = match.at == \"close\" ? match.open : match.close;\n      if (other)\n        cm.state.tagOther = cm.markText(other.from, other.to, {className: \"CodeMirror-matchingtag\"});\n      else\n        cm.state.failedTagMatch = true;\n    });\n  }\n\n  function maybeUpdateMatch(cm) {\n    if (cm.state.failedTagMatch) doMatchTags(cm);\n  }\n\n  CodeMirror.commands.toMatchingTag = function(cm) {\n    var found = CodeMirror.findMatchingTag(cm, cm.getCursor());\n    if (found) {\n      var other = found.at == \"close\" ? found.open : found.close;\n      if (other) cm.extendSelection(other.to, other.from);\n    }\n  };\n});\n", "export default require(\"./node_modules/codemirror/addon/edit/matchtags.js\");"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,kBAA2B;AAAA,eACzD,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,kBAAkB,GAAG,GAAG;AAAA;AAExD,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,MAAAA,YAAW,aAAa,aAAa,OAAO,SAAS,IAAI,KAAK,KAAK;AACjE,YAAI,OAAO,OAAOA,YAAW,MAAM;AACjC,aAAG,IAAI,kBAAkB,WAAW;AACpC,aAAG,IAAI,kBAAkB,gBAAgB;AACzC,gBAAM,EAAE;AAAA,QACV;AACA,YAAI,KAAK;AACP,aAAG,MAAM,gBAAgB,OAAO,OAAO,YAAY,IAAI;AACvD,aAAG,GAAG,kBAAkB,WAAW;AACnC,aAAG,GAAG,kBAAkB,gBAAgB;AACxC,sBAAY,EAAE;AAAA,QAChB;AAAA,MACF,CAAC;AAED,eAAS,MAAM,IAAI;AACjB,YAAI,GAAG,MAAM;AAAQ,aAAG,MAAM,OAAO,MAAM;AAC3C,YAAI,GAAG,MAAM;AAAU,aAAG,MAAM,SAAS,MAAM;AAC/C,WAAG,MAAM,SAAS,GAAG,MAAM,WAAW;AAAA,MACxC;AAEA,eAAS,YAAY,IAAI;AACvB,WAAG,MAAM,iBAAiB;AAC1B,WAAG,UAAU,WAAW;AACtB,gBAAM,EAAE;AACR,cAAI,GAAG,kBAAkB;AAAG;AAC5B,cAAI,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,YAAY;AACjD,gBAAM,OAAO,KAAK,IAAI,MAAM,MAAM,IAAI,IAAI;AAAG,gBAAM,KAAK,KAAK,IAAI,IAAI,OAAO,GAAG,MAAM,EAAE;AACvF,cAAI,QAAQA,YAAW,gBAAgB,IAAI,KAAK,KAAK;AACrD,cAAI,CAAC;AAAO;AACZ,cAAI,GAAG,MAAM,eAAe;AAC1B,gBAAI,MAAM,MAAM,MAAM,SAAS,MAAM,OAAO,MAAM;AAClD,gBAAI;AAAK,iBAAG,MAAM,SAAS,GAAG,SAAS,IAAI,MAAM,IAAI,IAAI,EAAC,WAAW,yBAAwB,CAAC;AAAA,UAChG;AACA,cAAI,QAAQ,MAAM,MAAM,UAAU,MAAM,OAAO,MAAM;AACrD,cAAI;AACF,eAAG,MAAM,WAAW,GAAG,SAAS,MAAM,MAAM,MAAM,IAAI,EAAC,WAAW,yBAAwB,CAAC;AAAA;AAE3F,eAAG,MAAM,iBAAiB;AAAA,QAC9B,CAAC;AAAA,MACH;AAEA,eAAS,iBAAiB,IAAI;AAC5B,YAAI,GAAG,MAAM;AAAgB,sBAAY,EAAE;AAAA,MAC7C;AAEA,MAAAA,YAAW,SAAS,gBAAgB,SAAS,IAAI;AAC/C,YAAI,QAAQA,YAAW,gBAAgB,IAAI,GAAG,UAAU,CAAC;AACzD,YAAI,OAAO;AACT,cAAI,QAAQ,MAAM,MAAM,UAAU,MAAM,OAAO,MAAM;AACrD,cAAI;AAAO,eAAG,gBAAgB,MAAM,IAAI,MAAM,IAAI;AAAA,QACpD;AAAA,MACF;AAAA,IACF,CAAC;AAAA;AAAA;;;ACjED,IAAO,6CAAQ;", "names": ["CodeMirror"]}