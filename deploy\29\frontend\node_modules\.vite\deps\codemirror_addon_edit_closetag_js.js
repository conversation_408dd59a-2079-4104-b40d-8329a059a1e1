import {
  require_xml_fold
} from "./chunk-2C3PHAT4.js";
import {
  require_codemirror
} from "./chunk-2G24OWTA.js";
import {
  __commonJS
} from "./chunk-J43GMYXM.js";

// node_modules/codemirror/addon/edit/closetag.js
var require_closetag = __commonJS({
  "node_modules/codemirror/addon/edit/closetag.js"(exports, module) {
    (function(mod) {
      if (typeof exports == "object" && typeof module == "object")
        mod(require_codemirror(), require_xml_fold());
      else if (typeof define == "function" && define.amd)
        define(["../../lib/codemirror", "../fold/xml-fold"], mod);
      else
        mod(CodeMirror);
    })(function(CodeMirror2) {
      CodeMirror2.defineOption("autoCloseTags", false, function(cm, val, old) {
        if (old != CodeMirror2.Init && old)
          cm.removeKeyMap("autoCloseTags");
        if (!val)
          return;
        var map = { name: "autoCloseTags" };
        if (typeof val != "object" || val.whenClosing !== false)
          map["'/'"] = function(cm2) {
            return autoCloseSlash(cm2);
          };
        if (typeof val != "object" || val.whenOpening !== false)
          map["'>'"] = function(cm2) {
            return autoCloseGT(cm2);
          };
        cm.addKeyMap(map);
      });
      var htmlDontClose = [
        "area",
        "base",
        "br",
        "col",
        "command",
        "embed",
        "hr",
        "img",
        "input",
        "keygen",
        "link",
        "meta",
        "param",
        "source",
        "track",
        "wbr"
      ];
      var htmlIndent = [
        "applet",
        "blockquote",
        "body",
        "button",
        "div",
        "dl",
        "fieldset",
        "form",
        "frameset",
        "h1",
        "h2",
        "h3",
        "h4",
        "h5",
        "h6",
        "head",
        "html",
        "iframe",
        "layer",
        "legend",
        "object",
        "ol",
        "p",
        "select",
        "table",
        "ul"
      ];
      function autoCloseGT(cm) {
        if (cm.getOption("disableInput"))
          return CodeMirror2.Pass;
        var ranges = cm.listSelections(), replacements = [];
        var opt = cm.getOption("autoCloseTags");
        for (var i = 0; i < ranges.length; i++) {
          if (!ranges[i].empty())
            return CodeMirror2.Pass;
          var pos = ranges[i].head, tok = cm.getTokenAt(pos);
          var inner = CodeMirror2.innerMode(cm.getMode(), tok.state), state = inner.state;
          var tagInfo = inner.mode.xmlCurrentTag && inner.mode.xmlCurrentTag(state);
          var tagName = tagInfo && tagInfo.name;
          if (!tagName)
            return CodeMirror2.Pass;
          var html = inner.mode.configuration == "html";
          var dontCloseTags = typeof opt == "object" && opt.dontCloseTags || html && htmlDontClose;
          var indentTags = typeof opt == "object" && opt.indentTags || html && htmlIndent;
          if (tok.end > pos.ch)
            tagName = tagName.slice(0, tagName.length - tok.end + pos.ch);
          var lowerTagName = tagName.toLowerCase();
          if (!tagName || tok.type == "string" && (tok.end != pos.ch || !/[\"\']/.test(tok.string.charAt(tok.string.length - 1)) || tok.string.length == 1) || tok.type == "tag" && tagInfo.close || tok.string.indexOf("/") == pos.ch - tok.start - 1 || dontCloseTags && indexOf(dontCloseTags, lowerTagName) > -1 || closingTagExists(cm, inner.mode.xmlCurrentContext && inner.mode.xmlCurrentContext(state) || [], tagName, pos, true))
            return CodeMirror2.Pass;
          var emptyTags = typeof opt == "object" && opt.emptyTags;
          if (emptyTags && indexOf(emptyTags, tagName) > -1) {
            replacements[i] = { text: "/>", newPos: CodeMirror2.Pos(pos.line, pos.ch + 2) };
            continue;
          }
          var indent = indentTags && indexOf(indentTags, lowerTagName) > -1;
          replacements[i] = {
            indent,
            text: ">" + (indent ? "\n\n" : "") + "</" + tagName + ">",
            newPos: indent ? CodeMirror2.Pos(pos.line + 1, 0) : CodeMirror2.Pos(pos.line, pos.ch + 1)
          };
        }
        var dontIndentOnAutoClose = typeof opt == "object" && opt.dontIndentOnAutoClose;
        for (var i = ranges.length - 1; i >= 0; i--) {
          var info = replacements[i];
          cm.replaceRange(info.text, ranges[i].head, ranges[i].anchor, "+insert");
          var sel = cm.listSelections().slice(0);
          sel[i] = { head: info.newPos, anchor: info.newPos };
          cm.setSelections(sel);
          if (!dontIndentOnAutoClose && info.indent) {
            cm.indentLine(info.newPos.line, null, true);
            cm.indentLine(info.newPos.line + 1, null, true);
          }
        }
      }
      function autoCloseCurrent(cm, typingSlash) {
        var ranges = cm.listSelections(), replacements = [];
        var head = typingSlash ? "/" : "</";
        var opt = cm.getOption("autoCloseTags");
        var dontIndentOnAutoClose = typeof opt == "object" && opt.dontIndentOnSlash;
        for (var i = 0; i < ranges.length; i++) {
          if (!ranges[i].empty())
            return CodeMirror2.Pass;
          var pos = ranges[i].head, tok = cm.getTokenAt(pos);
          var inner = CodeMirror2.innerMode(cm.getMode(), tok.state), state = inner.state;
          if (typingSlash && (tok.type == "string" || tok.string.charAt(0) != "<" || tok.start != pos.ch - 1))
            return CodeMirror2.Pass;
          var replacement, mixed = inner.mode.name != "xml" && cm.getMode().name == "htmlmixed";
          if (mixed && inner.mode.name == "javascript") {
            replacement = head + "script";
          } else if (mixed && inner.mode.name == "css") {
            replacement = head + "style";
          } else {
            var context = inner.mode.xmlCurrentContext && inner.mode.xmlCurrentContext(state);
            var top = context.length ? context[context.length - 1] : "";
            if (!context || context.length && closingTagExists(cm, context, top, pos))
              return CodeMirror2.Pass;
            replacement = head + top;
          }
          if (cm.getLine(pos.line).charAt(tok.end) != ">")
            replacement += ">";
          replacements[i] = replacement;
        }
        cm.replaceSelections(replacements);
        ranges = cm.listSelections();
        if (!dontIndentOnAutoClose) {
          for (var i = 0; i < ranges.length; i++)
            if (i == ranges.length - 1 || ranges[i].head.line < ranges[i + 1].head.line)
              cm.indentLine(ranges[i].head.line);
        }
      }
      function autoCloseSlash(cm) {
        if (cm.getOption("disableInput"))
          return CodeMirror2.Pass;
        return autoCloseCurrent(cm, true);
      }
      CodeMirror2.commands.closeTag = function(cm) {
        return autoCloseCurrent(cm);
      };
      function indexOf(collection, elt) {
        if (collection.indexOf)
          return collection.indexOf(elt);
        for (var i = 0, e = collection.length; i < e; ++i)
          if (collection[i] == elt)
            return i;
        return -1;
      }
      function closingTagExists(cm, context, tagName, pos, newTag) {
        if (!CodeMirror2.scanForClosingTag)
          return false;
        var end = Math.min(cm.lastLine() + 1, pos.line + 500);
        var nextClose = CodeMirror2.scanForClosingTag(cm, pos, null, end);
        if (!nextClose || nextClose.tag != tagName)
          return false;
        var onCx = newTag ? 1 : 0;
        for (var i = context.length - 1; i >= 0; i--) {
          if (context[i] == tagName)
            ++onCx;
          else
            break;
        }
        pos = nextClose.to;
        for (var i = 1; i < onCx; i++) {
          var next = CodeMirror2.scanForClosingTag(cm, pos, null, end);
          if (!next || next.tag != tagName)
            return false;
          pos = next.to;
        }
        return true;
      }
    });
  }
});

// dep:codemirror_addon_edit_closetag_js
var codemirror_addon_edit_closetag_js_default = require_closetag();
export {
  codemirror_addon_edit_closetag_js_default as default
};
//# sourceMappingURL=codemirror_addon_edit_closetag_js.js.map
