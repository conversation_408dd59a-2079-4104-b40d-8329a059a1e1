{"version": 3, "sources": ["../../codemirror/mode/htmlmixed/htmlmixed.js", "dep:codemirror_mode_htmlmixed_htmlmixed_js"], "sourcesContent": ["// CodeMirror, copyright (c) by <PERSON><PERSON> and others\n// Distributed under an MIT license: https://codemirror.net/5/LICENSE\n\n(function(mod) {\n  if (typeof exports == \"object\" && typeof module == \"object\") // CommonJS\n    mod(require(\"../../lib/codemirror\"), require(\"../xml/xml\"), require(\"../javascript/javascript\"), require(\"../css/css\"));\n  else if (typeof define == \"function\" && define.amd) // AMD\n    define([\"../../lib/codemirror\", \"../xml/xml\", \"../javascript/javascript\", \"../css/css\"], mod);\n  else // Plain browser env\n    mod(CodeMirror);\n})(function(CodeMirror) {\n  \"use strict\";\n\n  var defaultTags = {\n    script: [\n      [\"lang\", /(javascript|babel)/i, \"javascript\"],\n      [\"type\", /^(?:text|application)\\/(?:x-)?(?:java|ecma)script$|^module$|^$/i, \"javascript\"],\n      [\"type\", /./, \"text/plain\"],\n      [null, null, \"javascript\"]\n    ],\n    style:  [\n      [\"lang\", /^css$/i, \"css\"],\n      [\"type\", /^(text\\/)?(x-)?(stylesheet|css)$/i, \"css\"],\n      [\"type\", /./, \"text/plain\"],\n      [null, null, \"css\"]\n    ]\n  };\n\n  function maybeBackup(stream, pat, style) {\n    var cur = stream.current(), close = cur.search(pat);\n    if (close > -1) {\n      stream.backUp(cur.length - close);\n    } else if (cur.match(/<\\/?$/)) {\n      stream.backUp(cur.length);\n      if (!stream.match(pat, false)) stream.match(cur);\n    }\n    return style;\n  }\n\n  var attrRegexpCache = {};\n  function getAttrRegexp(attr) {\n    var regexp = attrRegexpCache[attr];\n    if (regexp) return regexp;\n    return attrRegexpCache[attr] = new RegExp(\"\\\\s+\" + attr + \"\\\\s*=\\\\s*('|\\\")?([^'\\\"]+)('|\\\")?\\\\s*\");\n  }\n\n  function getAttrValue(text, attr) {\n    var match = text.match(getAttrRegexp(attr))\n    return match ? /^\\s*(.*?)\\s*$/.exec(match[2])[1] : \"\"\n  }\n\n  function getTagRegexp(tagName, anchored) {\n    return new RegExp((anchored ? \"^\" : \"\") + \"<\\/\\\\s*\" + tagName + \"\\\\s*>\", \"i\");\n  }\n\n  function addTags(from, to) {\n    for (var tag in from) {\n      var dest = to[tag] || (to[tag] = []);\n      var source = from[tag];\n      for (var i = source.length - 1; i >= 0; i--)\n        dest.unshift(source[i])\n    }\n  }\n\n  function findMatchingMode(tagInfo, tagText) {\n    for (var i = 0; i < tagInfo.length; i++) {\n      var spec = tagInfo[i];\n      if (!spec[0] || spec[1].test(getAttrValue(tagText, spec[0]))) return spec[2];\n    }\n  }\n\n  CodeMirror.defineMode(\"htmlmixed\", function (config, parserConfig) {\n    var htmlMode = CodeMirror.getMode(config, {\n      name: \"xml\",\n      htmlMode: true,\n      multilineTagIndentFactor: parserConfig.multilineTagIndentFactor,\n      multilineTagIndentPastTag: parserConfig.multilineTagIndentPastTag,\n      allowMissingTagName: parserConfig.allowMissingTagName,\n    });\n\n    var tags = {};\n    var configTags = parserConfig && parserConfig.tags, configScript = parserConfig && parserConfig.scriptTypes;\n    addTags(defaultTags, tags);\n    if (configTags) addTags(configTags, tags);\n    if (configScript) for (var i = configScript.length - 1; i >= 0; i--)\n      tags.script.unshift([\"type\", configScript[i].matches, configScript[i].mode])\n\n    function html(stream, state) {\n      var style = htmlMode.token(stream, state.htmlState), tag = /\\btag\\b/.test(style), tagName\n      if (tag && !/[<>\\s\\/]/.test(stream.current()) &&\n          (tagName = state.htmlState.tagName && state.htmlState.tagName.toLowerCase()) &&\n          tags.hasOwnProperty(tagName)) {\n        state.inTag = tagName + \" \"\n      } else if (state.inTag && tag && />$/.test(stream.current())) {\n        var inTag = /^([\\S]+) (.*)/.exec(state.inTag)\n        state.inTag = null\n        var modeSpec = stream.current() == \">\" && findMatchingMode(tags[inTag[1]], inTag[2])\n        var mode = CodeMirror.getMode(config, modeSpec)\n        var endTagA = getTagRegexp(inTag[1], true), endTag = getTagRegexp(inTag[1], false);\n        state.token = function (stream, state) {\n          if (stream.match(endTagA, false)) {\n            state.token = html;\n            state.localState = state.localMode = null;\n            return null;\n          }\n          return maybeBackup(stream, endTag, state.localMode.token(stream, state.localState));\n        };\n        state.localMode = mode;\n        state.localState = CodeMirror.startState(mode, htmlMode.indent(state.htmlState, \"\", \"\"));\n      } else if (state.inTag) {\n        state.inTag += stream.current()\n        if (stream.eol()) state.inTag += \" \"\n      }\n      return style;\n    };\n\n    return {\n      startState: function () {\n        var state = CodeMirror.startState(htmlMode);\n        return {token: html, inTag: null, localMode: null, localState: null, htmlState: state};\n      },\n\n      copyState: function (state) {\n        var local;\n        if (state.localState) {\n          local = CodeMirror.copyState(state.localMode, state.localState);\n        }\n        return {token: state.token, inTag: state.inTag,\n                localMode: state.localMode, localState: local,\n                htmlState: CodeMirror.copyState(htmlMode, state.htmlState)};\n      },\n\n      token: function (stream, state) {\n        return state.token(stream, state);\n      },\n\n      indent: function (state, textAfter, line) {\n        if (!state.localMode || /^\\s*<\\//.test(textAfter))\n          return htmlMode.indent(state.htmlState, textAfter, line);\n        else if (state.localMode.indent)\n          return state.localMode.indent(state.localState, textAfter, line);\n        else\n          return CodeMirror.Pass;\n      },\n\n      innerMode: function (state) {\n        return {state: state.localState || state.htmlState, mode: state.localMode || htmlMode};\n      }\n    };\n  }, \"xml\", \"javascript\", \"css\");\n\n  CodeMirror.defineMIME(\"text/html\", \"htmlmixed\");\n});\n", "export default require(\"./node_modules/codemirror/mode/htmlmixed/htmlmixed.js\");"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAGA,KAAC,SAAS,KAAK;AACb,UAAI,OAAO,WAAW,YAAY,OAAO,UAAU;AACjD,YAAI,sBAAiC,eAAuB,sBAAqC,aAAqB;AAAA,eAC/G,OAAO,UAAU,cAAc,OAAO;AAC7C,eAAO,CAAC,wBAAwB,cAAc,4BAA4B,YAAY,GAAG,GAAG;AAAA;AAE5F,YAAI,UAAU;AAAA,IAClB,GAAG,SAASA,aAAY;AACtB;AAEA,UAAI,cAAc;AAAA,QAChB,QAAQ;AAAA,UACN,CAAC,QAAQ,uBAAuB,YAAY;AAAA,UAC5C,CAAC,QAAQ,mEAAmE,YAAY;AAAA,UACxF,CAAC,QAAQ,KAAK,YAAY;AAAA,UAC1B,CAAC,MAAM,MAAM,YAAY;AAAA,QAC3B;AAAA,QACA,OAAQ;AAAA,UACN,CAAC,QAAQ,UAAU,KAAK;AAAA,UACxB,CAAC,QAAQ,qCAAqC,KAAK;AAAA,UACnD,CAAC,QAAQ,KAAK,YAAY;AAAA,UAC1B,CAAC,MAAM,MAAM,KAAK;AAAA,QACpB;AAAA,MACF;AAEA,eAAS,YAAY,QAAQ,KAAK,OAAO;AACvC,YAAI,MAAM,OAAO,QAAQ,GAAG,QAAQ,IAAI,OAAO,GAAG;AAClD,YAAI,QAAQ,IAAI;AACd,iBAAO,OAAO,IAAI,SAAS,KAAK;AAAA,QAClC,WAAW,IAAI,MAAM,OAAO,GAAG;AAC7B,iBAAO,OAAO,IAAI,MAAM;AACxB,cAAI,CAAC,OAAO,MAAM,KAAK,KAAK;AAAG,mBAAO,MAAM,GAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,UAAI,kBAAkB,CAAC;AACvB,eAAS,cAAc,MAAM;AAC3B,YAAI,SAAS,gBAAgB;AAC7B,YAAI;AAAQ,iBAAO;AACnB,eAAO,gBAAgB,QAAQ,IAAI,OAAO,SAAS,OAAO,mCAAsC;AAAA,MAClG;AAEA,eAAS,aAAa,MAAM,MAAM;AAChC,YAAI,QAAQ,KAAK,MAAM,cAAc,IAAI,CAAC;AAC1C,eAAO,QAAQ,gBAAgB,KAAK,MAAM,EAAE,EAAE,KAAK;AAAA,MACrD;AAEA,eAAS,aAAa,SAAS,UAAU;AACvC,eAAO,IAAI,QAAQ,WAAW,MAAM,MAAM,WAAY,UAAU,SAAS,GAAG;AAAA,MAC9E;AAEA,eAAS,QAAQ,MAAM,IAAI;AACzB,iBAAS,OAAO,MAAM;AACpB,cAAI,OAAO,GAAG,SAAS,GAAG,OAAO,CAAC;AAClC,cAAI,SAAS,KAAK;AAClB,mBAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AACtC,iBAAK,QAAQ,OAAO,EAAE;AAAA,QAC1B;AAAA,MACF;AAEA,eAAS,iBAAiB,SAAS,SAAS;AAC1C,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,cAAI,OAAO,QAAQ;AACnB,cAAI,CAAC,KAAK,MAAM,KAAK,GAAG,KAAK,aAAa,SAAS,KAAK,EAAE,CAAC;AAAG,mBAAO,KAAK;AAAA,QAC5E;AAAA,MACF;AAEA,MAAAA,YAAW,WAAW,aAAa,SAAU,QAAQ,cAAc;AACjE,YAAI,WAAWA,YAAW,QAAQ,QAAQ;AAAA,UACxC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,0BAA0B,aAAa;AAAA,UACvC,2BAA2B,aAAa;AAAA,UACxC,qBAAqB,aAAa;AAAA,QACpC,CAAC;AAED,YAAI,OAAO,CAAC;AACZ,YAAI,aAAa,gBAAgB,aAAa,MAAM,eAAe,gBAAgB,aAAa;AAChG,gBAAQ,aAAa,IAAI;AACzB,YAAI;AAAY,kBAAQ,YAAY,IAAI;AACxC,YAAI;AAAc,mBAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG;AAC9D,iBAAK,OAAO,QAAQ,CAAC,QAAQ,aAAa,GAAG,SAAS,aAAa,GAAG,IAAI,CAAC;AAE7E,iBAAS,KAAK,QAAQ,OAAO;AAC3B,cAAI,QAAQ,SAAS,MAAM,QAAQ,MAAM,SAAS,GAAG,MAAM,UAAU,KAAK,KAAK,GAAG;AAClF,cAAI,OAAO,CAAC,WAAW,KAAK,OAAO,QAAQ,CAAC,MACvC,UAAU,MAAM,UAAU,WAAW,MAAM,UAAU,QAAQ,YAAY,MAC1E,KAAK,eAAe,OAAO,GAAG;AAChC,kBAAM,QAAQ,UAAU;AAAA,UAC1B,WAAW,MAAM,SAAS,OAAO,KAAK,KAAK,OAAO,QAAQ,CAAC,GAAG;AAC5D,gBAAI,QAAQ,gBAAgB,KAAK,MAAM,KAAK;AAC5C,kBAAM,QAAQ;AACd,gBAAI,WAAW,OAAO,QAAQ,KAAK,OAAO,iBAAiB,KAAK,MAAM,KAAK,MAAM,EAAE;AACnF,gBAAI,OAAOA,YAAW,QAAQ,QAAQ,QAAQ;AAC9C,gBAAI,UAAU,aAAa,MAAM,IAAI,IAAI,GAAG,SAAS,aAAa,MAAM,IAAI,KAAK;AACjF,kBAAM,QAAQ,SAAUC,SAAQC,QAAO;AACrC,kBAAID,QAAO,MAAM,SAAS,KAAK,GAAG;AAChC,gBAAAC,OAAM,QAAQ;AACd,gBAAAA,OAAM,aAAaA,OAAM,YAAY;AACrC,uBAAO;AAAA,cACT;AACA,qBAAO,YAAYD,SAAQ,QAAQC,OAAM,UAAU,MAAMD,SAAQC,OAAM,UAAU,CAAC;AAAA,YACpF;AACA,kBAAM,YAAY;AAClB,kBAAM,aAAaF,YAAW,WAAW,MAAM,SAAS,OAAO,MAAM,WAAW,IAAI,EAAE,CAAC;AAAA,UACzF,WAAW,MAAM,OAAO;AACtB,kBAAM,SAAS,OAAO,QAAQ;AAC9B,gBAAI,OAAO,IAAI;AAAG,oBAAM,SAAS;AAAA,UACnC;AACA,iBAAO;AAAA,QACT;AAAC;AAED,eAAO;AAAA,UACL,YAAY,WAAY;AACtB,gBAAI,QAAQA,YAAW,WAAW,QAAQ;AAC1C,mBAAO,EAAC,OAAO,MAAM,OAAO,MAAM,WAAW,MAAM,YAAY,MAAM,WAAW,MAAK;AAAA,UACvF;AAAA,UAEA,WAAW,SAAU,OAAO;AAC1B,gBAAI;AACJ,gBAAI,MAAM,YAAY;AACpB,sBAAQA,YAAW,UAAU,MAAM,WAAW,MAAM,UAAU;AAAA,YAChE;AACA,mBAAO;AAAA,cAAC,OAAO,MAAM;AAAA,cAAO,OAAO,MAAM;AAAA,cACjC,WAAW,MAAM;AAAA,cAAW,YAAY;AAAA,cACxC,WAAWA,YAAW,UAAU,UAAU,MAAM,SAAS;AAAA,YAAC;AAAA,UACpE;AAAA,UAEA,OAAO,SAAU,QAAQ,OAAO;AAC9B,mBAAO,MAAM,MAAM,QAAQ,KAAK;AAAA,UAClC;AAAA,UAEA,QAAQ,SAAU,OAAO,WAAW,MAAM;AACxC,gBAAI,CAAC,MAAM,aAAa,UAAU,KAAK,SAAS;AAC9C,qBAAO,SAAS,OAAO,MAAM,WAAW,WAAW,IAAI;AAAA,qBAChD,MAAM,UAAU;AACvB,qBAAO,MAAM,UAAU,OAAO,MAAM,YAAY,WAAW,IAAI;AAAA;AAE/D,qBAAOA,YAAW;AAAA,UACtB;AAAA,UAEA,WAAW,SAAU,OAAO;AAC1B,mBAAO,EAAC,OAAO,MAAM,cAAc,MAAM,WAAW,MAAM,MAAM,aAAa,SAAQ;AAAA,UACvF;AAAA,QACF;AAAA,MACF,GAAG,OAAO,cAAc,KAAK;AAE7B,MAAAA,YAAW,WAAW,aAAa,WAAW;AAAA,IAChD,CAAC;AAAA;AAAA;;;ACxJD,IAAO,iDAAQ;", "names": ["CodeMirror", "stream", "state"]}